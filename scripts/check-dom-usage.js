#!/usr/bin/env node
import { execSync } from 'child_process';
import chalk from 'chalk';

// 添加忽略文件列表（使用相对路径）
const IGNORE_FILES = [
  'scripts/check-dom-usage.js'
];

// 获取暂存区的变更文件
const changedFiles = execSync('git diff --cached --name-only --diff-filter=ACMR', { encoding: 'utf-8' })
  .split('\n')
  .filter(Boolean)
  .filter(file => {
    const shouldCheck = file.endsWith('.js') || file.endsWith('.ts') || 
                       file.endsWith('.jsx') || file.endsWith('.tsx');
    const isIgnored = IGNORE_FILES.some(ignorePath => file.endsWith(ignorePath));
    return shouldCheck && !isIgnored;
});

if (changedFiles.length === 0) {
  process.exit(0);
}

const domKeywords = ['document', 'window'];
const invalidFiles = [];

// 按文件识别
// changedFiles.forEach(file => {
//   const content = execSync(`git show :${file}`, { encoding: 'utf-8' });
//   const lines = content.split('\n');
  
//   lines.forEach((line, index) => {
//     domKeywords.forEach(keyword => {
//       if (line.includes(keyword) && !line.includes(`// ALLOW ${keyword}`)) {
//         invalidFiles.push({
//           file,
//           line: index + 1,
//           keyword,
//           code: line.trim()
//         });
//       }
//     });
//   });
// });

// 按行识别
changedFiles.forEach(file => {
  // 获取变更的行号
  const diffOutput = execSync(`git diff --cached -U0 ${file}`, { encoding: 'utf-8' });
  const changedLines = new Set();

  let currentLine = 0;
  let inHunk = false;
  
  diffOutput.split('\n').forEach(line => {
    // 匹配 @@ -a,b +c,d @@ 格式
    if (line.startsWith('@@')) {
      const match = line.match(/@@ -\d+(?:,\d+)? \+(\d+)(?:,(\d+))? @@/);
      if (match) {
        currentLine = parseInt(match[1], 10);
        inHunk = true;
      }
    } 
    // 处于代码块内
    else if (inHunk) {
      // 新增或修改的行（以+开头）
      if (line.startsWith('+')) {
        changedLines.add(currentLine);
        currentLine++;
      } 
      // 删除的行（以-开头）不影响新文件的行号
      else if (line.startsWith('-')) {
        // 不增加行号
      } 
      // 上下文行（以空格开头）
      else if (line.startsWith(' ')) {
        currentLine++;
      }
    }
  });

  const content = execSync(`git show :${file}`, { encoding: 'utf-8' });
  const lines = content.split('\n');
  
  lines.forEach((line, index) => {
    const lineNumber = index + 1;
    // 只检查变更的行
    if (changedLines.has(lineNumber)) {
      domKeywords.forEach(keyword => {
        if (line.includes(keyword) && !line.includes(`// ALLOW ${keyword}`)) {
          invalidFiles.push({
            file,
            line: lineNumber,
            keyword,
            code: line.trim()
          });
        }
      });
    }
  });
});

if (invalidFiles.length > 0) {
  // 过滤掉忽略文件中的违规项
  const filteredInvalidFiles = invalidFiles.filter(
    ({file}) => !IGNORE_FILES.some(ignorePath => file.endsWith(ignorePath))
  );

  if (filteredInvalidFiles.length > 0) {
    console.log(chalk.red('⚠️ 检测到以下文件包含不允许的DOM API使用:'));
    
    filteredInvalidFiles.forEach(({ file, line, keyword, code }) => {
      console.log(chalk.yellow(`  ${file}:${line}`));
      console.log(`  关键词: ${chalk.red(keyword)}`);
      console.log(`  代码: ${code}\n`);
    });

    console.log(chalk.blue('如果必须使用这些API，请在代码后添加注释:'));
    console.log(chalk.green('  // ALLOW document 或 // ALLOW window'));
    console.log(chalk.red('\n提交被阻止，请修复上述问题后再试。'));
    process.exit(1);
  } else {
    console.log(chalk.green('✅ 所有需要检查的文件都符合规范'));
  }
}

process.exit(0);
