#!/usr/bin/env node

/**
 * Git pre-commit hook for file size checking (Node.js version)
 * 图片文件限制: 500KB
 * 其他文件限制: 100KB
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置常量
const CONFIG = {
  IMAGE_MAX_SIZE: 500 * 1024,    // 500KB
  OTHER_MAX_SIZE: 100 * 1024,     // 100KB
  IMAGE_EXTENSIONS: new Set([
    'jpg', 'jpeg', 'png', 'gif', 'bmp',
    'tiff', 'tif', 'webp', 'svg', 'ico',
    'apng', 'jfif', 'avif', 'heic', 'heif',
  ]),
  // 白名单配置：可以是完整路径或者使用通配符
  WHITELIST: [
    'yarn.lock',
  ]
};

// 颜色常量
const COLORS = {
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  CYAN: '\x1b[36m',
  RESET: '\x1b[0m',
  BOLD: '\x1b[1m'
};

/**
 * 彩色日志输出
 */
const log = {
  error: (msg) => console.log(`${COLORS.RED}${msg}${COLORS.RESET}`),
  success: (msg) => console.log(`${COLORS.GREEN}${msg}${COLORS.RESET}`),
  warning: (msg) => console.log(`${COLORS.YELLOW}${msg}${COLORS.RESET}`),
  info: (msg) => console.log(`${COLORS.CYAN}${msg}${COLORS.RESET}`),
  normal: (msg) => console.log(msg)
};

/**
 * 检查文件是否为图片
 */
function isImage(filePath) {
  const ext = path.extname(filePath).toLowerCase().slice(1);
  return CONFIG.IMAGE_EXTENSIONS.has(ext);
}

/**
 * 检查文件是否在白名单中
 */
function isWhitelisted(filePath) {
  // 如果没有配置白名单，返回 false
  if (!CONFIG.WHITELIST || CONFIG.WHITELIST.length === 0) {
    return false;
  }

  // 使用简单的通配符匹配
  for (const pattern of CONFIG.WHITELIST) {
    // 完全匹配
    if (pattern === filePath) {
      return true;
    }

    // 简单通配符匹配
    // 将通配符转换为正则表达式
    const regexPattern = pattern
      .replace(/\./g, '\\.')             // 先转义所有的点
      .replace(/\*\*/g, '##DOUBLESTAR##') // 临时替换 **
      .replace(/\*/g, '[^/]*')           // * 匹配除了 / 之外的任意字符
      .replace(/##DOUBLESTAR##/g, '.*')  // ** 匹配任意字符（包括 /）
      .replace(/\?/g, '.');              // ? 匹配单个字符

    const regex = new RegExp(`^${regexPattern}$`);
    if (regex.test(filePath)) {
      return true;
    }
  }

  return false;
}

/**
 * 格式化文件大小显示
 */
function formatSize(bytes) {
  if (bytes >= 1024 * 1024) {
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
  } else if (bytes >= 1024) {
    return `${(bytes / 1024).toFixed(1)}KB`;
  } else {
    return `${bytes}B`;
  }
}

/**
 * 获取待提交的文件列表
 */
function getStagedFiles() {
  try {
    const output = execSync('git diff --cached --name-only --diff-filter=AM', {
      encoding: 'utf8',
      stdio: ['pipe', 'pipe', 'pipe']
    });

    return output.trim().split('\n').filter(file => file.trim() !== '');
  } catch (error) {
    log.error('❌ 获取待提交文件列表失败');
    console.error(error.message);
    process.exit(1);
  }
}

/**
 * 检查单个文件大小
 */
function checkFileSize(filePath) {
  try {
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return { valid: true, skipped: true };
    }

    // 检查是否在白名单中
    if (isWhitelisted(filePath)) {
      return {
        filePath,
        valid: true,
        skipped: true,
        whitelisted: true,
        reason: '在白名单中'
      };
    }

    const stats = fs.statSync(filePath);
    const fileSize = stats.size;
    const isImg = isImage(filePath);
    const maxSize = isImg ? CONFIG.IMAGE_MAX_SIZE : CONFIG.OTHER_MAX_SIZE;
    const fileType = isImg ? '图片' : '文件';
    const sizeLimit = isImg ? '500KB' : '100KB';

    const result = {
      filePath,
      fileSize,
      formattedSize: formatSize(fileSize),
      isImage: isImg,
      fileType,
      sizeLimit,
      valid: fileSize <= maxSize,
      skipped: false,
      whitelisted: false
    };

    return result;
  } catch (error) {
    log.warning(`⚠️  无法检查文件: ${filePath} - ${error.message}`);
    return { valid: true, skipped: true };
  }
}

/**
 * 显示检查结果
 */
function displayResult(result) {
  if (result.skipped && !result.whitelisted) return;

  // 白名单文件特殊处理
  if (result.whitelisted) {
    console.log(`  ${COLORS.CYAN}⚪ ${result.filePath} - ${result.reason}${COLORS.RESET}`);
    return;
  }

  const icon = result.valid ? '✅' : '❌';
  const color = result.valid ? COLORS.GREEN : COLORS.RED;
  const status = result.valid ? `${result.fileType}大小正常` : `${result.fileType}超过 ${result.sizeLimit} 限制`;

  console.log(`  ${color}${icon} ${result.filePath} (${result.formattedSize}) - ${status}${COLORS.RESET}`);
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log();
  log.error('🚫 提交被阻止！');
  console.log();
  log.warning('文件大小限制:');
  console.log('  • 图片文件: 最大 500KB');
  console.log('  • 其他文件: 最大 100KB');
  console.log();
  log.warning('建议解决方案:');
  console.log('  • 压缩图片文件大小');
  console.log('  • 联系动效同学处理大文件');
  console.log('  • 考虑将大文件放在离线包拆包中');
  console.log();
}

/**
 * 主函数
 */
function main() {
  log.info('🔍 检查文件大小限制...');

  // 获取待提交文件列表
  const stagedFiles = getStagedFiles();

  if (stagedFiles.length === 0) {
    log.success('✅ 没有文件需要检查');
    process.exit(0);
  }

  console.log('\n检查文件列表:');

  let hasViolation = false;
  const results = [];

  // 检查每个文件
  for (const file of stagedFiles) {
    const result = checkFileSize(file);
    results.push(result);

    if (!result.valid && !result.skipped) {
      hasViolation = true;
    }

    displayResult(result);
  }

  // 显示统计信息
  const whitelistedFiles = results.filter(r => r.whitelisted);
  const checkedFiles = results.filter(r => !r.skipped || r.whitelisted);
  const validFiles = checkedFiles.filter(r => r.valid && !r.whitelisted);
  const invalidFiles = checkedFiles.filter(r => !r.valid);

  console.log();
  let statsMsg = `📊 检查统计: 总计 ${checkedFiles.length} 个文件`;
  if (whitelistedFiles.length > 0) {
    statsMsg += `, 白名单 ${whitelistedFiles.length} 个`;
  }
  statsMsg += `, 通过 ${validFiles.length} 个, 失败 ${invalidFiles.length} 个`;
  log.info(statsMsg);

  // 如果有违规文件，阻止提交
  if (hasViolation) {
    showHelp();
    process.exit(1);
  }

  log.success('✅ 所有文件大小检查通过！');
  process.exit(0);
}

// 错误处理
process.on('uncaughtException', (error) => {
  log.error(`❌ 未捕获的异常: ${error.message}`);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  log.error(`❌ 未处理的 Promise 拒绝: ${reason}`);
  process.exit(1);
});

// 运行主函数
if (require.main === module) {
  main();
}
