'use strict';

/**
 * @music/elint-preset-social
 *
 * npm：http://npm.hz.netease.com/package/@music/elint-preset-cocos
 * gitlab：https://g.hz.netease.com/cloudmusic-frontend/lint/elint-preset-cocos
 *
 * 此文件自动生成，禁止修改！
 * 此文件自动生成，禁止修改！
 * 此文件自动生成，禁止修改！
 */

module.exports = {
    plugins: ['stylelint-prettier'],
    extends: ['@music/stylelint-config-base', 'stylelint-config-prettier'],
    rules: {
        'prettier/prettier': true,
    },
};
