// babel-preset-taro 更多选项和默认值：
// https://github.com/NervJS/taro/blob/next/packages/babel-preset-taro/README.md
module.exports = {
    presets: [
        ['taro', {
            framework: 'react',
            ts: true,
            useBuiltIns: process.env.TARO_ENV === 'h5' ? 'usage' : false,
            corejs: 3,
            // 添加 targets 配置，确保兼容低端设备
            targets: {
                browsers: [
                    'last 3 versions',
                    'Android >= 6',
                    'ios >= 13'
                ]
            }
        }]
    ]
}
