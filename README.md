## 背景

1. 需要同时投放H5、微信小程序、抖音小程序等平台

### 环境要求
要求：node > 20
yarn 

### 运行
```
yarn dev:h5

## 目录介绍

## Taro 3.6.16
2025-05-14：taro从3.5.7升级到3.6.16，升级原因：
1. 解决小程序渲染报错问题
2. 解决安卓端页面跳转，上一页提前display: none导致离线包加载图片提前可见问题
3. 3.6.17以上版本swiper内嵌scrollView以后无法滑动

### React 18

### pnpm管理依赖

### Zustand

### 测试环境调试工具 Eruda

### 注意事项
> 由于需要兼容小程序运行，以下禁止API

1、document 和 window 不可用
2、IntersectionObserver 不可用
3、requestAnimationFrame 不可用

## 常见问题

1. 书写 css 的时候如何不让 px 单位转换成 rem

使用大写的 `PX`

2. 小程序下不允许用window和document，会在commit阶段阻止提交，必须添加 // ALLOW 注释才能允许提交
> document和window替换方案见文档，持续更新中：https://docs.popo.netease.com/lingxi/0abe5f429d744be4a94d2225d5ec2ad1?tab=1&xyz=1745231803637&xyz=1745287706176
```
// 不允许的写法
const height = document.body.clientHeight; // 会被阻止

// 建议写法
const info = getSystemInfoSync()
console.log(info.windowWidth, info.windowHeight)

// 允许的写法
const height = document.body.clientHeight; // ALLOW document
```

## 业务说明
[文档地址](https://docs.popo.netease.com/lingxi/3c152f09db4f474caee918439e19e814)


