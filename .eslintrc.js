'use strict';

/**
 * @music/elint-preset-social
 *
 * npm：http://npm.hz.netease.com/package/@music/elint-preset-cocos
 * gitlab：https://g.hz.netease.com/cloudmusic-frontend/lint/elint-preset
 *
 * 此文件自动生成，禁止修改！
 * 此文件自动生成，禁止修改！
 * 此文件自动生成，禁止修改！
 */

module.exports = {
    plugins: ['prettier'],
    extends: [
        '@music/eslint-plugin-base',
        '@music/eslint-plugin-react',
        '@music/eslint-plugin-typescript/react',
        'eslint-config-prettier',
    ].map(require.resolve),
    rules: {
        'prettier/prettier': 'error',
        'arrow-body-style': 'off',
        'max-len': 'off',
        'prefer-arrow-callback': 'off',
        '@typescript-eslint/explicit-module-boundary-types': 'off',
    },
};
