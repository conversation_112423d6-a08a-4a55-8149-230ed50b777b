import plugin from 'tailwindcss/plugin';

/* eslint-disable no-param-reassign */
/** @type {import('tailwindcss').Config} */

module.exports = {
    content: ['./src/**/*.{js,ts,jsx,tsx}'],
    plugins: [
        plugin(function ({ matchUtilities }) {
            matchUtilities({
                // Usage:
                // bgimg-[main-1.png]
                // bgimg-[main-1.png,top_center,375px_905px,no-repeat]
                // bgimg-['https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/31414343507/f896/f56b/bd2b/e724bb3fdf70a1091c7bceaa337e9e79.png']
                bgimg: (value) => {
                    const [img, position = 'center', size = 'contain', repeat = 'no-repeat'] =
                        value.split(',');

                    if (!img) {
                        return {
                            backgroundPosition: position,
                            backgroundSize: size,
                            backgroundRepeat: repeat,
                            // background: `${position} / ${size} ${repeat} url(${url})`,
                        };
                    }
                    const isCdnImage = (v) => /https?:\/\//.test(v) || /\/\//.test(v);
                    const url = isCdnImage(img) ? img : `"@/assets/${img}"`;

                    // Tailwind 会把 _ 转成空格，所以我们不用处理
                    return {
                        backgroundPosition: position,
                        backgroundSize: size,
                        backgroundRepeat: repeat,
                        backgroundImage: `url(${url})`,
                        // background: `${position} / ${size} ${repeat} url(${url})`,
                    };
                },
                // Usage:
                // background: linear-gradient(0deg, #D1934E, #91725D); = bglinear-[0_#D1934E_#91725D]
                bglinear: (value) => {
                    //Tailwind 会把 _ 转成空格，所以我们不用处理
                    const [angle = 0, ...colors] = value.split(' ');
                    const colorStr = colors.map((color) => color.replace('-', ' ')).join(', ');
                    return {
                        background: `linear-gradient(${angle}deg, ${colorStr})`,
                    };
                },
                bglinear3: (value) => {
                    //Tailwind 会把 _ 转成空格，所以我们不用处理
                    const [angle = 0, color1, color2, color3] = value.split(' ');
                    return {
                        background: `linear-gradient(${angle}deg, ${color1.replace('-', ' ')}, ${color2.replace('-', ' ')}, ${color3.replace('-', ' ')})`,
                    };
                },
                singleline: (value) => {
                    return {
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis',
                    };
                },
            });
        }),
    ],
    theme: {
        spacing: Array.from({ length: 1000 }).reduce((map, _, index) => {
            map[index] = `${index}px`;
            return map;
        }, {}),
        extend: {
            flex: {
              '2': '2 1 0%',
              '3': '3 1 0%'
            }
          }
    },
    corePlugins: {
        // 小程序不需要 preflight，因为这主要是给 h5 的，如果你要同时开发多端，你应该使用 process.env.TARO_ENV 环境变量来控制它
        preflight: process.env.TARO_ENV === 'h5',
    },
};
