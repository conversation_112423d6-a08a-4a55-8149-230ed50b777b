registry=https://registry.npmmirror.com
@netease:registry=http://rnpm.hz.netease.com
@youdata:registry=http://rnpm.hz.netease.com
@music:registry=http://rnpm.hz.netease.com
@dawn:registry=http://rnpm.hz.netease.com

# 取消缓存 (pre/post)install 钩子的结果。
side-effects-cache=false
force=true

public-hoist-pattern[]=@music/ct-*
public-hoist-pattern[]=@music/mobile-*
public-hoist-pattern[]=@music/chitu-plugin-ssg
public-hoist-pattern[]=@music/chitu-plugin-ssr
public-hoist-pattern[]=@music/react-dev
public-hoist-pattern[]=@music/react-dom-dev
public-hoist-pattern[]=@music/chitu-plugin-ssr-compat
public-hoist-pattern[]=@babel/eslint-plugin
public-hoist-pattern[]=eslint-config-prettier
public-hoist-pattern[]=eslint-plugin-prettier
public-hoist-pattern[]=stylelint-config-prettier
public-hoist-pattern[]=stylelint-prettier
public-hoist-pattern[]=preact
public-hoist-pattern[]=preact-compat
public-hoist-pattern[]=@typescript-eslint/eslint-plugin
public-hoist-pattern[]=@commitlint/cli
public-hoist-pattern[]=lint-staged
public-hoist-pattern[]=stylelint
public-hoist-pattern[]=@music/stylelint-config-base
public-hoist-pattern[]=eslint
public-hoist-pattern[]=husky
public-hoist-pattern[]=prettier
public-hoist-pattern[]=@babel/eslint-parser
public-hoist-pattern[]=eslint-plugin-*
public-hoist-pattern[]=*eslint*
public-hoist-pattern[]=ts-transform-paths
public-hoist-pattern[]=tailwindcss
public-hoist-pattern[]=@changesets/cli
