<!DOCTYPE html>
<html>

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta content="width=device-width,initial-scale=1,user-scalable=no,viewport-fit=cover" name="viewport" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="format-detection" content="telephone=no,address=no" />
    <meta name="apple-mobile-web-app-status-bar-style" content="white" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <link rel="preconnect" href="https://d1.music.126.net" crossorigin />
    <!-- font-family: 'SourceHanSerifCN-Bold'; -->
    <link rel="preload" as="style" onload="this.rel='stylesheet'" href="https://d1.music.126.net/dmusic/obj/w5zCg8OAw6HDjzjDgMK_/60408188060/707e/e3d6/2beb/3f4fa64d30b85fccfcd1123292926d35.css">
    <!-- font-family: 'Alibaba PuHuiTi'; -->
    <link rel="preload" as="style" onload="this.rel='stylesheet'" href="https://d1.music.126.net/dmusic/obj/w5zCg8OAw6HDjzjDgMK_/60408204682/32f4/acaa/2de5/2e90aa9a0d10487ba630ec58a9f897b8.css">
    <script>
        // 兼容低端android手机，globalThis没有的问题
        this.globalThis || (this.globalThis = this)
    </script>
    <title>无限空间</title>
    <%= puzzle_script %>
    <script>
        <%= htmlWebpackPlugin.options.script %>
    </script>
</head>

<body>
    <div id="app"></div>
</body>

</html>
