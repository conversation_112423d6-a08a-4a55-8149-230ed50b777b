const tabs = [
    {
        pagePath: 'pages/message/index',
        text: '消息',
    },
    {
        pagePath: 'pages/friends/index',
        text: '好友',
    },
    {
        pagePath: 'pages/explore/index',
        text: '发现',
    },
    {
        pagePath: 'pages/self/index',
        text: '我的',
    },
];

const pages = [
    'pages/message/index',
    'pages/friends/index',
    'pages/explore/index',
    'pages/self/index',
    'pages/home/<USER>',
    'pages/chat/index',
    'pages/profile/index',
    'pages/image-preview/index',
    'pages/gacha/index',
    'pages/gacha-card/index',
    'pages/gacha-explain/index',
    'pages/chapter-info/index',
    'pages/ending-info/index',
    'pages/ending-retrospect/index',
    'pages/market/index',
    'pages/task/index',
    'pages/profile-edit/index',
    'pages/appreciate/index',
    'pages/join-group/index',
    'pages/system-notify/index',
];

module.exports = {
    pages,
    tabBar: {
        backgroundColor: '#fff',
        borderStyle: 'white',
        color: '#fff',
        selectedColor: '#fff',
        list: tabs,
    },
};
