import { getImage } from '@/utils/image';
import { isAndroid } from '@/utils';

/// 抽卡中动画(1)
const GACHA_LOADING_LOOP_DEFAULT =
    '//d1.music.126.net/dmusic/e812/2574/303d/042c78b59aa8f25423c4367908bbc1de.mp4?infoId=2612185&mp4type=normal';
const GACHA_LOADING_LOOP = getImage(GACHA_LOADING_LOOP_DEFAULT);

/// 抽卡结果前动画(2.1)
const GACHA_RESULT_ANIMATOR_R_DEFAULT =
    '//d1.music.126.net/dmusic/a67f/f451/8ab8/b2c46dfa43b4c4c6c656dbd02daba30b.mp4?infoId=2612204&mp4type=normal';
const GACHA_RESULT_ANIMATOR_R = getImage(GACHA_RESULT_ANIMATOR_R_DEFAULT);

/// 抽卡结果前动画(2.2)
const GACHA_RESULT_ANIMATOR_SR_DEFAULT =
    '//d1.music.126.net/dmusic/ed40/0828/133b/47db55e07a57e116741b0225263e1826.mp4?infoId=2611232&mp4type=normal';
const GACHA_RESULT_ANIMATOR_SR = getImage(GACHA_RESULT_ANIMATOR_SR_DEFAULT);

/// 抽卡结果前动画(2.3)
const GACHA_RESULT_ANIMATOR_SSR_DEFAULT =
    '//d1.music.126.net/dmusic/8dc0/898f/cbc2/70ee4201ca604601611f84122bf6f45b.mp4?infoId=2612205&mp4type=normal';
const GACHA_RESULT_ANIMATOR_SSR = getImage(GACHA_RESULT_ANIMATOR_SSR_DEFAULT);

/// 抽卡结果页动效(3.1)(多抽)
const GACHA_RESULT_BG_START_DEFAULT =
    '//d1.music.126.net/dmusic/8358/6e20/49c2/5c2efae414a6d7d7f5a6f8460b31aced.mp4?infoId=2612211&clientCache=true&maxAge=604800';
const GACHA_RESULT_BG_START = getImage(GACHA_RESULT_BG_START_DEFAULT);

/// 抽卡结果页动效(3.1)(单抽)
const GACHA_RESULT_BG_START_SINGLE_DEFAULT =
    '//d1.music.126.net/dmusic/4451/4d97/1ecd/b6bb2e6d87e57793c42c2535e9b2bc38.mp4?infoId=2613227&clientCache=true&maxAge=604800';
const GACHA_RESULT_BG_START_SINGLE = getImage(GACHA_RESULT_BG_START_SINGLE_DEFAULT);

/// 抽卡结果页动效(3.2)(多抽)
const GACHA_RESULT_BG_LOOP_DEFAULT =
    '//d1.music.126.net/dmusic/7c03/08e3/b47e/d3ec807d8321ab3c8aaeadb5284bec6b.mp4?infoId=2613176&clientCache=true&maxAge=604800';
const GACHA_RESULT_BG_LOOP = getImage(GACHA_RESULT_BG_LOOP_DEFAULT);

/// 抽卡结果页动效(3.1)(单抽)
const GACHA_RESULT_BG_LOOP_SINGLE_DEFAULT =
    '//d1.music.126.net/dmusic/7744/946d/0c3d/0f8caebc911c3affcff3297036e5d4c0.mp4?infoId=2613228&clientCache=true&maxAge=604800';
const GACHA_RESULT_BG_LOOP_SINGLE = getImage(GACHA_RESULT_BG_LOOP_SINGLE_DEFAULT);

/// 抽卡结果页预览页视频~
const LOTTERY_PREVIEW_BG_EFFECT_DEFAULT =
    'http://nos.netease.com/vodkgeyttp8/021372cea04aa8274e195210a6e66c4f.mp4';
export const LOTTERY_PREVIEW_BG_EFFECT = getImage(LOTTERY_PREVIEW_BG_EFFECT_DEFAULT);

/// 抽卡页人物动效(P0)

const GACHA_BG_VIDEO_DEFAULT =
    '//d1.music.126.net/dmusic/ae55/c6ce/4782/519b0c5d191247c681264c09e585c19f.mp4?infoId=2613263';
export const GACHA_BG_VIDEO = getImage(GACHA_BG_VIDEO_DEFAULT);

export const GACHA_BGM =
    'https://m7.music.126.net/20930428200224/44554de2e24e1db1fb303a558bf28308/ymusic/obj/w5zDlMODwrDDiGjCn8Ky/59276696401/126a/02be/d659/6363ee92e8c31e1851d572e75629ac97.mp3';

export const GACAG_ANIMATOR = {
    ANIMATOR_BG_RESULT_GUIDE_R: GACHA_RESULT_ANIMATOR_R,
    ANIMATOR_BG_RESULT_GUIDE_SR: GACHA_RESULT_ANIMATOR_SR,
    ANIMATOR_BG_RESULT_GUIDE_SSR: GACHA_RESULT_ANIMATOR_SSR,
    ANIMATOR_BG_LOADING: GACHA_LOADING_LOOP,

    ANIMATOR_CARDS_BG: '',
    ANIMATOR_RESULT_BG: GACHA_RESULT_BG_LOOP,
    ANIMATOR_RESULT_BG_START: GACHA_RESULT_BG_START,
    ANIMATOR_RESULT_BG_LOOP: GACHA_RESULT_BG_LOOP,
    ANIMATOR_RESULT_BG_START_SINGLE: GACHA_RESULT_BG_START_SINGLE,
    ANIMATOR_RESULT_BG_LOOP_SINGLE: GACHA_RESULT_BG_LOOP_SINGLE,
};

/**
 * 跳转到抽卡页前的 预下载逻辑~
 */
export function preloadVideo() {
    fetch(GACHA_BG_VIDEO);
    if (isAndroid) {
        fetch(LOTTERY_PREVIEW_BG_EFFECT);
    }
}
