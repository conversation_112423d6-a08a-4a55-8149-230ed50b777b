import React, { useEffect, useCallback } from 'react';
import classNames from 'classnames';
import { View, Image } from '@tarojs/components';
import BackIcon from '@/assets/self/right-icon.png';
import { useDialog } from '@/components/dialog';
import TaroSafeAreaView from '@/components/safe-area-view';
import { AtInput } from 'taro-ui';
import Taro from '@tarojs/taro';
import domAdapter from '@/utils/adapter/domAdapter';
import RealNameAuthModal from '@/pages/modals/real-name-modal';
import SwipeBack from '@/utils/adapter/swipeBack';
import useUserInfoStore from '@/store/useUserInfoStore';
import GenderSelectModal from './GenderSelectModal';
import HeaderTitle from './HeaderTitle';
import 'taro-ui/dist/style/components/input.scss';
import 'taro-ui/dist/style/components/icon.scss';
import './index.scss';

const effectiveStatusMap = {
    1: '只可以输入中文/字母/数字哦',
    2: '请输入2-20个字符',
};

const ProfileEdit = () => {
    const effectiveStatus = useUserInfoStore().effectiveStatus;
    const userBaseInfo = useUserInfoStore().userBaseInfo;
    const nickname = useUserInfoStore().nickname;
    const gender = useUserInfoStore().selectedGender;
    const genderSelectModal = useDialog(GenderSelectModal);
    const setGender = useUserInfoStore().setSelectedGender;
    const setNickname = useUserInfoStore().setNickname;
    const validateInput = useUserInfoStore().validateInput;

    const genderStr = gender === 1 ? '男' : '女';

    const onChange = useCallback(
        (value: string) => {
            validateInput(value);
            setNickname(value);
        },
        [validateInput, setNickname]
    );

    useEffect(() => {
        if (userBaseInfo?.userBase?.nickname) {
            setNickname(userBaseInfo.userBase.nickname);
        }
    }, [setNickname, userBaseInfo?.userBase?.nickname]);

    useEffect(() => {
        if (userBaseInfo?.userBase?.gender) {
            setGender(userBaseInfo?.userBase?.gender as 1 | 2);
        }
    }, [setGender, userBaseInfo?.userBase?.gender]);

    // 初始化校验
    useEffect(() => {
        validateInput(nickname);
    }, [nickname, validateInput]);

    const realNameModal = useDialog(RealNameAuthModal);

    useEffect(() => {
        Taro.eventCenter.on('kShowRealNameModal', () => {
            domAdapter.blurActiveElement();
            realNameModal.show();
        });
        return () => {
            Taro.eventCenter.off('kShowRealNameModal');
        };
    }, [realNameModal]);

    useEffect(() => {
        SwipeBack.enabled = true;
        return () => {
            SwipeBack.enabled = true;
        };
    }, []);

    return (
        <TaroSafeAreaView className="m-profile-edit">
            <HeaderTitle title="个人信息" />
            <View className={classNames('content', { error: effectiveStatus })}>
                <AtInput
                    name="value"
                    title="昵称"
                    placeholder="请输入昵称"
                    type="text"
                    value={nickname}
                    onChange={onChange}
                    onFocus={() => {
                        SwipeBack.enabled = false;
                    }}
                    onBlur={() => {
                        SwipeBack.enabled = true;
                    }}
                />
                <View className={classNames('content-tips', { error: effectiveStatus })}>
                    {effectiveStatusMap[effectiveStatus as keyof typeof effectiveStatusMap]}
                </View>
            </View>
            <View
                className="gender"
                onClick={() => {
                    genderSelectModal.show();
                }}>
                <View className="gender-title">性别</View>
                <View className="gender-name">{genderStr}</View>
                <Image className="gender-icon" src={BackIcon} />
            </View>
        </TaroSafeAreaView>
    );
};

export default React.memo(ProfileEdit);
