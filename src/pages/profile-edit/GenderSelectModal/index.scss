.m-gender-select-modal {
    width: 100%;
    height: 308px;
    background-color: #fff;
    border-radius: 16px 16px 0 0;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    transform-origin: bottom center;

    .gender-select-title-view {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 30px;
    }

    .gender-select-title {
        font-size: 18px;
        font-weight: 600;
        color: #000;
    }

    .gender-select-title-icon {
        position: absolute;
        right: 20px;
        top: 20px;
        width: 26px;
        height: 26px;
    }

    .gender-select-content {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 25px;

        .gender-select-item {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;

            .gender-select-item-icon {
                width: 105px;
                height: 124px;
            }

            &.active {
                .gender-select-item-icon {
                    background-color: #000;
                }
            }

            &:not(:last-child) {
                margin-right: 42px;
            }

            .gender-select-item-title {
                font-size: 14px;
                color: rgba(0, 0, 0, 0.4);
                margin-top: 10px;
            }
        }
    }
}
