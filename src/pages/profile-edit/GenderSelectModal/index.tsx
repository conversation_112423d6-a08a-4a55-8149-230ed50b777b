import React, { useCallback } from 'react';
import { View, Text, Image } from '@tarojs/components';
import CloseIcon from '@/assets/common/message_favorability_close.png';
import FemaleActiveIcon from '@/assets/self/female-active.png';
import FemaleInactiveIcon from '@/assets/self/female-inactive.png';
import MaleActiveIcon from '@/assets/self/male-active.png';
import MaleInactiveIcon from '@/assets/self/male-inactive.png';
import { coronaWarnMessage } from '@music/mat-base-h5';
import useUserInfoStore from '@/store/useUserInfoStore';
import { CreateModalProps } from '@/components/dialog';
import Popup from '@/components/Popup';

import './index.scss';

const GenderSelectModalContent = ({ dismiss }: { dismiss: () => void }) => {
    const gender = useUserInfoStore().selectedGender;
    const setSelectedGender = useUserInfoStore().setSelectedGender;

    const handleGenderSelect = useCallback(
        (curGender: 1 | 2) => {
            try {
                if (gender !== curGender) {
                    setSelectedGender(curGender);
                }
            } catch (error) {
                coronaWarnMessage('handleGenderSelect', {
                    error: (error || {}).stack || error,
                });
            }
        },
        [gender, setSelectedGender]
    );

    const femaleIcon = gender === 2 ? FemaleActiveIcon : FemaleInactiveIcon;
    const maleIcon = gender === 1 ? MaleActiveIcon : MaleInactiveIcon;

    return (
        <Popup visible onClose={dismiss}>
            {(close) => (
                <View className="m-gender-select-modal">
                    <View className="gender-select-title-view">
                        <Text className="gender-select-title">选择你的性别</Text>
                    </View>
                    <Image className="gender-select-title-icon" src={CloseIcon} onClick={close} />
                    <View className="gender-select-content">
                        <View className="gender-select-item" onClick={() => handleGenderSelect(2)}>
                            <Image className="gender-select-item-icon" src={femaleIcon} />
                            <Text className="gender-select-item-title">女</Text>
                        </View>
                        <View className="gender-select-item" onClick={() => handleGenderSelect(1)}>
                            <Image className="gender-select-item-icon" src={maleIcon} />
                            <Text className="gender-select-item-title">男</Text>
                        </View>
                    </View>
                </View>
            )}
        </Popup>
    );
};

const GenderSelectModal: CreateModalProps = {
    type: 'modal_custom',
    render(dismiss) {
        return <GenderSelectModalContent dismiss={dismiss} />;
    },
};

export default GenderSelectModal;
