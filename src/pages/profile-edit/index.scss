.m-profile-edit {
    width: 100%;
    height: 100%;
    z-index: 1000;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: #f8f8f8;
    padding-left: 20px;
    padding-right: 20px;

    .at-input {
        background-color: #fff;
        width: 335px;
        height: 50px;
        margin: 0 auto;
        border-radius: 8px;
        padding: 0;

        .at-input__container {
            height: 100%;
        }

        .weui-input {
            text-align: right;
            font-size: 14px;
            font-weight: 600;
            height: 100%;
        }
    }

    .weui-input::placeholder {
        color: #000;
        font-size: 14px;
        font-weight: 600;
        opacity: 1;
    }

    .at-input__title {
        text-align: center;
        width: 62px;
        color: rgba(0, 0, 0, 0.4);
        font-size: 14px;
        font-weight: 600;
    }

    .content {
        margin-top: 28px;

        &.error {
            .at-input {
                border: 1px solid rgba(241, 83, 99, 0.3);
            }
        }
    }

    .content-tips {
        color: rgba(0, 0, 0, 0.2);
        font-size: 11px;
        margin-top: 5px;

        &.error {
            color: #f15363;
        }
    }

    .gender {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        border-radius: 8px;
        background: #fff;
        width: 335px;
        height: 50px;
        padding: 12px 16px;
        margin-top: 20px;

        .gender-title {
            color: rgba(0, 0, 0, 0.4);
            font-size: 14px;
            font-weight: 600;
        }

        .gender-name {
            color: #000;
            font-size: 14px;
            font-weight: 600;
            margin-left: auto;
        }

        .gender-icon {
            width: 10px;
            height: 10px;
            margin-left: 4px;
        }
    }
}
