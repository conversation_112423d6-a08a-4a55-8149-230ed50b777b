import { Image, Text, View } from '@tarojs/components';
import React, { CSSProperties, useCallback } from 'react';
import backIcon from '@/assets/self/icon-back.png';
import useUserInfoStore from '@/store/useUserInfoStore';
import { throttle } from '@/utils';
import { useBalanceStore } from '@/store/balanceStore';
import Taro from '@tarojs/taro';

import './index.scss';

interface TitleProps {
    style?: string | CSSProperties;
    title: string;
}

const Header = ({ style = {}, title }: TitleProps) => {
    const updateUserBaseInfo = useUserInfoStore().updateUserBaseInfo;
    const effectiveStatus = useUserInfoStore().effectiveStatus;
    const gender = useUserInfoStore().selectedGender;
    const nickname = useUserInfoStore().nickname;
    const getUserBaseInfo = useUserInfoStore().getUserBaseInfo;

    const onBack = useCallback(() => {
        Taro.navigateBack();
        getUserBaseInfo();
        useBalanceStore.getState().requestBalance();
    }, [getUserBaseInfo]);

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const onSave = useCallback(
        throttle(async () => {
            if (effectiveStatus !== 0) return;
            await updateUserBaseInfo({
                base: {
                    nickname,
                    gender,
                },
            });
        }),
        [updateUserBaseInfo, nickname, gender]
    );

    return (
        <View className="m-self-header-title" style={style}>
            <View className="wrapper">
                <Image className="header-back" src={backIcon} onClick={onBack} />
                {title && <Text className="header-title">{title}</Text>}
                <View className="right-save" onClick={onSave}>
                    保存
                </View>
            </View>
        </View>
    );
};

export default React.memo(Header);
