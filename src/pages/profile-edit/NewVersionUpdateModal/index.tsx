import React, { useCallback, useState } from 'react';
import { View, Text, Image } from '@tarojs/components';
import { DateUtil } from '@music/helper';
import CloseIcon from '@/assets/upgrade-modal/upgrade-close-icon.png';
import NewIcon from '@/assets/upgrade-modal/new-icon.png';
import BgLineIcon from '@/assets/upgrade-modal/upgrade-bg-line.png';
import ExhcangeBtnIcon from '@/assets/upgrade-modal/upgrade-exchange-btn.png';
import ExpBtnIcon from '@/assets/upgrade-modal/upgrade-exp-btn.png';
import UpIcon from '@/assets/upgrade-modal/upgrade-up-icon.png';
import UpdateTitleIcon from '@/assets/upgrade-modal/update-title.png';
import { throttle } from '@/utils';
import { openUrl } from '@/utils/rpc';
import RootPortal from '@/components/RootPortal';
import classNames from 'classnames';
import useUserInfoStore from '@/store/useUserInfoStore';
import { coronaWarnMessage } from '@music/mat-base-h5';

import './index.scss';

const NewVersionUpdateModal = ({ show, onClose }: { show: boolean; onClose: () => void }) => {
    const [isLeaving, setIsLeaving] = useState(false);
    const firstEnterPopupInfo = useUserInfoStore((state) => state.firstEnterPopupInfo);

    const handleFloatLayoutChange = useCallback(() => {
        setIsLeaving(true);
        setTimeout(() => {
            onClose();
        }, 300);
    }, [onClose]);

    const onClickExp = throttle(() => {
        try {
            handleFloatLayoutChange();
            if (firstEnterPopupInfo?.jumpKey) {
                openUrl(firstEnterPopupInfo?.jumpKey);
            }
        } catch (error) {
            coronaWarnMessage('onClickExp', {
                error: (error || {}).stack || error,
            });
        }
    });

    if (!show) return null;

    const Modal = (
        <RootPortal>
            <View
                className={classNames('m-new-version-update-mask', {
                    leaving: isLeaving,
                    entering: !isLeaving,
                })}
            />
            <View className={`m-new-version-update-modal ${isLeaving ? 'leaving' : 'entering'}`}>
                <View className="update-title">
                    <Image className="new-icon" src={NewIcon} />
                    <Image className="up-icon" src={UpIcon} />
                    <Image className="update-title-icon" src={UpdateTitleIcon} />
                </View>
                <Image className="update-bg-line" src={BgLineIcon} />
                <Image className="close-icon" src={CloseIcon} onClick={handleFloatLayoutChange} />
                <View className="new-version-update-content">
                    <View className="new-version-update-content-title">
                        亲爱的用户：
                        <br />
                    </View>
                    <View className="new-version-update-content-text">
                        您好，由于服务内容调整， 心颜全新升级。 <br />
                        您所遗留的金币已按
                        <Text className="highlight">人民币等额兑换成新货币</Text>
                        ，可前往我的页面查看。
                        <br />
                        如果您需要提现积分，请点击下方入口进行提现。
                    </View>
                    <View className="new-version-update-content-text">
                        提现入口于
                        <Text className="highlight">
                            {DateUtil.format(firstEnterPopupInfo?.registerTime, 'YYYY年MM月DD日')}
                        </Text>
                        关闭，逾期请通过下方入口与客服联系操作提现。
                    </View>
                    <View className="new-version-update-content-text">
                        感谢您的配合！
                        <br />
                        焕新升级，快来体验吧！
                    </View>
                </View>
                <View className="new-version-upgrade-btns">
                    <Image
                        className="exchange-btn"
                        src={ExpBtnIcon}
                        onClick={handleFloatLayoutChange}
                    />
                    <Image className="exp-btn" src={ExhcangeBtnIcon} onClick={onClickExp} />
                </View>
            </View>
        </RootPortal>
    );

    return Modal;
};

export default NewVersionUpdateModal;
