.m-new-version-update-modal {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 530px;
    background-color: #fff;
    z-index: 1000;
    border-radius: 16px 16px 0 0;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    animation: slideUp 0.3s ease-out forwards;
    transform-origin: bottom center;
    padding: 20px 25px;
    background: center / 100% 100% repeat url('~@/assets/upgrade-modal/upgrade-bg.png');

    &.leaving {
        animation: slideDown 0.3s ease-in forwards;
    }

    &.entering {
        animation: slideUp 0.3s ease-out forwards;
    }

    .update-title {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 70px;
    }

    .new-icon {
        position: absolute;
        right: -28px;
        top: -73px;
        width: 36px;
        height: 18px;
        z-index: 1;
    }

    .up-icon {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 28px;
        width: 44px;
        height: 44px;
    }

    .update-title-icon {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 0;
        width: 95px;
    }

    &::after {
        content: 'NEW VERSION UPGRADE';
        position: absolute;
        left: 50%;
        font-family: 'PingFang SC', sans-serif;
        transform: translateX(-50%);
        top: 82px;
        color: rgba(255, 255, 255, 0.7);
        text-shadow: 0 0 10px #fff;
        font-size: 8px;
        letter-spacing: 2px;
        white-space: nowrap;
    }

    .close-icon {
        position: absolute;
        right: 20px;
        top: 20px;
        width: 26px;
        height: 26px;
    }

    .update-bg-line {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        top: 115px;
        width: 260px;
    }

    .new-version-update-content {
        margin-top: 35px;

        &-title {
            color: #333437;
            font-size: 15px;
            font-weight: 500;
        }

        &-text {
            color: rgba(51, 52, 55, 0.7);
            font-size: 14px;
            line-height: 23px;

            .highlight {
                color: #333437;
                font-weight: 600;
            }
        }

        & > taro-view-core {
            &:not(:first-child) {
                margin-top: 8px;
            }
        }

        .new-version-update-item {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;

            .new-version-update-item-icon {
                width: 105px;
                height: 124px;
            }

            &.active {
                .gender-select-item-icon {
                    background-color: #000;
                }
            }

            &:not(:last-child) {
                margin-right: 42px;
            }

            .gender-select-item-title {
                font-size: 14px;
                color: rgba(0, 0, 0, 0.4);
                margin-top: 10px;
            }
        }
    }

    .new-version-upgrade-btns {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        margin-top: 21px;

        .exchange-btn {
            width: 325px;
            height: 48px;
            margin-bottom: 10px;
        }

        .exp-btn {
            width: 325px;
            height: 48px;
        }
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideDown {
    from {
        transform: translateY(0);
        opacity: 1;
    }

    to {
        transform: translateY(100%);
        opacity: 0;
    }
}

.m-new-version-update-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 999;

    &.entering {
        animation: fadeIn 0.3s ease-in-out;
    }

    &.leaving {
        // animation: fadeOut 0.3s ease-in-out;
        display: none;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}
