.chapter-info-page-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    align-items: center;

    .content-wrapper {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        align-items: center;
        position: relative;

        .close {
            width: 26px;
            height: 26px;
            flex-shrink: 0;
        }

        .flex-content {
            flex: 1;
        }

        .bottom-bg {
            position: fixed;
            bottom: 0;
            width: 100%;
            height: 325px;
            display: flex;
            flex-direction: column;
            background: rgba(34, 34, 34, 0.6);
            filter: blur(30px);
        }

        .bottom-content {
            width: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            align-items: center;
            position: relative;

            .content-card {
                width: 100%;
                display: flex;
                flex-direction: column;
                position: relative;

                .level-txt {
                    color: #fff;
                    font-family: 'SourceHanSerifCN-Bold', sans-serif;
                    font-size: 32px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                    letter-spacing: 1px;
                    margin-left: 26px;
                }

                .chapter-name {
                    color: #fff;
                    font-family: 'SourceHanSerifCN-Bold', sans-serif;
                    font-size: 26px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                    letter-spacing: 1px;
                    margin-left: 26px;
                    margin-right: 26px;
                    padding-bottom: 11px;
                    position: relative;

                    &::after {
                        content: '';
                        display: block;
                        position: absolute;
                        left: 0;
                        bottom: 0;
                        width: 215px;
                        height: 27px;
                        background-image: url('../../assets/chapter-info/icon-shadow-logo.png');
                        background-size: 100% 100%;
                    }
                }

                .lock-img {
                    width: 22px;
                    height: 22px;
                    margin-left: 27px;
                    margin-top: 23px;
                }

                .chapter-content {
                    font-size: 12px;
                    font-weight: normal;
                    line-height: 150%;
                    letter-spacing: 0.08em;
                    color: #fff;
                    opacity: 0.7;
                    margin-left: 28px;
                    margin-right: 31px;
                    margin-top: 10px;
                }
            }

            .explore {
                height: 48px;
                width: calc(100% - 56px);
                font-size: 16px;
                font-weight: 600;
                letter-spacing: 0.08em;
                color: #7f5c5b;
                border-radius: 71px;
                background: #f2dcca;
                margin-top: 35px;
                align-items: center;
                text-align: center;
                line-height: 48px;
            }

            .go-ending {
                color: #fff;
                text-align: center;
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                line-height: 100%;
                margin-top: 17px;
                margin-bottom: 65px;
                position: relative;

                &::after {
                    content: '';
                    display: inline-block;
                    width: 10px;
                    height: 10px;
                    background: url('../../assets/chapter-info/icon-to-ending.png') no-repeat
                        center/contain;
                    margin-left: 1px;
                }
            }

            .source-container {
                display: flex;
                flex-direction: column;
                width: 100%;
                align-items: center;
                padding-bottom: 56px;
                margin-top: 73px;

                .lottery {
                    height: 48px;
                    width: calc(100% - 56px);
                    font-size: 16px;
                    font-weight: 600;
                    letter-spacing: 0.08em;
                    color: #7f5c5b;
                    border-radius: 71px;
                    background: #f2dcca;
                    align-items: center;
                    text-align: center;
                    line-height: 48px;
                    margin-bottom: 15px;
                }

                .exchange {
                    height: 48px;
                    width: calc(100% - 56px);
                    font-size: 16px;
                    font-weight: 600;
                    letter-spacing: 0.08em;
                    color: #fff;
                    border-radius: 71px;
                    background: rgba(255, 255, 255, 0.2);
                    align-items: center;
                    text-align: center;
                    line-height: 48px;
                }
            }
        }
    }

    .taro-img__mode-aspectfill {
        object-fit: cover;
    }
}
