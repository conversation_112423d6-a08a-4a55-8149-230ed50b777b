import CircleLoading from '@/components/CircleLoading';
import { ConfirmModalProvider } from '@/components/confirm-modal';
import { useDialog } from '@/components/dialog';
import ImageVideoBg from '@/components/ImageVideoBg';
import { addFriendApi } from '@/service/friendApi';
import {
    chapterDetailApi,
    endingDetailApi,
    precheckChapterApi,
    profileDetailApi,
    startChapterApi,
} from '@/service/profileApi';
import { ChapterCardSource, ChapterDetail } from '@/service/res/ChapterDetail';
import { withStopPropagation } from '@/utils/eventUtils';
import { getImage } from '@/utils/image';
import { parseUrlParamJson } from '@/utils/parseUtils';
import { openKey } from '@/utils/rpc';
import { Image, Text, View } from '@tarojs/components';
import Taro, {
    getCurrentInstance,
    useDidShow,
    showLoading,
    showToast,
    hideLoading,
} from '@tarojs/taro';
import React, { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { debounce } from '@/utils';
import Header from '@/components/Header';

import { jump2EndingInfo, jump2Gacha, jump2Market, jump2Epicplayer } from '@/router';

import closeIcon from '../../assets/chapter-info/icon-close.png';
import lockIcon from '../../assets/chapter-info/icon-lock.png';
import { TabType } from '../market/store/store';
import { AigcRobotProfile, CardStatus, ChapterLevel } from '../../../types/AigcRobotProfile';
import { GameChapterStartResult, StartCode } from '../profile/GameChapterStartResult';

import cardCollectStore from '../appreciate/store/cardCollectStore';
import './index.scss';

const ChapterInfo = () => {
    const { robotUserId, chapterId, chapter, from } = useMemo(() => {
        const params = getCurrentInstance().router?.params || {};
        return {
            robotUserId: params.robotUserId as string,
            chapterId: params.chapterId as string,
            chapter: parseUrlParamJson<ChapterDetail>(params.chapter),
            from: params.from as string | undefined,
        };
    }, []);

    const [chapterInfo, setChapterInfo] = useState<ChapterDetail | null>(chapter);

    const [profile, setProfile] = useState<AigcRobotProfile | null>(null);

    useDidShow(() => {
        profileDetailApi({ robotUserId })
            .then((res) => {
                setProfile(res);
            })
            .catch((err) => {
                showToast({
                    title: err.message,
                    icon: 'none',
                });
            });
        chapterDetailApi(chapterId)
            .then((res) => {
                setChapterInfo(res);
            })
            .catch((err) => {
                showToast({
                    title: err.message,
                    icon: 'none',
                });
            });
    });

    const onClickClose = useCallback(() => {
        Taro.navigateBack();
    }, []);

    const dialog = useDialog(ConfirmModalProvider);

    const startChapter = useCallback(() => {
        if (!chapterInfo) return;
        startChapterApi({ chapterId: chapterInfo.chapterId, roleId: chapterInfo.roleId })
            .then((res) => {
                const result = res as GameChapterStartResult;
                if (result.code === StartCode.SUCCESS) {
                    jump2Epicplayer({
                        chapterId: chapterInfo.chapterId,
                        roleId: chapterInfo.roleId,
                        chapterPlayRecordId: result.recordId,
                    });
                    Taro.navigateBack();
                } else if (result.code === StartCode.NO_STAMINA) {
                    jump2Market({});
                } else {
                    showToast({
                        title: result.message || '系统繁忙，请稍后再试',
                        icon: 'none',
                    });
                }
            })
            .catch((err) => {
                showToast({
                    title: err.message,
                    icon: 'none',
                });
            });
    }, [chapterInfo]);

    const precheck = useCallback(async () => {
        if (!chapterInfo) return;
        try {
            const precheckResult = await precheckChapterApi({ chapterId: chapterInfo.chapterId });
            if (precheckResult.recordId) {
                dialog?.show({
                    title: '是否继续上次的探索',
                    info: '（继续探索不消耗体力）',
                    cancelText: '重新探索',
                    closeOnClickOverlay: true,
                    cancelStyle: {
                        color: '#7F5C5B',
                        textAlign: 'center',
                        fontSize: '18px',
                        fontStyle: 'normal',
                        fontWeight: 600,
                        lineHeight: '18px',
                        borderRadius: '128px',
                        background: 'rgba(242, 220, 202, 0.3)',
                    },
                    confirmText: '确定',
                    confirmStyle: {
                        color: '#7F5C5B',
                        textAlign: 'center',
                        fontSize: '18px',
                        fontStyle: 'normal',
                        fontWeight: 600,
                        lineHeight: '18px',
                        borderRadius: '128px',
                        background: '#F2DCCA',
                    },
                    onCancel: () => {
                        // react18的合并渲染，会让上一次dismiss和下一次dismiss合并渲染，需要延迟一下
                        setTimeout(() => {
                            dialog?.show({
                                title: `确定消耗${precheckResult.requiredStamina}体力开启1次探索吗？`,
                                onConfirm: () => {
                                    startChapter();
                                },
                            });
                        }, 0);
                    },
                    onConfirm: () => {
                        jump2Epicplayer({
                            chapterId: chapterInfo.chapterId,
                            roleId: chapterInfo.roleId,
                            chapterPlayRecordId: precheckResult.recordId,
                        });
                        Taro.navigateBack();
                    },
                });
            } else if (precheckResult.firstStart) {
                dialog?.show({
                    title: '确定开启探索吗？',
                    info: '首次探索不消耗体力',
                    onConfirm: () => {
                        startChapter();
                    },
                });
            } else {
                dialog?.show({
                    title: `确定消耗${precheckResult.requiredStamina}体力开启1次探索吗？`,
                    onConfirm: () => {
                        startChapter();
                    },
                });
            }
        } catch (error) {
            showToast({
                title: error.message,
                icon: 'none',
            });
        }
    }, [chapterInfo, dialog, startChapter]);

    const onClickExplore = useCallback(async () => {
        if (!chapterInfo) return;
        if (profile?.friend) {
            precheck();
        } else {
            dialog?.show({
                title: '该角色不是您的好友，无法探索该角色的故事',
                cancelText: '我知道了',
                confirmText: '加好友',
                onConfirm: () => {
                    addFriendApi(profile?.userId)
                        .then(() => {
                            precheck();
                            Taro.eventCenter.trigger('profile_update', {
                                userId: profile?.userId,
                                friend: true,
                            });
                            setProfile({ ...profile, friend: true });
                            showToast({
                                title: '添加成功',
                            });
                        })
                        .catch((err) => {
                            showToast({
                                title: err.message,
                                icon: 'none',
                            });
                        });
                },
            });
        }
    }, [chapterInfo, dialog, precheck, profile, setProfile]);

    const onClickGoEnding = useCallback(async () => {
        if (from === 'ending') {
            Taro.navigateBack();
            return;
        }
        showLoading();
        endingDetailApi({ chapterId })
            .then((res) => {
                if (!res || res.length <= 0) {
                    return;
                }
                const userId = profile?.userId;
                const endingStr = encodeURIComponent(JSON.stringify(res[0]));
                const ending2Str = encodeURIComponent(res[1] ? JSON.stringify(res[1]) : '');
                jump2EndingInfo({
                    uiState: null,
                    userId: `${userId}`,
                    ending: endingStr,
                    ending2: ending2Str,
                    from: 'chapter',
                });
            })
            .catch((err) => {
                showToast({
                    title: err.message,
                    icon: 'none',
                });
            })
            .finally(() => {
                hideLoading();
            });
    }, [chapterId, from, profile?.userId]);

    const onClickLottery = useCallback(() => {
        if (!chapterInfo) return;
        jump2Gacha({});
    }, [chapterInfo]);

    const onClickExchange = useCallback(() => {
        if (!chapterInfo) return;
        jump2Market({ type: TabType.card });
    }, [chapterInfo]);

    useEffect(() => {
        cardCollectStore.getState().removeNewTag('chapter', robotUserId, chapterId, '');
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // console.log('chapterInfo', chapterInfo);

    return (
        <View className="chapter-info-page-wrapper">
            {chapterInfo ? (
                <Fragment>
                    <ImageVideoBg
                        imgUrl={getImage(chapterInfo?.chapterImg)}
                        videoUrl={getImage(chapterInfo?.chapterBigImg)}
                        showMask={chapterInfo?.cardStatus === CardStatus.LOCKED}
                    />
                    <View className="content-wrapper">
                        <Header
                            backConfig={{ src: '' }}
                            funcConfig={{
                                node: (
                                    <Image
                                        className="close"
                                        src={closeIcon}
                                        mode="aspectFill"
                                        onClick={onClickClose}
                                    />
                                ),
                            }}
                        />
                        <View className="flex-content" />
                        <View className="bottom-bg" />
                        <View className="bottom-content">
                            <View className="content-card">
                                <Text className="level-txt">{chapterInfo?.chapterLevel}</Text>
                                <Text className="chapter-name">{chapterInfo?.chapterName}</Text>
                                {chapterInfo?.cardStatus === CardStatus.LOCKED ? (
                                    <Image className="lock-img" src={lockIcon} />
                                ) : (
                                    <Text className="chapter-content">
                                        {chapterInfo?.chapterDesc}
                                    </Text>
                                )}
                            </View>
                            {chapterInfo?.cardStatus !== CardStatus.LOCKED ? (
                                <Fragment>
                                    <Text
                                        className="explore"
                                        onClick={withStopPropagation(onClickExplore)}
                                        style={{
                                            visibility: chapterInfo?.enableExploration
                                                ? 'visible'
                                                : 'hidden',
                                        }}>
                                        进入剧情探索
                                    </Text>
                                    <Text
                                        className="go-ending"
                                        style={{
                                            visibility:
                                                chapterInfo.chapterLevel !== ChapterLevel.R
                                                    ? 'visible'
                                                    : 'hidden',
                                        }}
                                        onClick={withStopPropagation(onClickGoEnding)}>
                                        查看结局卡
                                    </Text>
                                </Fragment>
                            ) : (
                                <View className="source-container">
                                    {chapterInfo?.chapterCardSources?.includes(
                                        ChapterCardSource.Lottery
                                    ) === true && (
                                        <Text
                                            className="lottery"
                                            onClick={debounce(withStopPropagation(onClickLottery))}>
                                            去抽奖
                                        </Text>
                                    )}
                                    {chapterInfo?.chapterCardSources?.includes(
                                        ChapterCardSource.ShardExchange
                                    ) === true && (
                                        <Text
                                            className="exchange"
                                            onClick={withStopPropagation(onClickExchange)}>
                                            去兑换
                                        </Text>
                                    )}
                                </View>
                            )}
                        </View>
                    </View>
                </Fragment>
            ) : (
                <CircleLoading />
            )}
        </View>
    );
};

export default React.memo(ChapterInfo);
