/* eslint-disable no-console */
import { ContextStore, StoreLifecycle, StoreCreator } from '@/components/storeContext/StoreContext';
import NIMService from '@/hooks/useNewNIM';
import { nim } from '@/hooks/nimInstance';
import { showToast } from '@tarojs/taro';
import { create } from 'zustand';
import RetrospectApi from './api';
import { audioStoreProvider } from './audioStore';

export type SceneType = 'chat' | 'scene';

export interface MessageItem {
    messageClientId: string;
    isSelf: boolean;
    iceBreakFlag?: boolean; // 是否开场白
    fromNick: string;
    variableList?: [
        {
            variableKey: string;
            defaultValue: number;
            currentValue: number;
        }
    ];
    numberInfo?: [
        {
            numberType: number;
            number: number;
            incrType: number;
            name: string;
        }
    ];
    text: string;
    audioUrl?: string;
}

export interface SceneMeta {
    timestamp: number;
    type: SceneType;
    playIndex: number;
    nickname?: string;
    nicknameType?: string;
    message?: string;
    scenePlayRecordId?: string;
    audio?: string;
    messageList?: MessageItem[];
}

export interface RetrospectListItem {
    recordId: string;
    details?: string[];
}

export interface RetrospectState extends StoreLifecycle {
    list: SceneMeta[];
    request: (recordId: string) => Promise<void>;
    playAudio: (audio: string) => Promise<void>;
}

const getMessageListByTime = async (
    sessionId: string,
    beginTime: number,
    endTime: number
): Promise<MessageItem[]> => {
    const conversationId = NIMService.convertSessionIdToConversationId(sessionId);
    const resultNimList = await nim.V2NIMMessageService.getMessageList({
        conversationId,
        beginTime,
        endTime,
        direction: 1,
    });
    // console.log('getMessageListByTime resultNimList', resultNimList);
    const result = resultNimList
        ?.map((item) => {
            try {
                if (item.isSelf) {
                    return {
                        messageClientId: item.messageClientId,
                        isSelf: true,
                        fromNick: '我',
                        text: item.text,
                    } as MessageItem;
                }
                const serverExt = JSON.parse(item.serverExtension).serverExt;
                if (serverExt?.iceBreakFlag) {
                    return {
                        messageClientId: item.messageClientId,
                        iceBreakFlag: true,
                        isSelf: false,
                        fromNick: serverExt.fromNick,
                        text: item.text,
                    } as MessageItem;
                }
                try {
                    const content = JSON.parse(item.attachment?.raw).content.content;
                    let textInfo = { result: '', action: '' };
                    try {
                        textInfo = JSON.parse(content.text) as { result: string; action: string };
                    } catch (e) {
                        textInfo = { result: content.text, action: '' };
                    }

                    return {
                        messageClientId: item.messageClientId,
                        isSelf: false,
                        fromNick: serverExt.fromNick,
                        variableList: JSON.parse(serverExt?.variableList),
                        numberInfo: JSON.parse(serverExt?.numberInfo),
                        text: textInfo.result,
                        audioUrl: content.audioUrl,
                        iceBreakFlag: false,
                    } as MessageItem;
                } catch (e) {
                    return {
                        messageClientId: item.messageClientId,
                        iceBreakFlag: true,
                        isSelf: false,
                        fromNick: serverExt.fromNick,
                        text: item.text,
                    } as MessageItem;
                }
            } catch (e) {
                console.log('getMessageListByTime error', e);
                return {
                    messageClientId: item.messageClientId,
                } as MessageItem;
            }
        })
        .filter((item) => item.text);
    return result;
};

export const retrospectStoreCreator: StoreCreator<RetrospectState> = (
    contextStore: ContextStore
) => {
    return create<RetrospectState>((set, get) => {
        const audioPlayer = contextStore.get(audioStoreProvider);
        return {
            list: [],
            request: async (recordId) => {
                try {
                    const recordQuery = await RetrospectApi.query(recordId);
                    const rawDetails = recordQuery.details ?? ([] as string[]);
                    let sceneItemList = rawDetails
                        .map((detail) => {
                            const sceneItem = JSON.parse(JSON.parse(detail)) as SceneMeta;
                            return sceneItem;
                        })
                        .filter(
                            (item) =>
                                (item.type === 'scene' && item.message) || item.type === 'chat'
                        );
                    // 对chat类型补全云信数据
                    if (sceneItemList.some((item) => item.type === 'chat')) {
                        const chatInfos = await RetrospectApi.sceneChatInfo(recordId);
                        sceneItemList = await Promise.all(
                            sceneItemList.map(async (item) => {
                                if (item.type !== 'chat') return item;

                                const findChatInfo = chatInfos?.sceneChatAggregateInfos?.find(
                                    (chatInfo) =>
                                        chatInfo.scenePlayRecordId === item.scenePlayRecordId
                                );

                                if (!findChatInfo) return item;
                                const messageList = await getMessageListByTime(
                                    findChatInfo.accId,
                                    findChatInfo.beginTime,
                                    findChatInfo.endTime
                                );
                                console.log('nimMessageList', messageList.length);
                                return {
                                    ...item,
                                    messageList,
                                } as SceneMeta;
                            })
                        );
                    }

                    set({
                        list: sceneItemList,
                    });
                    // console.log('recordQuery', sceneItemList);
                } catch (err) {
                    showToast({
                        title: err.message,
                        icon: 'none',
                    });
                }
            },
            playAudio: async (audio) => {
                try {
                    const result = await RetrospectApi.resourceDetail(audio);
                    const audioUrl = result[0].resourceUrl;
                    if (audioUrl) {
                        await audioPlayer.getState().play(audioUrl);
                    } else {
                        showToast({
                            title: '暂无音频',
                        });
                    }
                } catch (err) {
                    showToast({
                        title: err.message,
                        icon: 'none',
                    });
                }
            },
        };
    });
};
