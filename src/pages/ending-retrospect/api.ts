import fetch from '@/utils/fetch';
import { ChapterRecordChatAggregateInfo, GameResourceDetail, RecordQuery } from './types';

const query = (recordId: string): Promise<RecordQuery> =>
    fetch('/api/sociallive/ai/game/record/query', {
        method: 'post',
        data: { recordId },
    }) as Promise<RecordQuery>;

const sceneChatInfo = (recordId: string): Promise<ChapterRecordChatAggregateInfo> =>
    fetch('/api/sociallive/ai/game/chapter/record/aggregate/info', {
        method: 'post',
        data: { recordId },
    }) as Promise<ChapterRecordChatAggregateInfo>;

const resourceDetail = (nosResourceIds: string): Promise<GameResourceDetail[]> =>
    fetch('/api/sociallive/ai/game/resource/detail', {
        method: 'post',
        data: { nosResourceIds },
    }) as Promise<GameResourceDetail[]>;

const RetrospectApi = {
    query,
    sceneChatInfo,
    resourceDetail,
};

export default RetrospectApi;
