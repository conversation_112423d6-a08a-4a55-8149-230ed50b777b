/* eslint-disable no-console */
import { useContextStore } from '@/components/storeContext/StoreContext';
import { Image, Text, View } from '@tarojs/components';
import React, { useCallback } from 'react';
import ICON_AUDIO from '../../../assets/ending-retrospect/icon-audio.png';
import { retrospectStoreCreator, SceneMeta } from '../retrospectStore';
import './index.scss';

const SceneItem = ({ item }: { item: SceneMeta }) => {
    const useRetrospectStore = useContextStore(retrospectStoreCreator);

    const playAudio = useCallback(() => {
        useRetrospectStore.getState().playAudio(item.audio);
    }, [item.audio, useRetrospectStore]);

    return (
        <View className="scene-item">
            {item.nickname === '旁白' ? (
                <Text className="narratage">{item.message}</Text>
            ) : (
                <View className="content">
                    <View className="header">
                        <Text className="name">{item.nickname}</Text>
                        {item.audio && (
                            <Image className="audio" src={ICON_AUDIO} onClick={playAudio} />
                        )}
                    </View>
                    <Text className="message">{item.message}</Text>
                </View>
            )}
        </View>
    );
};

export default React.memo(SceneItem);
