.scene-item {
    display: flex;
    flex-direction: column;

    .narratage {
        align-self: stretch;
        color: #fff;
        font-size: 15px;
        font-style: normal;
        font-weight: 400;
        line-height: 22.5px;
        opacity: 0.6;
        margin-top: 23px;
    }

    .content {
        display: flex;
        flex-direction: column;
        margin-top: 23px;

        .header {
            display: flex;
            flex-direction: row;
            align-items: center;

            .name {
                color: #f2dcca;
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: 16px;
            }

            .audio {
                width: 18px;
                height: 18px;
                margin-left: 4px;
            }
        }

        .message {
            color: #fff;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px;
            margin-top: 12px;
        }
    }
}
