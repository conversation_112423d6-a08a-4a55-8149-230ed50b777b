/* eslint-disable no-console */
import { StoreCreator, StoreLifecycle } from '@/components/storeContext/StoreContext';
import { createInnerAudioContext, InnerAudioContext, showToast } from '@tarojs/taro';
import { create } from 'zustand';

export interface AudioState extends StoreLifecycle {
    play: (audioUrl: string) => Promise<void>;
}

export type AudioPlayer = InnerAudioContext & { Instance?: HTMLAudioElement };

export const audioStoreProvider: StoreCreator<AudioState> = () => {
    return create<AudioState>(() => {
        let audioPlayer: AudioPlayer | undefined;
        return {
            onCleared: () => {
                if (audioPlayer) {
                    audioPlayer.stop();
                    // https://github.com/NervJS/taro/pull/14130
                    try {
                        audioPlayer.destroy();
                    } catch (e) {
                        console.log(e);
                    }
                    audioPlayer = undefined;
                }
            },
            play: async (audioUrl) => {
                try {
                    if (!audioPlayer) {
                        audioPlayer = createInnerAudioContext();
                        audioPlayer.onPlay(() => {
                            console.log('播放开始');
                        });
                        audioPlayer.onEnded(() => {
                            console.log('播放结束');
                        });
                        audioPlayer.onError((err) => {
                            console.log('播放出错');
                            showToast({
                                title: err.errMsg,
                                icon: 'none',
                            });
                        });
                        audioPlayer.onStop(() => {
                            console.log('播放停止');
                        });
                    }
                    audioPlayer.src = audioUrl;

                    audioPlayer.play();
                } catch (err) {
                    showToast({
                        title: err.message,
                        icon: 'none',
                    });
                }
            },
        };
    });
};
