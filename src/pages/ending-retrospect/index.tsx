/* eslint-disable no-console */
import ImageVideoBg from '@/components/ImageVideoBg';
import TaroSafeAreaView from '@/components/safe-area-view';
import StoreProvider, { useContextStore } from '@/components/storeContext/StoreContext';
import { getImage } from '@/utils/image';
import { parseUrlParamJson } from '@/utils/parseUtils';
import { Image, Text, View } from '@tarojs/components';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import React, { useCallback, useEffect, useMemo } from 'react';
import { DialogRootLayer } from '@/components/dialog';
import ICON_CLOSE from '../../assets/ending-retrospect/icon-close.png';
import { AigcEndingCardInfo } from '../../../types/AigcRobotProfile';
import ChatItem from './ChatItem';
import './index.scss';
import { retrospectStoreCreator } from './retrospectStore';
import SceneItem from './SceneItem';

export interface EndingRetroProps {
    endingInfo: AigcEndingCardInfo;
}

const EndingRetrospect = ({ endingInfo }: EndingRetroProps) => {
    const useRetrospectStore = useContextStore(retrospectStoreCreator);

    useEffect(() => {
        useRetrospectStore.getState().request(endingInfo.relatedRecordId);
    }, [endingInfo, useRetrospectStore]);

    const list = useRetrospectStore((state) => state.list);

    const onClose = useCallback(() => {
        Taro.navigateBack();
    }, []);

    return (
        <TaroSafeAreaView className="ending-retrospect-page-wrapper">
            <ImageVideoBg
                imgUrl={getImage(endingInfo?.endingCardImg)}
                videoUrl={getImage(endingInfo?.endingCardBigImg)}
                showMask
                maskColor="rgba(0, 0, 0, 0.6)"
            />
            <View className="content-wrapper">
                <View className="header-content-wrapper">
                    <Text className="title">剧情回顾</Text>
                    <Image className="close" src={ICON_CLOSE} onClick={onClose} />
                </View>
                <View className="header-line" />
                <View className="ending-retrospect-record-content">
                    {list.map((item) => {
                        const isChat = item.type === 'chat';
                        return (
                            <View className="ending-retrospect-record-item" key={item.timestamp}>
                                {isChat ? <ChatItem item={item} /> : <SceneItem item={item} />}
                            </View>
                        );
                    })}
                </View>
            </View>
        </TaroSafeAreaView>
    );
};

const EndingRetrospectPage = () => {
    const endingInfo = useMemo(() => {
        const params = getCurrentInstance().router?.params || {};
        return parseUrlParamJson<AigcEndingCardInfo>(params.ending as string);
    }, []);

    return (
        <StoreProvider type="page">
            <EndingRetrospect endingInfo={endingInfo} />
            <DialogRootLayer />
        </StoreProvider>
    );
};

export default React.memo(EndingRetrospectPage);
