/* eslint-disable no-console */
import { Text, View, Image } from '@tarojs/components';
import React, { Fragment } from 'react';
import { MessageItem, SceneMeta } from '../retrospectStore';
import './index.scss';
import ICON_LOVE from '../../../assets/ending-retrospect/icon-love.png';

const MsgItem = ({ msgItem }: { msgItem: MessageItem }) => {
    const currentRound = msgItem.variableList?.find(
        (item) => item.variableKey === '轮次'
    )?.currentValue;
    const currentNum = msgItem.numberInfo?.[0]?.number ?? -1;
    return (
        <View className="chat-item">
            {msgItem.iceBreakFlag ? (
                <Text className="narratage">{msgItem.text}</Text>
            ) : (
                <View className="content">
                    <View className="header">
                        <Text className="name">{msgItem.fromNick}</Text>
                    </View>
                    <Text className="message">{msgItem.text}</Text>
                    {currentNum >= 0 && (
                        <View className="num-wrapper">
                            <Image className="num-icon" src={ICON_LOVE} />
                            <Text className="num">+{currentNum}</Text>
                        </View>
                    )}
                    {currentRound && (
                        <View className="line-wrapper">
                            <View className="line" />
                            <Text className="line-name">第{currentRound}轮</Text>
                        </View>
                    )}
                </View>
            )}
        </View>
    );
};

const ChatItem = ({ item }: { item: SceneMeta }) => {
    return (
        <Fragment>
            {(item.messageList || []).map((message) => (
                <MsgItem key={message.messageClientId} msgItem={message} />
            ))}
        </Fragment>
    );
};

export default React.memo(ChatItem);
