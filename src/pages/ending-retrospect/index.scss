.ending-retrospect-page-wrapper {
    // background-color: #faf7f9;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    align-items: center;
    position: relative;

    .content-wrapper {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        align-items: center;
        position: relative;

        .header-content-wrapper {
            height: 44px;
            width: 100%;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;

            .title {
                color: #fff;
                text-align: right;
                font-size: 18px;
                font-style: normal;
                font-weight: 600;
                line-height: 18px;
                margin-left: 20px;
            }

            .close {
                width: 26px;
                height: 26px;
                margin-right: 20px;
            }
        }

        .header-line {
            width: 100%;
            height: 0.5px;
            background: #fff;
            opacity: 0.1;
            margin-top: 6px;
        }

        .ending-retrospect-record-content {
            width: 100%;
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            display: flex;
            flex-direction: column;
            padding: 0 20px;
            margin-bottom: 30px;
            transform: translateZ(0); // 强制开启GPU加速层
            will-change: transform; // 提示浏览器优化

            .ending-retrospect-record-item {
                width: 100%;
                display: flex;
                flex-direction: column;
                flex-shrink: 0;
            }
        }
    }
}
