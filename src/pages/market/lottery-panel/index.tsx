import { View, Text, Image } from '@tarojs/components';
import React, { useEffect } from 'react';
import './index.scss';
import { useDialog } from '@/components/dialog';
import { useContextStore } from '@/components/storeContext/StoreContext';
import lotteryIcon from '@/assets/market/ic_market_lottery.png';
import { getCoinIcon } from '@/utils/appSourceAdapter';
import starIcon from '@/assets//self-power/icon_star.png';
import useLotteryPanelStore from './useLotteryPanelStore';
import { ShopItem } from '../card-panel/shopTypes';
import BuyConfirm from '../card-panel/buyConfirm';
import { marketStoreCreator } from '../store/store';
import Banner from '../banner';

const LotteryItem = ({ shopItem }: { shopItem: ShopItem }) => {
    const confirmDialog = useDialog(BuyConfirm);

    const confirmClick = () => {
        confirmDialog?.show(shopItem, 'ticket');
    };

    return (
        <View className="grid-item">
            <View className="content-container" onClick={confirmClick}>
                <Image className="icon" src={lotteryIcon} />
                <Text className="value">{shopItem.count}</Text>
                <Text className="desc">{shopItem.productName}</Text>
                <View className="price-container">
                    <Image className="price-icon-prefix" src={getCoinIcon()} />
                    <Text className="price">{shopItem.amount}</Text>
                </View>
            </View>
            {shopItem.tagDesc && (
                <View className="tag-container">
                    <Text className="tag-prefix">限时加赠</Text>
                    <Image className="tag-icon-prefix" src={starIcon} />
                    <Text className="tag-value">{shopItem.tagDesc}</Text>
                </View>
            )}
        </View>
    );
};

const LotteryPanel = () => {
    const lotteryList = useLotteryPanelStore((state) => state.lotteryList);

    useEffect(() => {
        useLotteryPanelStore.getState().fetch();
    }, []);

    const useMarketStore = useContextStore(marketStoreCreator);
    const bannerList = useMarketStore((state) => state.bannerList);

    return (
        <View className="lottery-panel-wrapper">
            <View className="grid-container">
                {lotteryList?.map((data) => (
                    <LotteryItem key={data.productId} shopItem={data} />
                ))}
                {bannerList.length > 0 && <Banner list={bannerList} type="lottery" />}
            </View>
        </View>
    );
};

export default React.memo(LotteryPanel);
