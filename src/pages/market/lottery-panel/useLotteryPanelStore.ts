import { create } from 'zustand';
import { showToast } from '@tarojs/taro';
import { MessageErrorCode } from '@/types/im';
import { openUrl, toast } from '@/utils/rpc';
import { getCoinName } from '@/utils/appSourceAdapter';
import useDetailStore from '../../chat/store/useDetailStore';
import { ShopItem } from '../card-panel/shopTypes';
import { shopExchangeApi, shopListApi } from '../card-panel/shopApi';
import { useBalanceStore } from '../../../store/balanceStore';
import { gotoRechargePage } from '../pay-panel/payPanelStore';

interface LotteryPanelState {
    lotteryList?: ShopItem[];
    fetch: () => Promise<void>;
    exchange: (item?: ShopItem) => Promise<void>;
}

const useLotteryPanelStore = create<LotteryPanelState>((set, get, store) => {
    let currentUserId = '';
    useDetailStore.subscribe((state, prev) => {
        if (state.userId !== prev.userId || currentUserId !== state.userId) {
            if (currentUserId) {
                set(store.getInitialState());
            }
            currentUserId = state.userId;
        }
    });
    return {
        lotteryList: undefined,
        fetch: async () => {
            shopListApi({
                type: 'ticket',
                sceneType: 'chapter_card',
            })
                .then((res) => {
                    console.log('req', 'useLotteryPanelStore', 'res:', res);
                    set({ lotteryList: res });
                })
                .catch((err) => {
                    console.log('req', 'useLotteryPanelStore', 'res:', err);

                    showToast({
                        title: err.message,
                        icon: 'none',
                    });
                });
        },
        exchange: async (item) => {
            shopExchangeApi({
                productId: item.productId,
                type: 'ticket',
                sceneType: 'chapter_card',
            })
                .then(() => {

                    showToast({
                        title: '购买成功！',
                        icon: 'none',
                        duration: 2000,
                    });
                    useBalanceStore.getState().requestExchangeBalance();
                    useBalanceStore.getState().requestBalance();
                })
                .catch((err) => {
                    if (err.code === MessageErrorCode.balaceInsufficient) {
                        toast(`${getCoinName()}不足，请充值后使用`);
                        setTimeout(() => {
                            gotoRechargePage();
                        }, 1500);
                    } else {

                        showToast({
                            title: err.message,
                            icon: 'none',
                        });
                    }
                });
        },
    };
});

export default useLotteryPanelStore;
