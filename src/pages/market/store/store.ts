/* eslint-disable no-console */
import { StoreLifecycle, StoreCreator } from '@/components/storeContext/StoreContext';
import { ComponentType } from 'react';
import { create } from 'zustand';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { filterBanner } from '@/utils/appSourceAdapter';
import { fetchBannerList } from '../card-panel/shopApi';

export enum TabType {
    stamina = 'stamina',
    recharge = 'recharge',
    lottery = 'lottery',
    card = 'card',
}

export interface TabItem {
    title: string;
    type: string;
    component: ComponentType<unknown>;
}

export interface BannerItem {
    id: string;
    pictureUrl: string;
    jumpLink: string;
}

export interface MarketState extends StoreLifecycle {
    tabList: TabItem[];
    setTabList: (tabList: TabItem[]) => void;
    currentTab?: TabItem;
    currentIndex: number;
    setCurrentIndex: (currentIndex: number) => void;
    setCurrentType: (currentType: string) => void;
    bannerList: BannerItem[];
    fetchBannerList: () => Promise<void>;
}

export const marketStoreCreator: StoreCreator<MarketState> = () => {
    return create<MarketState>((set, get) => ({
        tabList: [],
        setTabList: (tabList) => {
            set({ tabList });
        },
        currentIndex: 0,
        setCurrentIndex: (currentIndex) => {
            console.log('setCurrentTab', currentIndex);
            set({ currentIndex, currentTab: get().tabList[currentIndex] });
        },
        setCurrentType: (currentType) => {
            console.log('setCurrentType', currentType);
            const currentTabIndex = get().tabList.findIndex((item) => item.type === currentType);
            if (currentTabIndex !== -1) {
                set({ currentIndex: currentTabIndex, currentTab: get().tabList[currentTabIndex] });
            }
        },
        bannerList: [],
        fetchBannerList: async () => {
            set({ bannerList: [] });
            try {
                const res = await fetchBannerList({ position: '4' });
                console.log('fetchBannerList', res);
                if (res && res.length > 0) {
                    set({ bannerList: filterBanner(res) });
                }
            } catch (e) {
                coronaWarnMessage('banner', `获取 banner ${e}`);
            }
        },
    }));
};
