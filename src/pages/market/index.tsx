/* eslint-disable no-console */
import Gift_Panel_Recharge_Add from '@/assets/common/gift_panel_recharge_add.png';
import ICON_CHIP from '@/assets/market/ic_market_card_chip_1.png';
import lotteryIcon from '@/assets/market/ic_market_lottery.png';
import ICON_STAR_SMALL from '@/assets/market/ic_stamina.png';
import { DialogRootLayer } from '@/components/dialog';
import TaroSafeAreaView from '@/components/safe-area-view';
import StoreProvider, { useContextStore } from '@/components/storeContext/StoreContext';
import { getGameBalance } from '@/service/res/balanceRes';
import { pageDidAppear } from '@/utils/rpc';
import { Image, View } from '@tarojs/components';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import React, { useCallback, useEffect } from 'react';
import { getCoinIcon } from '@/utils/appSourceAdapter';
import { AtTabs, AtTabsPane } from 'taro-ui';
import Header from '@/components/Header';
import backIcon from '@/assets/header/icon-back.png';
import SelfPower from './self-power';
import CardPanel from './card-panel';
import LotteryPanel from './lottery-panel';
import { gotoRechargePage } from './pay-panel/payPanelStore';
import { useBalanceStore } from '../../store/balanceStore';
import { marketStoreCreator, TabItem, TabType } from './store/store';
import './index.scss';

const RightNode = () => {
    const staminaBalance = useBalanceStore((state) => state.staminaBalance);
    const balance = useBalanceStore((state) => state.balance);
    const shardBalance = useBalanceStore((state) => state.exchangeBalance?.shardBalance);
    const ticketBalance = useBalanceStore((state) => state.exchangeBalance?.ticketBalance);

    const useMarketStore = useContextStore(marketStoreCreator);
    const currentTab = useMarketStore((state) => state.currentTab);

    const showStamina = currentTab?.type === TabType.stamina;
    const showShard = currentTab?.type === TabType.card;
    const showTicket = currentTab?.type === TabType.lottery;
    const showBalance = currentTab?.type !== TabType.card;

    const handleGotoRecharge = () => {
        gotoRechargePage();
    };

    return (
        <View className="right-wrapper">
            {showStamina && staminaBalance && (
                <View className="powerInfo">
                    <Image src={ICON_STAR_SMALL} className="image" />
                    <View className="num">{staminaBalance.balance}</View>
                    <View className="count">/{staminaBalance.autoRecoveryLimit}</View>
                </View>
            )}

            {showShard && (
                <View className="goldInfo">
                    <Image src={ICON_CHIP} className="imageLeft" />
                    <View className="count">{getGameBalance(shardBalance) || '0'}</View>
                </View>
            )}

            {showTicket && (
                <View className="goldInfo">
                    <Image src={lotteryIcon} className="imageLeft" />
                    <View className="count">{getGameBalance(ticketBalance) || '0'}</View>
                </View>
            )}

            {showBalance && balance && (
                <View className="goldInfo">
                    <Image src={getCoinIcon()} className="imageLeft" />
                    <View className="count">{balance.balance}</View>
                    <Image
                        src={Gift_Panel_Recharge_Add}
                        className="gift-panel-recharge-add"
                        onClick={handleGotoRecharge}
                    />
                </View>
            )}
        </View>
    );
};

const Market = () => {
    const useMarketStore = useContextStore(marketStoreCreator);

    useEffect(() => {
        const tabList: TabItem[] = [
            { title: '体力兑换', component: SelfPower, type: TabType.stamina },
            { title: '奖券兑换', component: LotteryPanel, type: TabType.lottery },
            { title: '卡面兑换', component: CardPanel, type: TabType.card },
        ];
        useMarketStore.getState().setTabList(tabList);
        useBalanceStore.getState().requestBalance();
        useBalanceStore.getState().requestStaminaBalance();
        useBalanceStore.getState().requestExchangeBalance();
        const { type } = getCurrentInstance().router?.params || {};
        useMarketStore.getState().setCurrentType(type || 'stamina');
        useMarketStore.getState().fetchBannerList();
    }, [useMarketStore]);

    const tabList = useMarketStore((state) => state.tabList);
    const currentTab = useMarketStore((state) => state.currentIndex);

    const handlerClickTab = useCallback(
        (index: number) => {
            useMarketStore.getState().setCurrentIndex(index);
        },
        [useMarketStore]
    );

    useEffect(() => {
        Taro.eventCenter.on('MarketChangeTab', (index) => {
            useMarketStore.getState().setCurrentIndex(index);
        });
        return () => {
            Taro.eventCenter.off('MarketChangeTab');
        };
    }, [useMarketStore]);

    useEffect(() => {
        pageDidAppear(() => {
            useBalanceStore.getState().requestBalance();
            // 3秒后再刷新一次
            setTimeout(() => {
                useBalanceStore.getState().requestBalance();
            }, 3000);
        });
    }, []);

    return (
        <TaroSafeAreaView className="market-page-wrapper">
            <View className="market-list-bg" />
            <View className="market-window">
                <View className="market-icon-bg" />
                <Header
                    hasStatusBar={false}
                    backConfig={{ src: backIcon }}
                    titleConfig={{ title: '商城' }}
                    funcConfig={{ node: <RightNode /> }}
                />
                {tabList && tabList.length > 0 && (
                    <AtTabs
                        current={currentTab}
                        tabList={tabList}
                        swipeable={false}
                        animated={false}
                        onClick={handlerClickTab}>
                        {tabList.map((tab, index) => (
                            <AtTabsPane key={tab.type} current={currentTab} index={index}>
                                <tab.component />
                            </AtTabsPane>
                        ))}
                    </AtTabs>
                )}
            </View>
        </TaroSafeAreaView>
    );
};

const MarketPage = () => {
    return (
        <StoreProvider type="page">
            <Market />
            <DialogRootLayer />
        </StoreProvider>
    );
};

export default React.memo(MarketPage);
