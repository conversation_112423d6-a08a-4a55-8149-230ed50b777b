import { CreateModalProps, useDialog } from '@/components/dialog';
import { balanceQuery, payoutApply, productPage } from '@/service/physicalPowerApi';
import { Image, View } from '@tarojs/components';
import Taro, { showToast, createAnimation } from '@tarojs/taro';
import { getGlobalData } from '@/utils';
import { getCoinName } from '@/utils/appSourceAdapter';
import React, { FunctionComponent, useEffect, useRef, useState } from 'react';
import { AtModal } from 'taro-ui';
import 'taro-ui/dist/style/components/modal.scss';
import { MessageErrorCode } from '@/types/im';
import { coronaWarnMessage } from '@music/mat-base-h5';
import ICON_CLOCK from '@/assets/self-power/icon_clock.png';
import ICON_ERROR from '@/assets/self-power/icon_toast_error.png';
import ICON_SUCCESS from '@/assets/self-power/icon_toast_success.png';

import { useBalanceStore } from '../../../store/balanceStore';

import PowerPanel from './power-panel';
import { gotoRechargePage } from '../pay-panel/payPanelStore';

import './index.scss';

function Info({
    nextRecoverStr,
    allRecoverStr,
}: {
    nextRecoverStr: string;
    allRecoverStr: string;
}) {
    return (
        <View>
            <View className="recoverInfoBg">
                <View className="recoverInfo">
                    <View className="preTxt">下次</View>
                    <Image src={ICON_CLOCK} className="image" />
                    <View className="time">{nextRecoverStr}</View>
                </View>
                <View className="recoverInfo">
                    <View className="preTxt">全部</View>
                    <Image src={ICON_CLOCK} className="image" />
                    <View className="time">{allRecoverStr}</View>
                </View>
            </View>
        </View>
    );
}

function ShowDialog({
    glod,
    power,
    productId,
    receiveAmount,
    confirmClick,
    cancelClick,
}: {
    glod: string;
    power: string;
    productId: string;
    // eslint-disable-next-line react/require-default-props
    receiveAmount?: string;
    confirmClick: (productId: string) => void;
    cancelClick: () => void;
}) {
    const handleConfirmClick = () => {
        confirmClick(productId);
    };
    const handleCancelClick = () => {
        cancelClick();
    };

    return (
        <View className="confirmContainer">
            <View className="at-raw">
                <View className="titleBg">
                    <View className="title">确定消耗 </View>
                    <View className="titleNum">{glod}</View>
                    <View className="title"> {getCoinName()}</View>
                </View>
                <View className="titleBg">
                    <View className="title">购买 </View>
                    <View className="titleNum">{receiveAmount ?? power}</View>
                    <View className="title"> 体力值吗？</View>
                </View>
                <View className="space" />
                <View className="at-row at-row__justify--between">
                    <View className="cancelBtnBg" onClick={handleCancelClick}>
                        <View className="cancelBtn">取消</View>
                    </View>
                    <View className="funSpace" />
                    <View className="confirmBtnBg" onClick={handleConfirmClick}>
                        <View className="confirmBtn">确定</View>
                    </View>
                </View>
            </View>
        </View>
    );
}

export interface ShowDialogProps {
    glod: string;
    power: string;
    productId: string;
    receiveAmount?: string;
    confirmClick: (productId: string) => void;
    cancelClick: () => void;
    handleDialogClose: () => void;
}

export const ShowDialogProvider: CreateModalProps = {
    type: 'modal_custom',
    isModal: true,
    render(dismiss: () => void, confirmProps: ShowDialogProps) {
        return (
            <AtModal
                isOpened
                onClose={confirmProps.handleDialogClose}
                className="power_selected_custom-modal">
                <ShowDialog {...confirmProps} />
            </AtModal>
        );
    },
};

function PowerToast({
    animationData,
    toastIcon,
    toastContent,
}: {
    animationData: TaroGeneral.IAnyObject;
    toastIcon: string;
    toastContent: string;
}) {
    return (
        <View style={{ position: 'relative', zIndex: 90 }}>
            <View
                style={{
                    position: 'absolute',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    top: '50',
                    width: '100%',
                }}>
                <View animation={animationData} style={{ opacity: 0 }}>
                    <View className="toastRoot">
                        <View className="toastBg">
                            <View className="toastIconRoot">
                                <Image src={toastIcon} className="toastIcon" />
                            </View>
                            <View className="toastStr">{toastContent}</View>
                        </View>
                    </View>
                </View>
            </View>
        </View>
    );
}

const SelfPower: FunctionComponent = () => {
    const currentPowerConfigDefault = getGlobalData<number>('currentPowerConfig') || 0;
    const powerLimitConfigFault = getGlobalData<number>('powerLimitConfig') || 0;

    const recoverDurConfig = useRef(60 * 3 * 1000 - 1);
    const recoverPointConfig = useRef(1);

    const [refreshRecoverInfo, setRefreshRecoverInfo] = useState<number>();
    const nextRecover = useRef(120000);
    const allRecover = useRef(1292000);
    const nextRecoverStr = useRef('00:00:00');
    const allRecoverStr = useRef('00:00:00');

    const powerLimitConfig = useRef(powerLimitConfigFault);
    const currentPowerConfig = useRef(currentPowerConfigDefault);

    const [toastContent, setToastContent] = useState('');
    const [toastIcon, setToastIcon] = useState(ICON_SUCCESS);
    const toastIntervalId = useRef<NodeJS.Timeout>();

    const [list, setList] = useState([]);

    const [animationData, setAnimationData] = useState({});
    function hideToast() {
        const animation = createAnimation({
            duration: 10,
            timingFunction: 'ease',
        });
        animation.translateY(5).opacity(0).step();
        setAnimationData(animation.export());
    }
    function handleShowToast() {
        const animation = createAnimation({
            duration: 150,
            timingFunction: 'ease',
        });

        animation.translateY(-5).opacity(1).step();
        setAnimationData(animation.export());

        if (toastIntervalId.current !== undefined) {
            clearTimeout(toastIntervalId.current);
        }
        toastIntervalId.current = setTimeout(() => {
            hideToast();
        }, 2000);
    }

    const balanceQueryReq = async () => {
        balanceQuery()
            .then((res) => {
                recoverDurConfig.current = (res?.singleRecoveryMills || 0) - 1;
                recoverPointConfig.current = res?.singleRecoveryNum || 1;
                currentPowerConfig.current = res?.balance || 0;
                powerLimitConfig.current = res?.autoRecoveryLimit || 0;
                nextRecover.current = res?.nextRecoveryRemainMills || 0;
                allRecover.current = res?.allRecoveryRemainMills || 0;
            })
            .catch((err: { code: number; message: string }) => {
                coronaWarnMessage('接口请求出错-balanceQuery', `err:${JSON.stringify(err)}`);
            });
    };

    const productPageReq = async () => {
        productPage('{"payType":"exchange_stamina"}')
            .then((res) => {
                setList(res?.levels || []);
            })
            .catch((err: { code: number; message: string }) => {
                coronaWarnMessage('接口请求出错-productPage', `err:${JSON.stringify(err)}`);
            });
    };

    const payoutApplyReq = async (productId: string) => {
        payoutApply(`{"payType":"exchange_stamina","productId":${productId}}`)
            .then(() => {
                useBalanceStore.getState().requestBalance();
                useBalanceStore.getState().requestStaminaBalance();
                balanceQueryReq();
                setToastIcon(ICON_SUCCESS);
                setToastContent('购买成功，快去和喜爱的好友聊天吧！');
                handleShowToast();
            })
            .catch((err: { code: number; message: string }) => {
                if (err.code === MessageErrorCode.balaceInsufficient) {
                    showToast({
                        title: `${getCoinName()}不足，请充值后使用`,
                        icon: 'none',
                    });
                    setTimeout(() => {
                        gotoRechargePage();
                    }, 1500);
                } else {
                    showToast({
                        title: err.message,
                        icon: 'none',
                    });
                }
            });
    };

    useEffect(() => {
        const needShowToast = Taro.Current.router?.params.needShowToast;
        if (needShowToast === 'true') {
            setTimeout(() => {
                setToastIcon(ICON_ERROR);
                setToastContent('体力不足，继续对话请购买体力');
                handleShowToast();
            }, 200);
        }

        balanceQueryReq();

        productPageReq();

        const timer = setInterval(() => {
            if (currentPowerConfig.current < powerLimitConfig.current) {
                let currentAllRecoverConfig = allRecover.current;
                if (currentAllRecoverConfig > 0) {
                    const hours = Math.floor(currentAllRecoverConfig / 3600000);
                    const remainingMs = currentAllRecoverConfig % 3600000;
                    const minutes = Math.floor(remainingMs / 60000);
                    const seconds = Math.floor((remainingMs % 60000) / 1000);
                    allRecoverStr.current = `${hours.toString().padStart(2, '0')}:${minutes
                        .toString()
                        .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                } else {
                    nextRecoverStr.current = '00:00:00';
                    allRecoverStr.current = '00:00:00';
                }
                allRecover.current = currentAllRecoverConfig - 1000;

                if (currentAllRecoverConfig > 0) {
                    currentAllRecoverConfig = nextRecover.current;
                    if (currentAllRecoverConfig < 0) {
                        currentAllRecoverConfig = recoverDurConfig.current;
                    }
                    const hours = Math.floor(currentAllRecoverConfig / 3600000);
                    const remainingMs = currentAllRecoverConfig % 3600000;
                    const minutes = Math.floor(remainingMs / 60000);
                    const seconds = Math.floor((remainingMs % 60000) / 1000);
                    nextRecoverStr.current = `${hours.toString().padStart(2, '0')}:${minutes
                        .toString()
                        .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    const value = currentAllRecoverConfig - 1000;
                    if (value < 0) {
                        currentPowerConfig.current = Math.min(
                            currentPowerConfig.current + recoverPointConfig.current,
                            powerLimitConfig.current
                        );
                        useBalanceStore.getState().requestStaminaBalance();
                    }

                    nextRecover.current = value;
                }
            } else {
                nextRecoverStr.current = '00:00:00';
                allRecoverStr.current = '00:00:00';
            }
            setRefreshRecoverInfo(Date.now());
        }, 1000);

        return () => {
            if (toastIntervalId.current !== undefined) {
                clearTimeout(toastIntervalId.current);
            }
            clearInterval(timer);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const dialog = useDialog(ShowDialogProvider);

    const handleDialogConfirm = (productId: string) => {
        dialog?.hide();
        payoutApplyReq(productId);
    };

    const handleDialogCancel = () => {
        dialog?.hide();
    };
    const handleDialogClose = () => {
        dialog?.hide();
    };

    const handleSelectedClick = (
        glod: string,
        originalAmount: string,
        productId: string,
        receiveAmount?: string
    ) => {
        dialog?.show({
            glod,
            power: originalAmount,
            productId,
            receiveAmount,
            confirmClick: handleDialogConfirm,
            cancelClick: handleDialogCancel,
            handleDialogClose,
        });
    };

    return (
        <View className="history">
            <View className="rootBg">
                <PowerToast
                    animationData={animationData}
                    toastIcon={toastIcon}
                    toastContent={toastContent}
                />
                {refreshRecoverInfo && (
                    <Info
                        nextRecoverStr={nextRecoverStr.current}
                        allRecoverStr={allRecoverStr.current}
                    />
                )}
                <PowerPanel list={list} click={handleSelectedClick} />
            </View>
        </View>
    );
};

export default SelfPower;
