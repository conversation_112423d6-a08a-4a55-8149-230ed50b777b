.power_selected_custom-modal {
    .at-modal__overlay {
        background-color: rgba(0, 0, 0, 0.5);
    }

    .at-modal__container {
        width: 80%;
        max-width: 300px;
        border-radius: 20px;
    }

    .modal-content {
        padding: 30px 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .confirmContainer {
        min-height: 202px;
        border-radius: 20px;
        padding-top: 50px;
        padding-bottom: 30px;
        background-color: #fff;

        .space {
            height: 30px;
        }

        .funSpace {
            width: 24px;
        }

        .titleBg {
            display: flex;
            justify-content: center;
            align-items: center;

            .title {
                white-space: break-spaces;
                font-size: 18px;
                font-weight: 600;
                color: #000;
            }

            .titleNum {
                white-space: break-spaces;
                font-size: 18px;
                font-weight: 600;
                color: #ff689e;
            }
        }

        .confirmBtnBg {
            display: flex;
            background-color: #ff689e;
            border-radius: 128px;
            justify-content: center;
            align-items: center;
            width: 116px;
            margin-right: 29.5px;
            height: 48px;

            .confirmBtn {
                font-size: 18px;
                font-weight: 600;
                color: #fff;
            }
        }

        .cancelBtnBg {
            display: flex;
            background-color: #bfbfbf;
            border-radius: 128px;
            margin-left: 29.5px;
            justify-content: center;
            align-items: center;
            width: 116px;
            height: 48px;

            .cancelBtn {
                font-size: 18px;
                font-weight: 600;
                color: #fff;
            }
        }
    }
}

.history {
    height: 100%;
    // background-color: #FCF5F5;

    .rootBg {
        height: 100%;
        // background-color: #FCF5F5;
    }

    .toastRoot {
        display: flex;
        justify-content: center;

        .toastBg {
            position: absolute;
            z-index: 90;
            height: 40px;
            padding-left: 12px;
            padding-right: 12px;
            background-color: #fff;
            border-radius: 12px;
            display: flex;
            align-items: center;

            .toastIconRoot {
                display: flex;
                justify-content: center;

                .toastIcon {
                    height: 16px;
                    width: 16px;
                }
            }

            .toastStr {
                margin-left: 6px;
                font-size: 13px;
                font-weight: 600;
                color: rgba(0, 0, 0, 0.6);
            }
        }
    }

    .titleContainer {
        height: 44px;
        background-color: #fcf5f5;

        .iconSpace {
            height: 26px;
            width: 26px;
            margin-left: 20px;
            margin-right: 20px;
        }

        .iconContainer {
            display: flex;
            height: 44px;
            margin-left: 20px;
            margin-right: 20px;
            align-items: center;

            .icon {
                height: 26px;
                width: 26px;
            }
        }

        .titleStrContainer {
            display: flex;
            height: 44px;
            align-items: center;
            justify-content: center;

            .titleStr {
                align-items: center;
                font-size: 18px;
                font-weight: 600;
                color: #000;
            }
        }
    }

    .powerInfo {
        margin-left: 24px;
        min-width: 78px;
        height: 26px;
        border-radius: 18px;
        background-color: #ffe5ee;
        display: flex;
        margin-top: 12px;
        align-items: center;

        .image {
            width: 20px;
            height: 20px;
            margin-left: 4px;
        }

        .num {
            font-size: 16px;
            margin-left: 6px;
            font-weight: 600;
            color: #000;
        }

        .count {
            font-size: 16px;
            color: #000;
            opacity: 0.5;
            margin-right: 10px;
        }
    }

    .goldInfo {
        margin-right: 20px;
        height: 26px;
        border-radius: 18px;
        background-color: #ffe5ee;
        display: flex;
        margin-top: 12px;
        align-items: center;

        .imageLeft {
            width: 20px;
            height: 20px;
            margin-left: 4px;
        }

        .imageRight {
            width: 20px;
            height: 20px;
            margin-right: 6px;
        }

        .count {
            margin-left: 6px;
            font-size: 16px;
            min-width: 63px;
            max-width: 100px;
            color: #000;
            margin-right: 6px;
        }
    }

    .recoverInfoBg {
        width: calc(100% - 38px);
        height: 33px;
        margin-left: 19px;
        margin-bottom: 14px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        border-radius: 15px;
        background: rgba(255, 104, 158, 0.08);

        .recoverInfo {
            margin-left: 16px;
            display: flex;
            align-items: center;

            .image {
                width: 12px;
                height: 12px;
                margin-right: 2px;
                margin-left: 4px;
            }

            .preTxt {
                color: #ff689e;
                text-align: center;
                font-size: 12px;
                font-style: normal;
                font-weight: 600;
                line-height: 16.8px;
                opacity: 0.5;
            }

            .time {
                color: #ff689e;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 16.8px;
                opacity: 0.5;
            }
        }
    }

    .goldInfoList {
        padding-left: 12.5px;
        padding-right: 12.5px;
        margin-top: 20px;

        .goldItemInfoDefault {
            margin-left: 7.5px;
            margin-right: 7.5px;
            height: 136px;
            width: 100%;
            border-radius: 10px;
            margin-bottom: 18px;
        }

        .goldItemInfo {
            position: relative;
            margin-left: 7.5px;
            margin-right: 7.5px;
            height: 136px;
            width: 100%;
            border-radius: 10px;
            align-items: center;
            margin-bottom: 18px;
            background-color: #fff;
            border: 0.5px solid rgba(255, 104, 158, 0.4);

            .imageContainer {
                display: flex;
                margin-top: 16px;
                justify-content: center;

                .image {
                    width: 38px;
                    height: 38px;
                }
            }

            .percentContainer {
                display: flex;
                justify-content: center;

                .originalAmount {
                    margin-top: 6px;
                    font-size: 20px;
                    font-weight: 1000;
                    min-height: 28px;
                }
            }

            .worthContainer {
                display: flex;
                margin-top: 15px;
                justify-content: center;

                .worthImg {
                    width: 16px;
                    height: 16px;
                    align-self: center;
                }

                .worthTxt {
                    font-size: 12px;
                    margin-left: 3px;
                    opacity: 0.6;
                }
            }

            .precent {
                position: absolute;
                right: -2px;
                top: -2px;
                padding-left: 6px;
                padding-right: 6px;
                padding-top: 3px;
                padding-bottom: 3px;
                border-radius: 137px;
                background-color: #ff689e;
                color: white;
                font-size: 11px;
            }
        }
    }
}
