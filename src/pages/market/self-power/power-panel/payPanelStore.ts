import { StoreLifecycle } from '@/components/storeContext/StoreContext';
import { productPage } from '@/service/physicalPowerApi';
import { PayProductList } from '@/service/res/PayPanelRes';
import { showToast } from '@tarojs/taro';
import { create } from 'zustand';

export interface PayPanelState extends StoreLifecycle {
    productList?: PayProductList;
    requestProductList: () => void;
}

export const powerPanelStoreProvider = () => {
    return create<PayPanelState>((set, get) => ({
        requestProductList: () => {
            productPage('{"payType":"exchange_stamina"}')
                .then((res) => {
                    console.log('req', 'payProductListApi', 'res:', res);
                    set({ productList: res });
                })
                .catch((err) => {
                    showToast({
                        title: err.message,
                    });
                });
        },
        onCreated: () => {
            get().requestProductList();
        },
    }));
};
