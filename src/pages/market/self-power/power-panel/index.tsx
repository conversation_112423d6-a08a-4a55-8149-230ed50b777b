import { ProductLevel } from '@/service/res/PayPanelRes';
import { Image, Text, View } from '@tarojs/components';
import React, { useCallback } from 'react';
import staminaIcon from '@/assets/market/ic_market_stamina.png';
import { getCoinIcon } from '@/utils/appSourceAdapter';
import starIcon from '@/assets/self-power/icon_star.png';
import { useContextStore } from '@/components/storeContext/StoreContext';
import { marketStoreCreator } from '@/pages/market/store/store';
import Banner from '@/pages/market/banner';
import './index.scss';

export interface PowerProducrItemProps {
    productLevel: ProductLevel;
    click: (productLevel: ProductLevel) => void;
}

const PowerProducrItem = ({ productLevel, click }: PowerProducrItemProps) => {
    const onClickItem = useCallback(() => {
        click(productLevel);
    }, [click, productLevel]);

    return (
        <View className="grid-item">
            <View className="content-container" onClick={onClickItem}>
                <Image
                    className="icon"
                    src={productLevel.product.extend.productImg || staminaIcon}
                />
                <Text className="value">{productLevel.product.originalAmount}</Text>
                <View className="price-container">
                    <Image className="price-icon-prefix" src={getCoinIcon()} />
                    <Text className="price">{productLevel.product.originalPrice}</Text>
                </View>
            </View>
            {productLevel.product.subTitle && (
                <View className="tag-container">
                    <Text className="tag-prefix">限时加赠</Text>
                    <Image className="tag-icon-prefix" src={starIcon} />
                    <Text className="tag-value">{productLevel.product.subTitle}</Text>
                </View>
            )}
        </View>
    );
};

export interface PowerPanelProps {
    list?: ProductLevel[];
    click: (
        glod: string,
        originalAmount: string,
        productId: string,
        receiveAmount?: string
    ) => void;
}

const PowerPanel = ({ list, click }: PowerPanelProps) => {
    const onClickItem = useCallback(
        (productLevel: ProductLevel) => {
            click(
                productLevel.product.originalPrice,
                productLevel.product.originalAmount,
                `${productLevel.product.id}`,
                productLevel.product.receiveAmount
            );
        },
        [click]
    );

    const useMarketStore = useContextStore(marketStoreCreator);
    const bannerList = useMarketStore((state) => state.bannerList);

    return (
        <View className="power-panel-wrapper">
            <View className="grid-container">
                {list?.map((productLevel) => (
                    <PowerProducrItem
                        key={productLevel.product.id}
                        productLevel={productLevel}
                        click={onClickItem}
                    />
                ))}
                {bannerList.length > 0 && <Banner list={bannerList} type="power" />}
            </View>
        </View>
    );
};

export default React.memo(PowerPanel);
