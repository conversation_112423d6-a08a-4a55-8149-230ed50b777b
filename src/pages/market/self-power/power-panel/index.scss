.power-panel-wrapper {
    width: 100%;
    height: calc(100% - 60px);
    @supports (env(safe-area-inset-bottom, 0)) {
        height: calc(100% - 60px - env(safe-area-inset-bottom, 0));
    }
    display: flex;
    flex-direction: column;
    overflow: hidden;
    align-items: center;
    padding: 0 13px;

    .grid-container {
        display: flex;
        flex-wrap: wrap;
        overflow-y: auto;
        width: 100%;
        padding-bottom: 50px;

        .grid-item {
            display: flex;
            flex-direction: column;
            flex: 0 0 calc((100vw - 28px - 40px) / 3); // 三列布局，减去margin
            margin: 6px 7px;
            position: relative;

            .content-container {
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                border-radius: 10px;
                // border: 0.5px solid rgba(255, 104, 158, 0.40);
                box-shadow: 0 0 0 0.5px rgba(255, 104, 158, 0.4);
                overflow: hidden;

                .icon {
                    width: 76px;
                    height: 76px;
                    margin-top: 10px;
                }

                .value {
                    color: #000;
                    font-size: 13px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                }

                .price-container {
                    margin-top: 10px;
                    width: 100%;
                    height: 26px;
                    display: flex;
                    flex-direction: row;
                    background-color: #ff689e;
                    align-items: center;
                    justify-content: center;

                    .price-icon-prefix {
                        width: 16px;
                        height: 16px;
                    }

                    .price {
                        color: #fff;
                        text-align: center;
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 600;
                        line-height: 26px;
                        margin-left: 3px;
                    }
                }
            }

            .tag-container {
                position: absolute;
                right: -2px;
                top: -2px;
                display: flex;
                flex-direction: row;
                align-items: center;
                height: 21px;
                border-radius: 137px;
                background: linear-gradient(90deg, #ffe8b9 0%, #ffd07c 100%);
                padding: 2px 5px;

                .tag-prefix {
                    color: #684b16;
                    text-align: right;
                    font-size: 11px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 140%;
                }

                .tag-icon-prefix {
                    width: 12px;
                    height: 12px;
                }

                .tag-value {
                    color: #684b16;
                    text-align: right;
                    font-size: 11px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 140%;
                }

                @media screen and (max-width: 320px) {
                    padding: 0 1px; // 减小内边距
                    height: 18px; // 减小高度

                    .tag-prefix,
                    .tag-value {
                        font-size: 10px; // 减小字体大小
                        line-height: 130%; // 缩小行高
                    }

                    .tag-icon-prefix {
                        width: 10px; // 缩小图标尺寸
                        height: 10px;
                        margin: 0 1px; // 缩小图标间距（如果有的话）
                    }
                }
            }

            @media screen and (max-width: 320px) {
                flex: 0 0 calc((100vw - 20px - 36px) / 3);
                margin: 6px 5px;
            }
        }
    }
}
