/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable no-console */
import { useContextStore } from '@/components/storeContext/StoreContext';
import { ProductLevel } from '@/service/res/PayPanelRes';
import { isAndroid, isIOS } from '@/utils';
import { startRecharge } from '@/utils/rpc';
import { rpc } from '@music/mat-base-h5';
import { Image, Text, View } from '@tarojs/components';
import { showToast } from '@tarojs/taro';
import React, { useCallback, useEffect, useRef } from 'react';
import goldIcon from '../../../assets/market/icon-gold.png';
import { useBalanceStore } from '../../../store/balanceStore';
import { marketStoreCreator, TabType } from '../store/store';
import './index.scss';
import { payPanelStoreCreator, supportPay } from './payPanelStore';

const ProducrItem = ({ productLevel }: { productLevel: ProductLevel }) => {
    const onClickItem = useCallback(() => {
        startRecharge({
            payType: 'recharge_ai_coin',
            productId: String(productLevel.product.id),
            payPrice: productLevel.product.paymentPrice,
            coinNum: productLevel.product.receiveAmount,
            channelCodes: JSON.stringify(productLevel.channels),
            coinName: '环渊晶石',
        });
    }, [productLevel]);

    return (
        <View className="grid-item">
            <View className="content-container" onClick={onClickItem}>
                <Image className="icon" src={goldIcon} />
                <Text className="value">{productLevel.product.receiveAmount}</Text>
                <Text className="desc">环渊晶石</Text>
                <Text className="price">￥{productLevel.product.paymentPrice}</Text>
            </View>
            {productLevel.product.subTitle && (
                <View className="tag-container">
                    <Text className="tag-prefix">限时加赠</Text>
                    <Image className="tag-icon-prefix" src={goldIcon} />
                    <Text className="tag-value">{productLevel.product.subTitle}</Text>
                </View>
            )}
        </View>
    );
};

const PayPanel = () => {
    const usePayPanelStore = useContextStore(payPanelStoreCreator);
    const productList = usePayPanelStore((state) => state.productList);

    useEffect(() => {
        usePayPanelStore.getState().requestProductList();
    }, [usePayPanelStore]);

    const pollInterval = useRef<NodeJS.Timeout | null>(null);
    const pollCount = useRef(0);

    useEffect(() => {
        if (isIOS || isAndroid) {
            const startPolling = () => {
                pollInterval.current && clearInterval(pollInterval.current);
                pollCount.current = 0;

                pollInterval.current = setInterval(() => {
                    if (pollCount.current >= 5) {
                        pollInterval.current && clearInterval(pollInterval.current);
                        return;
                    }
                    useBalanceStore.getState().requestBalance();
                    pollCount.current += 1;
                }, 2000);
            };

            const paySuccessCallback = (res: unknown) => {
                console.log('paySuccessCallback', res);
                useBalanceStore.getState().requestBalance();
                startPolling();
            };

            const payFailCallback = (res: unknown) => {
                console.log('payFailCallback', res);
            };

            rpc.on('event.nmy_rn_event_pay_rechargeSuccess', paySuccessCallback);
            rpc.on('event.nmy_rn_event_pay_rechargeFail', payFailCallback);

            return () => {
                rpc.off('event.nmy_rn_event_pay_rechargeSuccess', paySuccessCallback);
                rpc.off('event.nmy_rn_event_pay_rechargeFail', paySuccessCallback);
                pollInterval.current && clearInterval(pollInterval.current);
            };
        }
        return () => { };
    }, []);

    const useMarketStore = useContextStore(marketStoreCreator);
    const currentTab = useMarketStore((state) => state.currentTab);
    if (currentTab?.type === TabType.recharge && !supportPay()) {
        showToast({
            title: '当前版本过低，请升级后充值',
            icon: 'none',
        });
    }

    return (
        <View className="pay-panel-wrapper">
            <View className="grid-container">
                {productList?.levels?.map((productLevel) => (
                    <ProducrItem key={productLevel.product.id} productLevel={productLevel} />
                ))}
            </View>
        </View>
    );
};

export default React.memo(PayPanel);
