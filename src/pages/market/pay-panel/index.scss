.pay-panel-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    align-items: center;
    padding: 0 18px;

    .grid-container {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -7px; // 抵消子元素的margin
        overflow-y: auto;
        width: 100%;
        padding-bottom: 50px;

        .grid-item {
            display: flex;
            flex-direction: column;
            flex: 0 0 calc(33.333% - 14px); // 三列布局，减去margin
            margin: 6px 7px;
            position: relative;

            .content-container {
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                border-radius: 10px;
                // border: 0.5px solid rgba(255, 104, 158, 0.40);
                box-shadow: 0 0 0 0.5px rgba(255, 104, 158, 0.4);
                overflow: hidden;

                .icon {
                    width: 28px;
                    height: 28px;
                    margin-top: 20px;
                }

                .value {
                    color: #000;
                    font-size: 13px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                    margin-top: 9px;
                }

                .desc {
                    color: #000;
                    font-size: 10px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                }

                .price {
                    margin-top: 10px;
                    width: 100%;
                    height: 26px;
                    background-color: #ff689e;
                    color: #fff;
                    text-align: center;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 26px;
                }
            }

            .tag-container {
                position: absolute;
                right: -2px;
                top: -2px;
                display: flex;
                flex-direction: row;
                align-items: center;
                height: 21px;
                border-radius: 137px;
                background: linear-gradient(90deg, #ffe8b9 0%, #ffd07c 100%);
                padding: 2px 5px;

                .tag-prefix {
                    color: #684b16;
                    text-align: right;
                    font-size: 11px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 140%;
                }

                .tag-icon-prefix {
                    width: 12px;
                    height: 12px;
                }

                .tag-value {
                    color: #684b16;
                    text-align: right;
                    font-size: 11px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 140%;
                }
            }
        }
    }
}
