/* eslint-disable no-console */
import { payProductListApi } from '@/service/payApi';
import { PayProductList } from '@/service/res/PayPanelRes';
import { showToast } from '@tarojs/taro';
import Env from '@music/mobile-env';
import { create } from 'zustand';
import { getAppType, openUrl } from '@/utils/rpc';
import { getRechargeUrl, getSupportPay } from '@/utils/appSourceAdapter';
import { isIOS } from '@/utils';

export interface PayPanelState {
    productList?: PayProductList;
    requestProductList: () => void;
}

export const supportPay = (): boolean => {
    return getSupportPay();
};

export const gotoRechargePage = () => {
    if (!supportPay()) {
        showToast({
            title: '当前版本过低，请升级后充值',
            icon: 'none',
        });
        return;
    }
    openUrl(getRechargeUrl());
};

export const payPanelStoreCreator = () => {
    return create<PayPanelState>((set) => ({
        requestProductList: () => {
            if (!supportPay()) {
                return;
            }
            const request = (appType: string) => {
                payProductListApi({
                    payType: 'recharge_ai_coin',
                    regionCode: 'CN',
                    appType,
                    osType: isIOS ? 'ios' : 'android',
                })
                    .then((res) => {
                        set({ productList: res });
                    })
                    .catch((err) => {
                        showToast({
                            title: err.message,
                            icon: 'none',
                        });
                    });
            };

            let appType = 'release';
            if (isIOS) {
                getAppType((e) => {
                    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                    e.appType && (appType = e.appType);
                    console.log('appType', appType);
                    request(appType);
                });
            } else {
                request(appType);
            }
        },
    }));
};
