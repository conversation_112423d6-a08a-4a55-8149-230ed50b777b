import React from 'react';
import { Image, Swiper, SwiperItem } from '@tarojs/components';
import EventTrackView from '@/components/EventTrack';
import { addClickLog } from '@/utils/logTool';
import { jumpLink } from '@/router';
import { BannerItem } from '../store/store';

import './index.scss';

const Banner = ({ list, type }: { list: BannerItem[]; type: string }) => {
    return (
        <div className="bannerWrapper">
            <Swiper
                className="bannerSwiper"
                indicatorDots
                indicatorColor="#000000"
                indicatorActiveColor="#00000080">
                {list.map((item) => (
                    <SwiperItem key={item.id} className="bannerItem">
                        <EventTrackView
                            params={{
                                _spm:
                                    type === 'lottery'
                                        ? 'mod_ai_store_ticket_banner|page_ai_store_ticket|page_ai_store'
                                        : 'mod_ai_store_physcial_banner|page_ai_store_physcial|page_ai_store',
                                s_ctype: 'banner',
                                s_cid: item.id,
                                url: item.jumpLink,
                            }}
                            isPage={false}
                        />
                        <Image
                            className="bannerImage"
                            src={item.pictureUrl}
                            mode="aspectFill"
                            onClick={() => {
                                jumpLink(item.jumpLink);
                                addClickLog(
                                    type === 'lottery'
                                        ? 'mod_ai_store_ticket_banner|page_ai_store_ticket|page_ai_store'
                                        : 'mod_ai_store_physcial_banner|page_ai_store_physcial|page_ai_store',
                                    {
                                        s_ctype: 'banner',
                                        s_cid: item.id,
                                        url: item.jumpLink,
                                    }
                                );
                            }}
                        />
                    </SwiperItem>
                ))}
            </Swiper>
        </div>
    );
};

export default React.memo(Banner);
