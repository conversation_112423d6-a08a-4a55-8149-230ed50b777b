.market-page-wrapper {
    background-color: #fff;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    align-items: center;

    .market-list-bg {
        position: absolute;
        width: 100%;
        height: 278px;
        background-image: url('../../assets/explore/icon-page-bg.jpg');
        background-repeat: no-repeat;
        background-size: cover;
        background-position: top center;
        top: 0;
    }

    .market-window {
        height: 100%;
        width: 100vw;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: relative;

        .market-icon-bg {
            position: absolute;
            width: 114px;
            height: 55px;
            background-image: url('../../assets/explore/icon-page-logo.png');
            background-repeat: no-repeat;
            background-size: cover;
            background-position: top center;
            top: 0;
            right: 0;
            opacity: 0.5;
        }
    }

    .right-wrapper {
        display: flex;
        height: 100%;
        align-items: center;
        z-index: 1;

        .powerInfo {
            min-width: 78px;
            height: 26px;
            border-radius: 18px;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;

            .image {
                width: 26px;
                height: 26px;
                margin-left: 2px;
            }

            .num {
                font-size: 15px;
                margin-left: 0;
                font-weight: 600;
                color: #000;
            }

            .count {
                font-size: 15px;
                color: #000;
                opacity: 0.5;
                margin-right: 8px;
            }

            @media screen and (max-width: 320px) {
                .image {
                    width: 20px;
                    height: 20px;
                }

                .num {
                    font-size: 13px;
                }

                .count {
                    font-size: 13px;
                }
            }
        }

        .goldInfo {
            height: 26px;
            border-radius: 18px;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            margin-left: 5px;

            .imageLeft {
                width: 26px;
                height: 26px;
                margin-left: 4px;
            }

            .count {
                margin-left: 6px;
                min-width: 30px;
                color: #000;
                margin-right: 6px;
                letter-spacing: 0;
                font-size: 14px;
                font-weight: 600;
            }

            .gift-panel-recharge-add {
                margin-right: 6px;
                width: 16px;
                height: 16px;
            }

            @media screen and (max-width: 320px) {
                .imageLeft {
                    width: 20px;
                    height: 20px;
                }

                .count {
                    font-size: 13px;
                }

                .gift-panel-recharge-add {
                    font-size: 13px;
                }
            }
        }
    }

    .at-tabs {
        margin-top: 11px;

        .at-tabs__header {
            background-color: transparent;
            padding-left: 20px;

            .at-tabs__item {
                color: #000;
                opacity: 0.4;
                font-size: 16px;
                font-weight: 600;
                line-height: normal;
                letter-spacing: 0;
                padding: 0 0 4px 0;
                flex: none;
                margin-right: 20px;
            }

            .at-tabs__item--active {
                color: #ff689e;
                opacity: 1;
                font-size: 16px;
                font-weight: 600;
                line-height: normal;
                letter-spacing: 0;
            }

            .at-tabs__item-underline {
                border-radius: 28px;
                background: #ff689e;
                width: 16px;
                height: 2px;
                position: absolute;
                left: 50%;
                bottom: 0;
                transform: translateX(-50%) scaleX(0);
                transition: none;
            }

            .at-tabs__item--active .at-tabs__item-underline {
                transform: translateX(-50%) scaleX(1);
            }
        }

        .at-tabs__body {
            height: 100%;

            .at-tabs__underline {
                background-color: rgba(0, 0, 0, 0.1);
                height: 0.5px !important;
                margin-top: 7px;
            }

            .at-tabs-pane {
                height: 100%;
                margin-top: 20px;
            }
        }
    }

    .taro-img__mode-aspectfill {
        object-fit: cover;
    }
}
