import { View, Text, Image } from '@tarojs/components';
import React, { useState } from 'react';
import './index.scss';
import ICON_CHIP from '@/assets/market/ic_market_card_small.png';
import { useDialog } from '@/components/dialog';
import { CardStatus } from '@/types/AigcRobotProfile';
import CardPreview from '@/components/CardPreview';
import { optimizeImage } from '@/utils/image';
import { ShopItem } from './shopTypes';
import BuyConfirm from './buyConfirm';
import useCardPanelStore from './cardPanelStore';

const CardItem = ({ card }: { card: ShopItem }) => {
    const confirmDialog = useDialog(BuyConfirm);
    const [cardViewVisible, setCardViewVisible] = useState(false);
    const [cardInfo, setCardInfo] = useState<any>(null);

    const optimizedCardCover = optimizeImage({
        src: card?.cover,
        width: 375,
        height: 812,
    });

    const confirmClick = () => {
        if (card.status !== -1) {
            confirmDialog?.show(card, 'card');
        }
    };

    const itemClick = () => {
        useCardPanelStore
            .getState()
            .fetchRobotId(card.chapterId)
            .then((res) => {
                setCardInfo({
                    rewardIcon: res.chapterImg,
                    rewardLevel: res.chapterLevel,
                    rewardName: res.chapterName,
                    isLock: res.cardStatus === CardStatus.LOCKED,
                    rewardDesc: res.chapterDesc,
                });
                setCardViewVisible(true);
            });
    };

    return (
        <View className="card-item-container">
            <View className="card-item-img-container" onClick={itemClick}>
                <Image className="card-item-img" src={optimizedCardCover} mode="aspectFill" />
            </View>
            <View className="card-item-bottom-container">
                <View className="card-item-title-container" onClick={itemClick}>
                    <Text className="card-item-title">{card.productName}</Text>
                    <View className="card-item-chip-container">
                        <Image className="card-item-chip-img" src={ICON_CHIP} />
                        {card.status !== -1 && (
                            <Text className="card-item-chip-title">{card.amount}</Text>
                        )}
                        {card.status === -1 && (
                            <Text className="card-item-chip-title card-item-title-disable">
                                {card.amount}
                            </Text>
                        )}
                    </View>
                </View>
                {card.status === -1 && (
                    <Text className="card-item-button card-item-button-disable">已拥有</Text>
                )}
                {card.status !== -1 && (
                    <Text className="card-item-button" onClick={confirmClick}>
                        兑换
                    </Text>
                )}
            </View>
            {cardViewVisible && (
                <CardPreview data={cardInfo} onClose={() => setCardViewVisible(false)} />
            )}
        </View>
    );
};

export default React.memo(CardItem);
