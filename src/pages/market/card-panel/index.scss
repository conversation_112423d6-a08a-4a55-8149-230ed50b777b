.card-panel-container {
    width: 100%;
    height: calc(100% - 60px);
    @supports (env(safe-area-inset-bottom, 0)) {
        height: calc(100% - 60px - env(safe-area-inset-bottom, 0));
    }
    display: flex;
    flex-direction: column;
    overflow: hidden;
    align-items: center;

    .card-grid-container {
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        flex-direction: row;
        padding-left: 20px;
        padding-right: 20px;
        overflow-y: auto;
        box-sizing: border-box;

        .card-item-container {
            width: calc((100% - 11px) / 2);
            display: flex;
            flex-direction: column;
            margin-top: 18px;

            &:nth-child(odd) {
                margin-right: 11px;
            }

            &:nth-child(1) {
                margin-top: 0;
            }

            &:nth-child(2) {
                margin-top: 0;
            }

            .card-item-img-container {
                width: 100%;
                height: 216px;
                position: relative;
            }

            .card-item-img {
                width: 100%;
                height: 216px;
                border-radius: 10px;

                img {
                    object-fit: cover;
                }
            }

            .card-item-ssr {
                width: 81px;
                height: 30px;
                position: absolute;
                right: 0;
                bottom: 0;
            }

            .card-item-title {
                font-size: 14px;
                font-weight: 600;
                color: #000;
                word-break: break-all;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                line-clamp: 1;
                -webkit-line-clamp: 1;
                overflow: hidden;
            }

            .card-item-title-disable {
                text-decoration: line-through;
            }

            .card-item-bottom-container {
                width: 100%;
                flex-direction: row;
                margin-top: 8px;
                position: relative;

                .card-item-button {
                    width: 58px;
                    height: 25px;
                    background-color: #ff689e;
                    border-radius: 25px;
                    color: #fff;
                    font-size: 12px;
                    font-weight: 600;
                    text-align: center;
                    padding-top: 4px;
                    position: absolute;
                    right: 0;
                    top: 0;
                    margin-top: 6px;
                }

                .card-item-button-disable {
                    background-color: #d9d9d9;
                }
            }

            .card-item-title-container {
                flex-direction: column;
                width: fit-content;
                justify-content: flex-start;
                max-width: 90px;
            }

            .card-item-chip-container {
                display: flex;
                flex-direction: row;
                margin-top: 2px;
            }

            .card-item-chip-title {
                font-size: 12px;
                font-weight: 400;
                color: rgba($color: #000, $alpha: 0.8);
                word-break: break-all;
                text-overflow: ellipsis;
                display: -webkit-box;
                margin-left: 2px;
                -webkit-box-orient: vertical;
                line-clamp: 1;
                -webkit-line-clamp: 1;
                overflow: hidden;
            }

            .card-item-chip-img {
                width: 16px;
                height: 16px;
                border-bottom-right-radius: 10px;
            }
        }
    }
}
