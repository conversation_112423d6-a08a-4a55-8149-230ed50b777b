import fetch from '@/utils/fetch';

export const shopListApi = (data: { type: string; sceneType: string }): any =>
    fetch('/api/sociallive/ai/game/shop/list', {
        method: 'post',
        data,
    });

export const shopExchangeApi = (data: {
    productId: string;
    type: string;
    sceneType: string;
}): any =>
    fetch('/api/sociallive/ai/game/shop/exchange', {
        method: 'post',
        data,
    });

export const exchangeBalance = (): any =>
    fetch('/api/sociallive/ai/game/query/balance', {
        method: 'post',
        data: {
            sceneType: 'chapter_card',
        },
    });

export const fetchCardInfo = (data: { chapterId: string }): any =>
    fetch('/api/sociallive/ai/game/chapter/card/aggregate/info', {
        method: 'post',
        data,
    });

export const fetchBannerList = (data: { position: string }): any =>
    fetch('/api/mirth/home/<USER>/list', {
        method: 'post',
        data,
    });
