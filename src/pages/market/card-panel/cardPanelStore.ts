import { create } from 'zustand';
import { showToast } from '@tarojs/taro';
import { MessageErrorCode } from '@/types/im';
import { jump2GachaCard } from '@/router';
import { useBalanceStore } from '@/store/balanceStore';
import useDetailStore from '../../chat/store/useDetailStore';
import { CardInfo, ShopItem } from './shopTypes';
import { fetchCardInfo, shopExchangeApi, shopListApi } from './shopApi';

interface CardPanelState {
    cardList?: ShopItem[];
    fetch: () => Promise<void>;
    fetchRobotId: (chapterId: string) => Promise<CardInfo>;
    exchange: (item?: ShopItem) => Promise<void>;
}

const useCardPanelStore = create<CardPanelState>((set, get, store) => {
    let currentUserId = '';
    useDetailStore.subscribe((state, prev) => {
        if (state.userId !== prev.userId || currentUserId !== state.userId) {
            if (currentUserId) {
                set(store.getInitialState());
            }
            currentUserId = state.userId;
        }
    });
    return {
        cardList: undefined,
        fetch: async () => {
            shopListApi({
                type: 'card',
                sceneType: 'chapter_card',
            })
                .then((res) => {
                    console.log('req', 'useCardPanelStore', 'res:', res);
                    set({ cardList: res });
                })
                .catch((err) => {
                    console.log('req', 'useCardPanelStore', 'res:', err);
                    showToast({
                        title: err.message,
                        icon: 'none',
                    });
                });
        },
        fetchRobotId: async (chapterId) => {
            return fetchCardInfo({
                chapterId,
            }).catch((err) => {
                console.log('req', 'useCardPanelStore', 'res:', err);
                showToast({
                    title: err.message,
                    icon: 'none',
                });
            });
        },
        exchange: async (item) => {
            shopExchangeApi({
                productId: item.productId,
                type: 'card',
                sceneType: 'chapter_card',
            })
                .then((res) => {
                    // 刷新余额&跳转卡面
                    useBalanceStore.getState().requestExchangeBalance();
                    get().fetch();
                    console.log('exchangejson', JSON.stringify(res.chapterInfoItem));
                    jump2GachaCard({ infoJsonStr: JSON.stringify(res.chapterInfoItem), robotUserId: res.robotUserId })
                    showToast({
                        title: '兑换成功！',
                        icon: 'none',
                        duration: 2000,
                    });
                })
                .catch((err) => {
                    if (err.code === MessageErrorCode.balaceInsufficient) {
                        showToast({
                            title: '碎片不足，请获取后兑换',
                            icon: 'none',
                            duration: 2000,
                        });
                    } else {
                        showToast({
                            title: err.message,
                            icon: 'none',
                        });
                    }
                });
        },
    };
});

export default useCardPanelStore;
