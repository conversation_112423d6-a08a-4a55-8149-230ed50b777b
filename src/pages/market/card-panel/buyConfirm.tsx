import React from 'react';
import { AtModal } from 'taro-ui';
import { View, Text, CommonEventFunction } from '@tarojs/components';
import { CreateModalProps } from '@/components/dialog';
import '@/pages/chat/components/ChatRecordList/MsgItem/monologue/index.scss';
import useCardPanelStore from './cardPanelStore';
import useLotteryPanelStore from '../lottery-panel/useLotteryPanelStore';
import { ShopItem } from './shopTypes';

const BuyConfirmContent = ({
    isOpened,
    dismiss,
    onClose,
    shopItem,
    type,
}: {
    isOpened: boolean;
    dismiss: () => void;
    onClose: CommonEventFunction;
    shopItem: ShopItem;
    type: string;
}) => {
    const confirm = () => {
        if (type === 'card') {
            useCardPanelStore.getState().exchange(shopItem);
        } else if (type === 'ticket') {
            useLotteryPanelStore.getState().exchange(shopItem);
        }
        dismiss();
    };

    return (
        <View className="commonConfirmContainer">
            <AtModal
                isOpened={isOpened}
                className="custom-modal"
                onClose={onClose}
                closeOnClickOverlay={false}>
                <View className="modal-content">
                    <Text className="text-title">
                        确定消耗{shopItem.amount}
                        {shopItem.currency}购买{shopItem.productName}吗？
                    </Text>
                    <View className="btn-group">
                        <View className="cancel" onClick={dismiss}>
                            取消
                        </View>
                        <View className="confirm" onClick={() => confirm()}>
                            确定
                        </View>
                    </View>
                </View>
            </AtModal>
        </View>
    );
};

const BuyConfirm: CreateModalProps = {
    type: 'modal_custom',
    isModal: true,
    render(dismiss, shopItem, type) {
        return (
            <BuyConfirmContent
                dismiss={dismiss}
                isOpened={undefined}
                onClose={undefined}
                shopItem={shopItem}
                type={type}
            />
        );
    },
};

export default BuyConfirm;
