import { View } from '@tarojs/components';
import React, { useEffect } from 'react';
import './index.scss';
import CardItem from './cardItem';
import useCardPanelStore from './cardPanelStore';

const CardPanel = () => {
    const cardList = useCardPanelStore((state) => state.cardList);

    useEffect(() => {
        useCardPanelStore.getState().fetch();
    }, []);

    return (
        <View className="card-panel-container">
            <View className="card-grid-container">
                {cardList?.map((data) => (
                    <CardItem key={data.productId} card={data} />
                ))}
            </View>
        </View>
    );
};

export default React.memo(CardPanel);
