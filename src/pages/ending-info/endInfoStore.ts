import { StoreCreator, StoreLifecycle } from '@/components/storeContext/StoreContext';
import { AigcEndingCardInfo } from '@/types/AigcRobotProfile';
import { create } from 'zustand';
import { addFriendApi, isFriendApi } from '@/service/friendApi';
import Taro, { showLoading, showToast, hideLoading } from '@tarojs/taro';

export const State = {
    middle: 1,
    full_1: 2,
    full_2: 3,
} as const;

export type ShowState = (typeof State)[keyof typeof State];

export interface EndInfoState extends StoreLifecycle {
    currentState: ShowState;
    lastState: ShowState;
    isDouble: boolean;
    userId?: number;
    from?: string;
    endingInfo1?: AigcEndingCardInfo; // 左上
    endingInfo2?: AigcEndingCardInfo; // 右下
    selectEndingInfo?: AigcEndingCardInfo;
    init: (
        endingInfo1: AigcEndingCardInfo,
        endingInfo2: AigcEndingCardInfo | undefined,
        userId: number,
        from: string | undefined,
        uiState: ShowState | undefined
    ) => void;
    updateState: (state: ShowState) => void;
    addFriend: (success: () => void) => void;
    isFriend: () => Promise<boolean>;
}

export const endInfoStoreCreator: StoreCreator<EndInfoState> = () => {
    return create<EndInfoState>((set, get) => ({
        currentState: State.full_1,
        lastState: State.full_1,
        isDouble: false,
        init: (endingInfo1, endingInfo2, userId, from, uiState) => {
            let currentState: ShowState;
            let lastState: ShowState;
            if (endingInfo2) {
                currentState = uiState || State.middle;
                lastState = State.middle;
            } else {
                currentState = State.full_1;
                lastState = State.full_1;
            }
            set({
                endingInfo1,
                endingInfo2,
                userId,
                from,
                isDouble: !!endingInfo2,
                currentState,
                lastState,
                selectEndingInfo: endingInfo1,
            });
        },
        updateState: (state) => {
            const currentState = get().currentState;
            let selectEndingInfo = get().selectEndingInfo;
            if (state === State.full_2) {
                selectEndingInfo = get().endingInfo2;
            } else if (state === State.full_1) {
                selectEndingInfo = get().endingInfo1;
            }
            set({
                currentState: state,
                lastState: currentState,
                selectEndingInfo,
            });
        },
        addFriend: (success) => {
            const userId = get().userId;
            addFriendApi(userId)
                .then(() => {
                    Taro.eventCenter.trigger('profile_update', {
                        userId,
                        friend: true,
                    });
                    showToast({
                        title: '添加成功',
                    });
                    success();
                })
                .catch((err) => {
                    showToast({
                        title: err.message,
                        icon: 'none',
                    });
                });
        },
        isFriend: async () => {
            showLoading();
            try {
                const result = await isFriendApi(get().userId);
                return !!result;
            } finally {
                hideLoading();
            }
        },
    }));
};
