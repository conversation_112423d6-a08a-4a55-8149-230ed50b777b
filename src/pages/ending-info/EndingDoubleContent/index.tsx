import { Text, View } from '@tarojs/components';
import React, { useCallback } from 'react';
import './index.scss';
import { useContextStore } from '@/components/storeContext/StoreContext';
import { endInfoStoreCreator, State } from '@/pages/ending-info/endInfoStore';
import classNames from 'classnames';
import { isPointInTriangle } from '@/pages/ending-info/utils';
import { CardStatus } from '@/types/AigcRobotProfile';

const EndingDoubleContent = () => {
    const useEndInfoStore = useContextStore(endInfoStoreCreator);
    const endingInfo1 = useEndInfoStore((state) => state.endingInfo1);
    const endingInfo2 = useEndInfoStore((state) => state.endingInfo2);
    const currentState = useEndInfoStore((state) => state.currentState);
    const show = currentState === State.middle;

    const handleClick = useCallback(
        (e) => {
            const rect = e.currentTarget.getBoundingClientRect();
            const x = e.pageX - rect.left; // 相对于元素左上角的x坐标
            const y = e.pageY - rect.top; // 相对于元素左上角的y坐标

            const width = rect.width;
            const height = rect.height;

            const isBottomRight = isPointInTriangle(
                { x, y },
                { x: width, y: 0 }, // 右上角
                { x: width, y: height }, // 右下角
                { x: 0, y: height } // 左下角
            );

            if (isBottomRight) {
                useEndInfoStore.getState().updateState(State.full_2);
            } else {
                useEndInfoStore.getState().updateState(State.full_1);
            }
        },
        [useEndInfoStore]
    );

    return (
        <View
            className={classNames('ending-double-content-wrapper', {
                show,
                hide: !show,
            })}
            onClick={handleClick}>
            <View className="ending-1-card-bg">
                <View className="ending-info-wrapper ending-1-card">
                    <View className="ending-name good-logo">
                        <Text className="text-content">
                            {endingInfo1.cardStatus === CardStatus.LOCKED
                                ? '结局一'
                                : endingInfo1.endingName}
                        </Text>
                        <View
                            className={classNames({
                                lock: endingInfo1.cardStatus === CardStatus.LOCKED,
                                unlock: endingInfo1.cardStatus !== CardStatus.LOCKED,
                            })}
                        />
                    </View>
                    <Text className="chapter-name">探索剧情《{endingInfo1.bindChapterName}》</Text>
                </View>
            </View>
            <View className="ending-2-card-bg">
                <View className="ending-info-wrapper ending-2-card">
                    <View className="ending-name bad-logo">
                        <Text className="text-content">
                            {endingInfo2.cardStatus === CardStatus.LOCKED
                                ? '结局二'
                                : endingInfo2.endingName}
                        </Text>
                        <View
                            className={classNames({
                                lock: endingInfo2.cardStatus === CardStatus.LOCKED,
                                unlock: endingInfo2.cardStatus !== CardStatus.LOCKED,
                            })}
                        />
                    </View>
                    <Text className="chapter-name">探索剧情《{endingInfo2.bindChapterName}》</Text>
                </View>
            </View>
        </View>
    );
};

export default React.memo(EndingDoubleContent);
