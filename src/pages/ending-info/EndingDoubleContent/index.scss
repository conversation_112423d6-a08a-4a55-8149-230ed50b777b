.ending-double-content-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    align-items: center;
    position: absolute;
    left: 0;
    top: 0;

    &.show {
        animation: fadeIn 200ms forwards;
        pointer-events: auto;
    }

    &.hide {
        animation: fadeOut 200ms forwards;
        pointer-events: none;
    }

    .ending-1-card-bg {
        position: absolute;
        top: 90px;
        left: 0;
        width: 234px;
        height: 119px;
        background: url('../../../assets/chapter-info/icon-ending1-shadow-bg.png');
        background-size: 100% 100%;
        display: flex;
        flex-direction: row;
        align-items: center;

        .ending-1-card {
            margin-left: 21px;
        }
    }

    .ending-2-card-bg {
        position: absolute;
        right: 0;
        bottom: 64px;
        width: 234px;
        height: 119px;
        background: url('../../../assets/chapter-info/icon-ending2-shadow-bg.png');
        background-size: 100% 100%;
        display: flex;
        flex-direction: row-reverse;
        align-items: center;

        .ending-2-card {
            margin-right: 21px;
        }
    }

    .ending-info-wrapper {
        max-width: 180px;
        display: flex;
        flex-direction: column;
        flex-shrink: 0;

        .ending-name {
            color: #fff;
            font-family: 'SourceHanSerifCN-Bold', sans-serif;
            font-size: 26px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            letter-spacing: 1px;
            padding-bottom: 11px;
            position: relative;
            display: flex;
            flex-direction: row;
            align-items: center;

            .text-content {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .lock {
                display: inline-block;
                flex-shrink: 0;
                width: 22px;
                height: 22px;
                margin-left: 3px;
                background-image: url('../../../assets/chapter-info/icon-ending-lock.png');
                background-size: 100% 100%;
            }

            .unlock {
                display: inline-block;
                flex-shrink: 0;
                width: 20px;
                height: 20px;
                margin-left: 5px;
                background-image: url('../../../assets/chapter-info/icon-look.png');
                background-size: 100% 100%;
            }

            &.good-logo {
                &::before {
                    content: '';
                    display: block;
                    position: absolute;
                    left: -6.8px;
                    bottom: 0;
                    width: 156px;
                    height: 25px;
                    background-image: url('../../../assets/chapter-info/icon-shadow-good.png');
                    background-size: contain;
                    background-repeat: no-repeat;
                    background-position: left center;
                }
            }

            &.bad-logo {
                &::before {
                    content: '';
                    display: block;
                    position: absolute;
                    left: -3.8px;
                    bottom: 0;
                    width: 124px;
                    height: 25px;
                    background-image: url('../../../assets/chapter-info/icon-shadow-bad.png');
                    background-size: contain;
                    background-repeat: no-repeat;
                    background-position: left center;
                }
            }
        }

        .chapter-name {
            color: #fff;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;
            margin-top: 3px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        @keyframes fadeIn {
            0% {
                opacity: 0;
            }

            100% {
                opacity: 1;
            }
        }

        @keyframes fadeOut {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }
    }
}
