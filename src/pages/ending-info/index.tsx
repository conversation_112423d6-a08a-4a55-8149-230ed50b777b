import { DialogRootLayer } from '@/components/dialog';
import StoreProvider, { useContextStore } from '@/components/storeContext/StoreContext';
import { endInfoStoreCreator, ShowState, State } from '@/pages/ending-info/endInfoStore';
import EndingContent from '@/pages/ending-info/EndingContent';
import EndingDoubleContent from '@/pages/ending-info/EndingDoubleContent';
import ImageVideoBgClipper from '@/pages/ending-info/ImageVideoBgClipper';
import { parseUrlParamJson } from '@/utils/parseUtils';
import { Image, View } from '@tarojs/components';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import React, { useCallback, useEffect, useMemo } from 'react';
import closeIcon from '../../assets/chapter-info/icon-back.png';
import { AigcEndingCardInfo } from '../../../types/AigcRobotProfile';
import './index.scss';
import cardCollectStore from '../appreciate/store/cardCollectStore';

const EndingInfo = () => {
    const { ending, ending2, userId, from, uiState } = useMemo(() => {
        const params = getCurrentInstance().router?.params || {};
        return {
            ending: params.ending as string,
            userId: params.userId as unknown as number,
            ending2: params.ending2 as string | undefined,
            from: params.from as string,
            uiState: params.uiState
                ? (Number.parseInt(params.uiState, 10) as ShowState)
                : State.middle,
        };
    }, []);

    const ending1Info = useMemo(() => {
        return parseUrlParamJson<AigcEndingCardInfo>(ending);
    }, [ending]);

    const ending2Info = useMemo(() => {
        return ending2 ? parseUrlParamJson<AigcEndingCardInfo>(ending2) : undefined;
    }, [ending2]);

    const useEndInfoStore = useContextStore(endInfoStoreCreator);
    useMemo(() => {
        useEndInfoStore.getState().init(ending1Info, ending2Info, userId, from, uiState);
    }, [ending1Info, ending2Info, from, uiState, useEndInfoStore, userId]);

    const isDouble = useEndInfoStore((state) => state.isDouble);

    const onClickClose = useCallback(() => {
        Taro.navigateBack();
    }, []);

    useEffect(() => {
        if (ending1Info) {
            cardCollectStore
                .getState()
                .removeNewTag(
                    'ending',
                    `${userId}`,
                    ending1Info.bindChapterId,
                    ending1Info.endingCardId
                );
        }

        if (ending2Info) {
            cardCollectStore
                .getState()
                .removeNewTag(
                    'ending',
                    `${userId}`,
                    ending2Info.bindChapterId,
                    ending2Info.endingCardId
                );
        }
    }, [ending1Info, ending2Info, userId]);
    return (
        <View className="ending-info-page-wrapper">
            <ImageVideoBgClipper />
            <EndingContent />
            {isDouble && <EndingDoubleContent />}
            <View className="header-position">
                <View className="ending-header-wrapper">
                    <Image
                        className="close-ending"
                        src={closeIcon}
                        mode="aspectFill"
                        onClick={onClickClose}
                    />
                </View>
            </View>
        </View>
    );
};

const EndingInfoPage = () => {
    return (
        <StoreProvider type="page">
            <EndingInfo />
            <DialogRootLayer />
        </StoreProvider>
    );
};

export default React.memo(EndingInfoPage);
