export interface Point {
    x: number;
    y: number;
}

/**
 * 判断点是否在三角形内部
 * @param point 要检测的点坐标
 * @param vertex1 三角形顶点1
 * @param vertex2 三角形顶点2
 * @param vertex3 三角形顶点3
 * @returns 如果点在三角形内部则返回 true，否则返回 false
 */
export const isPointInTriangle = (
    point: Point,
    vertex1: Point,
    vertex2: Point,
    vertex3: Point
): boolean => {
    // 使用向量叉积法判断点是否在三角形内

    // 计算向量AB与向量AP的叉积
    const crossProduct1 =
        (vertex2.x - vertex1.x) * (point.y - vertex1.y) -
        (vertex2.y - vertex1.y) * (point.x - vertex1.x);

    // 计算向量BC与向量BP的叉积
    const crossProduct2 =
        (vertex3.x - vertex2.x) * (point.y - vertex2.y) -
        (vertex3.y - vertex2.y) * (point.x - vertex2.x);

    // 计算向量CA与向量CP的叉积
    const crossProduct3 =
        (vertex1.x - vertex3.x) * (point.y - vertex3.y) -
        (vertex1.y - vertex3.y) * (point.x - vertex3.x);

    // 如果三个叉积的符号相同（都为正或都为负），则点在三角形内
    return (
        (crossProduct1 >= 0 && crossProduct2 >= 0 && crossProduct3 >= 0) ||
        (crossProduct1 <= 0 && crossProduct2 <= 0 && crossProduct3 <= 0)
    );
};
