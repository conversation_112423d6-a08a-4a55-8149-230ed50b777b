.image-video-bg-wrapper-1 {
    &.fade-out-anim {
        animation: full-fade-out 200ms ease forwards;
    }

    &.fade-in-anim {
        animation: full-fade-in 200ms ease forwards;
    }
}

.image-video-bg-wrapper-2 {
    .image-video-bg {
        clip-path: polygon(100% 0, 100% 100%, 0 100%);
        transform: translateZ(0);
        will-change: transform;

        //.bg-img,
        //.bg-video,
        //.bg-mask {
        //    clip-path: inherit;
        //    transform: translateZ(0);
        //    will-change: transform;
        //}
    }

    /**
     * 从中间状态(右下三角形)过渡到完全隐藏
     */
    &.middle-none-anim {
        &.establish {
            .image-video-bg {
                clip-path: polygon(100% 100%, 100% 100%, 100% 100%);
                animation: none;
            }
        }

        .image-video-bg {
            animation: middle-none 200ms ease forwards;
        }
    }

    /**
     * 从完全隐藏状态过渡到中间状态(右下三角形)
     */
    &.none-middle-anim {
        &.establish {
            .image-video-bg {
                clip-path: polygon(100% 0, 100% 100%, 0 100%);
                animation: none;
            }
        }

        .image-video-bg {
            animation: none-middle 200ms ease forwards;
        }
    }

    /**
     * 从中间状态(右下三角形)过渡到全屏显示
     */
    &.middle-full-anim {
        &.establish {
            .image-video-bg {
                clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
                animation: none;
            }
        }

        .image-video-bg {
            animation: middle-full 200ms ease forwards;
        }
    }

    /**
     * 从全屏显示过渡到中间状态(右下三角形)
     */
    &.full-middle-anim {
        &.establish {
            .image-video-bg {
                clip-path: polygon(100% 0, 100% 100%, 0 100%);
                animation: none;
            }
        }

        .image-video-bg {
            animation: full-middle 200ms ease forwards;
        }
    }

    &.fade-out-anim {
        animation: full-fade-out 200ms ease forwards;

        .image-video-bg {
            clip-path: none;
        }
    }

    &.fade-in-anim {
        animation: full-fade-in 200ms ease forwards;

        .image-video-bg {
            clip-path: none;
        }
    }
}

@keyframes middle-none {
    0% {
        clip-path: polygon(100% 0, 100% 100%, 0 100%);
    }

    10% {
        clip-path: polygon(100% 10%, 100% 100%, 10% 100%);
    }

    20% {
        clip-path: polygon(100% 20%, 100% 100%, 20% 100%);
    }

    25% {
        clip-path: polygon(100% 25%, 100% 100%, 25% 100%);
    }

    30% {
        clip-path: polygon(100% 30%, 100% 100%, 30% 100%);
    }

    40% {
        clip-path: polygon(100% 40%, 100% 100%, 40% 100%);
    }

    50% {
        clip-path: polygon(100% 50%, 100% 100%, 50% 100%);
    }

    60% {
        clip-path: polygon(100% 60%, 100% 100%, 60% 100%);
    }

    70% {
        clip-path: polygon(100% 70%, 100% 100%, 70% 100%);
    }

    75% {
        clip-path: polygon(100% 75%, 100% 100%, 75% 100%);
    }

    80% {
        clip-path: polygon(100% 80%, 100% 100%, 80% 100%);
    }

    90% {
        clip-path: polygon(100% 90%, 100% 100%, 90% 100%);
    }

    100% {
        clip-path: polygon(100% 100%, 100% 100%, 100% 100%);
    }
}

@keyframes none-middle {
    0% {
        clip-path: polygon(100% 100%, 100% 100%, 100% 100%);
    }

    10% {
        clip-path: polygon(100% 90%, 100% 100%, 90% 100%);
    }

    20% {
        clip-path: polygon(100% 80%, 100% 100%, 80% 100%);
    }

    25% {
        clip-path: polygon(100% 75%, 100% 100%, 75% 100%);
    }

    30% {
        clip-path: polygon(100% 70%, 100% 100%, 70% 100%);
    }

    40% {
        clip-path: polygon(100% 60%, 100% 100%, 60% 100%);
    }

    50% {
        clip-path: polygon(100% 50%, 100% 100%, 50% 100%);
    }

    60% {
        clip-path: polygon(100% 40%, 100% 100%, 40% 100%);
    }

    70% {
        clip-path: polygon(100% 30%, 100% 100%, 30% 100%);
    }

    75% {
        clip-path: polygon(100% 25%, 100% 100%, 25% 100%);
    }

    80% {
        clip-path: polygon(100% 20%, 100% 100%, 20% 100%);
    }

    90% {
        clip-path: polygon(100% 10%, 100% 100%, 10% 100%);
    }

    100% {
        clip-path: polygon(100% 0, 100% 100%, 0 100%);
    }
}

@keyframes middle-full {
    0% {
        clip-path: polygon(100% 0, 100% 100%, 0 100%);
    }

    10% {
        clip-path: polygon(90% 0, 100% 0, 100% 100%, 0 100%, 0 90%);
    }

    20% {
        clip-path: polygon(80% 0, 100% 0, 100% 100%, 0 100%, 0 80%);
    }

    25% {
        clip-path: polygon(75% 0, 100% 0, 100% 100%, 0 100%, 0 75%);
    }

    30% {
        clip-path: polygon(70% 0, 100% 0, 100% 100%, 0 100%, 0 70%);
    }

    40% {
        clip-path: polygon(60% 0, 100% 0, 100% 100%, 0 100%, 0 60%);
    }

    50% {
        clip-path: polygon(50% 0, 100% 0, 100% 100%, 0 100%, 0 50%);
    }

    60% {
        clip-path: polygon(40% 0, 100% 0, 100% 100%, 0 100%, 0 40%);
    }

    70% {
        clip-path: polygon(30% 0, 100% 0, 100% 100%, 0 100%, 0 30%);
    }

    75% {
        clip-path: polygon(25% 0, 100% 0, 100% 100%, 0 100%, 0 25%);
    }

    80% {
        clip-path: polygon(20% 0, 100% 0, 100% 100%, 0 100%, 0 20%);
    }

    90% {
        clip-path: polygon(10% 0, 100% 0, 100% 100%, 0 100%, 0 10%);
    }

    100% {
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    }
}

@keyframes full-middle {
    0% {
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    }

    10% {
        clip-path: polygon(10% 0, 100% 0, 100% 100%, 0 100%, 0 10%);
    }

    20% {
        clip-path: polygon(20% 0, 100% 0, 100% 100%, 0 100%, 0 20%);
    }

    25% {
        clip-path: polygon(25% 0, 100% 0, 100% 100%, 0 100%, 0 25%);
    }

    30% {
        clip-path: polygon(30% 0, 100% 0, 100% 100%, 0 100%, 0 30%);
    }

    40% {
        clip-path: polygon(40% 0, 100% 0, 100% 100%, 0 100%, 0 40%);
    }

    50% {
        clip-path: polygon(50% 0, 100% 0, 100% 100%, 0 100%, 0 50%);
    }

    60% {
        clip-path: polygon(60% 0, 100% 0, 100% 100%, 0 100%, 0 60%);
    }

    70% {
        clip-path: polygon(70% 0, 100% 0, 100% 100%, 0 100%, 0 70%);
    }

    75% {
        clip-path: polygon(75% 0, 100% 0, 100% 100%, 0 100%, 0 75%);
    }

    80% {
        clip-path: polygon(80% 0, 100% 0, 100% 100%, 0 100%, 0 80%);
    }

    90% {
        clip-path: polygon(90% 0, 100% 0, 100% 100%, 0 100%, 0 90%);
    }

    100% {
        clip-path: polygon(100% 0, 100% 100%, 0 100%);
    }
}

@keyframes full-fade-in {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes full-fade-out {
    0% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}
