import ImageVideoBg from '@/components/ImageVideoBg';
import { getImage } from '@/utils/image';
import { View } from '@tarojs/components';
import React, { Fragment, useEffect, useRef } from 'react';
import './index.scss';
import { useContextStore } from '@/components/storeContext/StoreContext';
import { endInfoStoreCreator, State } from '@/pages/ending-info/endInfoStore';
import { CardStatus } from '@/types/AigcRobotProfile';

const ImageVideoBgClipper = () => {
    const useEndInfoStore = useContextStore(endInfoStoreCreator);
    const endingInfo1 = useEndInfoStore((state) => state.endingInfo1);
    const endingInfo2 = useEndInfoStore((state) => state.endingInfo2);
    const currentState = useEndInfoStore((state) => state.currentState);
    const lastState = useEndInfoStore((state) => state.lastState);
    // 使用ref来跟踪当前的动画状态
    const bg2AnimStateRef = useRef('');
    const animationChangedRef = useRef(false);

    let bg1AnimState = '';
    let bg2AnimState = '';
    if (endingInfo2) {
        if (currentState === State.full_1 && lastState === State.middle) {
            bg2AnimState = 'middle-none-anim';
        } else if (currentState === State.middle && lastState === State.full_1) {
            bg2AnimState = 'none-middle-anim';
        } else if (currentState === State.full_2 && lastState === State.middle) {
            bg2AnimState = 'middle-full-anim';
        } else if (currentState === State.middle && lastState === State.full_2) {
            bg2AnimState = 'full-middle-anim';
        } else if (currentState === State.full_1 && lastState === State.full_2) {
            bg1AnimState = 'fade-in-anim';
            bg2AnimState = 'fade-out-anim';
        } else if (currentState === State.full_2 && lastState === State.full_1) {
            bg1AnimState = 'fade-out-anim';
            bg2AnimState = 'fade-in-anim';
        }
    }

    if (bg2AnimStateRef.current !== bg2AnimState) {
        bg2AnimStateRef.current = bg2AnimState;
        animationChangedRef.current = true;
    }

    const imgVideoWrapperRef = useRef(null);
    const [addEstablishClass, setAddEstablishClass] = React.useState(false);
    useEffect(() => {
        // 查找背景元素
        const bgElement = imgVideoWrapperRef.current;

        if (animationChangedRef.current && bg2AnimStateRef.current && bgElement) {
            setAddEstablishClass(false);
            animationChangedRef.current = false;
        }

        // 监听动画结束事件
        const handleAnimationEnd = () => {
            const currentAnim = bg2AnimStateRef.current;

            // 仅当有动画类且动画结束时添加establish类
            if (currentAnim) {
                setAddEstablishClass(true);
            }
        };

        bgElement?.addEventListener('animationend', handleAnimationEnd);

        return () => {
            bgElement?.removeEventListener('animationend', handleAnimationEnd);
        };
    }, [currentState, lastState]);

    return (
        <Fragment>
            <View className={`${bg1AnimState}`}>
                <ImageVideoBg
                    imgUrl={getImage(endingInfo1?.endingCardImg)}
                    videoUrl={getImage(endingInfo1?.endingCardBigImg)}
                    showMask={endingInfo1.cardStatus === CardStatus.LOCKED}
                />
            </View>
            {endingInfo2 && (
                <View
                    className={`image-video-bg-wrapper-2 ${bg2AnimState} ${addEstablishClass ? 'establish' : ''
                        }`}
                    ref={imgVideoWrapperRef}>
                    <ImageVideoBg
                        imgUrl={getImage(endingInfo2?.endingCardImg)}
                        videoUrl={getImage(endingInfo2?.endingCardBigImg)}
                        showMask={endingInfo2.cardStatus === CardStatus.LOCKED}
                    />
                </View>
            )}
        </Fragment>
    );
};

export default React.memo(ImageVideoBgClipper);
