import { ConfirmModalProvider } from '@/components/confirm-modal';
import { useDialog } from '@/components/dialog';
import { withStopPropagation } from '@/utils/eventUtils';
import { Image, Text, View } from '@tarojs/components';
import Taro, { showToast } from '@tarojs/taro';
import React, { useCallback } from 'react';
import { useContextStore } from '@/components/storeContext/StoreContext';
import { endInfoStoreCreator, State } from '@/pages/ending-info/endInfoStore';
import switch1Icon from '@/assets/chapter-info/icon-switch-1.png';
import switch2Icon from '@/assets/chapter-info/icon-switch-2.png';
import IconRelationType from '@/assets/chapter-info/icon-end-content-relation-type.png';
import classNames from 'classnames';
import { bindChapterRelationName, CardStatus } from '@/types/AigcRobotProfile';
import { jump2ChapterInfo, jump2EndingRetrospect } from '@/router';
import './index.scss';

const EndingContent = () => {
    const useEndInfoStore = useContextStore(endInfoStoreCreator);
    const endingInfo = useEndInfoStore((state) => state.selectEndingInfo);
    const currentState = useEndInfoStore((state) => state.currentState);
    const isDouble = useEndInfoStore((state) => state.isDouble);

    const dialog = useDialog(ConfirmModalProvider);

    const continueExplore = useCallback(() => {
        dialog?.show({
            title: '确定开始回顾他的故事吗？',
            onConfirm: () => {
                jump2EndingRetrospect({ ending: endingInfo });
            },
        });
    }, [dialog, endingInfo]);

    const onClickSwitch = useCallback(() => {
        const isFromMiddle = useEndInfoStore.getState().lastState === State.middle;
        if (isFromMiddle) {
            if (useEndInfoStore.getState().currentState === State.full_1) {
                useEndInfoStore.getState().updateState(State.full_2);
            } else {
                useEndInfoStore.getState().updateState(State.full_1);
            }
        } else {
            useEndInfoStore.getState().updateState(State.middle);
        }
    }, [useEndInfoStore]);

    const onClickExplore = useCallback(async () => {
        if (!endingInfo) return;
        try {
            const isFriend = await useEndInfoStore.getState().isFriend();
            if (!isFriend) {
                dialog?.show({
                    title: '您已不是该角色的好友，无法回顾该角色的故事',
                    cancelText: '我知道了',
                    confirmText: '加好友',
                    onConfirm: () => {
                        useEndInfoStore.getState().addFriend(() => {
                            continueExplore();
                        });
                    },
                });
                return;
            }
            jump2EndingRetrospect({ ending: endingInfo });
        } catch (err) {
            showToast({
                title: err?.message,
                icon: 'none',
            });
        }
    }, [endingInfo, dialog, useEndInfoStore, continueExplore]);

    const onClickGoChapter = useCallback(async () => {
        if (useEndInfoStore.getState().from === 'chapter') {
            Taro.navigateBack();
            return;
        }
        const robotUserId = useEndInfoStore.getState().userId;
        jump2ChapterInfo({
            chapterId: endingInfo.bindChapterId,
            robotUserId: `${robotUserId}`,
            chapter: null,
            from: 'ending',
        });
    }, [endingInfo.bindChapterId, useEndInfoStore]);

    const show = currentState !== State.middle;

    let endingName = endingInfo.endingName;
    if (endingInfo.cardStatus === CardStatus.LOCKED) {
        if (isDouble) {
            endingName = currentState === State.full_1 ? '结局一' : '结局二';
        } else {
            endingName = '结局';
        }
    }

    return (
        <View
            className={classNames('ending-content-wrapper', {
                show,
                hide: !show,
            })}>
            <View className="bottom-bg" />
            <View className="bottom-content">
                <View className="content-card">
                    {endingInfo.bindChapterType === 1 && (
                        <View className="content-relation-type">
                            <Image src={IconRelationType} className="content-relation-type-image" />
                            <Text className="content-relation-type-text">
                                {bindChapterRelationName(endingInfo.bindChapterRelationType)}
                            </Text>
                        </View>
                    )}

                    <Text
                        className={classNames('ending-name', {
                            lock: endingInfo.cardStatus === CardStatus.LOCKED,
                            'good-logo': isDouble && currentState === State.full_1,
                            'bad-logo': isDouble && currentState === State.full_2,
                            'normal-logo': !isDouble,
                        })}>
                        {endingName}
                    </Text>
                    <Text className="chapter-name">探索剧情《{endingInfo.bindChapterName}》</Text>
                    {isDouble && (
                        <Image
                            className="switch"
                            src={currentState === State.full_1 ? switch1Icon : switch2Icon}
                            mode="aspectFill"
                            onClick={onClickSwitch}
                        />
                    )}
                </View>
                <Text
                    className={classNames('explore', {
                        invisible: endingInfo.cardStatus === CardStatus.LOCKED,
                    })}
                    onClick={withStopPropagation(onClickExplore)}>
                    进入剧情回顾
                </Text>
                <Text className="go-chapter" />
            </View>
        </View>
    );
};

export default React.memo(EndingContent);
