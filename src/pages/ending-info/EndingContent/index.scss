.ending-content-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    align-items: center;

    &.show {
        animation: fadeIn 200ms forwards;
        pointer-events: auto;
    }

    &.hide {
        animation: fadeOut 200ms forwards;
        pointer-events: none;
    }

    .bottom-bg {
        position: fixed;
        bottom: 0;
        width: 100%;
        height: 325px;
        display: flex;
        flex-direction: column;
        background: rgba(34, 34, 34, 0.6);
        filter: blur(30px);
    }

    .bottom-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        align-items: center;
        position: absolute;
        bottom: 0;

        .content-card {
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            flex-direction: column;
            position: relative;

            .content-relation-type {
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
                margin-left: 26px;
                margin-right: 26px;
                height: 20px;
                border-radius: 10px;

                box-shadow: 0 0 4px 0 rgba(255, 175, 203, 1);
                box-shadow: 0 0 4px 0 rgba(255, 128, 174, 1) inset;
                // border: 0.5px solid rgba(255, 255, 255, 1);

                padding: 3px 6px 3px 3px;

                &-image {
                    width: 14px;
                    height: 14px;
                }

                &-text {
                    margin-top: 0;
                    margin-left: 2px;
                    font-family: FZYaSongS-B-GB, sans-serif;
                    font-weight: 400;
                    font-size: 10px;
                    line-height: 170%;
                    letter-spacing: 0;
                    color: rgba(255, 255, 255, 1);
                    background-color: transparent;
                    filter: drop-shadow(0 0 2px rgba(255, 104, 158, 1));
                }
            }

            .ending-name {
                color: #fff;
                font-family: 'SourceHanSerifCN-Bold', sans-serif;
                font-size: 26px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                letter-spacing: 1px;
                margin-top: 5px;
                margin-left: 26px;
                margin-right: 26px;
                padding-bottom: 11px;
                position: relative;

                &.lock {
                    position: relative;

                    &::after {
                        content: '';
                        display: inline-block;
                        position: static;
                        width: 22px;
                        height: 22px;
                        margin-left: 3px;
                        background-image: url('../../../assets/chapter-info/icon-shadow-good.png');
                        background-size: 100% 100%;
                    }
                }

                &.good-logo {
                    &::before {
                        content: '';
                        display: block;
                        position: absolute;
                        left: -10.2px;
                        bottom: 0;
                        width: 187px;
                        height: 30px;
                        background-image: url('../../../assets/chapter-info/icon-shadow-good.png');
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: left center;
                    }
                }

                &.bad-logo {
                    &::before {
                        content: '';
                        display: block;
                        position: absolute;
                        left: -7.2px;
                        bottom: 0;
                        width: 149px;
                        height: 30px;
                        background-image: url('../../../assets/chapter-info/icon-shadow-bad.png');
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: left center;
                    }
                }

                &.normal-logo {
                    &::before {
                        content: '';
                        display: block;
                        position: absolute;
                        left: -7.2px;
                        bottom: 0;
                        width: 120px;
                        height: 21px;
                        background-image: url('../../../assets/chapter-info/icon-shadow-normal.png');
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: left center;
                    }
                }
            }

            .chapter-name {
                margin-left: 31px;
                color: #fff;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 18px;
                margin-top: 5px;
            }

            .switch {
                width: 33px;
                height: 33px;
                position: absolute;
                right: 40px;
                bottom: 0;
            }
        }

        .explore {
            height: 48px;
            width: calc(100% - 56px);
            font-size: 16px;
            font-weight: 600;
            letter-spacing: 0.08em;
            color: #7f5c5b;
            border-radius: 71px;
            background: #f2dcca;
            margin-top: 13px;
            align-items: center;
            text-align: center;
            line-height: 48px;

            &.invisible {
                visibility: hidden;
            }
        }

        .go-chapter {
            color: #fff;
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 100%;
            opacity: 0.4;
            margin-bottom: 65px;
            margin-top: 17px;
            position: relative;

            // &::after {
            //     content: '';
            //     display: inline-block;
            //     width: 10px;
            //     height: 10px;
            //     background: url('../../../assets/chapter-info/icon-next.png') no-repeat
            //         center/contain;
            //     margin-left: 1px;
            // }
        }
    }

    @keyframes fadeIn {
        0% {
            opacity: 0;
        }

        100% {
            opacity: 1;
        }
    }

    @keyframes fadeOut {
        0% {
            opacity: 1;
        }

        100% {
            opacity: 0;
        }
    }
}
