@import 'taro-ui/dist/style/components/float-layout.scss';
@import 'taro-ui/dist/style/components/tabs.scss';

.gift-panel-wrap {
    border-top-left-radius: 20px; /* 左上角圆角半径 */
    border-top-right-radius: 20px; /* 右上角圆角半径 */
    height: 377px;
    max-height: 377px;
    min-height: 377px;
    width: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .at-tabs {
        margin-top: 30px;
        overflow: visible;

        &.show-second-tab-red-dot .at-tabs__header .at-tabs__item:nth-child(2) {
            position: relative;

            &::after {
                content: '';
                position: absolute;
                top: -2px;
                right: -5px;
                width: 5px;
                height: 5px;
                background-color: #ff561e;
                border-radius: 50%;
                z-index: 1;
            }
        }

        .at-tabs__header {
            margin-left: 18px;
            width: 100px;
            padding-top: 1px;
            overflow: visible;

            .at-tabs__item {
                color: rgba(0, 0, 0, 0.4);
                font-size: 15px;
                font-weight: 400;
                line-height: 14px;
                letter-spacing: 0;
                padding: 0 0 4px 0;
                flex: none;
                margin-right: 20px;
            }

            .at-tabs__item--active {
                color: #000;
                opacity: 1;
                font-size: 15px;
                font-weight: 600;
                line-height: 14px;
                letter-spacing: 0;
            }

            .at-tabs__item-underline {
                opacity: 0;
            }
        }

        .at-tabs__body {
            .at-tabs__underline {
                background-color: transparent;
            }

            .gift-panel-scrollview {
                width: 100%;
                padding-top: 9px;
                height: 238px;
            }

            .gift-panel-items-wrap {
                display: flex;
                justify-content: flex-start;
                flex-flow: row wrap;
                gap: 0 7px;
                margin: 0 6px;
            }

            .gift-panel-placeholder {
                display: flex;
                justify-content: center;
                align-items: center;
                font-family: 'PingFang SC', sans-serif;
                font-weight: 400;
                font-size: 14px;
                line-height: 15.4px;
                letter-spacing: 0;
                text-align: center;
                color: #000;
                opacity: 0.4;
                width: 100%;
                height: 237px;
            }
        }
    }

    .gift-panel-recharge-wrap {
        background-color: #f5f5f5;
        height: 26px;
        border-radius: 13px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        position: absolute;
        right: 14px;
        top: 24px;
        min-width: 114px;

        .gift-panel-recharge-icon {
            width: 18px;
            height: 18px;
            margin-left: 5px;
        }

        .gift-panel-recharge-balance {
            font-family: 'PingFang SC', sans-serif;
            font-weight: 600;
            font-size: 14px;
            line-height: 19.6px;
            letter-spacing: 0;
            color: #000;
            margin-left: 5px;
            margin-right: 26px;
        }

        .gift-panel-recharge-add {
            position: absolute;
            right: 5px;
            width: 16px;
            height: 16px;
        }
    }

    .gift-panel-bottom {
        margin: 0 14px 0 18px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        height: 52px;
        width: 100%;
        margin-bottom: env(safe-area-inset-bottom);

        .gift-panel-bottom-btn {
            margin-right: 14px;
            width: 123px;
            height: 38px;
            border-radius: 19px;
            background-color: #ff689e;
            display: flex;
            justify-content: center;
            align-items: center;

            .gift-panel-bottom-btn-text {
                font-family: 'PingFang SC', sans-serif;
                font-weight: 600;
                font-size: 14px;
                line-height: 14px;
                letter-spacing: 0;
                text-align: center;
                color: #fff;
            }
        }

        .gift-panel-bottom-number-select-container {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;

            .gift-panel-bottom-number-select-view {
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: flex-start;

                .gift-panel-bottom-number-select-view-minus-plus {
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    align-items: center;
                    width: 109px;
                    height: 24px;
                    background-color: #8787871A;
                    border-radius: 12px;
                    margin-left: 18px;

                    .gift-panel-bottom-number-select-view-plus {
                        width: 20px;
                        height: 20px;
                    }

                    .gift-panel-bottom-number-select-view-number {
                        color: #000;
                        font-size: 14px;
                        font-weight: 600;
                    }
    
                    .gift-panel-bottom-number-select-view-minus {
                        width: 20px;
                        height: 20px;
                        
                    }
                }

                .gift-panel-bottom-number-select-view-max {
                    width: 42px;
                    height: 22px;
                    margin-left: 8px;
                }
            }

            .gift-panel-bottom-tag {
                margin-left: 23px;
                margin-top: 10px;
                font-family: 'PingFang SC', sans-serif;
                font-weight: 400;
                font-size: 11px;
                line-height: 14px;
                letter-spacing: 0;
                color: #000;
                opacity: 0.4;
    
                .gift-panel-bottom-tag-fav {
                    color: #ff689e;
                    opacity: 1;
                }
            }
        }
    }
}

.custom-modal-balance {
    .modal-content {
        margin-left: 10%;
        margin-top: 250px;
        width: 80%;
        border-radius: 16px;
        padding: 30px 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: #fff;
    }

    .text-title {
        font-size: 18px;
        text-align: center;
        font-weight: 600;
        color: rgba(0, 0, 0, 1);
    }

    .text-content {
        margin-top: 12px;
        font-size: 13px;
        text-align: center;
        color: rgba(0, 0, 0, 0.4);
    }

    .btn-group {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: 30px;
    }

    .cancel,
    .confirm {
        width: 45%;
        height: 44px;
        font-size: 18px;
        font-weight: 600;
        border-radius: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        color: white;
    }

    .cancel {
        background-color: #bfbfbf;
    }

    .confirm {
        background-color: #ff689e;
    }
}
