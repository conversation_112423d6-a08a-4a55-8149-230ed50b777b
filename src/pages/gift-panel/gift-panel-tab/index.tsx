import React, { Component, useCallback, useEffect, useRef } from 'react';
import { Image, ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';

const GiftTabItem = React.memo(({ tabItem, currentTab, setCurrentTab }) => {
    const isSelect = tabItem.panelId === currentTab.panelId;
    const handlePress = () => {
        setCurrentTab(tabItem);
    };

    console.log('GiftTabItem', isSelect);

    return (
        <View className="items-center justify-center ml-9 pl-13 pr-13 h-47" onClick={handlePress}>
            <Text
                className={`text-[${isSelect ? '#FE1531' : '#282828'}] text-[16px] leading-[16px]`}>
                {tabItem.panelName}
            </Text>
            {isSelect && <View className="bg-[#FE1531] w-[24px] h-2 absolute bottom-0" />}
        </View>
    );
});

const GiftTab = React.memo(() => {
    const { tabList, currentTab, setCurrentTab } = usePanelTab();
    console.log(`GiftTab->tabList:${tabList.length}`);

    const scrollViewRef = useRef(null);
    const scrollViewWidthRef = useRef(null);
    const tabItemPositions = useRef([]);
    useEffect(() => {
        const index = tabList.findIndex((tab) => tab.panelId === currentTab.panelId);
        if (index !== -1 && scrollViewRef.current && tabItemPositions.current[index]) {
            const selectedItemPosition = tabItemPositions.current[index];
            const scrollViewWidth = scrollViewWidthRef.current; // 获取滚动视图的宽度
            const selectedItemWidth = selectedItemPosition.width; // 获取选中项的宽度
            const scrollPosition =
                selectedItemPosition.x - scrollViewWidth / 2 + selectedItemWidth / 2; // 计算滚动位置
            console.log(
                'GiftTab-->scrollPosition:',
                scrollPosition,
                scrollViewWidth,
                selectedItemPosition
            );
            scrollViewRef.current.scrollTo({
                x: scrollPosition,
                animated: false,
            });
        }
    }, [currentTab, tabList, tabItemPositions, scrollViewRef, scrollViewWidthRef]);

    return (
        <View className="flex-row h-47 items-center pl-5 pr-5 justify-between w-screen">
            <ScrollView
                ref={scrollViewRef}
                className="flex-row h-47"
                scrollX
                scrollY={false}
                showsHorizontalScrollIndicator={false}
                onLayout={(event) => {
                    scrollViewWidthRef.current = event.nativeEvent.layout.width;
                }}
                contentContainerStyle={{
                    alignItems: 'center',
                }}>
                {tabList.map((tabItem, index) => {
                    return (
                        <View
                            key={tabItem.panelId}
                            onLayout={(event) => {
                                tabItemPositions.current[index] = event.nativeEvent.layout;
                            }}>
                            <GiftTabItem
                                tabItem={tabItem}
                                currentTab={currentTab}
                                setCurrentTab={setCurrentTab}
                            />
                        </View>
                    );
                })}
            </ScrollView>
        </View>
    );
});

export default GiftTab;
