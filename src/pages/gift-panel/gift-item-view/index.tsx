import { Image, Text, View } from '@tarojs/components';
import React from 'react';
import './index.scss';
import { getCoinIcon } from '@/utils/appSourceAdapter';
import { GiftMeta, PackGiftMeta } from '@/pages/chat/store/useGiftResourcesStore';
import { useContextStore } from '@/components/storeContext/StoreContext';
import { redDotStoreCreator } from '@/pages/chat/components/Footer/redDotStore';
import classNames from 'classnames';
import Taro, { getCurrentInstance } from '@tarojs/taro';

export interface GiftItemViewProps {
    item: GiftMeta;
    currentSelectedGift: GiftMeta;
    isPack: boolean;
    onGiftItemClick: (item: GiftMeta) => void;
}

const GiftItemView = ({
    item,
    currentSelectedGift,
    isPack,
    onGiftItemClick,
}: GiftItemViewProps) => {
    const { robotUserId = '' } = getCurrentInstance().router?.params || {};
    const redDotStore = useContextStore(redDotStoreCreator);
    const normalPackageGiftReddotIds = redDotStore((state) => state.packageGiftIds);
    const showNormalPackageGiftReddot = isPack && normalPackageGiftReddotIds?.includes(item.id);
    const robotPackageGiftReddotSet = redDotStore((state) => state.robotPackageRedDotSet);
    const robotPackageGiftReddotIds = robotPackageGiftReddotSet.find(
        (ele) => ele?.robotUserId === robotUserId
    );
    const showRobotPackageGiftReddot =
        isPack && robotPackageGiftReddotIds?.giftIds?.includes(item.id);

    const showNew = showNormalPackageGiftReddot || showRobotPackageGiftReddot;
    return (
        <View
            className={classNames('gift-panel-item-wrap', {
                'gift-panel-item-wrap-red-dot': showNew,
            })}
            style={{
                height: isPack ? Taro.pxTransform(108) : Taro.pxTransform(117),
            }}
            onClick={() => onGiftItemClick(item)}>
            <View
                className="gift-panel-item-border"
                style={{
                    border:
                        currentSelectedGift?.id === item.id
                            ? '1px solid rgba(255, 104, 158, 0.4)'
                            : '1px solid transparent',
                    height: isPack ? Taro.pxTransform(98) : Taro.pxTransform(107),
                }}>
                <Image
                    src={
                        currentSelectedGift?.id === item.id
                            ? item.basicResource?.previewImg?.url
                            : item.basicResource?.thumbnailImg?.url
                    }
                    className="gift-panel-item-icon"
                />
                <Text className="gift-panel-item-name">{`${item.name}`}</Text>
                {!isPack && (
                    <View className="gift-panel-item-worth-wrap">
                        <Image src={getCoinIcon()} className="gift-panel-item-gold" />
                        <Text className="gift-panel-item-worth">{`${item.worth}`}</Text>
                    </View>
                )}
                {isPack && (
                    <View className="gift-panel-item-number-wrap">
                        <Text className="gift-panel-item-number">
                            {(item as PackGiftMeta).number}
                        </Text>
                    </View>
                )}
            </View>
        </View>
    );
};

export default React.memo(GiftItemView);
