import { CreateModalProps, useDialog } from '@/components/dialog';
import { Image, ScrollView, Text, View } from '@tarojs/components';
import Taro, { getCurrentInstance, showLoading, hideLoading, showToast } from '@tarojs/taro';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { AtTabs, AtTabsPane } from 'taro-ui';
import { getCoinIcon, getCoinName } from '@/utils/appSourceAdapter';

import './index.scss';
import Gift_Panel_Recharge_Add from '@/assets/common/gift_panel_recharge_add.png';
import Gift_Panel_Number_Select_Max_Highlight from '@/assets/common/gift_panel_number_select_max_highlight.png';
import Gift_Panel_Number_Select_Max from '@/assets/common/gift_panel_number_select_max.png';
import Gift_Panel_Number_Select_Plus_Highlight from '@/assets/common/gift_panel_number_select_plus_highlight.png';
import Gift_Panel_Number_Select_Minus_Highlight from '@/assets/common/gift_panel_number_select_minus_highlight.png';
import Gift_Panel_Number_Select_Plus from '@/assets/common/gift_panel_number_select_plus.png';
import Gift_Panel_Number_Select_Minus from '@/assets/common/gift_panel_number_select_minus.png';
import { LastMsgStatus, useMessageStore } from '@/hooks/messageStore';
import EventTrackView from '@/components/EventTrack';
import { useContextStore } from '@/components/storeContext/StoreContext';
import { redDotStoreCreator } from '@/pages/chat/components/Footer/redDotStore';
import classNames from 'classnames';
import GiftItemView from '@/pages/gift-panel/gift-item-view';
import { dialogStoreCreator } from '@/components/dialog/dialogStore';
import useGiftStore, { GiftMeta, PackGiftMeta } from '../chat/store/useGiftResourcesStore';
import { gotoRechargePage } from '../market/pay-panel/payPanelStore';
import { useBalanceStore } from '../../store/balanceStore';

const BalanceLackPanel = (props: { dismiss: () => void }) => {
    const dialogStore = useContextStore(dialogStoreCreator);
    const dialogBuilder = dialogStore((state) => state.dialogBuilder);

    const handleCancelClick = () => {
        props.dismiss();
    };

    const onSureClick = () => {
        dialogBuilder.hideAll();
        gotoRechargePage();
    };

    return (
        <View className="custom-modal-balance">
            <View className="modal-content">
                <Text className="text-title">您的{getCoinName()}数量不足，请充值后购买。</Text>
                {/* <Text className="text-content">您的金币数量不足，请充值后购买。</Text> */}
                <View className="btn-group">
                    <View className="cancel" onClick={handleCancelClick}>
                        我知道了
                    </View>
                    <View className="confirm" onClick={onSureClick}>
                        去充值
                    </View>
                </View>
            </View>
        </View>
    );
};

export const BalanceLackModal: CreateModalProps = {
    type: 'modal',
    isModal: true,
    render(dismiss) {
        return <BalanceLackPanel dismiss={dismiss} />;
    },
};

const PACKAGE_INDEX = 1;

const GiftPanel = () => {
    const kBackpackPanelCode = 'k-backpack';
    const { robotUserId = '' } = getCurrentInstance().router?.params || {};
    const balanceLackPanel = useDialog(BalanceLackModal);

    const balance = useBalanceStore((state) => state.balance);
    const giftItems = useGiftStore((state) => state.giftPanelItems);
    const giftPanels = useGiftStore((state) => state.giftPanel);
    const packItems = useGiftStore((state) => state.packbackItems);
    const panelsConfig = (giftPanels || [])
        .map((item) => ({
            ...item,
            title: item.panelName,
        }))
        .concat({ panelCode: kBackpackPanelCode, panelName: '背包', title: '背包' });

    const [currentIndex, setCurrentIndex] = useState(0);
    const [giftNumber, setGiftNumber] = useState(1);
    // 当前选中的礼物（默认是第一个礼物面板的第一个礼物）
    const [currentSelectedGift, setCurrentSelectedGift] = useState<GiftMeta | null>(
        giftItems[panelsConfig[0].panelCode]?.length > 0
            ? giftItems[panelsConfig[0].panelCode][0]
            : null
    );

    const dialogStore = useContextStore(dialogStoreCreator);
    const dialogBuilder = dialogStore((state) => state.dialogBuilder);

    const handleGotoRecharge = () => {
        dialogBuilder.hideAll();
        gotoRechargePage();
    };

    const onSendPack = useCallback(async () => {
        if (!currentSelectedGift) {
            return;
        }

        showLoading();
        const sessionPeriodId = useMessageStore.getState().periodId;
        try {
            const res = await useGiftStore
                .getState()
                .postPackSend(
                    [robotUserId],
                    currentSelectedGift.id,
                    giftNumber,
                    (currentSelectedGift as PackGiftMeta).resourceType || 1,
                    1,
                    sessionPeriodId
                );
            hideLoading();
            dialogBuilder.hideAll();

            useGiftStore.getState().updatePackbackItems(res.resourceId, res.canUseCount);
            setTimeout(() => {
                useMessageStore.getState().setLastMsgStatus(LastMsgStatus.sent);
            }, 800);
        } catch (e) {
            if (e.code === 20038) {
                // 强制实名弹窗不 toast
                hideLoading();
            } else {
                showToast({
                    title: `赠送失败，请稍后再试[${e.code}]`,
                    icon: 'none',
                });
            }
        }
    }, [currentSelectedGift, dialogBuilder, robotUserId, giftNumber]);

    const onSendGift = useCallback(async () => {
        if (!currentSelectedGift) {
            return;
        }

        showLoading();
        const currentPanelCode = giftPanels[currentIndex].panelCode;
        const sessionPeriodId = useMessageStore.getState().periodId;
        try {
            await useGiftStore
                .getState()
                .postGiftSend(
                    currentPanelCode,
                    [robotUserId],
                    currentSelectedGift.id,
                    giftNumber,
                    1,
                    sessionPeriodId
                );
            hideLoading();
            dialogBuilder.hideAll();

            setTimeout(() => {
                useMessageStore.getState().setLastMsgStatus(LastMsgStatus.sent);
            }, 800);
        } catch (e) {
            hideLoading();
            if (e.code === 1101) {
                balanceLackPanel.show();
            } else if (e.code === 20038) {
                // 强制实名弹窗不 toast
            } else {
                showToast({
                    title: `赠送失败，请稍后再试[${e.code}]`,
                    icon: 'none',
                });
            }
        }
    }, [
        dialogBuilder,
        currentSelectedGift,
        balanceLackPanel,
        currentIndex,
        giftPanels,
        robotUserId,
        giftNumber,
    ]);

    const onSendClick = useCallback(async () => {
        if (panelsConfig[currentIndex].panelCode === kBackpackPanelCode) {
            onSendPack();
        } else {
            onSendGift();
        }
    }, [panelsConfig, currentIndex, onSendPack, onSendGift]);

    const handleIndexClick = useCallback(
        (index: number) => {
            setCurrentIndex(index);

            if (panelsConfig[index].panelCode === kBackpackPanelCode) {
                setCurrentSelectedGift(packItems[0]);
            } else if (giftItems[panelsConfig[index].panelCode]?.length > 0) {
                setCurrentSelectedGift(giftItems[panelsConfig[index].panelCode][0]);
            } else {
                setCurrentSelectedGift(null);
            }

            setGiftNumber(1);
        },
        [giftItems, panelsConfig, packItems]
    );

    const onGiftItemClick = (item: GiftMeta) => {
        setCurrentSelectedGift(item);
        setGiftNumber(1);
    };

    const onGiftNumberPlusClick = useCallback(() => {
        if (currentIndex === 1 && giftNumber === (currentSelectedGift as PackGiftMeta)?.number) {
            return;
        }
        setGiftNumber(giftNumber + 1);
    }, [giftNumber, currentIndex, currentSelectedGift]);

    const onGiftNumberMinusClick = useCallback(() => {
        const newGiftNumber = giftNumber - 1;
        if (newGiftNumber < 1) {
            return;
        }
        setGiftNumber(newGiftNumber);
    }, [giftNumber]);

    const onGiftNumberMaxClick = useCallback(() => {
        const currentPack = currentSelectedGift;
        const maxGiftNumber = (currentPack as PackGiftMeta)?.number;
        if (typeof maxGiftNumber === 'number' && maxGiftNumber > 0) {
            setGiftNumber(maxGiftNumber);
        }
    }, [currentSelectedGift]);

    useEffect(() => {
        useGiftStore.getState().fetchBackpack(Number(robotUserId) || 0);
    }, [robotUserId]);

    const placeholdView = () => {
        return (
            <View className="gift-panel-placeholder">
                <Text>当前没有礼物</Text>
            </View>
        );
    };

    const giftsView = (gifts: GiftMeta[], isPack: boolean) => {
        if (!gifts || gifts.length === 0) {
            return placeholdView();
        }
        return (
            <ScrollView className="gift-panel-scrollview" scrollY scrollWithAnimation>
                <View className="gift-panel-items-wrap">
                    {gifts.map((item: GiftMeta = {} as GiftMeta) => {
                        return (
                            <GiftItemView
                                key={`gift-item-key-${item.id}`}
                                item={item}
                                currentSelectedGift={currentSelectedGift}
                                isPack={isPack}
                                onGiftItemClick={onGiftItemClick}
                            />
                        );
                    })}
                </View>
            </ScrollView>
        );
    };

    const packsView = (gifts: GiftMeta[]) => {
        return giftsView(gifts, true);
    };

    useEffect(() => {
        useBalanceStore.getState().requestBalance();
    }, []);

    const redDotStore = useContextStore(redDotStoreCreator);
    const showNormalPackageRedDotOnTab = redDotStore((state) => state.packageRedDot);
    const robotPackageRedDotOnTab = redDotStore((state) => state.robotPackageRedDotOnTab);
    const showRobotPackageRedDotOnTab = robotPackageRedDotOnTab?.includes(robotUserId);
    const packageRedDot = showRobotPackageRedDotOnTab || showNormalPackageRedDotOnTab;

    const needCleanPackageGift = useRef(false);
    useEffect(() => {
        if (currentIndex === PACKAGE_INDEX && packageRedDot) {
            // 切换背包时销毁红点和保存清除记录
            needCleanPackageGift.current = true;
            redDotStore.getState().dismissPackageRedDotOnTab(robotUserId);
        }
        if (currentIndex !== PACKAGE_INDEX && needCleanPackageGift.current) {
            // 切换到非背包时，清除礼物上新数据
            redDotStore.getState().dismissPackageRedDotOnItem(robotUserId);
        }
    }, [currentIndex, redDotStore, packageRedDot, robotUserId]);
    useEffect(() => {
        const cleanPackageGiftRun = () => {
            if (needCleanPackageGift.current) {
                redDotStore.getState().dismissPackageRedDotOnItem(robotUserId);
            }
        };
        Taro.eventCenter.on('GiftPanelModal_ONCLOSE', cleanPackageGiftRun);
        return () => {
            // 页面销毁时清理内存中礼物上新数据
            cleanPackageGiftRun();
            Taro.eventCenter.off('GiftPanelModal_ONCLOSE', cleanPackageGiftRun);
        };
    }, [redDotStore, robotUserId]);

    return (
        <EventTrackView params={{ _spm: 'page_ai_chat_gift|page_h5_biz', botid: robotUserId }}>
            <View className="gift-panel-wrap">
                <View className="gift-panel-recharge-wrap">
                    <Image src={getCoinIcon()} className="gift-panel-recharge-icon" />
                    <Text className="gift-panel-recharge-balance">{`${balance?.balance}`}</Text>
                    <Image
                        src={Gift_Panel_Recharge_Add}
                        className="gift-panel-recharge-add"
                        onClick={handleGotoRecharge}
                    />
                </View>
                <AtTabs
                    customStyle={{
                        height: 'auto',
                    }}
                    className={classNames({
                        'show-second-tab-red-dot': packageRedDot,
                    })}
                    current={currentIndex}
                    scroll
                    tabList={panelsConfig}
                    onClick={handleIndexClick}>
                    {panelsConfig.map((item, index) => {
                        return (
                            <AtTabsPane
                                key={`gift-panel-tab-${item.panelCode}`}
                                current={currentIndex}
                                index={index}>
                                <ScrollView
                                    className="gift-panel-scrollview"
                                    scrollY
                                    scrollWithAnimation>
                                    <View className="gift-panel-items-wrap">
                                        {item.panelCode !== kBackpackPanelCode
                                            ? giftsView(giftItems[item.panelCode], false)
                                            : packsView(packItems)}
                                    </View>
                                </ScrollView>
                            </AtTabsPane>
                        );
                    })}
                </AtTabs>
                <View className="gift-panel-bottom">
                    <View className="gift-panel-bottom-number-select-container">
                        <View className="gift-panel-bottom-number-select-view">
                            <View className="gift-panel-bottom-number-select-view-minus-plus">
                                <Image
                                    src={
                                        giftNumber === 1
                                            ? Gift_Panel_Number_Select_Minus
                                            : Gift_Panel_Number_Select_Minus_Highlight
                                    }
                                    className="gift-panel-bottom-number-select-view-minus"
                                    onClick={onGiftNumberMinusClick}
                                />
                                <Text className="gift-panel-bottom-number-select-view-number">
                                    {giftNumber}
                                </Text>
                                <Image
                                    src={
                                        currentIndex === 1 &&
                                            giftNumber === (currentSelectedGift as PackGiftMeta)?.number
                                            ? Gift_Panel_Number_Select_Plus
                                            : Gift_Panel_Number_Select_Plus_Highlight
                                    }
                                    className="gift-panel-bottom-number-select-view-plus"
                                    onClick={onGiftNumberPlusClick}
                                />
                            </View>
                            {currentIndex === 1 && (
                                <Image
                                    src={
                                        giftNumber === (currentSelectedGift as PackGiftMeta)?.number
                                            ? Gift_Panel_Number_Select_Max
                                            : Gift_Panel_Number_Select_Max_Highlight
                                    }
                                    className="gift-panel-bottom-number-select-view-max"
                                    onClick={onGiftNumberMaxClick}
                                />
                            )}
                        </View>
                        {currentSelectedGift &&
                            Number(currentSelectedGift?.attrsMap?.AI_INTIMACY_VALUE ?? 0) > 0 ? (
                            <Text className="gift-panel-bottom-tag">
                                赠送后可增加
                                <Text className="gift-panel-bottom-tag-fav">{`${Number(currentSelectedGift?.attrsMap?.AI_INTIMACY_VALUE ?? 0) *
                                    giftNumber
                                    }`}</Text>
                                好感度
                            </Text>
                        ) : (
                            <View />
                        )}
                    </View>
                    <View className="gift-panel-bottom-btn" onClick={onSendClick}>
                        <Text className="gift-panel-bottom-btn-text">赠送</Text>
                    </View>
                </View>
            </View>
        </EventTrackView>
    );
};

export const GiftPanelModal: CreateModalProps = {
    type: 'float',
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    props: {
        onClose: () => {
            Taro.eventCenter.trigger('GiftPanelModal_ONCLOSE');
        },
    },
    render(dismiss) {
        return <GiftPanel />;
    },
};

export default GiftPanel;
