.chat.chip {
    top: var(--status-bar-height);
    background: #faf7f8;

    .chat-outter-chipNewMsg {
        .go-to-newMsg {
            display: block;
        }
    }

    .chat-conatiners {
        position: relative;
        z-index: 20;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        width: 100%;
        height: calc(100vh - var(--status-bar-height) - 172px);
        margin-top: calc(88px - var(--safearea-height));
        margin-bottom: calc(84px + var(--safearea-height));

        .chat-container {
            .chat-record {
                .chat-plot {
                    background: #ffe5ee;
                    color: #ff689e;
                }
            }
        }
    }

    .chat-msg-item {
        .container {
            background: #fff;
            font-weight: 600;
            font-size: 13px;
            animation: none;
            max-width: 230px;
            color: #2a2726;

            .message {
                .richtext-msg {
                    &-bold-text {
                        color: #26262a;
                    }
                }
            }
        }

        .chat-msg-content-text {
            color: #26262a;
        }

        .bgAnimation {
            animation: bgColorGradientAnimation1 3s 1;
        }

        @keyframes bgColorGradientAnimation1 {
            0% {
                background: #fff;
            }

            50% {
                background: #ffe5ee;
            }

            100% {
                background: #fff;
            }
        }

        .date {
            color: #000;
            opacity: 0.2;
        }

        &.my {
            .container {
                background: #ff689e;
                color: #fff;

                .message {
                    .richtext-msg {
                        &-bold-text {
                            color: #fff;
                        }
                    }
                }
            }
        }

        .voiceContainer {
            background: rgb(255, 104, 158);
            backdrop-filter: blur(10px);
        }

        .opContainer {
            .popContainer {
                .contentStrContainer {
                    background-color: #fff;

                    .opTextUp,
                    .opTextDown,
                    .opTextCopy {
                        color: #000;
                    }
                }
            }
        }
    }

    .chat-msg-item-loading {
        &.chat-record {
            background: #fff;
        }
    }

    .aigcIntimacyLimitIncr-text {
        color: #26262a80;
    }

    .sceneStartContainer {
        background-color: rgba(183, 183, 197, 0.1);

        .sceneStartText {
            color: rgba(38, 38, 42, 1);
        }
    }

    .sceneEndContainer {
        background-color: rgba(183, 183, 197, 0.1);
    }

    .sceneWrapper {
        border-radius: 20px;
        border-width: 0;
        box-shadow: 0 0 10px rgba(255, 104, 158, 0.19);
        background-color: #fff;
        margin-bottom: 15px;

        .sceneContainer {
            position: relative;
            padding-bottom: 10px;

            .sceneHeader {
                display: block;
                position: relative;
                width: 100%;

                .sceneHeaderImage {
                    position: absolute;
                    top: 0;
                    left: 50%;
                    transform: translate(-50%);
                    object-fit: cover;
                }
            }

            .sceneStart {
                position: absolute;
                bottom: -15px;
                left: 50%;
                width: 135px;
                height: 30px;
                margin-top: 10px;
                transform: translate(-50%);

                .sceneStartImage {
                    width: 130px;
                    height: 35px;
                }
            }

            .sceneContent {
                background-color: #fff;
                padding-bottom: 20px;
            }
        }
    }
}

.bannerifo {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 44px;
    background: #faf7f8;
    z-index: 21;
}

.banner {
    position: absolute;
    left: 0;
    top: 44px;
    width: 100%;
    height: 44px;
    background-image: linear-gradient(90deg, #342328 0%, #5e5055 100%);
    z-index: 21;
    // border-bottom-left-radius: 10px;
    // border-bottom-right-radius: 10px;
}
