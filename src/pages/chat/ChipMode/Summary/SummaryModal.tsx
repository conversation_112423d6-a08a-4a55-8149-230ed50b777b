import React from 'react';
import { showLoading, hideLoading, showToast } from '@tarojs/taro';
import { Text, View } from '@tarojs/components';
import { CreateModalProps } from '@/components/dialog';
import useEpicPlayerStore from '@/store/useEpicPlayerStore';
import useChapterGuideStore from '@/store/useChapterGuideStore';
import { startChapterApi } from '@/service/profileApi';
import { GameChapterStartResult, StartCode } from '@/pages/profile/GameChapterStartResult';

import { jump2Market } from '@/router';
import useDetailStore from '../../store/useDetailStore';

import './index.scss';

const SummaryModalContent = ({ dismiss }: { dismiss: () => void }) => {
    const intro = useDetailStore((state) => state.robotInfo?.robotBaseInfo?.roleDesc);
    const targetRoleId = useChapterGuideStore((state) => state.roleId);
    const targetChapterId = useChapterGuideStore((state) => state.chapterId);

    const showChapterRecord = () => {
        showLoading();
        startChapterApi({ chapterId: targetChapterId, roleId: targetRoleId }).then((res) => {
            hideLoading();
            const result = res as GameChapterStartResult;
            if (result.code === StartCode.SUCCESS) {
                useEpicPlayerStore.getState().showEpicPlayer({
                    roleId: targetRoleId,
                    chapterId: targetChapterId,
                    recordId: result.recordId,
                    backToChat: false,
                });
            } else if (result.code === StartCode.NO_STAMINA) {
                jump2Market({});
            } else {
                showToast({
                    title: result.message || '系统繁忙，请稍后再试',
                    icon: 'none',
                });
            }
        });
    };

    return (
        <View className="summary-modal">
            <Text className="tit intro">简介</Text>
            <Text className="intro">{intro || '还没有自我介绍'}</Text>
            <View className="button active:opacity-50" onClick={dismiss} />
            <View className="close-button" onClick={dismiss} />
            {targetRoleId && targetChapterId && (
                <Text className="record-button" onClick={showChapterRecord}>
                    回忆
                </Text>
            )}
        </View>
    );
};

export const SummaryModal: CreateModalProps = {
    type: 'modal',
    isModal: true,
    render(dismiss) {
        return <SummaryModalContent dismiss={dismiss} />;
    },
};

export default SummaryModal;
