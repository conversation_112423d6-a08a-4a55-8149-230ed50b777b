.chat-summary {
    position: relative;

    .summary-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
    }

    .nickname {
        margin: 8px 0;
        font-weight: 600;
        font-size: 20px;
        color: #000;
        word-break: break-all;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
    }

    .tags-container {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
    }

    .tag {
        height: 19px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 98px;
        background: #f7eaf0;
        margin-right: 4px;
        font-size: 11px;
        color: #c56f92;
        padding: 4px 8px;
        white-space: nowrap;
        margin-bottom: 3px;
    }

    .desc-container {
        margin: 12px auto 16px;
        width: 335px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;
        padding: 12px 18px;
        border-radius: 12px;
        font-size: 12px;

        .content {
            color: #26262a;
            opacity: 0.5;
            white-space: pre-wrap;

            &.chip {
                color: rgba(38, 38, 42, 0.6);
            }

            &.immerse {
                color: rgba(255, 255, 255, 0.6);
            }
        }

        &.isTextOverflow {
            padding-bottom: 12px;
        }

        &.chip {
            background: rgba(183, 183, 197, 0.1);
            color: rgba(38, 38, 42, 0.6);
            height: auto;
        }

        &.immerse {
            border-radius: 12px;
            opacity: 1;
            background: rgba(3, 3, 3, 0.8);
            backdrop-filter: blur(50px);
            @supports (-webkit-touch-callout: none) {
                -webkit-backdrop-filter: none;
            }
            color: rgba(255, 255, 255, 0.8);
            // color: #C56F92;

            taro-text-core {
                /* stylelint-disable-line */
                word-break: break-all;
                text-overflow: ellipsis;
                line-height: 18px;
                overflow: hidden;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                // 指定5行
                -webkit-line-clamp: 5;
                max-height: 90px;
            }
        }

        .arrow {
            // position: absolute;
            // left: 50%;
            // transform: translateX(-50%);
            width: 10px;
            height: 10px;
        }
    }
}

.summary-modal {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 50;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .intro {
        color: #fff;
        opacity: 0.6;
        font-size: 15px;
        padding: 0 19px;
        white-space: pre-wrap;
    }

    .tit {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 600;
    }

    .close-button {
        position: absolute;
        top: 53px;
        right: 20px;
        width: 26px;
        height: 26px;
        background: center / 100% 100% repeat url('../../../../assets/common/close_icon.png');
    }

    .record-button {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 96px;
        width: 88px;
        height: 36px;
        border-radius: 18px;
        background-color: #b4b4b433;
        font-size: 14px;
        font-weight: 500;
        color: #fff;
        line-height: 36px;
        text-align: center;
    }

}
