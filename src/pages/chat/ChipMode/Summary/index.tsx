import React, { useCallback, useRef } from 'react';
import { View, Text } from '@tarojs/components';
import 'taro-ui/dist/style/components/flex.scss';

import Avatar from '@/components/avatar';
import { useDialog } from '@/components/dialog';

import { jump2Profile } from '@/router';
import { SummaryModal } from './SummaryModal';
import useDetailStore from '../../store/useDetailStore';

import './index.scss';

const Summary = () => {
    const robotInfo = useDetailStore((state) => state.robotInfo?.robotBaseInfo);
    const modal = useDialog(SummaryModal);
    const textRef = useRef<HTMLDivElement>(null);

    const showModal = useCallback(() => {
        modal.show();
    }, [modal]);

    const gotoProfile = useCallback(() => {
        const userId = useDetailStore.getState().userId;
        jump2Profile({
            robotUserId: userId,
            hasAiChapter: null,
            fromSource: 'chat',
        });
    }, []);

    return (
        <View className="chat-summary">
            <View className="summary-container">
                <Avatar
                    src={robotInfo?.avatarUrl || ''}
                    width={110}
                    height={110}
                    lazyload={false}
                    onClick={gotoProfile}
                />
                <Text className="nickname" onClick={gotoProfile}>
                    {robotInfo?.nickname || ''}
                </Text>
            </View>
            <View className="tags-container">
                {robotInfo?.labels?.map((robotItem) => (
                    <View className="tag" key={robotItem} onClick={gotoProfile}>
                        {robotItem}
                    </View>
                ))}
            </View>
            <View className="desc-container chip" onClick={showModal}>
                <Text ref={textRef} className="content chip">
                    {robotInfo?.roleDesc || ''}
                </Text>
            </View>
        </View>
    );
};

export default React.memo(Summary);
