import React, { useEffect, useState, memo } from 'react';
import { View } from '@tarojs/components';
import ErrorBoundary from '@/components/ErrorBoundary';
import ChatRecordList from '../components/ChatRecordList';
import ChatRecordListReverse from '../components/ChatRecordList/RecordListReverse';
import useDetailStore from '../store/useDetailStore';
import useIntimacyStore from '../components/Favoirability/store/useIntimacyStore';
import Summary from './Summary';
import './index.scss';
import { isChromeLower } from '../utils';

// 待实现
const ChipMode = () => {
    const chatMode = useDetailStore((state) => state.chatMode);
    const favorability = useIntimacyStore((state) => state.info);
    const [action, setAction] = useState(false);
    const showIntimacy =
        (favorability?.newIntimacyValue ?? 0) > 0 ||
        (favorability !== null && favorability?.friend);

    useEffect(() => {
        if (chatMode === 'chip') {
            // 进入沉浸式模式
            setAction(true);
        } else {
            // 退出沉浸式模式
            setAction(false);
        }
    }, [chatMode]);

    return (
        <ErrorBoundary>
            <View className="chat chip">
                <View className="bannerifo" />
                {showIntimacy && <View className="banner" />}
                <View className="chat-conatiners">
                    {isChromeLower ? (
                        <ChatRecordListReverse SummaryComp={Summary} type="chip" action={action} />
                    ) : (
                        <ChatRecordList SummaryComp={Summary} type="chip" action={action} />
                    )}
                </View>
            </View>
        </ErrorBoundary>
    );
};

export default memo(ChipMode);
