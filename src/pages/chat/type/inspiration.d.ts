export interface InspirationState {
    open: boolean;
    show: boolean;
    data: InspirationStoreData;
    location: number;
    unlockId: number;
    count: number;
    setLocation: (unlockId?: number) => void;
    fetchInspiration: (loc: number, id?: number) => Promise<void>;
    buyInspiration: () => Promise<void>;
    toggleInspiration: () => void;
    toggleInspirationShow: (needShow: boolean) => void;
    closeInspiration: () => void;
    sendText: (text: string) => void;
    setGuideList: (list: InspirationItem[]) => void;
    enterChat: () => void;
}

export interface InspirationItemData {
    id: number;
    list: string[];
    loading: boolean;
    error: boolean;
}

export interface InspirationStoreData {
    lastMsgId: string;
    useCount: number;
    totalCount: number;
    pay: boolean;
    amount: string;
    list: InspirationItemData[];
}

export interface InspirationItem {
    reply: string;
}

export interface InspirationData {
    replyList: string[][];
    totalCount: number;
    pay: boolean;
    amount: string;
    useCount: number;
}

export interface BuyInspirationData {
    unLockId: number;
}
