import React, { useEffect, useState, memo } from 'react';
import { useDidHide } from '@tarojs/taro';
import { View } from '@tarojs/components';
import ErrorBoundary from '@/components/ErrorBoundary';
import SwipeBack from '@/utils/adapter/swipeBack';
import ChatRecordList from '../components/ChatRecordList';
import ChatRecordListReverse from '../components/ChatRecordList/RecordListReverse';
import Summary from './Summary';
import VideoBg from './VideoBg';
import useDetailStore from '../store/useDetailStore';
import { isChromeLower } from '../utils';

import './index.scss';

// 待实现
const ImmerseMode = () => {
    const chatMode = useDetailStore((state) => state.chatMode);
    const [action, setAction] = useState(false);

    useEffect(() => {
        SwipeBack.enabled = chatMode !== 'immerse';

        if (chatMode === 'immerse') {
            // 进入沉浸式模式
            setAction(true);
        } else {
            // 退出沉浸式模式
            setAction(false);
        }
    }, [chatMode]);

    useDidHide(() => {
        SwipeBack.enabled = true;
    });

    return (
        <ErrorBoundary>
            <View className="chat immerse">
                <VideoBg action={action} />
                <View className="chat-immerse-mask-container">
                    <View className="chat-immerse-top-mask" />
                    <View className="chat-immerse-center-mask" />
                    <View className="chat-immerse-bottom-mask" />
                </View>
                <View className="chat-conatiners">
                    {isChromeLower ? (
                        <ChatRecordListReverse
                            SummaryComp={Summary}
                            type="immerse"
                            action={action}
                        />
                    ) : (
                        <ChatRecordList SummaryComp={Summary} type="immerse" action={action} />
                    )}
                </View>
            </View>
        </ErrorBoundary>
    );
};

export default memo(ImmerseMode);
