import useSwiperTouchStore from '@/pages/chat/store/useSwiperTouchStore';
import { optimizeImage } from '@/utils/image';
import { NormalVideo } from '@music/ct-animation';
import { SizeMode } from '@music/ct-animation/lib/core/BaseAni';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { Image, Video, View } from '@tarojs/components';
import Taro, { getCurrentInstance, createVideoContext } from '@tarojs/taro';
import React, { useMemo, useEffect, useState, useCallback } from 'react';

import domAdapter from '@/utils/adapter/domAdapter';
import { useContextStore } from '@/components/storeContext/StoreContext';
import { ImmerseEnterAnimStoreCreator } from '@/pages/chat/store/useImmerseEnterAnim';
import useDetailStore from '../../store/useDetailStore';
import './index.scss';

const VideoBg = ({ action }: { action: boolean }) => {
    // 提前从前置页面拿静态资源
    const { botBgUrl = '', botStandByUrl = '' } = getCurrentInstance().router?.params || {};
    // 人物图片背景
    const [url, setUrl] = useState<string>(decodeURIComponent(botBgUrl));
    // 人物默认视频背景
    const [standbyVideo, setStandbyVideo] = useState<string>(decodeURIComponent(botStandByUrl));
    const [showStandbyVideo, setShowStandbyVideo] = useState(false);
    // 人物亲密度提升动效视频背景
    const [actionVideo, setActionVideo] = useState<string | null>(null);
    const [upActionVideoZIndex, setUpActionVideoZIndex] = useState(false);
    const [upStandbyVideoZIndex, setStandbyVideoZIndex] = useState(false);

    const aigcChatResourceInfo = useDetailStore((state) => state.robotInfo?.aigcChatResourceInfo);

    // 个别机型Taro的video会加载失败，用normalVideo代替
    // 正常情况下taro的video体验更好
    const [backupVideoComp, setBackupVideoComp] = useState(false);

    useEffect(() => {
        if (['undefined', 'null', ''].includes(botBgUrl)) {
            const optimizedUrl = optimizeImage({
                src: aigcChatResourceInfo?.botBackgroundUrl,
                width: 375,
                height: 812,
            });
            setUrl(optimizedUrl);
        }
    }, [aigcChatResourceInfo?.botBackgroundUrl, botBgUrl]);

    useEffect(() => {
        // 好感度提升会刷新/api/mirth/user/aigc/chat/robot/info接口连续请求接口会导致cdn调度变化，
        // 随机返回p5和p6的视频，一旦视频url变化，会重新加载video，导致video看起来卡顿一下
        // 所以这里对 standbyVideo 判断，一旦存在，则不在重新设置视频源
        if (['undefined', 'null', ''].includes(standbyVideo)) {
            const actionUrl = aigcChatResourceInfo?.botStandByUrls?.[0];
            if (!actionUrl) {
                // 如果没有视频，聊天列表直接fadein，不用等视频加载
                Taro.eventCenter.trigger('standby_video_playing');
            } else {
                // const optimizedMp4 = getImage(actionUrl);
                // 不走客户端缓存
                setStandbyVideo(actionUrl);
            }
        }
    }, [aigcChatResourceInfo?.botStandByUrls, standbyVideo]);

    useEffect(() => {
        if (standbyVideo) {
            setShowStandbyVideo(true);
        }
    }, [standbyVideo]);

    const handleIntimacyIncrease = () => {
        try {
            createVideoContext('actionVideo')?.play();
        } catch (error) {
            coronaWarnMessage('actionVideo播放失败', {
                message: `errMsg: ${JSON.stringify(error)}`,
            });
        }
    };

    useEffect(() => {
        const actionUrl = aigcChatResourceInfo?.botActionUrls?.[0];

        if (actionUrl) {
            setActionVideo(actionUrl);
        }

        return () => {
            Taro.eventCenter.off('intimacy_increase_greater_2_event', handleIntimacyIncrease);
        };
    }, [aigcChatResourceInfo?.botActionUrls]);

    // 需要等页面准备好再监听，否则视频可能没有加载完
    const handleIncreaseListening = useCallback(() => {
        Taro.eventCenter.on('intimacy_increase_greater_2_event', handleIntimacyIncrease);
    }, []);

    // 图片和视频显示效果相关
    const { progress }: any = useSwiperTouchStore();
    // 在小程序中opacity属性加在Image组件和Video组件中能直接生效，但是在h5中，需要对组件内部的img标签生效
    // 所以h5中需要用css选择器设置，但是在小程序中可以直接style设置
    // 这里新增一个透明度的状态是为了兼容小程序场景
    const [videoOpacity, setVideoOpacity] = useState(1);

    // 因为Taro的Video组件不能直接设置video标签的opacity渐变效果，所以这里通过这种方式用css选择器设置
    useEffect(() => {
        setVideoOpacity(progress ** 2);
        domAdapter.setCSSProperty('--video-opacity', String(progress ** 2));
    }, [progress]);

    const useImmerseEnterAnimStore = useContextStore(ImmerseEnterAnimStoreCreator);
    const shouldPlay = useImmerseEnterAnimStore((state) => state.shouldPlay);
    const playBgVideo = useImmerseEnterAnimStore((state) => state.playBgVideo);

    const upStandbyVideoZIndexAnimPlay = useMemo(() => {
        // 如果视频已经提升了层级，根据动画状态决定是否播放
        if (upStandbyVideoZIndex) {
            return shouldPlay ? playBgVideo : true;
        }
        return false;
    }, [playBgVideo, shouldPlay, upStandbyVideoZIndex]);

    return (
        <View className="chat-videobg">
            <Image
                src={url}
                onError={(e) => {
                    coronaWarnMessage('videoBg背景图加载失败', {
                        message: `errMsg: ${JSON.stringify(e)} url: ${url}`,
                    });
                }}
                style={{ opacity: videoOpacity }}
                className="chat-bg"
            />

            {showStandbyVideo && (
                <Video
                    id="standbyVideo"
                    src={standbyVideo}
                    style={{ opacity: videoOpacity }}
                    className={`chat-standby-video ${
                        action ? 'chat-standby-video-active' : 'chat-standby-video-inactive'
                    } ${upStandbyVideoZIndexAnimPlay ? 'chat-standby-video-up' : ''}`}
                    muted
                    controls={false}
                    loop
                    autoplay
                    custom-cache={false}
                    showCenterPlayBtn={false}
                    objectFit="cover"
                    onPlay={() => {
                        setStandbyVideoZIndex(true);
                        // 视频显示后才能fadein聊天列表
                        Taro.eventCenter.trigger('standby_video_playing');
                    }}
                    onError={(e) => {
                        // 视频加载失败也显示聊天内容
                        Taro.eventCenter.trigger('standby_video_playing');
                        console.error(
                            `standby视频加载失败：${JSON.stringify(e)} -- url: ${standbyVideo}`
                        );
                        coronaWarnMessage('standby视频加载失败', {
                            message: `errMsg: ${JSON.stringify(e)} url: ${standbyVideo}`,
                        });
                        setShowStandbyVideo(false); // 设置错误状态，不再显示视频
                        setBackupVideoComp(true);
                    }}
                />
            )}

            {/* 动作视频dom节点放前面垫底 - 条件触发以后，层级提升「防止出现抖动」 */}
            {/* 设置progress === 1是为了滑动过程中渐变变化明显，不能有多个视频垫底，否则渐变效果不明显 */}
            {actionVideo && progress === 1 && (
                <Video
                    id="actionVideo"
                    src={actionVideo}
                    className={`chat-action-video ${
                        upActionVideoZIndex ? 'chat-action-video-up' : ''
                    }`}
                    muted
                    controls={false}
                    custom-cache={false}
                    loop={false}
                    objectFit="cover"
                    showCenterPlayBtn={false}
                    onEnded={() => {
                        setUpActionVideoZIndex(false);
                    }}
                    onPlay={() => {
                        setUpActionVideoZIndex(true);
                    }}
                    onLoadedMetaData={handleIncreaseListening}
                    onError={(e) => {
                        console.error(
                            `actionVideo视频加载失败：${JSON.stringify(e)} -- url: ${actionVideo}`
                        );
                        coronaWarnMessage('actionVideo视频加载失败', {
                            message: `errMsg: ${JSON.stringify(e)} url: ${actionVideo}`,
                        });
                        setActionVideo(''); // 设置错误状态，不再显示视频
                    }}
                />
            )}

            {/* 这里兜个底：个别安卓视频会解析失败，切换成ct-animation组件就能播放 */}
            {backupVideoComp && (
                <NormalVideo
                    autoplay
                    className={`chat-standby-video ${
                        action ? 'chat-standby-video-active' : 'chat-standby-video-inactive'
                    } ${upStandbyVideoZIndexAnimPlay ? 'chat-standby-video-up' : ''}`}
                    src={standbyVideo}
                    onPlay={() => {
                        setStandbyVideoZIndex(true);
                    }}
                    key={1}
                    loop
                    x5-playsinline="true"
                    sizeMode={SizeMode.Cover}
                    onError={(e) => {
                        console.error(
                            `backupVideoComp视频加载失败：${JSON.stringify(
                                e
                            )} -- url: ${standbyVideo}`
                        );
                        coronaWarnMessage('backupVideoComp视频加载失败', {
                            message: `errMsg: ${JSON.stringify(e)} url: ${standbyVideo}`,
                        });
                        setBackupVideoComp(false); // 设置错误状态，不再显示视频
                    }}
                />
            )}
        </View>
    );
};

export default React.memo(VideoBg);
