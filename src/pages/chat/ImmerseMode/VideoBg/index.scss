.chat-videobg {
    height: 100%;
    width: 100vw;
    background-color: transparent;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    overflow: hidden;

    /* stylelint-disable-next-line selector-type-no-unknown */
    #standbyVideo .taro-video-video,
    .chat-bg img {
        opacity: var(--video-opacity, 1);
    }

    .taro-video-bar.taro-video-bar-full {
        display: none;
    }

    // 视频和图片内容从顶部开始展示
    .chat-bg img,
    .chat-standby-video,
    .chat-action-video {
        object-position: top center; // 关键属性：保持顶部对齐
        object-fit: cover;
        width: 100vw;
        height: 100vh;
    }

    // 刚开始视频垫在图片下面，用户不可见
    .chat-action-video,
    .chat-standby-video {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 5;
    }

    // 初始状态让图片先显示，防止视频加载慢时出现空白
    .chat-bg {
        position: relative;
        z-index: 6;
    }

    // 无论如何，当动效触发以后，动效视频一定要在最上面
    .chat-action-video-up {
        z-index: 8;
    }

    // 初始状态，让默认视频在图片下层，等播放后再上移
    .chat-standby-video {
        width: 100vw;
        height: 100vh;
        background-color: transparent;
        opacity: 0;

        // 视频播放以后，层级上移
        &.chat-standby-video-up {
            z-index: 7;
        }

        &-active.chat-standby-video-up {
            animation: fadeIn 0.2s ease forwards;
        }

        &-inactive {
            opacity: 0;
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
            }

            to {
                opacity: 0;
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }
    }
}
