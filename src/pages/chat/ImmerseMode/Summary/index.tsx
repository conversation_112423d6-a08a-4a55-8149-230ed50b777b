import React, { useCallback, useEffect, useRef, useState } from 'react';
import { showLoading, showToast, hideLoading } from '@tarojs/taro';
import { ITouchEvent, Text, View } from '@tarojs/components';
import ICON_BOT_WHITE from '@/assets/common/icon_arrow_bot_white.png';
import FullScreenModal from '@/components/FullScreenModal';
import useEpicPlayerStore from '@/store/useEpicPlayerStore';
import useChapterGuideStore from '@/store/useChapterGuideStore';
import { startChapterApi } from '@/service/profileApi';
import { GameChapterStartResult, StartCode } from '@/pages/profile/GameChapterStartResult';
import { jump2Market } from '@/router';
import useDetailStore from '../../store/useDetailStore';

import './index.scss';

const Summary = () => {
    const textRef = useRef<HTMLDivElement>(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [isTextOverflow, setIsTextOverflow] = useState(false);
    const targetRoleId = useChapterGuideStore((state) => state.roleId);
    const targetChapterId = useChapterGuideStore((state) => state.chapterId);
    const showGuideTip = useChapterGuideStore((state) => state.showGuideTip);

    const robotInfo = useDetailStore((state) => state.robotInfo?.robotBaseInfo);
    const { roleDesc = '' } = robotInfo || {};

    useEffect(() => {
        if (textRef.current) {
            const { scrollHeight, offsetHeight } = textRef.current;
            const isOverflow = scrollHeight > offsetHeight;

            setIsTextOverflow(isOverflow);
        }
    }, [robotInfo?.roleDesc]);

    const showModal = useCallback(() => {
        setModalVisible(true);
        useChapterGuideStore.getState().endGuideTip();
    }, []);

    const showChapterRecord = () => {
        showLoading();
        startChapterApi({ chapterId: targetChapterId, roleId: targetRoleId }).then((res) => {
            hideLoading();
            const result = res as GameChapterStartResult;
            if (result.code === StartCode.SUCCESS) {
                useEpicPlayerStore.getState().showEpicPlayer({
                    roleId: targetRoleId,
                    chapterId: targetChapterId,
                    recordId: result.recordId,
                    backToChat: false,
                });
            } else if (result.code === StartCode.NO_STAMINA) {
                jump2Market({});
            } else {
                showToast({
                    title: result.message || '系统繁忙，请稍后再试',
                    icon: 'none',
                });
            }
        });
    };

    return (
        <View className="immerse-summary">
            {showGuideTip && (
                <Text className="chapter-tip" onClick={showModal}>
                    点击回顾剧情
                </Text>
            )}
            <View className="desc-container" onClick={showModal}>
                <Text ref={textRef} className="content">
                    {roleDesc}
                </Text>

                {isTextOverflow && (
                    <View className="icon">
                        <img src={ICON_BOT_WHITE} alt="" />
                    </View>
                )}
            </View>
            <FullScreenModal
                id="immerseSummary"
                onClose={() => setModalVisible(false)}
                visible={modalVisible}>
                <View
                    onClick={(e: ITouchEvent) => {
                        e.stopPropagation();
                    }}>
                    <View className="summary-modal">
                        <Text className="tit intro">简介</Text>
                        <Text className="intro">{roleDesc || '还没有自我介绍'}</Text>
                        <View className="close-button" onClick={() => setModalVisible(false)} />
                        {targetRoleId && targetChapterId && (
                            <Text className="record-button" onClick={showChapterRecord}>
                                回忆
                            </Text>
                        )}
                    </View>
                </View>
            </FullScreenModal>
        </View>
    );
};

export default Summary;
