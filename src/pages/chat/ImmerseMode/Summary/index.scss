.immerse-summary {
    position: relative;
    margin-bottom: 10px;
    padding-top: 64px;

    .chapter-tip {
        transform: translateX(100%);
        height: 42px;
        width: 106px;
        font-size: 12px;
        font-weight: 500;
        line-height: 42;
        color: rgba(255, 255, 255, 0.9);
        border-radius: 21px;
        background: rgba(3, 3, 3, 0.5);
        backdrop-filter: blur(50px);
        @supports (-webkit-touch-callout: none) {
            -webkit-backdrop-filter: none;
        }
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
    }

    .desc-container {
        margin: 12px auto 16px;
        width: 335px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;
        padding: 12px 18px;
        font-size: 12px;
        border-radius: 12px;
        opacity: 1;
        background: rgba(3, 3, 3, 0.8);
        backdrop-filter: blur(50px);
        color: rgba(255, 255, 255, 0.8);

        /* stylelint-disable-next-line selector-type-no-unknown */
        taro-text-core {
            word-break: break-all;
            text-overflow: ellipsis;
            line-height: 18px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            // 指定5行
            -webkit-line-clamp: 5;
            max-height: 90px;
        }

        .content {
            opacity: 0.5;
            color: rgba(255, 255, 255, 0.6);
            white-space: pre-wrap;
        }

        .arrow {
            width: 10px;
            height: 10px;
        }
    }
}

#immerseSummary {
    background-color: #000;
}

.summary-modal {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 50;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .intro {
        color: #fff;
        opacity: 0.6;
        font-size: 15px;
        padding: 0 19px;
        white-space: pre-wrap;
    }

    .tit {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 600;
    }

    .close-button {
        position: absolute;
        top: 53px;
        right: 20px;
        width: 26px;
        height: 26px;
        background: center / 100% 100% repeat url('../../../../assets/common/close_icon.png');
    }

    .record-button {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 96px;
        width: 88px;
        height: 36px;
        border-radius: 18px;
        background-color: #b4b4b433;
        font-size: 14px;
        font-weight: 500;
        color: #fff;
        line-height: 36px;
        text-align: center;
    }
}
