.chat.immerse {
    .chat-outter-immerseNewMsg {
        .go-to-newMsg {
            display: block;
        }
    }

    .chat-conatiners {
        width: 100%;
        height: calc(100% - var(--safearea-height) - var(--status-bar-height) - 88px - 84px);
        z-index: 5;
        overflow: hidden;
        position: relative;
        display: flex;
        flex-direction: column;
        margin-top: calc(88px + var(--status-bar-height));
        padding-bottom: calc(84px + var(--safearea-height));
        box-sizing: content-box;

        .chat-container {
            position: relative;
            height: 100%;
        }
    }

    .chat-record {
        .bgAnimation {
            animation: bgColorGradientAnimation 3s 1;
        }

        .chat-msg-item .msg .cus-avatar {
            display: none;
        }
    }

    @keyframes bgColorGradientAnimation {
        0% {
            background: #272727a0;
        }

        50% {
            background: #46313690;
        }

        100% {
            background: #272727a0;
        }
    }

    .chat-msg-item-loading {
        &.chat-record {
            background: rgba(38, 38, 38, 0.9);
            backdrop-filter: blur(20px);
        }
    }

    .chat-immerse-mask-container {
        position: absolute;
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        overflow: hidden;
        background: transparent;
        pointer-events: none;
        z-index: 1;

        .chat-immerse-top-mask {
            width: 100%;
            opacity: 0.6;
            height: 160px;
            background: linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0) 100%);
        }

        .chat-immerse-center-mask {
            width: 100%;
            height: 130px;
            background: transparent;
        }

        .chat-immerse-bottom-mask {
            width: 100%;
            flex: 1;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 80%);
        }
    }
}
