import React, { useCallback, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
// 导入必要的 Swiper 模块
import ErrorBoundary from '@/components/ErrorBoundary';

import 'swiper/css';

import ChipMode from './ChipMode';
import ImmerseMode from './ImmerseMode';
import useDetailStore from './store/useDetailStore';
import useSwiperTouchStore from './store/useSwiperTouchStore';

const SwiperView = () => {
    const swRef = React.useRef(null);
    const { setProgress, setTouchStart, setTouchMove }: any = useSwiperTouchStore();
    const chatMode = useDetailStore((state) => state.chatMode);
    const currentItemId = chatMode !== 'immerse' ? 0 : 1;
    const toggleChatMode = useDetailStore((state) => state.toggleChatMode);

    useEffect(() => {
        setProgress(chatMode === 'chip' ? 0 : 1);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const onChangeSwiper = useCallback(
        (e) => {
            if (e.activeIndex !== currentItemId) {
                toggleChatMode();
            }
        },
        [currentItemId, toggleChatMode]
    );

    const onProgress = useCallback(
        (e) => {
            setProgress(e.progress);
        },
        [setProgress]
    );

    const onTouchStart = useCallback(() => {
        setProgress(chatMode === 'chip' ? 0 : 1);
        setTouchStart(true);
    }, [chatMode, setProgress, setTouchStart]);

    const onTouchMove = useCallback(() => {
        setTouchMove(true);
    }, [setTouchMove]);

    const onTouchEnd = useCallback(() => {
        setTouchStart(false);
        setTouchMove(false);
    }, [setTouchMove, setTouchStart]);

    useEffect(() => {
        if (swRef.current) {
            swRef.current.swiper.allowSlideNext = true;
            swRef.current.swiper.allowSlidePrev = true;
            swRef.current.swiper.slideTo(currentItemId);
            swRef.current.swiper.allowSlideNext = currentItemId === 0;
            swRef.current.swiper.allowSlidePrev = currentItemId === 1;
        }
    }, [currentItemId]);

    return (
        <ErrorBoundary>
            <Swiper
                resistance={false}
                touchMoveStopPropagation
                // 添加以下参数提高滑动灵敏度
                threshold={5} // 降低滑动阈值，使滑动更容易触发
                touchRatio={2} // 增加触摸比率，使滑动更灵敏
                touchAngle={45} // 增大触摸角度，使斜向滑动也能被识别
                longSwipesRatio={0.1} // 减小长滑动的比率，使短距离滑动也能触发切换
                longSwipesMs={200} // 增加长滑动的时间阈值，使长滑动更容易触发切换
                followFinger // 确保滑块跟随手指移动
                allowSlideNext={currentItemId === 0}
                allowSlidePrev={currentItemId === 1}
                ref={swRef}
                initialSlide={currentItemId}
                className="w-full h-full"
                style={{
                    userSelect: 'none',
                    willChange: 'transform',
                }}
                autoplay={false}
                onSlideChange={onChangeSwiper}
                onProgress={onProgress}
                onTouchStart={onTouchStart}
                onTouchMove={onTouchMove}
                onTouchEnd={onTouchEnd}>
                <SwiperSlide>
                    <ChipMode />
                    {/* 聊天模式的蒙层 */}
                    {/* <View
                        className="absolute top-0 left-0 w-full h-full bg-black transition-opacity"
                        style={{
                            opacity: Math.max(progress * 0.5, 0),
                            zIndex: 10,
                            pointerEvents: 'none',
                        }}
                    /> */}
                </SwiperSlide>
                <SwiperSlide>
                    <ImmerseMode />
                    {/* 沉浸模式的蒙层 */}
                    {/* <View
                        className="absolute top-0 left-0 w-full h-full bg-black transition-opacity"
                        style={{
                            opacity: Math.min((1 - progress) * 0.5, 0.5),
                            zIndex: 10,
                            pointerEvents: 'none',
                        }}
                    /> */}
                </SwiperSlide>
            </Swiper>
        </ErrorBoundary>
    );
};

export default SwiperView;
