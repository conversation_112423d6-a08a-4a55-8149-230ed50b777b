import React, { Fragment, useEffect } from 'react';
import { useContextStore } from '@/components/storeContext/StoreContext';
import useDetailStore from '@/pages/chat/store/useDetailStore';
import { offAppHide, offAppShow, onAppHide, onAppShow, useDidHide, useDidShow } from '@tarojs/taro';
import BGMSettingStore from '@/pages/chat/bgm/BGMSettingStore';
import audioStoreAdapter from '@/utils/adapter/audioStoreAdapter';

function getFinalVolume(volume?: number) {
    const finalVolume = volume !== undefined ? volume / 100 : 0.5;
    return Math.max(0, Math.min(finalVolume, 1));
}

const ChatBGM = () => {
    const useBgmStore = useContextStore(audioStoreAdapter);
    const bgmUrl = useDetailStore((state) => state.robotInfo?.aigcChatResourceInfo?.bgmUrl);
    const bgmVolume = useDetailStore((state) => state.robotInfo?.aigcChatResourceInfo?.bgmVolume);
    const useBgmSettingStore = useContextStore(BGMSettingStore);
    const isAutoPlay = useBgmSettingStore((state) => state.isAutoPlay);

    const finalBGM = isAutoPlay ? bgmUrl : undefined;

    useEffect(() => {
        const musicTimer = setTimeout(() => {
            if (finalBGM) {
                useBgmStore.getState().setVolume(getFinalVolume(bgmVolume));
                useBgmStore.getState().playMusic(finalBGM);
            }
        }, 20);

        return () => {
            clearTimeout(musicTimer);
            useBgmStore.getState().stopMusic();
        };
    }, [finalBGM, useBgmStore, bgmVolume]);

    useDidShow(() => {
        if (!useBgmStore.getState().pauseByUser && isAutoPlay) {
            useBgmStore.getState().resumeMusic();
        }
    });

    useDidHide(() => {
        if (useBgmStore.getState().isPlaying) {
            useBgmStore.getState().pauseMusic(false);
        }
    });

    useEffect(() => {
        const handleAppShow = (result: any) => {
            if (!result.path.includes('chat/index')) {
                return;
            }
            if (useBgmStore.getState().pauseByUser) {
                return;
            }
            if (useBgmSettingStore.getState().isAutoPlay) {
                useBgmStore.getState().resumeMusic();
            }
        };

        const handleAppHide = () => {
            if (useBgmStore.getState().isPlaying) {
                useBgmStore.getState().pauseMusic(false);
            }
        };

        onAppShow(handleAppShow);
        onAppHide(handleAppHide);

        return () => {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            offAppShow(handleAppShow);
            offAppHide(handleAppHide);
        };
    }, [useBgmSettingStore, useBgmStore]);

    return <Fragment />;
};

export default React.memo(ChatBGM);
