import { create } from 'zustand';
import { getStorageSync, showToast, setStorage } from '@tarojs/taro';

interface BGMSettingState {
    isAutoPlay: boolean;
    setIsAutoPlay: (isAutoPlay: boolean) => void;
}

const BGM_SETTING_KEY = 'chat-bgm-setting-auto-play';

const getBGMSetting = (): boolean => {
    try {
        const isAutoPlay = getStorageSync(BGM_SETTING_KEY);
        return isAutoPlay !== false;
    } catch (e) {
        return true;
    }
};

const BGMSettingStore = () => {
    return create<BGMSettingState>((set, get) => ({
        isAutoPlay: getBGMSetting(),
        setIsAutoPlay: (isAutoPlay) => {
            const current = get().isAutoPlay;
            if (current === isAutoPlay) {
                return;
            }

            showToast({
                title: isAutoPlay ? '开启成功' : '关闭成功',
                icon: 'none',
                duration: 2000,
            });
            setStorage({
                key: BGM_SETTING_KEY,
                data: isAutoPlay,
            });
            set({ isAutoPlay });
        },
    }));
};

export default BGMSettingStore;
