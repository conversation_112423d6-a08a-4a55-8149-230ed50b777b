import React, { useEffect, useRef } from 'react';

import Taro, { getCurrentInstance, getStorageSync, setStorageSync, showToast } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import IconScenarioEntrance from '@/assets/chat/scene/ic_chat_scene_start_chip.png';
import IconScenarioEntranceFinish from '@/assets/chat/scene/ic_chat_scene_end_chip.png';
import IconScenarioEntranceImmerse from '@/assets/chat/scene/ic_chat_scene_start_immerse.png';
import IconScenarioEntranceFinishImmerse from '@/assets/chat/scene/ic_chat_scene_end_immerse.png';
import IconScenarioFinishGuide from '@/assets/chat/ic_chat_scenario_finish_guide.png';
import { useDialog } from '@/components/dialog';
import useScenarioStore from '@/pages/chat/store/useScenarioStore';
import { ConfirmModalProvider } from '@/components/confirm-modal';
import { useMessageStore } from '@/hooks/messageStore';
import EventTrackView from '@/components/EventTrack';
import { addClickLog } from '@/utils/logTool';
import { ChatType } from '@/pages/chat/type';
import ScenarioEndAlertDialog from '../EndAlert';
import ScenarioHalfInputDialog from '../HalfScreen';

const ScenarioEntranceView = ({ type }: { type: string }) => {
    const ref = useRef<any>();
    const timeId = useRef<any>();

    const robotUserId = getCurrentInstance().router?.params?.robotUserId;
    const mainModal = useDialog(ScenarioHalfInputDialog);
    const finishModal = useDialog(ScenarioEndAlertDialog);
    const finishSureDialog = useDialog(ConfirmModalProvider);
    const runing = useScenarioStore((state) => state.runing);

    // 是否发送新的消息
    const isNeedRefreshAll = useRef<boolean>(false);

    const handleScenarioStart = () => {
        mainModal.show();
    };

    const handleFinishEvent = async () => {
        // 进行请求。请求完成弹出结束场景弹窗
        try {
            await useScenarioStore.getState().postEndScenario(robotUserId, '0');
            finishModal.show(() => {
                handleScenarioStart();
            });
        } catch (e) {
            showToast({
                title: e.message || '结束场景失败,再试一次',
                icon: 'error',
            });
        }
    };

    const handleFinishSure = () => {
        finishSureDialog.show({
            title: '您要结束当前\n正在进行的场景吗？',
            confirmText: '结束',
            cancelText: '取消',
            onConfirm: () => {
                handleFinishEvent();
            },
        });
        setStorageSync('storage.scenario.finishGuideShowed', true);
    };

    const popupFinishGuide = () => {
        const element = ref.current;
        if (element) {
            // 初始设置透明度为 0
            element.style.opacity = '0';
            // 0.2 秒内渐变显示
            setTimeout(() => {
                element.style.transition = 'opacity 0.2s ease-in-out';
                element.style.opacity = '1';
            }, 0);
            // 5 秒后渐变消失
            const timeoutId = setTimeout(() => {
                element.style.transition = 'opacity 0.2s ease-in-out';
                element.style.opacity = '0';
            }, 5000);
            timeId.current = timeoutId;
        }
    };

    const handleClickEvent = () => {
        addClickLog('btn_ai_im_scene|page_ai_im|page_h5_biz');
        if (runing) {
            // 结束场景
            handleFinishSure();
        } else {
            // 开始场景
            handleScenarioStart();
            // 获取数据
            if (isNeedRefreshAll.current) {
                useScenarioStore.getState().fetchAllScenarioAigcIfNeed(robotUserId, '0', true);
                isNeedRefreshAll.current = false;
            } else {
                useScenarioStore.getState().fetchAllScenarioAigcIfNeed(robotUserId, '0', false);
            }
        }
    };

    useEffect(() => {
        useScenarioStore.getState().fetchScenarioState(robotUserId, '0');
        useScenarioStore.getState().fetchAllScenarioAigcIfNeed(robotUserId, '0', true);
        Taro.eventCenter.on('event.chat.sendMessageSuccess', () => {
            isNeedRefreshAll.current = true;
            const scenarioRuning = useScenarioStore.getState().runing;
            const isShowFinishGuide = getStorageSync('storage.scenario.finishGuideShowed');
            if (scenarioRuning && !isShowFinishGuide) {
                const messageCountWhenStartScene =
                    useScenarioStore.getState().messageCountWhenStartScene;
                const messageCount = useMessageStore.getState().messageList.filter((item) => {
                    return item.isSelf;
                }).length;
                if (messageCount - messageCountWhenStartScene > 5) {
                    setStorageSync('storage.scenario.finishGuideShowed', true);
                    popupFinishGuide();
                }
            }
        });

        return () => {
            Taro.eventCenter.off('event.chat.sendMessageSuccess');
            if (timeId.current) {
                clearInterval(timeId.current);
            }
        };
    }, [robotUserId]);

    function getIcon() {
        if (type === ChatType.CHIP) {
            return !runing ? IconScenarioEntrance : IconScenarioEntranceFinish;
        }
        return !runing ? IconScenarioEntranceImmerse : IconScenarioEntranceFinishImmerse;
    }

    return (
        <EventTrackView params={{ _spm: 'btn_ai_im_scene|page_ai_im|page_h5_biz' }} isPage={false}>
            <View className="bg-yellow-400 flex-1">
                <Image
                    ref={ref}
                    src={IconScenarioFinishGuide}
                    className="fixed w-[169px] h-[63px] right-0 top-[95px] z-10 opacity-0"
                />
                <View
                    className="fixed flex flex-col justify-start items-center pl-[3px] right-0 top-[158px] z-10"
                    onClick={handleClickEvent}>
                    <Image src={getIcon()} className="w-[44px] h-[48px] mt-[12px]" />
                </View>
            </View>
        </EventTrackView>
    );
};

export default ScenarioEntranceView;
