import React, { useCallback } from 'react';
import { Image, Text, View } from '@tarojs/components';
import { CreateModalProps } from '@/components/dialog';
import useScenarioStore from '@/pages/chat/store/useScenarioStore';
import { getCurrentInstance } from '@tarojs/taro';
import IconScenarioClose from '@/assets/chat/ic_chat_scenario_close.png';
import IconScenarioEndMain from '@/assets/chat/ic_chat_scenario_end_main.png';

import './index.scss';
import EventTrackView from '@/components/EventTrack';
import { addClickLog } from '@/utils/logTool';

const ScenarioEndAlertView = (props: { dismiss: () => void; reopen: () => void }) => {
    const onCloseEvent = useCallback(() => {
        props.dismiss();
    }, [props]);

    const onStartChatEvent = useCallback(() => {
        addClickLog('btn_ai_im_newscene_start|mod_ai_im_scene_end_pop|page_ai_im|page_h5_biz');
        const robotUserId = getCurrentInstance().router?.params?.robotUserId;
        useScenarioStore.getState().fetchAllScenarioAigcIfNeed(robotUserId, '0', false);
        props.dismiss();
        if (props.reopen) {
            props.reopen();
        }
    }, [props]);

    return (
        <EventTrackView
            params={{ _spm: 'mod_ai_im_scene_end_pop|page_ai_im|page_h5_biz' }}
            isPage={false}>
            <View className="scenario-end-alert-warp">
                <View className="scenario-end-alert-bg">
                    <View className="scenario-end-alert-contnet">
                        <Image
                            className="w-[229px] h-[103px] mt-[40px]"
                            src={IconScenarioEndMain}
                        />
                        <Text className="text-[18px] text-[#000000] font-[600] mt-[20px] ">
                            场景记忆已存储
                        </Text>
                        <View
                            className="w-[90%] h-[54px] bg-[#FF689E] mt-[40px] rounded-[27px] flex justify-center items-center"
                            onClick={onStartChatEvent}>
                            <Text className="text-[18px] text-[#FFFFFF] font-[600]">
                                开启新场景
                            </Text>
                        </View>
                    </View>
                    <Image
                        className="w-[26px] h-[26px] right-[20px] top-[20px] absolute"
                        src={IconScenarioClose}
                        onClick={onCloseEvent}
                    />
                </View>
            </View>
        </EventTrackView>
    );
};

const ScenarioEndAlertDialog: CreateModalProps = {
    type: 'float',
    render(dismiss: () => void, reopen: () => void) {
        console.info('fqfqfq ScenarioEndAlertDialog', dismiss, reopen);
        return <ScenarioEndAlertView dismiss={dismiss} reopen={reopen} />;
    },
};

export default ScenarioEndAlertDialog;
