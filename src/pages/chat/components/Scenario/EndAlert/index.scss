.scenario-end-alert-warp {
    border-top-left-radius: 20px; /* 左上角圆角半径 */
    border-top-right-radius: 20px; /* 右上角圆角半径 */
    height: 299px;
    @supports (padding-bottom: env(safe-area-inset-bottom)) {
        height: calc(299px + env(safe-area-inset-bottom));
    }
    width: 100%;
    background-color: #fff;

    .scenario-end-alert-bg {
        width: 100%;
        height: 100%;
        background: linear-gradient(180deg, #ffe8f1 100%, #ffe8f1 0%);

        .scenario-end-alert-contnet {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
        }
    }
}
