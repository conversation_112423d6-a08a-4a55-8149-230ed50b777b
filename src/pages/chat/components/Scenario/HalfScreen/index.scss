@import '~taro-ui/dist/style/components/textarea.scss';

.scenario-half-input-warp {
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    // height: 397px;

    // @supports (padding-bottom: env(safe-area-inset-bottom)) {
    //     height: calc(397px + env(safe-area-inset-bottom));
    // }

    width: 100%;
    background-color: #FFF4F8;

    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: -180px;
}

.scenario-half-input-custom-card {
    width: 324px;
    height: 240px;
    margin-top: 22px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    border-radius: 20px;
    box-shadow: 0px 0px 10px 0px #F7DAE4;
    background-color: white;
    // padding: 16px;
}

.scenario-half-input-safe-area-inset-bottom {
    height: 190px;

    @supports (height: env(safe-area-inset-bottom)) {
        height: calc(190px + env(safe-area-inset-bottom));
    }

    width: 100%;
    background-color: transparent;
}

@keyframes scenario-half-input-skeleton-loading {
    0% {
        background-position: 200% 0;
    }

    100% {
        background-position: -200% 0;
    }
}

// 添加骨骼效果
.scenario-half-input-skeleton {
    &-item {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: scenario-half-input-skeleton-loading 1.5s infinite;
        border-radius: 4px;
        width: 100%;
        height: 20px;
        margin-top: 5px;
    }
}

.scenario-half-input-swiper-wrap {
    height: 180px;
    width: 274px;

    .scenario-half-input-swiper {
        height: 180px;
        width: 274px;

        .swiper-pagination {
            bottom: 16px;

            .swiper-pagination-bullet {
                width: 4px;
                height: 4px;
                margin: 2px;
            }
        }
    }
}

.scenario-half-input-textarea {
    height: 131px;
    width: 274px;
    position: absolute;
    top: 0px;
    left: 0px;

    .taro-textarea {
        font-size: 14px;
        font-weight: 400;
        line-height: 170%;
        color: #000;
        caret-color: transparent;
    }
}

.scenario-half-input-textarea-show {
    height: 131px;
    width: 274px;
    position: absolute;
    top: 0px;
    left: 0px;

    .taro-textarea {
        font-size: 14px;
        font-weight: 400;
        line-height: 170%;
        color: #000;
        caret-color: red;
    }
}

// 新增的样式
.scenario-half-input-header-container {
    width: 100%;
    height: 67px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding-top: 22px;
    padding-left: 30px;
    padding-right: 25px;
}

.scenario-half-input-header-left {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
}

.scenario-half-input-header-title {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
}

.scenario-half-input-header-icon {
    width: 22px;
    height: 22px;
}

.scenario-half-input-header-text {
    font-size: 18px;
    color: #000000;
    font-weight: 600;
    margin-left: 4px;
}

.scenario-half-input-header-subtitle {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.4);
    font-weight: 400;
    margin-right: 4px;
}

.scenario-half-input-close-icon {
    width: 22px;
    height: 22px;
}

.scenario-half-input-tab-container {
    width: 324px;
    height: 42px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

.scenario-half-input-tab-item {
    width: 140px;
    height: 42px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    &-aigc {
        background-color: white;
        border-top-left-radius: 20px;
    }

    &-diy {
        background-color: #FFDEEA;
        border-top-left-radius: 20px;
    }

    &-aigc-active {
        background-color: #FFDEEA;
        border-top-right-radius: 20px;
    }

    &-diy-active {
        background-color: white;
        border-top-right-radius: 20px;
    }
}

.scenario-half-input-tab-icon {
    width: 18px;
    height: 18px;
    margin-top: 2px;
    margin-left: 18px;
    margin-right: 2px;
}

.scenario-half-input-tab-text {
    font-size: 15px;
    font-weight: 500;

    &-active {
        color: #FF689E;
    }

    &-inactive {
        color: #DC8FAB;
    }
}

.scenario-half-input-tab-divider {
    width: 45px;
    height: 42px;
}

.scenario-half-input-content-container {
    width: 324px;
    height: 198px;
    background-color: white;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    padding: 16px 25px;
}

.scenario-half-input-input-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    position: relative;
}

.scenario-half-input-placeholder-container {
    width: 274px;
    height: 131px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    // position: absolute;
    // top: 0px;
    // left: 0px;
    z-index: 50;
}

.scenario-half-input-placeholder-text {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.2);
    font-weight: 400;
}

.scenario-half-input-placeholder-example {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.2);
    font-weight: 400;
    margin-top: 4px;
    margin-left: 4px;
}

.scenario-half-input-gradient-overlay {
    width: 274px;
    height: 10px;

    @supports (background-image: linear-gradient(to top, rgba(255, 255, 255, 1), rgba(255, 255, 255,0))) {
        background-image: linear-gradient(to top, rgba(255, 255, 255, 1), rgba(255, 255, 255,0));
    }
    // background-color: #000;
    position: relative;
    bottom: 10px;

}

.scenario-half-input-counter-container {
    width: 100%;
    height: 15px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    margin-top: 2px;
}

.scenario-half-input-counter-text {
    font-size: 11px;
    font-weight: 400;

    &-normal {
        color: rgba(0, 0, 0, 0.2);
    }

    &-error {
        color: #FF561E;
    }
}

.scenario-half-input-suggestion-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 0 33px;
    margin-top: 10px;
}

.scenario-half-input-suggestion-text {
    font-size: 11px;
    font-weight: 400;
    color: #FF561E;

    &-bold {
        font-weight: 600;
    }
}

.scenario-half-input-start-button {
    width: 324px;
    height: 54px;
    margin: 16px 26px;
    border-radius: 27px;
    display: flex;
    justify-content: center;
    align-items: center;

    &-active {
        background-color: #FF689E;
    }

    &-inactive {
        background-color: rgba(255, 104, 158, 0.25);
    }
}

.scenario-half-input-start-button-text {
    font-size: 18px;
    color: #FFFFFF;
    font-weight: 600;
}

.scenario-half-input-aigc-content {
    width: 274px;
    height: 144px;
    font-size: 14px;
    color: #000000;
    font-weight: 400;
    overflow: auto;
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;

    /* Firefox */
    &::-webkit-scrollbar {
        display: none;
        /* Chrome, Safari and Opera */
    }
    position: relative;
}

.scenario-half-input-aigc-content-text {

}