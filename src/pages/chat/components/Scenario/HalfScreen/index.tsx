import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Image, Swiper, SwiperItem, Text, Textarea, View } from '@tarojs/components';
import { CreateModalProps, useDialog } from '@/components/dialog';
import IconScenarioIput from '@/assets/chat/ic_chat_scenario_input.png';
import IconScenarioClose from '@/assets/chat/ic_chat_scenario_close.png';
import IconScenarioAigcSelect from '@/assets/chat/ic_chat_scenario_tab_aigc_select.png';
import IconScenarioAigcUnSelect from '@/assets/chat/ic_chat_scenario_tab_aigc_unselect.png';
import IconScenarioAiHightlight from '@/assets/chat/ic_chat_scenario_tab_ai_highlight.png';
import IconScenarioAiNormal from '@/assets/chat/ic_chat_scenario_tab_ai_normal.png';
import IconScenarioAiReload from '@/assets/chat/ic_chat_scenario_ai_reload.png';
import Taro, {
    getCurrentInstance,
    getStorageSync,
    showLoading,
    setStorageSync,
    showToast,
    hideLoading,
    removeStorageSync,
} from '@tarojs/taro';
import './index.scss';
import useScenarioStore, {
    ScenarioRecommendFetchStatus,
} from '@/pages/chat/store/useScenarioStore';
import EventTrackView from '@/components/EventTrack';
import { addClickLog } from '@/utils/logTool';
import KeyboardWrapper from '@music/mat-keyboard-h5';

const ScenarioHalfInputView = (props: { dismiss: () => void }) => {
    const robotUserId = getCurrentInstance().router?.params?.robotUserId;
    const childrenRef = useRef<any>(null);
    const swRef = useRef<any>(null);
    const inputRef = useRef<any>(null);

    const scenarioRecommendResult = useScenarioStore((state) => state.scenarioRecommendResult);
    const [isTabAigc, setIsTabAigc] = useState<boolean>(false);
    const [currentAigcIndex, setCurrentAigcIndex] = useState<number>(0);
    const [input, setInput] = useState<string>(
        getStorageSync(`storage.scenario.diy.input_${robotUserId}` || '')
    );
    const [isInputing, setIsInputing] = useState(false);
    const [cursorShow, setCursorShow] = useState(false);
    const [suggestionList, setSuggestionList] = useState<string[]>([]);
    // 自行输入后失败的特殊处理
    const [isDIYEnableWhenFail, setIsDIYEnableWhenFail] = useState(true);

    const isStartEnable = useCallback(() => {
        if (isTabAigc && scenarioRecommendResult instanceof Array) {
            let item = '';
            if (currentAigcIndex < scenarioRecommendResult.length) {
                item = scenarioRecommendResult[currentAigcIndex];
            }
            const content = typeof item === 'string' ? item : '';
            return content.length > 0;
        }
        return isDIYEnableWhenFail && input.length > 0 && input.length <= 500;
    }, [isDIYEnableWhenFail, isTabAigc, currentAigcIndex, input.length, scenarioRecommendResult]);

    const onTabSelectEvent = useCallback((aigcSelect: boolean) => {
        setIsTabAigc(aigcSelect);
        if (aigcSelect) {
            addClickLog('btn_ai_im_scene_aigc|mod_ai_im_scene_pop|page_ai_im|page_h5_biz');
        }
    }, []);

    const onCloseEvent = useCallback(() => {
        props.dismiss();
    }, [props]);

    const onStartChatEvent = useCallback(async () => {
        if (!isStartEnable()) {
            return;
        }
        addClickLog('btn_ai_im_scene_start|mod_ai_im_scene_pop|page_ai_im|page_h5_biz');
        let content = '';
        if (isTabAigc && scenarioRecommendResult instanceof Array) {
            if (currentAigcIndex < scenarioRecommendResult.length) {
                content = scenarioRecommendResult[currentAigcIndex];
            }
            content = typeof content === 'string' ? content : '';
        } else {
            content = input?.trim();
            if (content.length < 5) {
                showToast({
                    title: '最少输入5字哦',
                    icon: 'error',
                });
                return;
            }
        }

        showLoading();
        try {
            const res = await useScenarioStore
                .getState()
                .postStartScenario(robotUserId, '0', content, isTabAigc);
            const errorMessage = res.message;

            if (Array.isArray(errorMessage) && errorMessage.length > 0) {
                hideLoading();
                setIsDIYEnableWhenFail(false);
                setSuggestionList(errorMessage);
            } else {
                if (isTabAigc) {
                    // 场景开始后，清空对应的数据。
                    useScenarioStore.setState({
                        scenarioRecommendResult: ScenarioRecommendFetchStatus.idle,
                    });
                } else {
                    removeStorageSync(`storage.scenario.diy.input_${robotUserId}`);
                    setInput('');
                }
                props.dismiss();
                showToast({
                    title: '场景已开始',
                });
            }
        } catch (e) {
            showToast({
                title: e.message || '场景开始失败',
                icon: 'error',
            });
        }
    }, [
        isTabAigc,
        currentAigcIndex,
        input,
        props,
        robotUserId,
        isStartEnable,
        scenarioRecommendResult,
    ]);

    const onChangeSwiper = useCallback((e) => {
        setCurrentAigcIndex(e.detail.current);
    }, []);

    const onInputBlur = () => {
        setIsInputing(false);
        setCursorShow(false);
    };

    const onInputFocus = () => {
        setIsInputing(true);

        setTimeout(() => {
            setCursorShow(true);
        }, 300);
        setTimeout(() => {
            setCursorShow(true);
        }, 600);
    };

    const onInputChange = (e: any) => {
        const val = e.detail?.value;
        setInput(val);
        setIsDIYEnableWhenFail(true);
    };

    const reloadScenarioAigc = () => {
        useScenarioStore.getState().fetchAllScenarioAigcIfNeed(robotUserId, '0', true);
    };

    const renderInput = () => {
        const node = (
            <View className="scenario-half-input-input-container">
                <Textarea
                    ref={inputRef}
                    value={input}
                    className={
                        cursorShow
                            ? 'scenario-half-input-textarea-show'
                            : 'scenario-half-input-textarea'
                    }
                    maxlength={-1}
                    onInput={onInputChange}
                    onBlur={onInputBlur}
                    onFocus={onInputFocus}
                />
                <View
                    className="scenario-half-input-placeholder-container"
                    style={{
                        // display: input.length > 0 || isInputing ? 'none' : 'flex',
                        visibility: input.length > 0 || isInputing ? 'hidden' : 'visible',
                        pointerEvents: 'none',
                    }}>
                    <View className="scenario-half-input-placeholder-text">
                        请输入一段场景描述，最少输入5字，越丰富的场景回复内容越细致哦~
                    </View>
                    <View className="scenario-half-input-placeholder-example">场景示例：</View>
                    <View className="scenario-half-input-placeholder-example">
                        {` • 一起在北海道看雪`}
                    </View>
                    <View className="scenario-half-input-placeholder-example">
                        {` • 暗恋自己的人送来午饭`}
                    </View>
                    <View className="scenario-half-input-placeholder-example">
                        {` • 被坏人绑架走`}
                    </View>
                </View>
                <View className="scenario-half-input-gradient-overlay" />
                <View className="scenario-half-input-counter-container">
                    <Text
                        className={
                            input.length > 500
                                ? 'scenario-half-input-counter-text scenario-half-input-counter-text-error'
                                : 'scenario-half-input-counter-text scenario-half-input-counter-text-normal'
                        }>
                        {input.length}
                    </Text>
                    <Text className="scenario-half-input-counter-text scenario-half-input-counter-text-normal">
                        /500
                    </Text>
                </View>
            </View>
        );
        return node;
    };

    const SkeletonItem = () => (
        <View className="scenario-half-input-skeleton">
            <View className="scenario-half-input-skeleton-item" />
            <View className="scenario-half-input-skeleton-item" />
            <View className="scenario-half-input-skeleton-item" />
            <View className="scenario-half-input-skeleton-item" />
            <View className="scenario-half-input-skeleton-item" />
            <View className="scenario-half-input-skeleton-item" />
        </View>
    );

    const renderAigcItem = (items: string[] | ScenarioRecommendFetchStatus) => {
        let node = null;
        if (items instanceof Array) {
            node = (
                <Swiper
                    ref={swRef}
                    className="scenario-half-input-swiper"
                    current={currentAigcIndex}
                    onChange={onChangeSwiper}
                    indicatorDots
                    indicatorColor="#00000034"
                    indicatorActiveColor="#00000080">
                    {items.map((item) => {
                        return (
                            <SwiperItem>
                                <View className="scenario-half-input-aigc-content">{item}</View>
                                <View className="scenario-half-input-gradient-overlay" />
                            </SwiperItem>
                        );
                    })}
                </Swiper>
            );
        } else if (items === ScenarioRecommendFetchStatus.error) {
            node = (
                <View
                    className="w-[274px] h-[144px] flex flex-col justify-center items-center"
                    onClick={() => {
                        reloadScenarioAigc();
                    }}>
                    <Text className="text-[14px] text-[#26262A60] font-[400] mb-[12px]">
                        加载失败，点击重试
                    </Text>
                    <Image src={IconScenarioAiReload} className="w-[62px] h-[38px]" />
                </View>
            );
        } else if (items === ScenarioRecommendFetchStatus.loading) {
            node = (
                <View className="w-[274px] h-[144px] text-[14px] text-black font-[400]">
                    {SkeletonItem()}
                </View>
            );
        }

        return node;
    };

    const renderAigc = () => {
        const node = (
            <View className="scenario-half-input-swiper-wrap">
                {renderAigcItem(scenarioRecommendResult)}
            </View>
        );
        return node;
    };

    useEffect(() => {
        return () => {
            setStorageSync(`storage.scenario.diy.input_${robotUserId}`, input);
        };
    }, [input, robotUserId]);

    return (
        <KeyboardWrapper action="cover" containers={[]} childrenRef={childrenRef}>
            <EventTrackView
                params={{ _spm: 'mod_ai_im_scene_pop|page_ai_im|page_h5_biz' }}
                isPage={false}>
                <View ref={childrenRef} className="scenario-half-input-warp">
                    <View className="scenario-half-input-header-container">
                        <View className="scenario-half-input-header-left">
                            <View className="scenario-half-input-header-title">
                                <Image
                                    src={IconScenarioIput}
                                    className="scenario-half-input-header-icon"
                                />
                                <Text className="scenario-half-input-header-text">输入场景</Text>
                            </View>
                            <Text className="scenario-half-input-header-subtitle">
                                设定场景，角色将基于场景进行回复～
                            </Text>
                        </View>
                        <Image
                            src={IconScenarioClose}
                            className="scenario-half-input-close-icon"
                            onClick={onCloseEvent}
                        />
                    </View>
                    <View className="scenario-half-input-custom-card">
                        <View className="scenario-half-input-tab-container">
                            <View
                                className={`scenario-half-input-tab-item ${
                                    isTabAigc
                                        ? 'scenario-half-input-tab-item-aigc'
                                        : 'scenario-half-input-tab-item-diy'
                                }`}
                                onClick={() => onTabSelectEvent(true)}>
                                <Image
                                    src={
                                        isTabAigc ? IconScenarioAiHightlight : IconScenarioAiNormal
                                    }
                                    className="scenario-half-input-tab-icon"
                                />
                                <Text
                                    className={`scenario-half-input-tab-text ${
                                        isTabAigc
                                            ? 'scenario-half-input-tab-text-active'
                                            : 'scenario-half-input-tab-text-inactive'
                                    }`}>
                                    生成
                                </Text>
                            </View>
                            <Image
                                src={isTabAigc ? IconScenarioAigcSelect : IconScenarioAigcUnSelect}
                                className="scenario-half-input-tab-divider"
                            />
                            <View
                                className={`scenario-half-input-tab-item ${
                                    isTabAigc
                                        ? 'scenario-half-input-tab-item-aigc-active'
                                        : 'scenario-half-input-tab-item-diy-active'
                                }`}
                                onClick={() => onTabSelectEvent(false)}>
                                <Text
                                    className={`scenario-half-input-tab-text ${
                                        isTabAigc
                                            ? 'scenario-half-input-tab-text-inactive'
                                            : 'scenario-half-input-tab-text-active'
                                    }`}>
                                    自己撰写
                                </Text>
                            </View>
                        </View>
                        <View className="scenario-half-input-content-container">
                            {!isTabAigc ? renderInput() : renderAigc()}
                        </View>
                    </View>

                    {!isTabAigc && suggestionList.length > 0 && (
                        <View className="scenario-half-input-suggestion-container">
                            <Text className="scenario-half-input-suggestion-text">
                                提交失败，以下内容需要调整哦：
                            </Text>
                            {suggestionList.map((item) => (
                                <Text className="scenario-half-input-suggestion-text scenario-half-input-suggestion-text-bold">{` • ${item}`}</Text>
                            ))}
                        </View>
                    )}
                    <View
                        className={`scenario-half-input-start-button ${
                            isStartEnable()
                                ? 'scenario-half-input-start-button-active'
                                : 'scenario-half-input-start-button-inactive'
                        }`}
                        onClick={onStartChatEvent}>
                        <Text className="scenario-half-input-start-button-text">开启对话</Text>
                    </View>
                    <View className="scenario-half-input-safe-area-inset-bottom" />
                </View>
            </EventTrackView>
        </KeyboardWrapper>
    );
};

const ScenarioHalfInputDialog: CreateModalProps = {
    type: 'no_scroll_float',
    render(dismiss) {
        return <ScenarioHalfInputView dismiss={dismiss} />;
    },
};

export default ScenarioHalfInputDialog;
