import React from 'react';
import { View, Image, Text } from '@tarojs/components';
import NEW_MSG_ARROW from '@/assets/chat/new_msg_arrow.png';

import './index.scss';

function NewMsgButton({ scrollToBottom }: { scrollToBottom: (...args: any) => void }) {
    return (
        <View
            className="go-to-newMsg"
            onClick={() => {
                scrollToBottom();
            }}>
            <Text>最新消息</Text>
            <Image className="new-msg-arrow" src={NEW_MSG_ARROW} />
        </View>
    );
}

export default NewMsgButton;
