import React, { useState, useEffect, useRef, useCallback } from 'react';
import Taro, { createAnimation } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';

import { Lottie } from '@music/ct-animation';
import { BaseAniMethod } from '@music/ct-animation/lib/core/BaseAni';

import upgradeBgImage from '@/assets/chat/upgrade_alert_background.png';
import upgradeBgImageMask from '@/assets/chat/upgrade_alert_background_mask.png';
import icon_guide_upgrade_arrow from '@/assets/chat/guide/icon_guide_upgrade_arrow.png';

import { ChapterStorylineDetailDto } from '../ChatPlot/MainPlot/const';
import './index.scss';

export interface IScoreQueueItemContent {
    targetUserId: string;
    curIntimacyLevelLabel: string;
    nickName: string;
    timestamp: number;
    guideRoleInfo?: {
        avatarUrl: string;
        content: string;
    };
    storylineDetailDto?: ChapterStorylineDetailDto;
    lastResourceUrl: string;
    curResourceUrl: string;
}

interface UpgradeAlertProps {
    data: IScoreQueueItemContent;
    close: () => void;
}

const LottieEffectSrc =
    'https://d1.music.126.net/dmusic/obj/w5nDkMKQwrLDjDzCm8Om/58313505392/238f/************/data.json';

const GuideAniamtaion = ({ content, avatarUrl }: { content: string; avatarUrl: string }) => (
    <View className="upgradeGuideRootContainer">
        <View style={{ position: 'relative', width: '100%', height: '100%' }}>
            <View className="upgradeGuideimageConatainer">
                <Image src={icon_guide_upgrade_arrow} className="upgradeGuideimageArrow" />
                <View className="upgradeGuideimage" />
            </View>
            <View className="upgradeGuidecontent">{content}</View>
            <View className="upgradeGuideavatarBgRoot">
                <View style={{ position: 'relative' }}>
                    <View className="upgradeGuideavatarBg" />
                    <Image src={avatarUrl} className="upgradeGuideavatar" />
                </View>
            </View>
        </View>
    </View>
);

const UpgradeAlert = ({ data, close }: UpgradeAlertProps) => {
    const [animationData, setAnimationData] = useState({});
    const lottieRef = useRef<BaseAniMethod>(null);
    const isGuide = data.guideRoleInfo !== undefined;
    const avatarUrl = data.guideRoleInfo?.avatarUrl || '';
    const content = data.guideRoleInfo?.content || '';
    const [isReady, setIsReady] = useState(false);
    const animation = createAnimation({
        duration: 500,
        timingFunction: 'ease',
    });

    const handleReady = useCallback(() => {
        setIsReady(true);
    }, []);

    const handleClick = () => {
        if (isGuide) {
            close();
            setAnimationData(animation.export());
            /// 通知升级动画结束
            Taro.eventCenter.trigger('guideAnimationCompleted');
        }
    };

    useEffect(() => {
        if (isReady && lottieRef.current) {
            lottieRef.current.setImgUrl('relationfrom', data.lastResourceUrl);
            lottieRef.current.setImgUrl('relationto', data.curResourceUrl);
        }
    }, [data, isReady]);

    useEffect(() => {
        animation.opacity(0).step({ duration: 500 });
        if (!isGuide) {
            setTimeout(() => {
                setAnimationData(animation.export());
            }, 2500);
            setTimeout(() => {
                close();
            }, 3000);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <View>
            <View
                className="upgrade-animation-container"
                style={{
                    backgroundColor: isGuide ? 'rgba(0, 0, 0, 0.7)' : 'transparent',
                    pointerEvents: isGuide ? 'all' : 'none',
                }}
                animation={animationData}
                onClick={handleClick}>
                <View className="animation-content-root">
                    <View className="animation-content">
                        <Image className="background-image" src={upgradeBgImage} mode="aspectFit" />
                        <Image
                            className="background-image-mask"
                            src={upgradeBgImageMask}
                            mode="aspectFit"
                        />
                        <View className="images-container">
                            <Lottie
                                ref={lottieRef}
                                src={LottieEffectSrc}
                                loop
                                autoplay
                                onReady={handleReady}
                            />
                        </View>
                        {isGuide && <GuideAniamtaion content={content} avatarUrl={avatarUrl} />}
                    </View>
                </View>
            </View>
        </View>
    );
};

export default UpgradeAlert;
