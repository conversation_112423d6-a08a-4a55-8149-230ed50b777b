@keyframes scaleUp {
    0% {
        transform: scale(0.1);
    }

    100% {
        transform: scale(1);
    }
}

.upgrade-animation-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 110;

    .animation-content-root {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        animation: scaleUp 0.5s ease forwards;
        width: 100%;
        height: 100%;
    }

    .animation-content {
        width: 100%;
        min-height: 190px;
        aspect-ratio: 1.187;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
    }

    .background-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .background-image-mask {
        position: absolute;
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .images-container {
        position: absolute;
        top: 120px;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 5px;
        width: 550px;
        height: 60px;
        padding: 0 100px;
        z-index: 1;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .relation-image {
        width: 60px;
        height: 60px;
        object-fit: contain;
    }
}

.upgradeGuideRootContainer {
    width: 241px;
    height: 102px;
    position: absolute;
    top: 185px;

    .upgradeGuideimageConatainer {
        width: 100%;
        height: 100%;
        position: relative;
        margin-top: 12px;
        display: flex;

        .upgradeGuideimageArrow {
            width: 20px;
            left: 50%;
            position: absolute;
            transform: translate(-50%);
            height: 9px;
        }

        .upgradeGuideimage {
            width: 100%;
            background-color: white;
            border-radius: 12px;
            height: 81px;
            margin-top: 9px;
        }
    }

    .upgradeGuidecontent {
        position: absolute;
        width: 100%;
        display: flex;
        justify-content: center;
        font-size: 14px;
        color: black;
        top: 45px;
    }

    .upgradeGuideavatarBgRoot {
        position: absolute;
        width: 42px;
        left: 18px;
        top: -12px;
        height: 42px;

        .upgradeGuideavatarBg {
            width: 42px;
            height: 42px;
            background-color: white;
            position: absolute;
            border-radius: 50%;
        }

        .upgradeGuideavatar {
            position: absolute;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-left: 1px;
            margin-top: 1px;
        }
    }
}
