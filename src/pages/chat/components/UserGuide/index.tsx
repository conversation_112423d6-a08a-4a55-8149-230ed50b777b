import React, { useEffect } from 'react';
import { Image, View } from '@tarojs/components';
import ErrorBoundary from '@/components/ErrorBoundary';
import './index.scss';
import { useContextStore } from '@/components/storeContext/StoreContext';
import likeOrNotGuide from '@/assets/chat/ic_chat_op_guide_immerse.png';
import { ChatGuideMode, GuideTipStoreCreator } from '../../store/useGuideTipStore';
import useOpStore from '../ChatRecordList/MsgItem/op/useOpStore';

// 引导播放组件
const UserGuideLayer: React.FC = () => {
    const guideTipStore = useContextStore(GuideTipStoreCreator);
    const guideMode = guideTipStore((state) => state.guideMode);
    const guideRect = useOpStore((state) => state.showGuideRect);

    useEffect(() => {
        if (guideMode === ChatGuideMode.Vote && guideRect) {
            // 然后回重新刷新展示引导
            // 最后3秒后关闭引导
            setTimeout(() => {
                useOpStore.getState().closeOp();
                guideTipStore.getState().closeGuideTip();
            }, 3000);
        }
    }, [guideMode, guideRect, guideTipStore]);

    return guideMode === ChatGuideMode.Vote && guideRect ? (
        <ErrorBoundary>
            <View className="w-full h-full absolute left-0 top-0 z-[98]">
                <Image
                    src={likeOrNotGuide}
                    style={{
                        left: guideRect.x,
                        top: guideRect.y - guideRect.height - 5,
                        width: guideRect.width,
                        height: guideRect.height,
                        position: 'absolute',
                    }}
                />
            </View>
        </ErrorBoundary>
    ) : null;
};

export default React.memo(UserGuideLayer);
