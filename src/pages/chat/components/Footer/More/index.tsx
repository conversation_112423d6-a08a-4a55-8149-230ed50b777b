/* eslint-disable react/no-array-index-key */
import React from 'react';
import classNames from 'classnames';
import { getCurrentInstance, showLoading } from '@tarojs/taro';
import { View, Text, Image } from '@tarojs/components';

import ICON_PHOTO from '@/assets/chat/Input/icon_chat_input_more_photo.png';
import ICON_RESTART from '@/assets/chat/Input/icon_chat_input_more_restart.png';
import ICON_GIFT from '@/assets/chat/Input/icon_chat_input_more_gift.png';
import ICON_PHOTO_WHITE from '@/assets/chat/Input/icon_chat_input_more_photo_white.png';
import ICON_RESTART_WHITE from '@/assets/chat/Input/icon_chat_input_more_restart_white.png';
import ICON_GIFT_WHITE from '@/assets/chat/Input/icon_chat_input_more_gift_white.png';

import { ChatType } from '@/pages/chat/type';
import { GiftPanelModal } from '@/pages/gift-panel';
import { addClickLog } from '@/utils/logTool';
import { useDialog } from '@/components/dialog';
import ErrorBoundary from '@/components/ErrorBoundary';
import { useContextStore } from '@/components/storeContext/StoreContext';

import { redDotStoreCreator } from '../redDotStore';
import { RefreshSessionModal } from '../refresh/RefreshSessionModal';
import { InputMoreItemType, InputMoreItemModel } from './type';

import './index.scss';

const InputMorePanel = ({
    type,
    selectImageAction,
}: {
    type: string;
    selectImageAction: () => void;
}) => {
    const refreshModal = useDialog(RefreshSessionModal);
    const giftPanel = useDialog(GiftPanelModal);
    const { robotUserId = '' } = getCurrentInstance().router?.params || {};

    const redDotStore = useContextStore(redDotStoreCreator);
    const showNormalPackageRedDotOnTab = redDotStore((state) => state.packageRedDot);
    const robotPackageRedDotOnTab = redDotStore((state) => state.robotPackageRedDotOnTab);
    const showRobotPackageRedDotOnTab = robotPackageRedDotOnTab?.includes(robotUserId);
    const packageRedDot = showRobotPackageRedDotOnTab || showNormalPackageRedDotOnTab;

    const moreItems: InputMoreItemModel[] = [
        {
            type: InputMoreItemType.Photo,
            title: '照片',
            icon: type === ChatType.CHIP ? ICON_PHOTO_WHITE : ICON_PHOTO,
        },
        {
            type: InputMoreItemType.gift,
            title: '礼物',
            icon: type === ChatType.CHIP ? ICON_GIFT_WHITE : ICON_GIFT,
            redDot: packageRedDot,
        },
        {
            type: InputMoreItemType.restart,
            title: '重启会话',
            icon: type === ChatType.CHIP ? ICON_RESTART_WHITE : ICON_RESTART,
        },
    ];

    const clickItemAction = (itemType: InputMoreItemType) => {
        let btnType = '';
        switch (itemType) {
            case InputMoreItemType.Photo:
                selectImageAction();
                showLoading();
                btnType = 'photo';
                break;
            case InputMoreItemType.restart:
                refreshModal?.show();
                btnType = 'restart';
                break;
            case InputMoreItemType.gift:
                giftPanel.show();
                // balancePanel.show();
                btnType = 'gift';
                break;
            default:
                break;
        }

        addClickLog('btn_ai_im_more_function|mod_ai_im_more|page_ai_im|page_h5_biz', {
            btn_type: btnType,
        });
    };

    return (
        <ErrorBoundary>
            <View
                className={classNames('chat-more-panel', {
                    [type]: true,
                })}>
                {moreItems.map((item, index) => (
                    <View
                        key={index}
                        className={classNames('more-item', {
                            'more-item-red-dot': item.redDot,
                        })}
                        onClick={() => clickItemAction(item.type)}>
                        <View className="icon-wrapper">
                            <Image className="icon-size" src={item.icon} />
                        </View>
                        <Text className="text-wrapper">{item.title}</Text>
                    </View>
                ))}
            </View>
        </ErrorBoundary>
    );
};

export default React.memo(InputMorePanel);
