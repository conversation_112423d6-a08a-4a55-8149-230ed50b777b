/* eslint-disable no-console */
import { StoreCreator, StoreLifecycle } from '@/components/storeContext/StoreContext';
import { create } from 'zustand';
import { SystemMessage } from '@/types/im';
import { getUserInfo } from '@/utils/rpc';

import useUserInfoStore from '@/store/useUserInfoStore';
import Taro, { getStorageSync, setStorageSync } from '@tarojs/taro';

// R卡背包的红点信息，R卡在背包中，不同的角色展示不同的背包。所以红点就会有角色和礼物信息
export interface RobotPackageRedDot {
    robotUserId: string;
    giftIds: number[];
}

export interface RedDotState extends StoreLifecycle {
    packageRedDot: boolean;
    packageGiftIds: number[];
    robotPackageRedDotOnTab: string[];
    robotPackageRedDotSet: RobotPackageRedDot[];
    dismissPackageRedDotOnTab: (robotUserId: string) => void;
    dismissPackageRedDotOnItem: (robotUserId: string) => void;
    onPackageGiftIdsNotify: (giftIds: number[], robotUserId?: string) => void;
}

const listeners = new Set<RedDotState>();

const getUserId = (): Promise<string> => {
    return new Promise((resolve) => {
        getUserInfo(
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            (result: { uid: string }) => {
                resolve(result.uid);
            },
            () => {
                resolve('');
            }
        );
    });
};

const saveGiftIds = async (giftIds: number[]) => {
    const userId = await getUserId();
    setStorageSync(`package_dot_${userId}`, giftIds);
};

const getGiftIds = async (): Promise<number[]> => {
    const userId = await getUserId();
    return getStorageSync(`package_dot_${userId}`) || [];
};

const saveRobotRedDotInfo = async (reddotInfo: RobotPackageRedDot[]) => {
    const userId = useUserInfoStore.getState().userBaseInfo?.userBase?.userId;
    const reddotInfoStr = JSON.stringify(reddotInfo);
    setStorageSync(`storage.gift.rebot_package_dot_${userId}`, reddotInfoStr);
};

const getRobotReddotInfo = async (): Promise<RobotPackageRedDot[]> => {
    const userId = useUserInfoStore.getState().userBaseInfo?.userBase?.userId;
    const reddotInfoStr = getStorageSync(`storage.gift.rebot_package_dot_${userId}`);
    return reddotInfoStr ? JSON.parse(reddotInfoStr) : [];
};

export const onPackageRedNotify = (message: SystemMessage) => {
    const content = message.contentExt?.serverExt;
    const giftIds = content?.content?.giftIds || [];
    const robotUserId = content?.content?.robotUserId;

    listeners.forEach((listener) => {
        listener.onPackageGiftIdsNotify(giftIds, robotUserId);
    });

    const saveRebotRedDotFunc = async () => {
        const originReddotInfo = await getRobotReddotInfo();
        let include = false;
        for (let i = 0; i < originReddotInfo.length; i++) {
            if (originReddotInfo[i]?.robotUserId === `${robotUserId}`) {
                const originGiftIds = originReddotInfo[i]?.giftIds;
                const combineGifts = originGiftIds.concat(
                    giftIds.filter((item: number) => !originGiftIds.includes(item))
                );
                originReddotInfo[i].giftIds = combineGifts;
                include = true;
                break;
            }
        }
        if (!include) {
            originReddotInfo.push({
                robotUserId: `${robotUserId}`,
                giftIds,
            });
        }

        saveRobotRedDotInfo(originReddotInfo);
    };

    const saveNormalRedDotFunc = async () => {
        const originGiftIds = await getGiftIds();
        const combineGifts = originGiftIds.concat(
            giftIds.filter((item: number) => !originGiftIds.includes(item))
        );
        saveGiftIds(combineGifts);
    };
    setTimeout(() => {
        if (robotUserId) {
            saveRebotRedDotFunc();
        } else {
            saveNormalRedDotFunc();
        }
    }, 100);
};

export const redDotStoreCreator: StoreCreator<RedDotState> = () => {
    return create<RedDotState>((set, get) => ({
        packageRedDot: false,
        packageGiftIds: [],
        robotPackageRedDotOnTab: [],
        robotPackageRedDotSet: [],
        dismissPackageRedDotOnTab: (robotUserId: string) => {
            set({ packageRedDot: false });
            saveGiftIds([]);

            const robotReddotInfoOnTab = get().robotPackageRedDotOnTab.filter(
                (item) => item !== robotUserId
            );
            set({ robotPackageRedDotOnTab: robotReddotInfoOnTab });
            const robotReddotInfo = get().robotPackageRedDotSet.filter(
                (item) => item.robotUserId !== robotUserId
            );
            saveRobotRedDotInfo(robotReddotInfo);
        },
        dismissPackageRedDotOnItem: (robotUserId: string) => {
            const robotReddotInfo = get().robotPackageRedDotSet.filter(
                (item) => item?.robotUserId !== robotUserId
            );
            set({ robotPackageRedDotSet: robotReddotInfo });
            set({ packageGiftIds: [] });
        },
        onPackageGiftIdsNotify: (giftIds, robotUserId?: string) => {
            if (robotUserId) {
                const originReddotInfo = get().robotPackageRedDotSet;
                let include = false;
                for (let i = 0; i < originReddotInfo.length; i++) {
                    if (originReddotInfo[i]?.robotUserId === `${robotUserId}`) {
                        const originGiftIds = originReddotInfo[i]?.giftIds;
                        const combineGifts = originGiftIds.concat(
                            giftIds.filter((item) => !originGiftIds.includes(item))
                        );
                        originReddotInfo[i].giftIds = combineGifts;
                        include = true;
                        break;
                    }
                }
                if (!include) {
                    originReddotInfo.push({
                        robotUserId: `${robotUserId}`,
                        giftIds,
                    });
                }

                set({ robotPackageRedDotSet: originReddotInfo });

                const robotReddotInfoOnTab = get().robotPackageRedDotOnTab;
                if (!robotReddotInfoOnTab.includes(robotUserId)) {
                    robotReddotInfoOnTab.push(robotUserId);
                }

                set({ robotPackageRedDotOnTab: robotReddotInfoOnTab });
            } else {
                set({ packageGiftIds: giftIds, packageRedDot: giftIds.length > 0 });
            }
        },
        onCreated: () => {
            listeners.add(get());
            getGiftIds()
                .then((giftIds) => {
                    get().onPackageGiftIdsNotify(giftIds);
                })
                .catch((e) => {
                    console.error(e);
                });

            getRobotReddotInfo()
                .then((reddotInfo) => {
                    set({ robotPackageRedDotSet: reddotInfo });
                    set({ robotPackageRedDotOnTab: reddotInfo.map((item) => item?.robotUserId) });
                })
                .catch((e) => {
                    console.error(e);
                });
        },
        onCleared: () => {
            listeners.delete(get());
        },
    }));
};
