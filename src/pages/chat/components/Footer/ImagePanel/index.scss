.input-image-panel-selected-image {
    position: absolute;
    left: 20px;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    box-sizing: border-box;
    border: 0.5px solid rgba(0, 0, 0, 0.1);
    background: rgb(0, 0, 0, 0.1);
    overflow: hidden; /* 让子视图按照父视图的圆角进行裁剪 */

    img {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        border: 0.5px solid rgba(18, 17, 17, 0.2);
        background: rgba(250, 247, 248, 1);
        object-fit: cover;
    }
}

.loading-container {
    position: absolute;
    left: 20px;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: rgba(109, 109, 109, 0.2);

    .loading-image {
        position: absolute;
        left: 12px;
        top: 12px;
        width: 16px;
        height: 16px;
    }
}

.remove-container {
    position: absolute;
    left: 34px;
    width: 26px;
    height: 26px;
    background: clear;

    .remove-button {
        position: absolute;
        width: 12px;
        height: 12px;
        left: 14px;
        background: clear;
    }
}
