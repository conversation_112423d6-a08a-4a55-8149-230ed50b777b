import React from 'react';
import Taro from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import ICON_CLOSE from '@/assets/chat/Input/chat_input_image_close.png';
import LOADING from '@/assets/chat/Input/loading.webp';

import { jump2ImgPreview } from '@/router';
import './index.scss';

const ImagePanel = ({
    image,
    loading,
    imageWidth,
    imageHeight,
    deleteAction,
}: {
    image: string;
    loading: boolean;
    imageWidth: number;
    imageHeight: number;
    deleteAction: () => void;
}) => {
    const previewImage = () => {
        Taro.nextTick(() => {
            jump2ImgPreview({ url: image, width: imageWidth, height: imageHeight });
        });
    };

    return (
        <View>
            <Image
                className="input-image-panel-selected-image"
                src={image}
                mode="aspectFill"
                onClick={previewImage}
            />
            {loading ? (
                <View className="loading-container">
                    {' '}
                    <Image className="loading-image" src={LOADING} webp mode="aspectFit" />{' '}
                </View>
            ) : null}
            <View
                className="remove-container"
                onClick={() => {
                    deleteAction();
                }}>
                <Image className="remove-button" src={ICON_CLOSE} webp />
            </View>
        </View>
    );
};

export default React.memo(ImagePanel);
