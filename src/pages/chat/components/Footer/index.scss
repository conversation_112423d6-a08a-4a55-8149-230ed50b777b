.chat-input-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: transparent;

    &.immerse-footer-enter-anim {
        opacity: 0;
        transform: translate(0, 30px);

        &.active {
            opacity: 1;
            transform: translate(0, 0);
            transition: opacity 400ms, transform 400ms;
        }
    }

    .input-portal {
        transition: height 200ms;
        width: 100%;
        height: 50px;
        overflow: hidden;

        .input-text-panel {
            width: 100%;
            min-height: 48px;
            display: flex;
            align-items: center;
            flex-shrink: 0;
            position: relative; // 确保相对定位
            transition: height 200ms; // 添加过渡效
            flex-direction: row;

            .input-panel-container {
                transition: background-color 200ms;
                margin-left: 21px;
                height: 48px;
                width: calc(100% - 21px - 66px);
                background-color: rgb(109, 109, 109, 0.5);
                border-radius: 24px;
                display: flex;
                align-items: center;
                padding-left: 0;
                gap: 3px;
                backdrop-filter: blur(10px);

                // 为按钮创建一个容器
                .input-panel-buttons {
                    display: flex;
                    align-items: center;
                }

                .input-panel-tip {
                    margin-left: 18px;
                    font-size: 14px;
                    font-weight: 400;
                    color: #ff689e;
                }

                .input-panel-text-div {
                    flex: 1;
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 14px;
                    background: transparent;
                    color: white;
                    margin-right: 18px;
                    margin-left: 18px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis !important;
                    // background-color: #000;
                }

                .input-panel-text-div.show-image {
                    margin-left: 0px;
                }

                .input-panel-text {
                    overflow-y: auto;
                    line-height: 20px;
                    width: 100%;
                    overscroll-behavior: contain;
                    resize: none;

                    .taro-textarea {
                        background: transparent;
                        overscroll-behavior: contain;
                    }
                }

                .input-panel-text::placeholder {
                    color: rgb(0, 0, 0, 0.2);
                }

                .input-panel-inspiration {
                    right: 11px;
                    width: 28px;
                    height: 28px;
                }

                .input-panel-parentheses {
                    display: none;
                    width: 20px;
                    height: 20px;
                }
            }

            .input-panel-more {
                margin-left: 10px;
                width: 36px;
                height: 36px;
                flex-shrink: 0;

                &.input-panel-more-red-dot {
                    &::after {
                        content: '';
                        position: absolute;
                        top: 0;
                        right: 0;
                        width: 5px;
                        height: 5px;
                        flex-shrink: 0;
                        border-radius: 5px;
                        background: #ff561e;
                    }
                }
            }
        }

        &.more-mode {
            height: 166px;
        }

        &.send-mode {
            height: auto;

            .input-text-panel {
                height: auto;

                .input-panel-container {
                    height: auto;
                    flex-direction: column;
                    align-items: flex-start;
                    border-radius: 8px;

                    .input-panel-text-div {
                        margin-top: 10px;
                        width: calc(100% - 36px);
                    }

                    .input-panel-buttons {
                        width: 100%;
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 10px;

                        .input-panel-parentheses {
                            display: flex;
                            order: 1; // 左边
                            margin-bottom: -10px;
                            margin-left: 18px;
                        }

                        .input-panel-inspiration {
                            order: 2; // 右边
                        }
                    }
                }
            }

            .input-panel-more {
                bottom: 6px;
            }
        }
    }

    .input-image-panel {
        width: 100%;
        height: 45px;
        // background-color: bisque;
        flex-shrink: 0;
        // margin-top: 2px;
    }

    .input-more-panel {
        width: 100%;
        height: 85px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 10px;
        flex-shrink: 0;
        transition: height 200ms;
    }

    .input-aigc {
        width: 100%;
        height: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 2px;

        .input-aigc-text {
            font-size: 8px;
            font-weight: 400;
            color: rgb(255, 255, 255, 0.3);
            display: flex;
        }
    }

    .input-safe-container {
        width: 100%;
        min-height: constant(safe-area-inset-bottom);
        min-height: env(safe-area-inset-bottom);
    }

    &.chip {
        .input-panel-container {
            background-color: rgb(229, 229, 229, 1);

            .input-panel-text {
                .taro-textarea {
                    color: rgba(38, 38, 42, 1);
                }
            }

            .input-panel-text::placeholder {
                color: rgb(38, 38, 42, 0.2);
            }
        }

        .input-aigc-text {
            color: rgb(0, 0, 0, 0.2);
            // color: rgb(0, 0, 0, 1);
        }

        .input-safe-container {
            background: #faf7f9;
            // background-color: #000;
        }
    }

    &.more {
        .input-aigc {
            // background-color: coral;
            // margin-top: 19px;
        }
    }

    .taro-textarea {
        color: #fff;
        line-height: 20px;
        overflow-x: hidden;
        resize: none;
        font-size: 14px;
    }
}
