/* eslint-disable react/prop-types */
import React, { useCallback } from 'react';
import { AtModal } from 'taro-ui';
import useScenarioStore from '@/pages/chat/store/useScenarioStore';
import { getCurrentInstance } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { CreateModalProps } from '@/components/dialog';
import useDetailStore from '../../../store/useDetailStore';
import './index.scss';

const RefreshSessionModalContent = ({ isOpened, dismiss, onClose }: any) => {
    const confirm = useCallback(async () => {
        await useDetailStore.getState().refreshSession();
        const robotUserId = getCurrentInstance().router?.params?.robotUserId;
        useScenarioStore.getState().cleanCurrentScenarioRecommendContent();
        useScenarioStore.getState().fetchScenarioState(robotUserId, '0');
        useScenarioStore.getState().fetchAllScenarioAigcIfNeed(robotUserId, '0', true);
        dismiss();
    }, [dismiss]);
    return (
        <AtModal
            isOpened={isOpened}
            className="custom-modal"
            onClose={onClose}
            closeOnClickOverlay={false}>
            <View className="modal-content">
                <Text className="text-title">是否要重启该会话？</Text>
                <Text className="text-content">
                    重启后，你和对方的聊天记录将被清空，对方对你的记忆将被清除，但好感度依然保留
                </Text>
                <View className="btn-group">
                    <View className="confirm" onClick={confirm}>
                        确定
                    </View>
                    <View className="cancel" onClick={dismiss}>
                        取消
                    </View>
                </View>
            </View>
        </AtModal>
    );
};

export const RefreshSessionModal: CreateModalProps = {
    type: 'modal_custom',
    isModal: true,
    render(dismiss) {
        return (
            <RefreshSessionModalContent
                dismiss={dismiss}
                isOpened={undefined}
                onClose={undefined}
            />
        );
    },
};

export default RefreshSessionModal;
