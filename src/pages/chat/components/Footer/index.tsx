import ICON_INSPIRATION_SHOW from '@/assets/chat//Input/icon_chat_input_inspiration_show.png';
import ICON_INSPIRATION_SHOW_WHITE from '@/assets/chat//Input/icon_chat_input_inspiration_show_white.png';
import ICON_INSPIRATION from '@/assets/chat/Input/chat_input_inspiration.png';
import ICON_INSPIRATION_WHITE from '@/assets/chat/Input/chat_input_inspiration_white.png';
import ICON_MORE from '@/assets/chat/Input/chat_input_more.png';
import ICON_MORE_WHITE from '@/assets/chat/Input/chat_input_more_white.png';
import ICON_MORE_CLOSE from '@/assets/chat/Input/icon_chat_input_more_close.png';
import ICON_MORE_CLOSE_WHITE from '@/assets/chat/Input/icon_chat_input_more_close_white.png';
import ICON_MORE_SEND from '@/assets/chat/Input/icon_chat_input_more_send.png';
import ICON_PARENTHESES from '@/assets/chat/Input/icon_chat_input_parentheses.png';
import ICON_PARENTHESES_WHITE from '@/assets/chat/Input/icon_chat_input_parentheses_white.png';
import ErrorBoundary from '@/components/ErrorBoundary';
import EventTrackView from '@/components/EventTrack';
import { useContextStore } from '@/components/storeContext/StoreContext';
import { LastMsgStatus, useMessageStore } from '@/hooks/messageStore';
import NIMService from '@/hooks/useNewNIM';
import useUploadImage from '@/hooks/useUploadImage';
import { redDotStoreCreator } from '@/pages/chat/components/Footer/redDotStore';
import { ImmerseEnterAnimStoreCreator } from '@/pages/chat/store/useImmerseEnterAnim';
import { ChatType } from '@/pages/chat/type';
import { apiImageGenerateContent, apiSendImage } from '@/service/imApi';
import { MessageErrorCode } from '@/types/im';
import { isIOS } from '@/utils';
import { imageUploadChannel } from '@/utils/appSourceAdapter';
import { addClickLog } from '@/utils/logTool';
import { ChatDataManager, ImageInfo } from '@/utils/managers/ChatDataManager';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { Image, Text, Textarea, TextareaProps, View } from '@tarojs/components';
import { BaseEventOrig } from '@tarojs/components/types/common';
import Taro, { getCurrentInstance, hideLoading, showToast, useLoad } from '@tarojs/taro';
import classNames from 'classnames';
import React, { forwardRef, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import useDetailStore from '../../store/useDetailStore';
import useInspirationStore from '../../store/useInspirationStore';
import useScrollStore, { InputPanelStatus } from '../../store/useScrollStore';
import useSwiperTouchStore from '../../store/useSwiperTouchStore';
import ChatPlot from '../ChatPlot';
import mainPlotListStore from '../ChatPlot/MainPlotList/mainPlotListStore';
import ImagePanel from './ImagePanel';
import './index.scss';
import InputMorePanel from './More';

function getInspirationImage(type: string, inspiration: boolean) {
    if (type === ChatType.CHIP) {
        return inspiration ? ICON_INSPIRATION_SHOW_WHITE : ICON_INSPIRATION_WHITE;
    }
    return inspiration ? ICON_INSPIRATION_SHOW : ICON_INSPIRATION;
}

function getParenthesesImage(type: string) {
    return type === ChatType.CHIP ? ICON_PARENTHESES_WHITE : ICON_PARENTHESES;
}

export interface ChatScreenState {
    saftAreaBottomHeight: number;
}

const InputPanel = forwardRef(
    (
        {
            type,
            onChangeInspiration,
        }: {
            type: string;
            onChangeInspiration?: (inspiration: boolean) => void;
        },
        ref
    ) => {
        const inputWrapRef: any = useRef();
        const { progress, touchStart, touchMove }: any = useSwiperTouchStore();
        const { robotAccid = '', robotUserId = '' } = getCurrentInstance().router?.params || {};
        const targetNickname =
            useDetailStore((state) => state.robotInfo?.robotBaseInfo?.nickname) || '';
        const hasAiChapter = mainPlotListStore((state) => state.hasAiChapter) || false;

        const inputRef = useRef<any>(null);
        const inspiration = useInspirationStore((state) => state.show);
        const inspirationOpen = useInspirationStore((state) => state.open);

        const [selectImage, setSelectImage] = useState('');
        const [selectImageNosKey, setSelectImageNosKey] = useState('');
        const [generateContent, setGenerateContent] = useState('');
        const [input, setInput] = useState('');

        // 图片上传相关数据
        const [loadingStatus, setLoadingStatus] = useState(false);
        const [selectImageWidth, setSelectImageWidth] = useState(0);
        const [selectImageHeight, setSelectImageHeight] = useState(0);

        // Footer发生高度变化时，状态存起来，方便外面监听footer高度变化
        const panelStatus = useScrollStore((state) => state.panelStatus);
        const setPanelStatus = useScrollStore((state) => state.setPanelStatus);

        const needFoucs = useRef(false);
        const fromSend = useRef(false);

        // sendIcon
        const moreImageMap: Record<number, string> = useMemo(() => {
            const finalInput = input.trim().replace(/^[\n\r]+|[\n\r]+$/g, '');
            return {
                [InputPanelStatus.normal]: type === ChatType.CHIP ? ICON_MORE_WHITE : ICON_MORE,
                [InputPanelStatus.send]:
                    // eslint-disable-next-line no-nested-ternary
                    finalInput.length > 0
                        ? ICON_MORE_SEND
                        : type === ChatType.CHIP
                        ? ICON_MORE_WHITE
                        : ICON_MORE,
                [InputPanelStatus.more]:
                    type === ChatType.CHIP ? ICON_MORE_CLOSE_WHITE : ICON_MORE_CLOSE,
            };
        }, [input, type]);
        /// 上传图片
        const uploadImage = useCallback(
            async (
                avatarImgUrlKey: string,
                avatarImgNosKey: string,
                width: number,
                height: number
            ) => {
                setSelectImage(avatarImgUrlKey);
                setSelectImageNosKey(avatarImgNosKey);
                setSelectImageWidth(width);
                setSelectImageHeight(height);

                if (!avatarImgNosKey) {
                    return;
                }

                if (isIOS && !generateContent) {
                    // TODO iOS 第一次聚焦但是不显示键盘!!!
                    inputRef.current.children[0].focus();
                } else {
                    Taro.nextTick(() => {
                        inputRef.current.children[0].focus();
                    });
                }

                setLoadingStatus(true);
                const userId = useDetailStore.getState().userId;
                try {
                    const res = await apiImageGenerateContent({
                        nosKey: avatarImgNosKey,
                        toUserId: userId || '',
                        sync: true,
                    });
                    hideLoading();
                    setGenerateContent(res.generateContent);
                    setLoadingStatus(false);
                } catch (err) {
                    setLoadingStatus(false);
                    hideLoading();
                }
            },
            // eslint-disable-next-line react-hooks/exhaustive-deps
            []
        );
        const [toUpload] = useUploadImage({
            needBeauty: false,
            ratio: -1,
            uploadSuccessCallback: (
                avatarImgUrlKey: string,
                avatarImgNosKey: string,
                width: number,
                height: number
            ) => {
                uploadImage(avatarImgUrlKey, avatarImgNosKey, width, height);
            },
            uploadFailCallback: () => {
                hideLoading();
            },
        });

        useLoad(() => {
            const dataManager = ChatDataManager.getInstance();
            const data = dataManager.getChatData(robotAccid);
            if (data && !!data.text) {
                setInput(data.text);
            }
            if (data && data.image === true) {
                const imageInfoStr = data.imageInfo;
                if (imageInfoStr) {
                    const imageInfo: ImageInfo = JSON.parse(imageInfoStr);
                    setSelectImage(imageInfo.selectImage);
                    setSelectImageNosKey(imageInfo.selectImageNosKey);
                    setLoadingStatus(false);
                    setGenerateContent(imageInfo.generateContent);
                    setSelectImageWidth(imageInfo.selectImageWidth);
                    setSelectImageHeight(imageInfo.selectImageHeight);
                }
                // 如果图片未上传 ，需要进行处理~
            }
        });

        useEffect(() => {
            const dataManager = ChatDataManager.getInstance();
            if (!input && !selectImage) {
                dataManager.removeChatData(robotAccid);
            } else {
                let imageMsg = false;
                let imageInfoObj: any = null;
                if (selectImage && !!selectImage) {
                    imageMsg = true;
                    imageInfoObj = {
                        selectImage,
                        selectImageNosKey,
                        generateContent,
                        selectImageWidth,
                        selectImageHeight,
                    };
                }
                let imageInfoStr = '';
                if (imageInfoObj) {
                    imageInfoStr = JSON.stringify(imageInfoObj);
                }

                dataManager.setChatData(robotAccid, {
                    image: imageMsg,
                    text: input,
                    imageInfo: imageInfoStr,
                    timestamp: new Date().getTime(),
                });
            }
        }, [
            input,
            selectImage,
            robotAccid,
            selectImageNosKey,
            generateContent,
            selectImageWidth,
            selectImageHeight,
        ]);

        const clickParenthesesAction = useCallback(() => {
            const currentInput = inputRef.current.children[0];
            const currentValue = input;
            const cursorPosition = currentInput.selectionStart;
            const newValue = `${currentValue.substring(
                0,
                cursorPosition
            )}（）${currentValue.substring(cursorPosition)}`;
            setInput(newValue);

            // 保持输入框的焦点
            Taro.nextTick(() => {
                // 将光标设置在括号中间
                currentInput.setSelectionRange(cursorPosition + 1, cursorPosition + 1);
                currentInput.focus();
                currentInput.scrollTop = currentInput.scrollHeight;
            });

            addClickLog('btn_ai_im_input_bracket|page_ai_im|page_h5_biz');
        }, [input]);

        const clickInspirationAction = useCallback(() => {
            // ai 回复中，不响应灵感回复的开启和关闭
            const inputing =
                useMessageStore.getState().lastMsgStatus === LastMsgStatus.sent &&
                useMessageStore.getState().showLoading;
            // 没有消息时，不响应灵感回复的开启和关闭
            const noMsg = useMessageStore.getState().messageList.length === 0;
            if (inputing || noMsg) {
                if (noMsg) {
                    coronaWarnMessage(
                        'NIMService',
                        `点击灵感回复时无消息, 当前是否正在加载消息=${
                            useMessageStore.getState().loadingData
                        }`
                    );
                }
                showToast({
                    title: '请等待对方回复',
                    icon: 'none',
                    duration: 3000,
                });
                return;
            }
            const inspira = useInspirationStore.getState().show;
            onChangeInspiration?.(inspira);
            if (inspiration === false) {
                setPanelStatus(InputPanelStatus.normal);
            }
            useInspirationStore.getState().toggleInspiration();

            addClickLog('btn_ai_im_inspiration|page_ai_im|page_h5_biz');
        }, [inspiration, onChangeInspiration, setPanelStatus]);

        function toggleInspirationShow() {
            if (useInspirationStore.getState().data) {
                useInspirationStore.getState().toggleInspirationShow(true);
            } else {
                useInspirationStore.getState().fetchInspiration(0, undefined);
            }
        }

        const handleInspiration = useCallback(() => {
            if (
                useMessageStore.getState().lastMsgStatus !== LastMsgStatus.sent &&
                !input &&
                !selectImage &&
                useInspirationStore.getState().open
            ) {
                toggleInspirationShow();
            }
        }, [input, selectImage]);

        const duringSend = useRef(false);
        const [line, setLine] = useState<number>(1);
        const rightLineRef = useRef<boolean>(false);
        const onLineChange = (event: BaseEventOrig<TextareaProps.onLineChangeEventDetail>) => {
            if (duringSend.current) {
                // fix: 发送点击时，还会触发onLineChange的回调，此处需要忽略
                return;
            }
            setLine(event.detail.lineCount);
            rightLineRef.current = true;
        };

        const fixLineCount = (text: string) => {
            // fix:如果onLineChange没有回调，line是未知的，基于字数长度估算line
            // 该场景在一开始就从草稿中填充文字长度，此时文字长度并没有触发onLineChange回调，此时文本长度是未知的
            if (text.length > 30) {
                setLine(3);
            } else if (text.length > 15) {
                setLine(2);
            } else {
                setLine(1);
            }
        };

        useEffect(() => {
            Taro.eventCenter.on('closeInspiration', () => {
                useInspirationStore.getState().closeInspiration();
            });
            Taro.eventCenter.on('receiveNewMsgHandleInspiration', () => {
                if (panelStatus === InputPanelStatus.normal) {
                    useInspirationStore.getState().fetchInspiration(0, undefined);
                }
            });
            Taro.eventCenter.on('msgSendFail', () => {
                if (
                    useInspirationStore.getState().open &&
                    panelStatus === InputPanelStatus.normal
                ) {
                    toggleInspirationShow();
                }
            });
            Taro.eventCenter.on('editInspiration', (data) => {
                if (data && data.msg) {
                    rightLineRef.current = false;
                    setInput(data.msg);
                    const currentInput = inputRef?.current?.children[0];
                    if (isIOS) {
                        currentInput?.focus();
                    }
                    // 在 iOS 上 focus 必须同步调用，所以在 inputOnFocus 里取不到 input
                    fixLineCount(data.msg);
                    Taro.nextTick(() => {
                        // 将光标设置到最后
                        currentInput?.setSelectionRange(-1, -1);
                        if (!isIOS) {
                            currentInput?.focus();
                        }
                        if (currentInput) {
                            currentInput.scrollTop = currentInput.scrollHeight;
                        }
                        setPanelStatus(InputPanelStatus.send);
                        useDetailStore.getState().setIsKeyboardBounce(true);
                    });
                }
            });
            return () => {
                Taro.eventCenter.off('closeInspiration');
                Taro.eventCenter.off('msgSendFail');
                Taro.eventCenter.off('receiveNewMsgHandleInspiration');
            };
        }, [handleInspiration, panelStatus, setPanelStatus]);

        const inputOnFocus = useCallback(() => {
            setPanelStatus(InputPanelStatus.send);
            useInspirationStore.getState().toggleInspirationShow(false);
            useDetailStore.getState().setIsKeyboardBounce(true);

            if (!rightLineRef.current) {
                // fix:如果onLineChange没有回调，line是未知的，基于字数长度估算line
                // 该场景在一开始就从草稿中填充文字长度，此时文字长度并没有触发onLineChange回调，此时文本长度是未知的
                fixLineCount(input);
            }

            Taro.nextTick(() => {
                // 获取焦点让游标在最后
                if (inputRef && inputRef.current && inputRef.current.children) {
                    const currentInput = inputRef.current.children[0];
                    if (currentInput) {
                        const cursorPosition = input?.length ?? 0;
                        currentInput.setSelectionRange(cursorPosition + 1, cursorPosition + 1);
                        currentInput.scrollTop = currentInput.scrollHeight;
                    }
                }
            });
        }, [input, setPanelStatus]);

        const inputOnBlur = useCallback(() => {
            useDetailStore.getState().setIsKeyboardBounce(false);
            Taro.nextTick(() => {
                if (needFoucs.current) {
                    needFoucs.current = false;
                    if (document.activeElement !== inputRef.current?.children?.[0]) {
                        setPanelStatus(InputPanelStatus.normal);
                    }
                } else {
                    setPanelStatus(InputPanelStatus.normal);
                }
                // 点击输入框右侧的发送按钮，会触发一次键盘的 inputOnBlur
                if (fromSend.current) {
                    fromSend.current = false;
                } else {
                    handleInspiration();
                }
                const currentInput = inputRef.current?.children?.[0];
                if (currentInput) {
                    currentInput.scrollTop = 0;
                }
            });
        }, [setPanelStatus, handleInspiration]);

        const onInputChange = (e: BaseEventOrig<TextareaProps.onInputEventDetail>) => {
            const value = e.detail?.value;
            if (duringSend.current) {
                // fix：点击发送按钮，会触发一次键盘的input回调。
                if (isIOS && value === '\n') {
                    Taro.nextTick(() => {
                        inputRef.current.children[0].value = '';
                        setInput('');
                    });
                }
                return;
            }

            // 使用正则表达式匹配单个 \r（不是\r\n组合的一部分）
            const singleCarriageReturnRegex = /^[^\r]*\r(?!\n)[^\r]*$/;
            if (singleCarriageReturnRegex.test(value)) {
                const convertedValue = value.replace(/\r(?!\n)/g, '\n');
                setInput(convertedValue);
                setLine(line + 1);

                const cursorPos = e.detail?.cursor !== undefined ? e.detail.cursor : value.length;
                const crPosition = value.indexOf('\r');
                const newCursorPos = cursorPos > crPosition ? crPosition + 1 : cursorPos;

                // 在下一个渲染周期后设置光标位置到末尾
                Taro.nextTick(() => {
                    if (inputRef && inputRef.current && inputRef.current.children) {
                        const currentInput = inputRef.current.children[0];
                        if (currentInput) {
                            currentInput.setSelectionRange(newCursorPos, newCursorPos);
                            currentInput.scrollTop = currentInput.scrollHeight;
                        }
                    }
                });
            } else {
                // 判断是否为删除换行操作
                const inputCount = input.split('\n').length - 1;
                const valueCount = value.split('\n').length - 1;
                if (inputCount > valueCount) {
                    setLine(line - 1);
                }
                setInput(value);
            }
            setPanelStatus(InputPanelStatus.send);
        };

        const deleteImageAction = useCallback(() => {
            setSelectImage('');
            setSelectImageNosKey('');
            setLoadingStatus(false);
            setGenerateContent('');
            if (panelStatus === InputPanelStatus.send && !isIOS) {
                Taro.nextTick(() => {
                    inputRef.current.children[0].focus();
                });
            }
        }, [panelStatus, inputRef]);

        const onSend = useCallback(() => {
            // fix：点击发送按钮，会触发onInputChange回调和onLineChange回调，所以区别这些触发是否是点击发送按钮
            if (duringSend.current) {
                // fix：防止重复send
                return;
            }
            duringSend.current = true;
            setTimeout(() => {
                duringSend.current = false;
            }, 200);

            inputRef.current.children[0].focus();
            // 去除输入首尾换行符
            const finalInput = input.trim().replace(/^[\n\r]+|[\n\r]+$/g, '');

            if (!finalInput || finalInput.length <= 0) {
                if (selectImageNosKey || loadingStatus) {
                    showToast({
                        title: '图片需要和文字一起发送哦',
                    });
                }
                return;
            }

            if (!selectImageNosKey) {
                NIMService.sendText(finalInput);
                Taro.eventCenter.trigger('event.chat.sendMessageSuccess', {
                    type: 'text',
                });
            } else if (!generateContent && loadingStatus) {
                // 正在上传中
                showToast({
                    title: '请等待图片加载完成后再发送',
                });
                return;
            } else {
                try {
                    const userId = useDetailStore.getState().userId;
                    apiSendImage({
                        nosKey: selectImageNosKey,
                        toUserId: userId || '',
                        userMsgDesc: finalInput,
                        generateContent,
                        width: selectImageWidth,
                        height: selectImageHeight,
                        uploadChannel: imageUploadChannel(),
                    })
                        .then(() => {
                            Taro.eventCenter.trigger('powerEvent', {
                                type: 'msgSendSuccess',
                                time: new Date().getTime(),
                            });
                            Taro.eventCenter.trigger('event.chat.sendMessageSuccess', {
                                type: 'image',
                            });
                            // 接口发送消息到云信，im会有延迟，这里要再延迟下出现loading，不然loading会被覆盖掉
                            Taro.nextTick(() => {
                                setTimeout(() => {
                                    useMessageStore.getState().setLastMsgStatus(LastMsgStatus.sent);
                                }, 50);
                            });
                        })
                        .catch((err: { code: number; message: string }) => {
                            if (err.code === MessageErrorCode.balaceInsufficient) {
                                Taro.eventCenter.trigger('powerEvent', {
                                    type: 'msgSendFiledForHypodynamia',
                                    time: new Date().getTime(),
                                });
                            } else if (err.code === MessageErrorCode.antiSpam) {
                                showToast({
                                    title: '消息违规，未发送成功',
                                    icon: 'none',
                                    duration: 2000,
                                });
                            } else if (err.code === MessageErrorCode.antiCheat) {
                                showToast({
                                    title: '操作异常，请稍后再试',
                                    icon: 'none',
                                    duration: 2000,
                                });
                            } else if (err.code === MessageErrorCode.realNameAuth) {
                                // 实名认证处罚不提示
                            } else {
                                showToast({
                                    title: err.message || '消息发送失败，请稍后再试',
                                    icon: 'none',
                                    duration: 2000,
                                });
                            }
                        });
                    deleteImageAction();
                } catch (err) {
                    console.log(err);
                }
            }

            inputRef.current.value = '';
            setInput('');
            needFoucs.current = true;
            rightLineRef.current = false;
            setLine(1);
        }, [
            input,
            selectImageNosKey,
            generateContent,
            loadingStatus,
            setInput,
            selectImageWidth,
            selectImageHeight,
            deleteImageAction,
        ]);

        const onKeyDown = useCallback(
            (e: any) => {
                if (isIOS && e.key === 'Enter') {
                    // iOS 的发送检测
                    onSend();
                }
            },
            [onSend]
        );

        const onConfirm = useCallback(() => {
            onSend();
        }, [onSend]);

        useEffect(() => {
            Taro.eventCenter.on('resetInputPanel', () => {
                deleteImageAction();
                inputRef.current.value = '';
                setInput('');
                setPanelStatus(InputPanelStatus.normal);
                useInspirationStore.getState().closeInspiration();
            });

            return () => {
                Taro.eventCenter.off('resetInputPanel');
                Taro.eventCenter.off('onListScroll');
            };
        }, [inputRef, deleteImageAction, setPanelStatus]);

        const clickMoreAction = useCallback(() => {
            switch (panelStatus) {
                case InputPanelStatus.normal:
                    setPanelStatus(InputPanelStatus.more);
                    useDetailStore.getState().setIsMoreBounce(true);
                    useInspirationStore.getState().toggleInspirationShow(false);
                    break;
                case InputPanelStatus.send:
                    useDetailStore.getState().setIsMoreBounce(true);
                    useInspirationStore.getState().toggleInspirationShow(false);

                    if (input.length > 0) {
                        fromSend.current = true;
                        onSend();
                    } else {
                        Taro.nextTick(() => {
                            setPanelStatus(InputPanelStatus.more);
                            useInspirationStore.getState().toggleInspirationShow(false);
                        });
                    }
                    break;
                case InputPanelStatus.more:
                    setPanelStatus(InputPanelStatus.normal);
                    useDetailStore.getState().setIsMoreBounce(false);
                    handleInspiration();
                    break;
                default:
                    break;
            }

            addClickLog('btn_ai_im_more|page_ai_im|page_h5_biz');
        }, [panelStatus, setPanelStatus, input.length, handleInspiration, onSend]);

        // chip 229-> 109 1 -> 0.5 | touchStart + touchMove
        // 229 - p * 120, 1 - p * 0.5
        // dom.style = 229 - p * 120, 1 - p * 0.5
        useEffect(() => {
            if (touchStart && touchMove && inputWrapRef && progress > 0.01 && progress < 0.99) {
                if (type === ChatType.CHIP) {
                    inputWrapRef.current.style.backgroundColor = `rgba(${229 - progress * 120}, ${
                        229 - progress * 120
                    }, ${229 - progress * 120}, ${1 - progress * 0.5})`;
                } else {
                    inputWrapRef.current.style.backgroundColor = `rgba(${
                        109 + (1 - progress) * 120
                    }, ${109 + (1 - progress) * 120}, ${109 + (1 - progress) * 120}, ${
                        0.5 + (1 - progress) * 0.5
                    })`;
                }
            } else if (type === ChatType.CHIP) {
                inputWrapRef.current.style.backgroundColor = 'rgba(229, 229, 229, 1)';
            } else {
                inputWrapRef.current.style.backgroundColor = 'rgba(109, 109, 109, 0.5)';
            }
        }, [progress, touchStart, touchMove, type]);

        useEffect(() => {
            if (touchStart && panelStatus === InputPanelStatus.more) {
                Taro.nextTick(() => {
                    setPanelStatus(InputPanelStatus.normal);
                    useDetailStore.getState().setIsMoreBounce(false);
                    handleInspiration();
                });
            }
        }, [touchStart, panelStatus, setPanelStatus, handleInspiration]);

        let cssMode = '';
        if (panelStatus === InputPanelStatus.send) {
            cssMode = 'send-mode';
        } else if (panelStatus === InputPanelStatus.more) {
            cssMode = 'more-mode';
        }
        const redDotStore = useContextStore(redDotStoreCreator);
        const showNormalPackageRedDotOnTab = redDotStore((state) => state.packageRedDot);
        const robotPackageRedDotOnTab = redDotStore((state) => state.robotPackageRedDotOnTab);
        const showRobotPackageRedDotOnTab = robotPackageRedDotOnTab?.includes(robotUserId);
        const packageRedDot = showRobotPackageRedDotOnTab || showNormalPackageRedDotOnTab;

        const useImmerseEnterAnimStore = useContextStore(ImmerseEnterAnimStoreCreator);
        const shouldPlay = useImmerseEnterAnimStore((state) => state.shouldPlay);
        const playFooter = useImmerseEnterAnimStore((state) => state.playFooter);

        useEffect(() => {
            mainPlotListStore.getState().getPlotsList(robotUserId);
            return () => {
                mainPlotListStore.getState().reset();
            };
        }, [robotUserId]);

        return (
            <ErrorBoundary>
                <View
                    ref={ref}
                    id="footerContainer"
                    onTouchMove={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        return false;
                    }}
                    className={classNames('chat-input-container', 'chat-wrapper', {
                        [type]: true,
                        more: panelStatus === InputPanelStatus.more,
                        'immerse-footer-enter-anim': shouldPlay,
                        active: playFooter,
                    })}>
                    {hasAiChapter &&
                        panelStatus !== InputPanelStatus.send &&
                        !inspiration &&
                        !selectImage?.length && <ChatPlot type={type} />}
                    {!!selectImage?.length && panelStatus !== InputPanelStatus.normal && (
                        <View className="input-image-panel">
                            <ImagePanel
                                image={selectImage}
                                loading={loadingStatus}
                                imageWidth={selectImageWidth}
                                imageHeight={selectImageHeight}
                                deleteAction={deleteImageAction}
                            />
                        </View>
                    )}
                    <View className={`input-portal ${cssMode}`}>
                        <View className="input-text-panel">
                            <View className="input-panel-container" ref={inputWrapRef}>
                                {selectImage.length > 0 &&
                                    panelStatus === InputPanelStatus.normal && (
                                        <Text className="input-panel-tip">[图片]</Text>
                                    )}
                                <View
                                    className={`input-panel-text-div ${
                                        selectImage.length > 0 &&
                                        panelStatus === InputPanelStatus.normal
                                            ? 'show-image'
                                            : ''
                                    }`}>
                                    <Textarea
                                        className="input-panel-text"
                                        style={{
                                            height:
                                                cssMode === 'send-mode'
                                                    ? 20 * Math.min(line, 3)
                                                    : 20,
                                        }}
                                        autoHeight={
                                            rightLineRef.current &&
                                            cssMode === 'send-mode' &&
                                            line <= 3
                                        }
                                        ref={inputRef}
                                        value={input}
                                        placeholder={
                                            selectImage.length > 0 &&
                                            panelStatus === InputPanelStatus.normal
                                                ? ''
                                                : `发送消息给${targetNickname}`
                                        }
                                        onTouchStart={(e) => {
                                            if (cssMode === 'send-mode') {
                                                e.stopPropagation();
                                            }
                                        }}
                                        onTouchMove={(e) => {
                                            if (cssMode === 'send-mode') {
                                                e.stopPropagation();
                                            }
                                        }}
                                        onLineChange={onLineChange}
                                        maxlength={500}
                                        onFocus={inputOnFocus}
                                        onBlur={inputOnBlur}
                                        onInput={onInputChange}
                                        onConfirm={onConfirm}
                                        confirmType={isIOS ? 'send' : 'return'}
                                        nativeProps={{
                                            enterkeyhint: isIOS ? 'send' : 'return',
                                            style: {
                                                wordBreak: 'break-word',
                                                overflowWrap: 'break-word',
                                                whiteSpace: 'pre-wrap',
                                                textJustify: 'inter-character',
                                                lineBreak: 'anywhere',
                                            },
                                            ...(process.env.TARO_ENV === 'h5'
                                                ? {
                                                      onKeyDown,
                                                  }
                                                : {}),
                                        }}
                                    />
                                </View>
                                <View
                                    className="input-panel-buttons"
                                    onTouchMove={(e) => {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        return false;
                                    }}
                                    onTouchStart={(e) => {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        return false;
                                    }}>
                                    <EventTrackView
                                        params={{
                                            _spm: 'btn_ai_im_input_bracket|page_ai_im|page_h5_biz',
                                        }}
                                        isPage={false}>
                                        <Image
                                            className="input-panel-parentheses"
                                            src={getParenthesesImage(type)}
                                            // 这里不能用onClick 会失焦
                                            onTouchStart={(e) => {
                                                e.stopPropagation();
                                                e.preventDefault();
                                                clickParenthesesAction();
                                                return false;
                                            }}
                                        />
                                    </EventTrackView>
                                    <Image
                                        className="input-panel-inspiration"
                                        src={getInspirationImage(type, inspirationOpen)}
                                        onTouchStart={() => {
                                            inputRef.current.children[0].blur();
                                            clickInspirationAction();
                                        }}
                                    />
                                </View>
                            </View>
                            <Image
                                className={classNames('input-panel-more', {
                                    'input-panel-more-red-dot':
                                        packageRedDot && panelStatus === InputPanelStatus.normal,
                                })}
                                src={moreImageMap[panelStatus]}
                                onClick={clickMoreAction}
                            />
                        </View>
                        {panelStatus === InputPanelStatus.more && (
                            <View className="input-more-panel">
                                <InputMorePanel type={type} selectImageAction={toUpload} />
                            </View>
                        )}
                    </View>
                    <View
                        className="input-aigc"
                        onTouchMove={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            return false;
                        }}>
                        <Text className="input-aigc-text">聊天内容由AI生成</Text>
                    </View>
                </View>
            </ErrorBoundary>
        );
    }
);

export default React.memo(InputPanel);
