import React, { memo } from 'react';
import classNames from 'classnames';
import { View, Image } from '@tarojs/components';
import Spin_Loading_Chat from '@/assets/effect/spining_loading_chat.png';
import Spin_Loading_Immerse from '@/assets/effect/spining_loading_immerse.png';
import Avatar from '@/components/avatar';
import { ChatType } from '@/pages/chat/type';
import './index.scss';

const LoadingItem = ({ type, avatar }: { type: string; avatar: string }) => {
    return (
        <View
            className={classNames('chat-msg-item', {
                my: false,
                [type]: true,
                loading: type === ChatType.CHIP,
            })}>
            {type === ChatType.CHIP && <Avatar lazyload={false} src={avatar || ''} />}
            <View
                className={classNames('chat-msg-item-loading', {
                    my: false,
                    [type]: true,
                })}>
                <Image
                    src={type === ChatType.IMMERSE ? Spin_Loading_Immerse : Spin_Loading_Chat}
                    style={{ width: '30px' }}
                />
            </View>
        </View>
    );
};

export default memo(LoadingItem);
