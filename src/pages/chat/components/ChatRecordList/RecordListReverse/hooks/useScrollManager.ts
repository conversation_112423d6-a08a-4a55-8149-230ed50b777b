import { useCallback, useRef, useState, useEffect } from 'react';
import useScrollStore, { InputPanelStatus } from '@/pages/chat/store/useScrollStore';
import useDetailStore from '@/pages/chat/store/useDetailStore';
import useInspirationStore from '@/pages/chat/store/useInspirationStore';

interface UseScrollManagerProps {
    type: string;
    scroller: React.RefObject<HTMLDivElement>;
    renderMask: () => void;
}

const useScrollManager = ({ type, scroller, renderMask }: UseScrollManagerProps) => {
    const [isScrollingUp, setIsScrollingUp] = useState(false);
    const prevScrollTopRef = useRef(0);

    const setChipScrollTop = useScrollStore((state) => state.setChipScrollTop);
    const setImmerseScrollTop = useScrollStore((state) => state.setImmerseScrollTop);
    const chipScrollTop = useScrollStore((state) => state.chipScrollTop);
    const immerseScrollTop = useScrollStore((state) => state.immerseScrollTop);

    // 键盘弹起：要以用户滚动为主
    // 更多面板打开：要以自动滚动到底为主
    // 灵感回复打开：要以自动滚动到底为主
    const isKeyboardBounce = useDetailStore((state) => state.isKeyboardBounce);
    const isMoreBounce = useDetailStore((state) => state.isMoreBounce);
    const inspirationShow = useInspirationStore((state) => state.show);

    // 标记是自动滚动，而不是用户主动滚动
    // 作用是滚动的过程中，不能拉取新消息
    const fromScrollToBottom = useRef(false);
    // 类似微信效果：用于标识是否正在自动滚动，自动滚动期间禁止用户滚动
    const isAutoScrolling = useRef(false);
    // 添加一个标记，表示用户是否正在主动滚动
    const isUserScrolling = useRef(false);

    // 添加一个定时器引用，用于跟踪用户滚动状态
    const userScrollingTimerRef = useRef<NodeJS.Timeout | null>(null);
    // 添加一个滚动定位定时器
    const scrollTopTimerRef = useRef<NodeJS.Timeout | null>(null);

    // 更新滚动位置的辅助函数
    const updateScrollPosition = useCallback(
        (scrollTop: number) => {
            if (type === 'chip') {
                setChipScrollTop(scrollTop);
            } else {
                setImmerseScrollTop(scrollTop);
            }
            prevScrollTopRef.current = scrollTop;
            renderMask();
        },
        [type, setChipScrollTop, setImmerseScrollTop, renderMask]
    );

    // 监听滚动事件
    const handleScroll = useCallback(
        (e: { detail: { scrollTop: any; scrollHeight: any } }) => {
            const { scrollTop = 0 } = e?.detail || {};

            // 标记用户正在滚动
            isUserScrolling.current = true;
            // 清除之前的用户滚动状态定时器
            if (userScrollingTimerRef.current) {
                clearTimeout(userScrollingTimerRef.current);
            }
            // 设置一个新的定时器，在用户停止滚动一段时间后重置状态
            userScrollingTimerRef.current = setTimeout(() => {
                isUserScrolling.current = false;
            }, 300); // 用户停止滚动1秒后认为滚动结束

            /// 判断滚动方向
            setIsScrollingUp(scrollTop < prevScrollTopRef.current);

            // 重置自动滚动标记
            if (scrollTopTimerRef.current) {
                clearTimeout(scrollTopTimerRef.current);
            }
            // 设置一个定时器，只有当用户停止滑动一段时间后才触发拉取
            scrollTopTimerRef.current = setTimeout(() => {
                fromScrollToBottom.current = false;
            }, 150);

            const { scrollHeight, scrollTop: scrollerScrollTop, offsetHeight } = scroller.current;
            // 真实滚动高度
            const actualScrollTop = scrollHeight - scrollerScrollTop - offsetHeight;

            // 更新滚动位置
            updateScrollPosition(actualScrollTop);
        },
        [scroller, updateScrollPosition]
    );

    // 滚动到底部的函数
    const scrollToBottom = useCallback(() => {
        // 如果用户正在主动滚动，不执行自动滚动到底部；
        // 键盘弹起强制滚动到底
        // 面板打开强制滚动到底
        if (isUserScrolling.current && !isKeyboardBounce && !isMoreBounce && !inspirationShow) {
            return;
        }

        if (!scroller.current) return;

        const scrollerElement = scroller.current;

        // 设置滚动状态
        fromScrollToBottom.current = true;
        isAutoScrolling.current = true;

        // 滚动到底期间禁用触摸事件
        scrollerElement.style.pointerEvents = 'none';

        // 获取滚动区域的高度
        const scrollHeight = scrollerElement.scrollHeight;
        const offsetHeight = scrollerElement.offsetHeight || 0;

        // 计算需要滚动到的位置（滚动到底部）
        const scrollToPosition = Math.max(0, scrollHeight - offsetHeight);

        requestAnimationFrame(() => {
            // 先尝试使用scrollTo API
            if (scroller.current.scrollTo) {
                scroller.current.scrollTo({
                    top: scrollToPosition,
                    behavior: 'smooth',
                });
            } else {
                scrollerElement.scrollTop = scrollToPosition;
            }

            // 重置滚动方向状态
            setIsScrollingUp(false);
            prevScrollTopRef.current = scrollToPosition;
        });

        // 重置滚动方向状态
        setIsScrollingUp(false);
        updateScrollPosition(scrollToPosition);

        // 设置一个定时器，在滚动完成后重置标志并恢复触摸事件
        setTimeout(() => {
            isAutoScrolling.current = false;
            if (scroller.current) {
                scrollerElement.style.pointerEvents = 'auto';
            }
        }, 300);
    }, [isKeyboardBounce, isMoreBounce, inspirationShow, scroller, updateScrollPosition]);

    // 离开chat页面重置滚动数据
    useEffect(() => {
        return () => {
            // 清除所有定时器
            if (userScrollingTimerRef.current) {
                clearTimeout(userScrollingTimerRef.current);
            }
            if (scrollTopTimerRef.current) {
                clearTimeout(scrollTopTimerRef.current);
            }
            setChipScrollTop(0);
            setImmerseScrollTop(0);
            useScrollStore.getState().panelStatus = InputPanelStatus.normal;
        };
    }, [setChipScrollTop, setImmerseScrollTop]);

    return {
        isScrollingUp,
        handleScroll,
        scrollToBottom,
        chipScrollTop,
        immerseScrollTop,
    };
};

export default useScrollManager;
