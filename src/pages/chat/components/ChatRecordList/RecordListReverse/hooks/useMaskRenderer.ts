import { useCallback, useRef } from 'react';
import useScrollStore from '@/pages/chat/store/useScrollStore';
import { MsgDataType } from '@/types/im';

interface UseMaskRendererProps {
    type: string;
    scroller: React.RefObject<HTMLDivElement>;
    newMessageList: any[];
}

// 渲染沉浸式消息遮罩层
const useMaskRenderer = ({ type, scroller, newMessageList }: UseMaskRendererProps) => {
    const lastRenderTime = useRef(0);
    const isRendering = useRef(false);
    const immerseScrollTop = useScrollStore((state) => state.immerseScrollTop);

    // 渲染沉浸式特殊的遮罩层效果
    const renderMask = useCallback(() => {
        if (!scroller.current || type !== 'immerse') {
            // 非沉浸模式防止内容透到状态栏，也设一个固定的遮罩
            if (scroller.current) {
                // 使用临时变量避免直接修改参数属性
                const scrollerElement = scroller.current;
                scrollerElement.style.webkitMaskImage = '';
            }
            return;
        }

        const fixedMask = `-webkit-linear-gradient(top, rgba(0, 0, 0, 0) 0%,rgba(0, 0, 0, 0) 60px,rgba(0, 0, 0) 80px)`;
        const { offsetHeight: parentHeight, scrollHeight } = scroller.current as HTMLElement;

        const scrollerElement = scroller.current;
        // 如果内容不足一屏或消息数量少于3条，不显示遮罩
        if (parentHeight === scrollHeight || newMessageList.length < 3) {
            scrollerElement.style.webkitMaskImage = fixedMask;
            return;
        }

        // 使用requestAnimationFrame优化渲染
        if (isRendering.current) return;
        isRendering.current = true;

        requestAnimationFrame(() => {
            const now = Date.now();
            // 限制渲染频率，最小间隔16ms（约60fps）
            if (now - lastRenderTime.current < 16) {
                isRendering.current = false;
                return;
            }

            // 如果用户正在向上滚动，移除遮罩效果
            // if (isScrollingUp) {
            //     scrollerElement.style.webkitMaskImage = fixedMask;
            //     isRendering.current = false;
            //     return;
            // }

            // 获取倒数第二条消息元素
            const items = scrollerElement.querySelectorAll('.chat-msg-item');
            // 获取最后两条元素
            const secondItem = items[newMessageList.length - 2];

            if (!secondItem) {
                // 如果没有找到倒数第二条消息，清除遮罩
                scrollerElement.style.webkitMaskImage = fixedMask;
                isRendering.current = false;
                return;
            }

            // 获取最后2条消息的高度
            const { offsetTop: secondItemOffsetTop } = secondItem as HTMLDivElement;

            // 真实滚动高度
            const actualScrollTop = scrollHeight - scrollerElement.scrollTop - parentHeight;

            // 遮罩层最小高度：不能遮住最后两条消息，这里计算的是最后两条消息的高度和margin值
            const maskMinHeight = scrollHeight - secondItemOffsetTop - 6;
            // 遮罩层最大高度：不能超出一屏的高度
            const maskMaxHeight = parentHeight;

            const maskMiddlePosition = Math.min(maskMinHeight + actualScrollTop, maskMaxHeight);

            const gradient = `-webkit-linear-gradient(bottom, rgba(0, 0, 0) 0%,rgba(0, 0, 0) ${
                maskMiddlePosition + 6
            }px,rgba(0, 0, 0, 0) ${maskMiddlePosition + 18}px)`;
            scrollerElement.style.webkitMaskImage = gradient;

            lastRenderTime.current = now;
            isRendering.current = false;
        });
    }, [newMessageList.length, type, scroller]);

    return { renderMask };
};

export default useMaskRenderer;
