import { useState, useEffect } from 'react';
import Taro from '@tarojs/taro';
import { MsgDataType } from '@/types/im';
import useAudioStore from '@/pages/chat/store/useAudioStore';

// 处理新手引导消息
const useGuideMessage = () => {
    const [guideMsgs, setGuideMsgs] = useState<MsgDataType[]>([]);

    useEffect(() => {
        Taro.eventCenter.on('showGuideMessage', (data) => {
            let index = 0;
            const msgCount = guideMsgs.length;
            if (msgCount > 0) {
                if (guideMsgs[0].messageClientId === 'loading') {
                    index = 1;
                }
            }
            setGuideMsgs([data, ...guideMsgs.slice(index)]);
            if (data.messageClientId === 'loading') {
                // 加载中动效
                return;
            }
            Taro.nextTick(() => {
                useAudioStore.getState().manualPlay(data, msgCount === 0);
            });
        });
        return () => {
            Taro.eventCenter.off('showGuideMessage');
        };
    }, [guideMsgs]);

    return { guideMsgs };
};

export default useGuideMessage;
