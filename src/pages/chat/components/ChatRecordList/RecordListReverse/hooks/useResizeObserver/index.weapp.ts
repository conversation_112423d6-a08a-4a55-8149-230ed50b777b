import { useEffect, useRef } from 'react';
import ResizeObserver from 'resize-observer-polyfill';
import Taro from '@tarojs/taro';
import useScrollStore from '@/pages/chat/store/useScrollStore';
import useDetailStore from '@/pages/chat/store/useDetailStore';

interface UseResizeObserverProps {
    scrollerOutter: React.RefObject<HTMLDivElement>;
}

// 监听Footer高度变化，移动列表
const useResizeObserver = ({ scrollerOutter }: UseResizeObserverProps) => {
    // Footer底部面板状态
    const panelStatus = useScrollStore((state) => state.panelStatus);
    const isKeyboardBounce = useDetailStore((state) => state.isKeyboardBounce);

    const timerRef = useRef<NodeJS.Timeout | null>(null);
    const lastHeightRef = useRef<number>(74); // 保存上一次的高度

    // 监听Footer组件高度变化，移动列表
    useEffect(() => {
        // 小程序环境使用Taro.createSelectorQuery和定时器
        const checkHeightChange = () => {
            const query = Taro.createSelectorQuery();
            query
                .select('#footerContainer')
                .boundingClientRect()
                .exec((res) => {
                    if (res && res[0]) {
                        const currentHeight = res[0].height;

                        // 计算高度变化量
                        const heightDiff = currentHeight - lastHeightRef.current;

                        // 更新scrollerOutter的位置
                        const query2 = Taro.createSelectorQuery();
                        query2
                            .select('#scrollerOutter')
                            .boundingClientRect()
                            .exec((res2) => {
                                if (res2 && res2[0]) {
                                    Taro.createSelectorQuery()
                                        .select('#scrollerOutter')
                                        .node()
                                        .exec((res3) => {
                                            if (res3 && res3[0] && res3[0].node) {
                                                // 使用setStyle设置transform
                                                res3[0].node.setStyle({
                                                    transform: `translateY(${heightDiff * -1}px)`,
                                                });
                                            }
                                        });
                                }
                            });
                        // 更新上一次高度的引用
                        lastHeightRef.current = currentHeight;
                    }
                });

            // 设置下一次检查
            timerRef.current = setTimeout(checkHeightChange, 200); // 每200ms检查一次
        };

        // 清理函数
        return () => {
            if (process.env.TARO_ENV !== 'h5') {
                // 清除定时器
                if (timerRef.current) {
                    clearTimeout(timerRef.current);
                    timerRef.current = null;
                }
            }
        };
    }, [scrollerOutter, isKeyboardBounce, panelStatus]);
};

export default useResizeObserver;
