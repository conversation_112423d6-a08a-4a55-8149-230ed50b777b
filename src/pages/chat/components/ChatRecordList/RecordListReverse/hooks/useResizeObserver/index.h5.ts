import { useEffect, useRef } from 'react';
import ResizeObserver from 'resize-observer-polyfill';
import Taro from '@tarojs/taro';
import useScrollStore from '@/pages/chat/store/useScrollStore';
import useDetailStore from '@/pages/chat/store/useDetailStore';

interface UseResizeObserverProps {
    scrollerOutter: React.RefObject<HTMLDivElement>;
}

// 监听Footer高度变化，移动列表
const useResizeObserver = ({ scrollerOutter }: UseResizeObserverProps) => {
    const observerRef = useRef<ResizeObserver | null>(null);
    // Footer底部面板状态
    const panelStatus = useScrollStore((state) => state.panelStatus);
    const isKeyboardBounce = useDetailStore((state) => state.isKeyboardBounce);

    // 监听Footer组件高度变化，移动列表
    useEffect(() => {
        const footerDom = document.getElementById('footerContainer'); // ALLOW document
        if (!footerDom) return;

        const lastHeight = 74;
        Taro.nextTick(() => {
            const observer = new ResizeObserver((entries) => {
                for (const entry of entries) {
                    const currentHeight = entry.target.getBoundingClientRect().height;

                    // 计算高度变化量
                    const heightDiff = currentHeight - lastHeight;

                    if (scrollerOutter.current) {
                        const scrollerOutterElement = scrollerOutter.current;
                        scrollerOutterElement.style.transform = `translateY(-${heightDiff}px)`;
                    }
                }
            });

            observer.observe(footerDom);

            observerRef.current = observer;
        });

        // eslint-disable-next-line consistent-return
        return () => {
            // 使用ref中保存的observer进行清理
            if (observerRef.current) {
                observerRef.current.disconnect();
            }
        };
    }, [scrollerOutter, isKeyboardBounce, panelStatus]);
};

export default useResizeObserver;
