import ErrorBoundary from '@/components/ErrorBoundary';
import { MsgDataType } from '@/types/im';
import { ScrollView, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';

import { useMessageStore } from '@/hooks/messageStore';

import useDetailStore from '@/pages/chat/store/useDetailStore';
import { isAndroidLower } from '@/pages/chat/utils';
import { useContextStore } from '@/components/storeContext/StoreContext';
import { ImmerseEnterAnimStoreCreator } from '@/pages/chat/store/useImmerseEnterAnim';
import classNames from 'classnames';
import useUserInfoStore from '@/store/useUserInfoStore';
import useInspirationStore from '../../store/useInspirationStore';
import useSwiperTouchStore from '../../store/useSwiperTouchStore';
import Inspiration from '../Inspiration';
import Loading from '../Loading';
import NewMsgButton from '../NewMsgButton';
import MsgItem from './MsgItem';

// 导入自定义 hooks
import useGuideMessage from './hooks/useGuideMessage';
import useMaskRenderer from './hooks/useMaskRenderer';
import useResizeObserver from './hooks/useResizeObserver';
import useScrollManager from './hooks/useScrollManager';

import './index.scss';

interface ChatRecordListProps {
    SummaryComp: any;
    type: string;
    action?: boolean;
}

export interface UserInfo {
    userBase: {
        avatarImgUrl: string;
        initTime: number;
        userId: string;
    };
}

// type必须从上级页面传进来，不能从store取，因为要共存两套样式；
const ChatRecordList = ({ SummaryComp, type, action }: ChatRecordListProps) => {
    const messageList = useMessageStore((state) => state.messageList);
    // 是否分页推送的消息，分页推送的不自动滚到底，收到消息才需要滚动到底
    const pushMsgByPage = useMessageStore((state) => state.pushMsgByPage);
    const firstPage = useDetailStore((state) => state.firstPage);
    const targetAvatar = useDetailStore((state) => state.robotInfo?.robotBaseInfo?.avatarUrl);
    const isMoreBounce = useDetailStore((state) => state.isMoreBounce);
    const isKeyboardBounce = useDetailStore((state) => state.isKeyboardBounce);

    // 新手引导消息
    const [newMessageList, setNewMessageList] = useState<MsgDataType[]>([]);

    // 灵感回复
    const inspirationShow = useInspirationStore((state) => state.show);
    const inspirationChange = useInspirationStore((state) => state.count);
    const list = useInspirationStore((state) => state.data);
    const self = useUserInfoStore.getState().userBaseInfo;

    const scroller = useRef<HTMLDivElement>(null);
    const scrollerContainer = useRef<HTMLDivElement>(null);

    // 监听底部面板Footer高度变化，控制列表transition动画
    const scrollerOutter = useRef<HTMLDivElement>(null);
    useResizeObserver({ scrollerOutter });

    // 处理新手引导消息
    const { guideMsgs } = useGuideMessage(type);

    // 渲染遮罩层
    const { renderMask } = useMaskRenderer({
        type,
        scroller,
        newMessageList,
    });

    // 使用滚动管理 hook
    const { handleScroll, scrollToBottom, chipScrollTop, immerseScrollTop } = useScrollManager({
        type,
        scroller,
        renderMask,
    });

    // 当滚动条滚了80px时，开始显示新消息按钮
    const showChipNewMsgBtn = useMemo(() => {
        return chipScrollTop < -80;
    }, [chipScrollTop]);

    const showImmerseNewMsgBtn = useMemo(() => {
        return immerseScrollTop < -80;
    }, [immerseScrollTop]);

    useEffect(() => {
        setNewMessageList([...messageList, ...guideMsgs]);
    }, [guideMsgs, messageList]);

    // 显示完整消息需要等滚动结束，避免闪屏
    const [showFullHistory, setShowFullHistory] = useState(false);

    // 分页消息更新不触发滚动到底
    // 首屏消息主动触发滚动到底，修复有时候无法定位到底的问题
    useEffect(() => {
        if (!newMessageList || newMessageList.length === 0) return;

        if (isAndroidLower) {
            setShowFullHistory(true);
        } else if (firstPage) {
            // 这里不要滚动到底，首屏直接定位到底，要节省时间；
            Taro.nextTick(() => {
                // 为什么设置为 -1 参考上面 scrollToBottom 的注释
                if (
                    scroller &&
                    scroller.current &&
                    typeof scroller.current.scrollTop === 'number'
                ) {
                    scroller.current.scrollTop = -1;
                }
                setShowFullHistory(true);
            });
        } else if (!pushMsgByPage) {
            Taro.nextTick(() => {
                scrollToBottom();
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [newMessageList, firstPage, pushMsgByPage]);

    // 渲染沉浸式特殊的遮罩层效果
    useEffect(() => {
        renderMask();

        const handleInspiration = () => {
            Taro.nextTick(() => {
                scrollToBottom();
            });
        };

        const handleTextFoldChange = () => {
            renderMask();
        };

        Taro.eventCenter.on('receiveNewMsgHandleInspiration', handleInspiration);
        Taro.eventCenter.on('textFoldChange', handleTextFoldChange);

        return () => {
            Taro.eventCenter.off('receiveNewMsgHandleInspiration', handleInspiration);
            Taro.eventCenter.off('textFoldChange', handleTextFoldChange);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [inspirationShow, list, newMessageList]);

    // 使用 ref 来跟踪是否是首次渲染
    const isFirstRender = useRef(true);
    // 触发条件，直接滚到底
    useEffect(() => {
        // 这里一定要屏蔽首次滚动，否则自动滚动会触发部分ios机型自动滚动白屏的bug
        if (isFirstRender.current) {
            isFirstRender.current = false;
            return;
        }

        Taro.nextTick(() => {
            scrollToBottom();
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isMoreBounce, isKeyboardBounce, inspirationShow, list, inspirationChange]);

    // 策划需求：沉浸模式显示完整消息要等视频加载以后再渐入
    const [showFullImmerseHistory, setShowFullImmerseHistory] = useState(false);

    // 在组件挂载后延迟显示完整历史
    useEffect(() => {
        Taro.eventCenter.on('standby_video_playing', () => {
            setShowFullImmerseHistory(true);
        });

        // 无论如何1s以后都显示消息
        const timer = setTimeout(() => {
            setShowFullImmerseHistory(true);
            setShowFullHistory(true);
        }, 1000);

        return () => {
            clearTimeout(timer);
            Taro.eventCenter.off('standby_video_playing');
        };
    }, []);

    const { progress }: any = useSwiperTouchStore();

    const useImmerseEnterAnimStore = useContextStore(ImmerseEnterAnimStoreCreator);
    const shouldPlay = useImmerseEnterAnimStore((state) => state.shouldPlay);
    const playRecordList = useImmerseEnterAnimStore((state) => state.playRecordList);

    return (
        <ErrorBoundary>
            <View
                ref={scrollerOutter}
                className={classNames(
                    `chat-outter chat-outter-${
                        showChipNewMsgBtn ? 'chipNewMsg' : ''
                    }  chat-outter-${showImmerseNewMsgBtn ? 'immerseNewMsg' : ''}`,
                    {
                        'immerse-record-list-enter-anim': shouldPlay,
                        active: playRecordList,
                    }
                )}>
                <View
                    ref={scrollerContainer}
                    id={`chatContainer_${type}`}
                    className="chat-container"
                    {...(type === 'chip' && showFullHistory
                        ? { style: { opacity: 1 - progress * 1.5 } }
                        : {})}>
                    <ScrollView
                        onScroll={handleScroll}
                        enhanced
                        scrollY
                        bounces={false}
                        pagingEnabled
                        scrollAnchoring
                        className={`chat-record chat-record-${type} chat-record-${type}-${
                            action ? 'active' : 'inactive'
                        } ${showFullImmerseHistory ? 'chat-record-immerse-show' : ''}`}
                        ref={scroller}>
                        {inspirationShow && <Inspiration type={type} />}
                        {newMessageList.map((msg, index) => {
                            if (msg.messageClientId === 'loading') {
                                return <Loading avatar={targetAvatar} type={type} key="loading" />;
                            }

                            const showLikeOrNot =
                                messageList.filter((item) => {
                                    return (
                                        item.contentExt?.content?.type === 'aigcCustomTextAudioMsg'
                                    );
                                })[0]?.messageClientId === msg.messageClientId;

                            return (
                                <MsgItem
                                    key={msg.messageClientId}
                                    msgData={msg}
                                    avatar={
                                        msg?.isSelf ? self?.userBase?.avatarImgUrl : targetAvatar
                                    }
                                    className="history-message"
                                    type={type}
                                    showLikeOrNot={showLikeOrNot}
                                />
                            );
                        })}
                        <SummaryComp />
                    </ScrollView>
                </View>
                <NewMsgButton scrollToBottom={scrollToBottom} />
            </View>
        </ErrorBoundary>
    );
};

export default memo(ChatRecordList);
