import { useCallback, useRef } from 'react';
import useScrollStore from '@/pages/chat/store/useScrollStore';
import { MsgDataType } from '@/types/im';

interface UseMaskRendererProps {
    type: string;
    scroller: React.RefObject<HTMLDivElement>;
    newMessageList: any[];
}

// 渲染沉浸式消息遮罩层
const useMaskRenderer = ({ type, scroller, newMessageList }: UseMaskRendererProps) => {
    const lastRenderTime = useRef(0);
    const isRendering = useRef(false);
    const immerseScrollTop = useScrollStore((state) => state.immerseScrollTop);

    // 渲染沉浸式特殊的遮罩层效果
    const renderMask = useCallback(() => {
        if (!scroller.current || type !== 'immerse') {
            // 非沉浸模式防止内容透到状态栏，也设一个固定的遮罩
            if (scroller.current) {
                // 使用临时变量避免直接修改参数属性
                const scrollerElement = scroller.current;
                scrollerElement.style.webkitMaskImage = '';
            }
            return;
        }

        const fixedMask = `-webkit-linear-gradient(top, rgba(0, 0, 0, 0) 0%,rgba(0, 0, 0, 0) 60px,rgba(0, 0, 0) 80px)`;
        const { offsetHeight: parentHeight, scrollHeight } = scroller.current as HTMLElement;

        const scrollerElement = scroller.current;
        // 如果内容不足一屏或消息数量少于3条，不显示遮罩
        if (parentHeight === scrollHeight || newMessageList.length < 3) {
            scrollerElement.style.webkitMaskImage = fixedMask;
            return;
        }

        // 使用requestAnimationFrame优化渲染
        if (isRendering.current) return;
        isRendering.current = true;

        requestAnimationFrame(() => {
            const now = Date.now();
            // 限制渲染频率，最小间隔16ms（约60fps）
            if (now - lastRenderTime.current < 16) {
                isRendering.current = false;
                return;
            }

            // 如果用户正在向上滚动，移除遮罩效果
            // if (isScrollingUp) {
            //     scrollerElement.style.webkitMaskImage = fixedMask;
            //     isRendering.current = false;
            //     return;
            // }

            // 获取倒数第二条消息元素
            const secondItem = scrollerElement.querySelectorAll('.chat-msg-item')?.[1];

            if (!secondItem) {
                // 如果没有找到倒数第二条消息，清除遮罩
                scrollerElement.style.webkitMaskImage = fixedMask;
                isRendering.current = false;
                return;
            }

            let maskMiddlePosition;

            // 获取倒数第二条消息的位置信息
            const { offsetTop } = secondItem as HTMLDivElement;

            // 倒数第二个元素在滚动距离为0时，距离底部的高度
            const offsetBottom = parentHeight - offsetTop;
            const actualScrollTop = scrollerElement.scrollTop;
            const maxScrollTop = scrollHeight - parentHeight;

            if (maxScrollTop <= 10 || actualScrollTop >= maxScrollTop - 10) {
                // 已经滚动到底部或接近底部，固定遮罩位置
                maskMiddlePosition = offsetBottom;
                // 当可滚动距离小于可展示区域时，按照比例缩小mask
            } else if (maxScrollTop < parentHeight - offsetBottom) {
                // 当可滚动距离小于可展示区域时，按比例缩小mask
                maskMiddlePosition =
                    offsetBottom +
                    (parentHeight - offsetBottom) *
                        (Math.abs(immerseScrollTop) / (scrollHeight - parentHeight));
            } else {
                maskMiddlePosition = offsetBottom - actualScrollTop;
            }

            maskMiddlePosition = Math.max(0, Math.min(parentHeight, maskMiddlePosition));

            scrollerElement.style.webkitMaskImage = `-webkit-linear-gradient(bottom, rgba(0, 0, 0) 0%,rgba(0, 0, 0) ${maskMiddlePosition}px,rgba(0, 0, 0, 0) ${
                maskMiddlePosition + 11
            }px)`;

            // let maskEndPosition;

            // if (maskMiddlePosition > parentHeight) {
            //     maskMiddlePosition = parentHeight - 30;
            //     maskEndPosition = parentHeight;
            // } else {
            //     // 当遮罩高度小于容器高度时
            //     maskEndPosition =
            //         maskMiddlePosition + Math.min(parentHeight * 0.05, Math.abs(immerseScrollTop));
            // }

            // // 向下滑动过程中，在一定范围内顶部需要固定遮罩
            // if (maskEndPosition > parentHeight - 20) {
            //     scrollerElement.style.webkitMaskImage = fixedMask;
            // } else {
            //     // - 20是为了解决个别机型滑动到底顶部能看到一点遮罩残影
            //     maskMiddlePosition = `${maskMiddlePosition}px`;
            //     maskEndPosition = `${maskEndPosition - 20}px`;
            //     // eslint-disable-next-line no-param-reassign
            //     scrollerElement.style.webkitMaskImage = `-webkit-linear-gradient(bottom, rgba(0, 0, 0) 0%,rgba(0, 0, 0) ${maskMiddlePosition},rgba(0, 0, 0, 0) ${maskEndPosition})`;
            // }

            lastRenderTime.current = now;
            isRendering.current = false;
        });
    }, [newMessageList.length, type, immerseScrollTop, scroller]);

    return { renderMask };
};

export default useMaskRenderer;
