import { useState, useEffect } from 'react';
import Taro from '@tarojs/taro';
import { MsgDataType } from '@/types/im';
import useAudioStore from '@/pages/chat/store/useAudioStore';
import { ChatType } from '@/pages/chat/type';

// 处理新手引导消息
const useGuideMessage = (type: string) => {
    const [guideMsgs, setGuideMsgs] = useState<MsgDataType[]>([]);

    useEffect(() => {
        if (type === ChatType.CHIP) {
            return undefined;
        }
        Taro.eventCenter.on('showGuideMessage', (data) => {
            let index = 0;
            const msgCount = guideMsgs.length;
            if (msgCount > 0) {
                if (guideMsgs[0].messageClientId === 'loading') {
                    index = 1;
                }
            }
            setGuideMsgs([data, ...guideMsgs.slice(index)]);
            if (data.messageClientId === 'loading') {
                // 加载中动效
                return;
            }
            Taro.nextTick(() => {
                useAudioStore.getState().manualPlay(data, msgCount === 0);
            });
        });
        return () => {
            Taro.eventCenter.off('showGuideMessage');
        };
    }, [type, guideMsgs]);

    return { guideMsgs };
};

export default useGuideMessage;
