import { useCallback, useRef, useState, useEffect } from 'react';
import Taro from '@tarojs/taro';
import { isAndroidLower } from '@/pages/chat/utils';
import useScrollStore, { InputPanelStatus } from '@/pages/chat/store/useScrollStore';
import useDetailStore from '@/pages/chat/store/useDetailStore';
import useInspirationStore from '@/pages/chat/store/useInspirationStore';

interface UseScrollManagerProps {
    type: string;
    scroller: React.RefObject<HTMLDivElement>;
    renderMask: () => void;
}

const useScrollManager = ({ type, scroller, renderMask }: UseScrollManagerProps) => {
    const [isScrollingUp, setIsScrollingUp] = useState(false);
    const prevScrollTopRef = useRef(0);

    const setChipScrollTop = useScrollStore((state) => state.setChipScrollTop);
    const setImmerseScrollTop = useScrollStore((state) => state.setImmerseScrollTop);
    const chipScrollTop = useScrollStore((state) => state.chipScrollTop);
    const immerseScrollTop = useScrollStore((state) => state.immerseScrollTop);

    // 键盘弹起：要以用户滚动为主
    // 更多面板打开：要以自动滚动到底为主
    // 灵感回复打开：要以自动滚动到底为主
    const isKeyboardBounce = useDetailStore((state) => state.isKeyboardBounce);
    const isMoreBounce = useDetailStore((state) => state.isMoreBounce);
    const inspirationShow = useInspirationStore((state) => state.show);

    // 标记是自动滚动，而不是用户主动滚动
    // 作用是滚动的过程中，不能拉取新消息
    const fromScrollToBottom = useRef(false);
    // 类似微信效果：用于标识是否正在自动滚动，自动滚动期间禁止用户滚动
    const isAutoScrolling = useRef(false);
    // 添加一个标记，表示用户是否正在主动滚动
    const isUserScrolling = useRef(false);

    // 添加一个定时器引用，用于跟踪用户滚动状态
    const userScrollingTimerRef = useRef<NodeJS.Timeout | null>(null);
    // 添加一个滚动定位定时器
    const scrollTopTimerRef = useRef<NodeJS.Timeout | null>(null);

    // 更新滚动位置的辅助函数
    const updateScrollPosition = useCallback(
        (scrollTop: number) => {
            if (type === 'chip') {
                setChipScrollTop(scrollTop);
            } else {
                setImmerseScrollTop(scrollTop);
            }
            prevScrollTopRef.current = scrollTop;
            renderMask();
        },
        [type, setChipScrollTop, setImmerseScrollTop, renderMask]
    );

    // 监听滚动事件
    const handleScroll = useCallback(
        (e: { detail: { scrollTop: any; scrollHeight: any } }) => {
            const { scrollTop = 0, scrollHeight = 0 } = e?.detail || {};

            // 标记用户正在滚动
            isUserScrolling.current = true;
            // 清除之前的用户滚动状态定时器
            if (userScrollingTimerRef.current) {
                clearTimeout(userScrollingTimerRef.current);
            }
            // 设置一个新的定时器，在用户停止滚动一段时间后重置状态
            userScrollingTimerRef.current = setTimeout(() => {
                isUserScrolling.current = false;
            }, 300); // 用户停止滚动1秒后认为滚动结束

            /// 判断滚动方向
            setIsScrollingUp(scrollTop < prevScrollTopRef.current);

            // 检查是否需要加载更多消息
            const actualScrollTop = scrollHeight + scrollTop;
            // 滚动中触发消息分页，滚动到底触发过程中不加载分页，只有用户手动滚动过程中才加载分页
            if (actualScrollTop <= 1500 && !fromScrollToBottom.current) {
                // 这里利用用户主动滚动来消除某些ios版本上的兼容性问题：
                // overflow-y: scroll;和flex-direction: column-reverse;冲突，会导致：滚动容器列表高度变化时，列表被动滚动后消息不可见
                Taro.eventCenter.trigger('scrollToPullMessage');
            }

            // 重置自动滚动标记
            if (scrollTopTimerRef.current) {
                clearTimeout(scrollTopTimerRef.current);
            }
            // 设置一个定时器，只有当用户停止滑动一段时间后才触发拉取
            scrollTopTimerRef.current = setTimeout(() => {
                fromScrollToBottom.current = false;
            }, 150);

            // 更新滚动位置
            updateScrollPosition(scrollTop);
        },
        [updateScrollPosition]
    );

    // 滚动到底部的函数
    const scrollToBottom = useCallback(() => {
        // 如果用户正在主动滚动，不执行自动滚动到底部；
        // 键盘弹起强制滚动到底
        // 面板打开强制滚动到底
        if (isUserScrolling.current && !isKeyboardBounce && !isMoreBounce && !inspirationShow) {
            return;
        }

        if (!scroller.current) return;

        const scrollerElement = scroller.current;

        // 设置滚动状态
        fromScrollToBottom.current = true;
        isAutoScrolling.current = true;

        // 滚动到底期间禁用触摸事件
        scrollerElement.style.pointerEvents = 'none';

        // 安卓6\7\8机型对flex-direction: column-reverse;布局滚动有问题，不做自动滚动操作
        if (!isAndroidLower && scrollerElement.scrollTo) {
            // 兼容部分 iOS 机型，不能滚动到 -1 位置；滚动 0 部分机型会出现白屏；所以执行两次
            scrollerElement.scrollTo({
                top: 0,
                behavior: 'smooth',
            });
            scrollerElement.scrollTo({
                top: -1,
                behavior: 'smooth',
            });
        } else if (!isAndroidLower) {
            // 兼容处理：如果scrollTo不可用，尝试使用scrollTop属性

            // NOTE: 在部分 iOS 系统版本上，当滚动容器具有 flex-direction: column-reverse 属性时，
            // 设置 scrollTop = 0 会导致滚动容器的内容直接消失，只有手动滚动一下才恢复。
            // 目前已知有这个问题的手机型号：
            // - iPhone 8 (iOS 16.7.10) "Mozilla/5.0 (iPhone; CPU iPhone OS 16_7_10 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 CloudMusic/0.1.1 NetEaseMirth/2.10.5"
            // 目前已知可以通过设置为 -1 的方式规避这个问题。
            // See: https://stackoverflow.com/questions/77229520/ios-safari-chrome-div-with-flex-directioncolumn-reverse-has-wrong-scroll-beh
            scrollerElement.scrollTop = -1;
        }

        // 重置滚动方向状态
        setIsScrollingUp(false);
        updateScrollPosition(0);

        // 设置一个定时器，在滚动完成后重置标志并恢复触摸事件
        setTimeout(() => {
            isAutoScrolling.current = false;
            if (scroller.current) {
                scrollerElement.style.pointerEvents = 'auto';
            }
        }, 300);
    }, [isKeyboardBounce, isMoreBounce, inspirationShow, scroller, updateScrollPosition]);

    // 离开chat页面重置滚动数据
    useEffect(() => {
        return () => {
            // 清除所有定时器
            if (userScrollingTimerRef.current) {
                clearTimeout(userScrollingTimerRef.current);
            }
            if (scrollTopTimerRef.current) {
                clearTimeout(scrollTopTimerRef.current);
            }
            setChipScrollTop(0);
            setImmerseScrollTop(0);
            useScrollStore.getState().panelStatus = InputPanelStatus.normal;
        };
    }, [setChipScrollTop, setImmerseScrollTop]);

    return {
        isScrollingUp,
        handleScroll,
        scrollToBottom,
        chipScrollTop,
        immerseScrollTop,
    };
};

export default useScrollManager;
