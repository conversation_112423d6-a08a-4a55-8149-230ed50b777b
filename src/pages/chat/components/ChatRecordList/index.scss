.chat-outter {
    height: 100%;
    overflow: hidden;
    margin-bottom: 10px;
    position: relative;
    will-change: transform;

    #chatContainer_chip {
        opacity: 0;
    }

    .go-to-newMsg {
        display: none;
    }

    .chat-container {
        height: 100%;
        position: relative;
        flex: 1;
        contain: strict;
        backface-visibility: hidden;
    }

    .chat-record {
        width: 100%;
        height: 100%;
        min-height: 100%;
        padding: 0 19px;
        position: relative;
        color: #fff;
        overflow-anchor: none;
        overflow-y: scroll;
        overscroll-behavior: contain;
        display: flex;
        flex-direction: column-reverse;
        -webkit-overflow-scrolling: touch;
        overscroll-behavior-y: none;

        // 添加内容区域的顶部间距
        &::after {
            content: '';
            display: block;
            height: 160px;
            width: 100%;
            flex-shrink: 0;
        }

        &-immerse {
            opacity: 0;

            // 切入沉浸式，并且视频加载完成后淡入
            &-active.chat-record-immerse-show {
                animation: fadeIn 0.2s ease forwards 0.3s;
            }

            // 滑出沉浸式淡出
            &-inactive.chat-record-immerse-show {
                opacity: 0;
            }
        }

        .chat-list {
            position: relative;
        }
    }

    @keyframes fadeOut {
        from {
            opacity: 1;
        }

        to {
            opacity: 0;
        }
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }

        to {
            opacity: 1;
        }
    }

    .immerse-record-list-enter-anim {
        opacity: 0;

        &.active {
            opacity: 1;
            transition: opacity 200ms;
        }
    }
}
