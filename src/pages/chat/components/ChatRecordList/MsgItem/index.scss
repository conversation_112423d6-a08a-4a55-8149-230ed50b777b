.chat-msg-item {
    text-align: center;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;

    &:not(:last-of-type) {
        margin-bottom: 12px;
    }

    .msg {
        width: 100%;
        display: flex;
        text-align: left;

        .cus-avatar {
            margin-right: 5px;
        }
    }

    .msgContainer {
        display: flex;
        flex-direction: column;
    }

    .voiceContainer {
        height: 19px;
        display: flex;
        flex-direction: row;
        align-items: center;
        align-self: flex-start;
        padding-left: 7px;
        padding-right: 7px;
        background: rgba(158, 158, 158, 0.6);
        backdrop-filter: blur(10px);
        // iOS特定样式
        @supports (-webkit-touch-callout: none) {
            -webkit-backdrop-filter: none;
        }
        border-radius: 19px;
        z-index: 1;
        margin-bottom: -10px;
    }

    .voiceTxt {
        font-size: 10px;
        font-weight: 500;
        color: #fff;
        font-family: 'Alibaba PuHuiTi', sans-serif;
        margin-left: 3px;
    }

    .voiceImg {
        width: 9px;
        height: 9px;
    }

    .popMask {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background-color: #000;
    }

    .opContainer {
        position: relative;

        .likeOrNotBubbleGuide {
            width: 198px;
            height: 32px;
            position: absolute;
            bottom: 58px;
        }

        .popContainer {
            position: absolute;
            height: 51px;
            left: 50%;
            bottom: 100%;
            transform: translate(-50%);
            z-index: 70;
            margin-bottom: 2px;
            transition: opacity 0.5s ease-in-out;

            &.show {
                opacity: 1;
            }

            &.hide {
                opacity: 0;
            }

            .arrowContainer {
                height: 5px;
                display: flex;
                justify-content: center;

                .arrowImg {
                    width: 14px;
                    height: 5px;
                }
            }

            .contentStrContainer {
                border-radius: 10px;
                height: 46px;
                padding: 5px;
                display: flex;
                align-items: center;
                background-color: #474747;
                backdrop-filter: blur(10px);
                // iOS特定样式
                @supports (-webkit-touch-callout: none) {
                    -webkit-backdrop-filter: none;
                }
                flex-direction: row;

                .singleOpContainerUp,
                .singleOpContainerDown,
                .singleOpContainerCopy {
                    width: 50px;
                    height: 38px;
                    flex-direction: column;
                    display: flex;
                    align-items: center;
                }

                .opTextUp,
                .opTextDown,
                .opTextCopy {
                    font-size: 10px;
                    color: #fff;
                    font-weight: 600;
                    margin-top: 2px;
                    font-family: 'PingFang SC', sans-serif;
                }

                .opImage {
                    width: 20px;
                    height: 20px;
                }
            }
        }

        .container {
            // min-width: 44px;
            width: fit-content;
            max-width: 262px;
            padding: 12px 15px 12px 15px;
            border-radius: 12px;
            background: #272727a0;
            backdrop-filter: blur(20px);
            @supports (-webkit-touch-callout: none) {
                -webkit-backdrop-filter: none; // 确保iOS上不使用backdrop-filter
            }
            // background-color: #000000;
            // width: auto;
            height: auto;
            position: relative;

            &.gift-container {
                display: flex;
                flex-direction: column;
                align-items: center;

                .message-img {
                    background-color: transparent;
                    // background-color: #fff;
                }

                .message {
                    font-size: 11px;
                    font-weight: 400;
                }

                .message-gift-number {
                    background-color: #00000032;
                    padding: 2px 4px 2px 4px;
                    position: absolute;
                    border-radius: 8px;
                    height: 16px;
                    width: auto;
                    top: 50px;
                    right: 12px;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .message-gift-number-text {
                        color: #fff;
                        font-size: 12px;
                        font-weight: 400;
                    }
                }


            }

            .msgContent {
                position: relative;
                width: auto;
                height: auto;
                display: flex;
                align-items: flex-start;
                justify-content: flex-start;
                flex-direction: column;
            }

            .favoirabilityView {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                margin-top: 6px;

                .favoirabilityScore {
                    margin-left: 14px;
                    font-family: 'PingFang SC', sans-serif;
                    font-size: 11px;
                    font-weight: normal;
                    line-height: 150%;
                    letter-spacing: 0;
                    font-variation-settings: 'opsz' auto;
                    color: #ff689e;
                }
            }

            .starIcon {
                position: absolute;
                bottom: -7px;
                left: -5px;
                width: 53px;
                height: 53px;
            }

            .starsIcon {
                position: absolute;
                right: -5px;
                bottom: 5px;
                width: 73px;
                height: 73px;
            }
        }
    }

    .date {
        font-size: 11px;
        opacity: 0.3;
        justify-self: center;
        width: 100%;
        margin-bottom: 12px;
    }

    .msgContentContainer {
        flex-direction: row;
        display: flex;
        align-items: center;
    }

    .msgBottomContainer {
        flex-direction: row;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        height: 32px;
        width: fit-content;

        .likeOrNot {
            flex-direction: row;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 52px;
            height: 22px;
            border-radius: 11px;
            background-color: #6e6e6e1a;

            .likeOrNotSep {
                width: 0.5px;
                height: 8px;
                background-color: #0000001a;
            }

            .likeOrNotItem {
                width: 25px;
                height: 22px;
                flex-direction: row;
                display: flex;
                justify-content: center;
                align-items: center;

                .likeOrNotIcon {
                    width: 12px;
                    height: 12px;
                }
            }
        }

        .likeOrNotGuide {
            width: 208px;
            height: 40px;
            position: absolute;
            left: 52px;
        }
    }

    .monologueOpContainer {
        align-self: center;
        position: relative;

        .monologuePopContainer {
            position: absolute;
            width: 128px;
            height: 55px;
            left: -70%;
            bottom: 100%;
            transform: translate(-50%);
            z-index: 70;
            margin-bottom: 2px;

            .monologueArrowContainer {
                height: 5px;
                display: flex;
                justify-content: flex-start;

                .monologueArrowImg {
                    width: 14px;
                    height: 5px;
                    margin-left: 97px;
                }
            }

            .monologueContentStrContainer {
                border-radius: 6px;
                height: 50px;
                padding-left: 10px;
                padding-right: 10px;
                padding-top: 10px;
                padding-bottom: 10px;
                display: flex;
                align-items: center;
                background-color: #fff;
                flex-direction: row;
                backdrop-filter: blur(10px);
                @supports (-webkit-touch-callout: none) {
                    -webkit-backdrop-filter: none;
                }

                .monologueContentStr {
                    font-size: 12px;
                    font-weight: 400;
                    color: #000;
                }
            }
        }
    }

    .monologueImage {
        width: 26px;
        height: 26px;
        margin-left: 5px;
        align-self: center;
    }

    .message {
        font-size: 13px;
        display: block;
        line-break: anywhere;
        word-wrap: break-word;
        word-break: break-word;
        white-space: pre-wrap;

        .richtext-msg {
            &-bold-text {
                color: #fff;
                font-weight: 600;
            }

            &-moving-text {
                color: #bcbcbc;
                font-weight: 400;
            }
        }
    }

    .message-img {
        background: #e5e5e5;
        border-radius: 12px;
    }

    &.my {
        .container {
            background: #ff689e;
            color: #fff;
            animation: none;
            font-weight: 600;

            .message {
                .richtext-msg {
                    &-bold-text {
                        color: rgba(255, 255, 255, 1);
                    }

                    &-moving-text {
                        color: rgba(255, 255, 255, 0.8);
                    }
                }
            }
        }

        .cus-avatar {
            margin-left: 5px;
        }

        .msgContainer {
            align-items: flex-end;
        }
    }

    .chat-msg-item-loading {
        width: 50px;
        height: 44px;
        border-radius: 12px;
        opacity: 1;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

.aigcIntimacyLimitIncr {
    width: calc(100vw - 38px);
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: auto;

    &-text {
        font-family: 'PingFang SC', sans-serif;
        font-size: 13px;
        font-weight: normal;
        text-align: center;
        letter-spacing: 0;
        color: #fff;
        opacity: 0.6;
    }
}
