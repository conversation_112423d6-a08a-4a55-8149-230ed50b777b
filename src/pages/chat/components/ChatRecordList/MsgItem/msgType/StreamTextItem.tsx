/* eslint-disable react/prop-types */
import React, { useEffect, useMemo, useState } from 'react';
import { View, Text } from '@tarojs/components';
import { MsgDataType } from '@/types/im';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import { debounce } from '@/utils';
import { useStreamingStore } from '@/hooks/useStreamingStore';
import { useTypewriterAnimation } from '@/hooks/useTypewriterAnimation';
import { getTextFromMessage, renderRichText } from '../utils';
import OpBubble from '../op/OpBubble';
import useOpStore from '../op/useOpStore';
import './StreamTextItem.scss';

interface StreamTextItemProps {
    msgData: MsgDataType;
    showLikeOrNot?: boolean;
}

const StreamTextItem: React.FC<StreamTextItemProps> = ({ msgData, showLikeOrNot }) => {
    // 订阅流式消息状态
    const streamingState = useStreamingStore((state) =>
        state.getStreamingState(msgData.messageClientId)
    );
    const animationConfig = useStreamingStore((state) => state.animationConfig);

    // 获取消息文本
    const originalText = useMemo(() => {
        return getTextFromMessage(msgData);
    }, [msgData]);

    // 决定要显示的文本
    const targetText = streamingState?.fullText || originalText;

    // 使用优化的打字机动画
    const { displayText, start, stop, isAnimating } = useTypewriterAnimation({
        text: targetText,
        onComplete: () => {
            // 动画完成时，标记为完成状态
            if (
                streamingState?.status ===
                V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_STREAMING
            ) {
                useStreamingStore.getState().completeStreaming(msgData.messageClientId);
            }
        },
        config: animationConfig,
        enabled: !!streamingState?.isAnimating,
    });

    // 操作面板相关
    const showBubbleItem = useOpStore((state) => state.msgData);
    const [operationShow, setOperationShow] = useState(false);
    const show = showBubbleItem?.messageClientId === msgData?.messageClientId;

    useEffect(() => {
        let timeoutId: NodeJS.Timeout | null = null;

        if (!show && operationShow) {
            timeoutId = setTimeout(() => {
                setOperationShow(false);
            }, 500);
        } else if (show) {
            setOperationShow(true);
        }

        return () => {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
        };
    }, [show, operationShow]);

    // 处理流式状态变化
    useEffect(() => {
        if (!streamingState) {
            return;
        }

        const { status } = streamingState;

        switch (status) {
            case V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_PLACEHOLDER:
                start(0);
                break;

            case V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_STREAMING:
                if (!isAnimating && streamingState.isAnimating) {
                    start(displayText.length);
                }
                break;

            case V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_COMPLETE:
            case V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_EXCEPTION:
            case V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_CANCEL:
                stop();
                break;
            default:
                break;
        }
    }, [streamingState, start, stop, isAnimating, displayText.length]);

    // 渲染文本内容，优化计算逻辑
    const textContent = useMemo(() => {
        const textToRender = streamingState?.isAnimating ? displayText : targetText;
        return renderRichText(textToRender);
    }, [displayText, targetText, streamingState?.isAnimating]);

    // 错误状态
    const errorMessage = streamingState?.error;

    // 早期返回优化
    if (!textContent && !errorMessage) {
        return null;
    }

    return (
        <View className="opContainer">
            {(show || operationShow) && (
                <OpBubble
                    msgData={msgData}
                    likeEnable={showLikeOrNot !== false}
                    cls={`${show ? 'show' : 'hide'}`}
                />
            )}
            <View
                className="container"
                onLongPress={debounce(() => {
                    useOpStore.getState().toggleOp(msgData);
                }, 300)}>
                <View className={`msgContent ${streamingState?.isAnimating ? 'streaming' : ''}`}>
                    {errorMessage ? (
                        <Text className="message error">{errorMessage}</Text>
                    ) : (
                        <Text className="message fade-in-text">{textContent}</Text>
                    )}
                </View>
            </View>
        </View>
    );
};

// 优化的 memo 比较，简化逻辑
export default React.memo(StreamTextItem, (prevProps, nextProps) => {
    // 消息ID不同，需要重渲染
    if (prevProps.msgData.messageClientId !== nextProps.msgData.messageClientId) {
        return false;
    }

    // 基础属性变化检查
    if (prevProps.showLikeOrNot !== nextProps.showLikeOrNot) {
        return false;
    }

    // 流式消息状态变化检查
    const prevStatus = prevProps.msgData.streamConfig?.status;
    const nextStatus = nextProps.msgData.streamConfig?.status;
    if (prevStatus !== nextStatus) {
        return false;
    }

    // 非流式消息文本内容变化检查
    if (
        !nextStatus ||
        nextStatus === V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_COMPLETE
    ) {
        return (prevProps.msgData.text || '') === (nextProps.msgData.text || '');
    }

    // 流式消息由 store 处理，避免重渲染
    return true;
});
