import React from 'react';
import { View, Text } from '@tarojs/components';
import { MsgDataType } from '@/types/im';

const IntimacyIncreaseItem = ({ msgData }: { msgData: MsgDataType }) => {
    const content = msgData.contentExt;
    const showText = content?.content?.content?.content;
    if (!showText) {
        return null;
    }

    return (
        <View className="aigcIntimacyLimitIncr">
            <Text className="aigcIntimacyLimitIncr-text">{`${showText}`}</Text>
        </View>
    );
};

export default React.memo(IntimacyIncreaseItem);
