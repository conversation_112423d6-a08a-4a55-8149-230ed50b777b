import React, { Fragment } from 'react';
import { View, Text, Image } from '@tarojs/components';
import { optimizeImage } from '@/utils/image';
import { MsgDataType } from '@/types/im';

const GiftItem = ({ msgData }: { msgData: MsgDataType }) => {
    const content = msgData.contentExt;
    const showText = content?.content?.content?.gift?.name;
    const showImage = content?.content?.content?.gift?.basicResource?.previewImg?.url;
    const giftNumber = content?.content?.content?.count || 1;
    const showWidth = 60;
    const showHeight = 60;
    const optimizedSrc = optimizeImage({
        src: showImage,
        width: showWidth,
        height: showHeight,
    });

    return (
        <Fragment>
            <View className="opContainer">
                {showText && (
                    <View className="container gift-container">
                        <Image
                            className="message-img"
                            mode="aspectFill"
                            src={optimizedSrc}
                            style={{ width: showWidth, height: showHeight }}
                        />
                        <Text className="message">{showText}</Text>
                        {giftNumber > 1 && (
                            <View className="message-gift-number">
                                <Text className="message-gift-number-text">{`x${giftNumber}`}</Text>
                            </View>
                        )}
                    </View>
                )}
            </View>
        </Fragment>
    );
};

export default React.memo(GiftItem);
