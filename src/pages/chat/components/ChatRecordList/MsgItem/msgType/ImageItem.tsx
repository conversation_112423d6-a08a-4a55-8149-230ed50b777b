import React, { Fragment, useEffect, useState } from 'react';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import { V2NIMMessageImageAttachment } from 'nim-web-sdk-ng/dist/esm/nim/src/V2NIMMessageService';
import Taro from '@tarojs/taro';
import { View, Text, Image } from '@tarojs/components';
import { debounce } from '@/utils';
import { MsgDataType } from '@/types/im';

import { jump2ImgPreview } from '@/router';
import OpBubble from '../op/OpBubble';
import useOpStore from '../op/useOpStore';
import { renderRichText } from '../utils';

const previewImageAction = (url: string, width: number, height: number) => {
    Taro.nextTick(() => {
        jump2ImgPreview({ url, width, height });
    });
};

const ImageItem = ({ msgData }: { msgData: MsgDataType }) => {
    const showBubbleItem = useOpStore((state) => state.msgData);

    let showText;
    let showImage;
    let imageWidth;
    let imageHeight;

    if (msgData.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_IMAGE) {
        showImage = (msgData.attachment as V2NIMMessageImageAttachment)?.url;
        imageWidth = (msgData.attachment as V2NIMMessageImageAttachment)?.width;
        imageHeight = (msgData.attachment as V2NIMMessageImageAttachment)?.height;
    } else {
        const content = msgData.contentExt;
        const customData = msgData?.customExt;
        showText = renderRichText(content?.content?.content?.text || '');
        showImage = content?.content?.content?.url;
        imageWidth = customData?.serverExt?.width;
        imageHeight = customData?.serverExt?.height;
    }

    let showWidth = 0;
    let showHeight = 0;
    if (Math.abs(imageWidth - imageHeight) < 2) {
        showWidth = 70;
        showHeight = 70;
    } else if (imageWidth > imageHeight) {
        showWidth = Math.ceil((imageWidth * 70) / imageHeight);
        showHeight = 70;
    } else {
        showWidth = 70;
        showHeight = Math.ceil((imageHeight * 70) / imageWidth);
    }

    const [opshow, setOpshow] = useState(false);
    const show =
        showBubbleItem !== undefined &&
        showBubbleItem?.messageClientId === msgData?.messageClientId;

    useEffect(() => {
        if (!show) {
            if (opshow) {
                setTimeout(() => {
                    setOpshow(false);
                }, 500);
            }
        } else {
            setOpshow(true);
        }
    }, [opshow, show]);

    return (
        <Fragment>
            <View
                className="container-img"
                onClick={() => previewImageAction(showImage, imageWidth, imageHeight)}>
                <Image
                    className="message-img"
                    mode="aspectFill"
                    src={showImage}
                    style={{ width: showWidth, height: showHeight }}
                />
            </View>
            <View className="opContainer">
                {(show || opshow) && (
                    <OpBubble
                        msgData={msgData}
                        cls={`${show ? 'show' : 'hide'}`}
                        likeEnable={false}
                    />
                )}
                {showText && (
                    <View
                        className="container"
                        onLongPress={debounce(() => {
                            useOpStore.getState().toggleOp(msgData);
                        }, 300)}>
                        <Text className="message">{showText}</Text>
                    </View>
                )}
            </View>
        </Fragment>
    );
};

export default React.memo(ImageItem);
