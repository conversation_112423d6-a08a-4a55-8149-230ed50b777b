import React, { useEffect, useMemo, useState } from 'react';
import { View, Text } from '@tarojs/components';
import { MsgDataType } from '@/types/im';
import { debounce } from '@/utils';
import { getTextFromMessage, renderRichText } from '../utils';
import OpBubble from '../op/OpBubble';
import useOpStore from '../op/useOpStore';

const TextItem = ({ msgData }: { msgData: MsgDataType }) => {
    const textMsg = useMemo(() => {
        const orginText = getTextFromMessage(msgData);
        return renderRichText(orginText);
    }, [msgData]);

    const showBubbleItem = useOpStore((state) => state.msgData);

    const [opshow, setOpshow] = useState(false);
    const show =
        showBubbleItem !== undefined &&
        showBubbleItem?.messageClientId === msgData?.messageClientId;

    useEffect(() => {
        if (!show) {
            if (opshow) {
                setTimeout(() => {
                    setOpshow(false);
                }, 500);
            }
        } else {
            setOpshow(true);
        }
    }, [opshow, show]);

    if (!textMsg) {
        return null;
    }

    return (
        <View className="opContainer">
            {(show || opshow) && (
                <OpBubble msgData={msgData} likeEnable={false} cls={`${show ? 'show' : 'hide'}`} />
            )}
            <View
                className="container"
                onLongPress={debounce(() => {
                    useOpStore.getState().toggleOp(msgData);
                }, 300)}>
                <View className="msgContent">
                    <Text className="message">{textMsg}</Text>
                </View>
            </View>
        </View>
    );
};

export default React.memo(TextItem);
