// 文字渐入效果
.fade-in-text {
    animation: text-fade-in 0.3s ease-out forwards;
    will-change: opacity; // GPU 加速
}

// 优化的文字渐入动画
@keyframes text-fade-in {
    0% {
        opacity: 0;
        transform: translateY(2px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

// 优化的流式消息内容样式
.msgContent {
    contain: layout style; // 限制重绘范围
    
    &.streaming {
        .fade-in-text {
            animation: text-fade-in 0.3s ease-out forwards;
            will-change: opacity, transform; // GPU 加速
        }
    }
    
    .message {
        &.error {
            color: #ff4d4f;
            font-style: italic;
        }
    }
}

// 优化的渐入动画
@keyframes fade-in {
    from {
        opacity: 0.8;
        transform: translateZ(0); // 强制启用 GPU 加速
    }
    to {
        opacity: 1;
        transform: translateZ(0);
    }
}

// 流式消息容器
.stream-message-container {
    position: relative;
    contain: layout style; // 优化渲染性能
}

// 渲染性能优化
.container {
    contain: layout style paint; // 优化重绘性能
}