import React, { useMemo, Fragment, useEffect, useCallback, useState } from 'react';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import Taro, { getStorageSync, showToast, setStorageSync, removeStorageSync } from '@tarojs/taro';
import { View, Text, Image } from '@tarojs/components';

import { useContextStore } from '@/components/storeContext/StoreContext';
import { ChatGuideMode, GuideTipStoreCreator } from '@/pages/chat/store/useGuideTipStore';
import { debounce } from '@/utils';
import { addClickLog } from '@/utils/logTool';
import { isToday } from '@/utils/timeHelper';

import { ChatType } from '@/pages/chat/type';
import useDetailStore from '@/pages/chat/store/useDetailStore';
import { MsgDataType, MessageErrorCode } from '@/types/im';
import { useDialog } from '@/components/dialog';
import playIcon from '@/assets/chat/ic_voice_playing.png';
import voiceIcon from '@/assets/chat/ic_voice_default.png';
import starIcon from '@/assets/chat/message_bubble_favoirability_star.png';
import starIconAni from '@/assets/chat/message_bubble_favoirability_star_anim.png';
import starsIconAni from '@/assets/chat/message_bubble_favoirability_star_right.png';
import monologueIcon from '@/assets/chat/ic_chat_entrance_monologue.png';
import monologueWhiteIcon from '@/assets/chat/ic_chat_entrance_monologue_white.png';
import likeNormalIcon from '@/assets/chat/ic_chat_op_like_normal.png';
import likeHighlightIcon from '@/assets/chat/ic_chat_op_like_highlight.png';
import notlikeNormalIcon from '@/assets/chat/ic_chat_op_not_like_normal.png';
import notlikeHighlightIcon from '@/assets/chat/ic_chat_op_not_like_highlight.png';
import likeOrNotGuide from '@/assets/chat/ic_chat_op_like_guide.png';

import { jump2Market } from '@/router';
import useUserInfoStore from '@/store/useUserInfoStore';
import { getTextFromMessage, renderRichText } from '../utils';
import { getAudioUrl } from '../../../../utils/getAudioUrl';
import useAudioStore from '../../../../store/useAudioStore';
import Monologue from '../monologue';
import useMonologueStore from '../monologue/useMonologueStore';
import MonologueConfirm from '../monologue/monologueConfirmDialog';
import OpBubble from '../op/OpBubble';
import useOpStore from '../op/useOpStore';
import OpDownReasonModal from '../op/opDownReasonModal';

import '../index.scss';

function convertMillisecondsToSeconds(milliseconds: number) {
    return Math.ceil(milliseconds / 1000);
}

const AudioTextItem = ({
    msgData,
    type,
    showLikeOrNot,
}: {
    msgData: MsgDataType;
    type: string;
    showLikeOrNot?: boolean;
}) => {
    const chatMode = useDetailStore((state) => state.chatMode);
    const serverExt = msgData.customExt?.serverExt;
    const playItem = useAudioStore((state) => state.playingItem);
    const favoirabilityInfo = useMemo(() => {
        return JSON.parse(serverExt?.numberInfo ?? '[]')?.filter(
            (item: any) => item.numberType === 1
        );
    }, [serverExt?.numberInfo]);
    const favoirabilityScore =
        favoirabilityInfo && favoirabilityInfo.length > 0 ? favoirabilityInfo[0]?.number : null;
    const incrType =
        favoirabilityInfo && favoirabilityInfo.length > 0 ? favoirabilityInfo[0]?.incrType : null;
    const sessionPeriodId = serverExt?.sessionPeriodId;
    const showFavoirabilityScore = favoirabilityScore !== null;
    const [needFavoirabilityAnimationPlay, setNeedFavoirabilityAnimationPlay] =
        React.useState(false);
    const textMsg = useMemo(() => {
        const orginText = getTextFromMessage(msgData);
        return renderRichText(orginText);
    }, [msgData]);
    const [showLikeOrNotGuide, setShowLikeOrNotGuide] = useState(false);
    const [messageLike, setMessageLike] = useState<boolean | null>(null);
    const opDownReasonDialog = useDialog(OpDownReasonModal);
    const guideTipStore = useContextStore(GuideTipStoreCreator);

    useEffect(() => {
        try {
            if (favoirabilityScore <= 0) {
                return;
            }
            const favoirabilityAnimationKey = `kfavoirabilityAnimationKey_${sessionPeriodId}_${msgData.messageClientId}`;
            const value = getStorageSync(favoirabilityAnimationKey);

            if (value !== null && value !== undefined && value !== '') {
                setNeedFavoirabilityAnimationPlay(true);
                Taro.eventCenter.trigger('taro_event_intimacy_increase_4304_score_animator', {
                    incrType,
                    idServer: msgData.messageClientId,
                });
                setTimeout(() => {
                    removeStorageSync(favoirabilityAnimationKey);
                }, 200);
            }
        } catch (e) {
            console.error('Error retrieving favoirability animation state:', e);
        }
    }, [favoirabilityScore, msgData, sessionPeriodId, needFavoirabilityAnimationPlay, incrType]);

    const duration = useMemo(() => {
        if (msgData?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_CUSTOM) {
            const content = msgData?.contentExt;
            if (content?.content?.type === 'aigcCustomTextAudioMsg') {
                return content?.content?.content?.duration;
            }
        }
        return 0;
    }, [msgData]);

    const showVoice = useMemo(() => {
        const value = getAudioUrl(msgData);
        return value !== null && value !== undefined && value !== '';
    }, [msgData]);

    const showMonologue = useMemo(() => {
        const value = msgData?.customExt?.serverExt?.monologueId;
        return value !== null && value !== undefined && value !== '' && value !== '0';
    }, [msgData]);

    const leftStarIcon = useMemo(() => {
        if (showFavoirabilityScore) {
            if (needFavoirabilityAnimationPlay) {
                return (
                    <Image src={`${starIconAni}?random=${Math.random()}`} className="starIcon" />
                );
            }
            return <Image src={starIcon} className="starIcon" />;
        }
        return null;
    }, [showFavoirabilityScore, needFavoirabilityAnimationPlay]);

    const rightStarIcon = useMemo(() => {
        // 这个神奇的random是因为taro的image组件不会重新加载图片，怎么都刷新，暂时通过这个方式来强制刷新
        if (showFavoirabilityScore && needFavoirabilityAnimationPlay) {
            return <Image src={`${starsIconAni}?random=${Math.random()}`} className="starsIcon" />;
        }
        return null;
    }, [showFavoirabilityScore, needFavoirabilityAnimationPlay]);

    const manualPlay = useCallback(() => {
        useAudioStore.getState().manualPlay(msgData);

        addClickLog('btn_ai_im_voice|page_ai_im|page_h5_biz');
    }, [msgData]);

    const showBubbleItem = useOpStore((state) => state.msgData);

    const monologueDialog = useDialog(Monologue);

    const monologueConfirmDialog = useDialog(MonologueConfirm);

    useEffect(() => {
        const handleCustomEvent = (data) => {
            if (
                data.msgId === msgData?.messageClientId &&
                type === useDetailStore.getState().chatMode
            ) {
                monologueDialog?.show(msgData);
            }
        };
        Taro.eventCenter.on('showMonologueDialog', handleCustomEvent);
        return () => {
            Taro.eventCenter.off('showMonologueDialog', handleCustomEvent);
        };
    }, [monologueDialog, msgData, type]);

    const monologueClick = useCallback(() => {
        useMonologueStore
            .getState()
            .fetchPermission(msgData)
            .then((res) => {
                if (res.unlock === true) {
                    monologueDialog?.show(msgData, res);
                } else {
                    monologueConfirmDialog?.show(msgData, res);
                }
            })
            .catch((error) => {
                if (error.code === MessageErrorCode.balaceInsufficient) {
                    showToast({
                        title: '体力不足',
                        icon: 'none',
                        duration: 2000,
                    });
                    jump2Market({});
                } else {
                    showToast({
                        title: error.message || '内心想法获取失败',
                        icon: 'none',
                        duration: 2000,
                    });
                }
            });
        addClickLog('btn_ai_im_os|page_ai_im|page_h5_biz');
    }, [monologueConfirmDialog, monologueDialog, msgData]);

    const messagelikeClick = useCallback(() => {
        useOpStore
            .getState()
            .likeActionWithoutBubble(msgData, messageLike === true ? 0 : 1)
            .then(() => {
                setMessageLike(messageLike === true ? null : true);
            });
    }, [msgData, messageLike]);

    const messageNotLikeClick = useCallback(() => {
        useOpStore
            .getState()
            .likeActionWithoutBubble(msgData, messageLike === false ? 0 : 2)
            .then((res: any) => {
                setMessageLike(messageLike === false ? null : false);
                if (messageLike !== false) {
                    opDownReasonDialog.show({ msgData });
                }
            });
    }, [msgData, messageLike, opDownReasonDialog]);

    const [opshow, setOpshow] = useState(false);
    const show =
        showBubbleItem !== undefined &&
        showBubbleItem?.messageClientId === msgData?.messageClientId;

    useEffect(() => {
        if (!show) {
            if (opshow) {
                setTimeout(() => {
                    setOpshow(false);
                }, 500);
            }
        } else {
            setOpshow(true);
        }
    }, [opshow, show]);

    useEffect(() => {
        function handleClickOutside() {
            setShowLikeOrNotGuide(false);
        }

        if (showLikeOrNot) {
            if (chatMode === ChatType.CHIP) {
                // 设置状态
                if (msgData.customExt?.serverExt?.opType === 1) {
                    setMessageLike(true);
                } else if (msgData.customExt?.serverExt?.opType === 2) {
                    setMessageLike(false);
                } else {
                    setMessageLike(null);
                }
            }

            // 是否需要展示引导 ，非首日登录 + 进入im + 缓存判断
            const initTime = useUserInfoStore.getState().userBaseInfo?.userBase?.initTime;
            const storageLikeOrNotGuideShowed = getStorageSync('storage.chat.likeOrNotGuideShowed');

            const needShowLikeOrNotGuide = !isToday(initTime) && !storageLikeOrNotGuideShowed;
            if (needShowLikeOrNotGuide) {
                if (chatMode === ChatType.CHIP && !guideTipStore.getState().hasShowedGuide) {
                    setStorageSync('storage.chat.likeOrNotGuideShowed', true);
                    setShowLikeOrNotGuide(true);
                    setTimeout(() => {
                        setShowLikeOrNotGuide(false);
                        document.removeEventListener('touchend', handleClickOutside);
                    }, 3000);
                    document.addEventListener('touchend', handleClickOutside);
                } else if (chatMode === ChatType.IMMERSE) {
                    if (guideTipStore.getState().guideMode === ChatGuideMode.Vote) {
                        setTimeout(() => {
                            useOpStore.getState().toggleOp(msgData, true);
                        }, 300);
                    }
                }
            }
        }

        return () => {
            document.removeEventListener('touchend', handleClickOutside);
        };
    }, [msgData, chatMode, showLikeOrNot, guideTipStore]);

    return (
        <Fragment>
            {showVoice && (
                <View className="voiceContainer" onClick={manualPlay}>
                    <Image
                        src={
                            playItem !== undefined &&
                            playItem?.messageClientId === msgData?.messageClientId
                                ? playIcon
                                : voiceIcon
                        }
                        className="voiceImg"
                    />
                    <Text className="voiceTxt">{`${convertMillisecondsToSeconds(duration)}"`}</Text>
                </View>
            )}
            <View className="opContainer">
                {(show || opshow) && (
                    <OpBubble
                        msgData={msgData}
                        cls={`${show ? 'show' : 'hide'}`}
                        showDownReasonAlert={() => {
                            opDownReasonDialog.show({ msgData });
                        }}
                    />
                )}
                <View className="msgContentContainer">
                    <View
                        className={
                            showFavoirabilityScore && needFavoirabilityAnimationPlay
                                ? 'container bgAnimation'
                                : 'container'
                        }
                        onLongPress={debounce(() => {
                            useOpStore.getState().toggleOp(msgData);
                        }, 300)}>
                        <View className="msgContent">
                            <Text className="message">{textMsg}</Text>
                            {showFavoirabilityScore && (
                                <View className="favoirabilityView">
                                    <Text className="favoirabilityScore">{`${
                                        favoirabilityScore > 0 ? '+' : ''
                                    }${favoirabilityScore}`}</Text>
                                </View>
                            )}
                        </View>
                        {leftStarIcon}
                        {rightStarIcon}
                    </View>
                    {showMonologue && (
                        <View className="monologueOpContainer">
                            <Image
                                src={
                                    chatMode === ChatType.CHIP ? monologueWhiteIcon : monologueIcon
                                }
                                className="monologueImage"
                                onClick={monologueClick}
                                style={{ marginTop: showVoice ? 8 : 0 }}
                            />
                        </View>
                    )}
                </View>
                {showLikeOrNot && type === 'chip' && (
                    <View className="msgBottomContainer">
                        <View className="likeOrNot">
                            <View className="likeOrNotItem" onClick={messagelikeClick}>
                                <Image
                                    src={messageLike === true ? likeHighlightIcon : likeNormalIcon}
                                    className="likeOrNotIcon"
                                />
                            </View>
                            <View className="likeOrNotSep" />
                            <View className="likeOrNotItem" onClick={messageNotLikeClick}>
                                <Image
                                    src={
                                        messageLike === false
                                            ? notlikeHighlightIcon
                                            : notlikeNormalIcon
                                    }
                                    className="likeOrNotIcon"
                                />
                            </View>
                        </View>
                        {showLikeOrNotGuide && (
                            <Image src={likeOrNotGuide} className="likeOrNotGuide" />
                        )}
                    </View>
                )}
            </View>
        </Fragment>
    );
};

export default React.memo(AudioTextItem);
