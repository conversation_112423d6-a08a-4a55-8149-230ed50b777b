import React, { useEffect, useState } from 'react';
import Taro from '@tarojs/taro';
import { remToPx } from '@/utils';
import useSceneStore from '@/pages/chat/store/useSceneStore';
import './index.scss';

const TextFold = ({
    content,
    id,
    type,
    theme = '',
}: {
    content: string;
    id: string;
    type: string;
    theme?: string;
}) => {
    const textRef = React.useRef<HTMLDivElement>(null);
    const [isFolded, setIsFolded] = React.useState(false);
    useEffect(() => {
        const textElement = textRef.current;
        if (textElement) {
            let height = remToPx(parseFloat(Taro.pxTransform(84)));
            if (theme === 'start') {
                height = remToPx(parseFloat(Taro.pxTransform(54)));
            }
            const isOverflowing = textElement.scrollHeight > height;
            setIsFolded(isOverflowing);
        }
    }, [content, theme]);

    // 使用id来获取状态
    const textFoldChecked = useSceneStore((state) => state.getTextFoldChecked(id));
    const setTextFoldChecked = useSceneStore((state) => state.setTextFoldChecked);
    const handleChange = (e: any) => {
        const checked = e.target.checked;
        // 传入id和checked状态
        setTextFoldChecked(id, checked);
        Taro.eventCenter.trigger('textFoldChange');
    };

    return (
        <div className="textFoldWrapper">
            <input
                id={`${id}-${type}`}
                checked={textFoldChecked}
                onChange={handleChange}
                className="textFoldExp"
                type="checkbox"
            />
            <div className={`textFoldText ${theme}`} ref={textRef}>
                {isFolded && <label className={`textFoldBtn ${theme}`} htmlFor={`${id}-${type}`} />}
                {content}
            </div>
        </div>
    );
};

export default React.memo(TextFold);
