.sceneEndContainer {
    width: calc(100vw - 40px);
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: auto;
    padding-top: 12px;
    padding-bottom: 12px;
    background: rgba(3, 3, 3, 0.2);
    backdrop-filter: blur(50px);
    @supports (-webkit-touch-callout: none) {
        -webkit-backdrop-filter: none; // 确保iOS上不使用backdrop-filter
    }

    .sceneEndHeader {
        width: 256px;
        height: 21px;
        align-self: center;
    }
}
