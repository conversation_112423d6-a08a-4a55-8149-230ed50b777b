.textFoldWrapper {
    display: flex;
    width: 100%;
    overflow: hidden;

    .textFoldText {
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: justify;
        color: #fff;
        position: relative;
        line-height: 21px;
        max-height: 84px;
        transition: 0.3s max-height ease-in-out;

        &.chip {
            color: #816d97;
        }

        &.chip::before {
            color: #fff;
        }

        &.start {
            line-height: 18px;
            max-height: 54px;
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
        }

        &.start::before {
            height: calc(100% - 17px);
            color: #000;
        }
    }

    .textFoldText::before {
        content: '';
        height: calc(100% - 19px);
        float: right;
    }

    .textFoldText::after {
        content: '';
        width: 999vw;
        position: absolute;
        margin-left: -100px;
    }

    .textFoldBtn {
        position: relative;
        float: right;
        clear: both;
        margin-left: 20px;
        font-size: 12px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.5);
        cursor: pointer;

        &.chip {
            color: rgba(129, 109, 151, 0.5);
        }

        &.chip::before {
            color: rgba(129, 109, 151, 0.5);
        }

        &.start {
            color: rgba(255, 255, 255, 0.3);
        }

        &.start::before {
            color: rgba(255, 255, 255, 0.6);
        }
    }

    .textFoldBtn::after {
        content: '展开';
    }

    .textFoldExp {
        display: none;
    }

    .textFoldExp:checked + .textFoldText {
        max-height: none;
    }

    .textFoldExp:checked + .textFoldText::after {
        visibility: hidden;
    }

    .textFoldExp:checked + .textFoldText .textFoldBtn::before {
        visibility: hidden;
    }

    .textFoldExp:checked + .textFoldText .textFoldBtn::after {
        content: '收起';
    }

    .textFoldBtn::before {
        content: '...';
        position: absolute;
        left: -8px;
        color: #fff;
        transform: translateX(-100%);
    }
}
