.sceneWrapper {
    position: relative;
    width: calc(100vw - 40px);
    background-color: rgba(40, 32, 50, 0.45);
    border-color: rgba(255, 255, 255, 0.5);
    border-radius: 20px;
    border-width: 0.5px;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.49) inset, 0 0 4px rgba(255, 255, 255, 0.33) inset;

    .sceneImmerseBg {
        position: absolute;
        width: 100%;
        height: 100%;
        max-height: 194px;
        left: 0;
        bottom: 0;
        object-fit: cover;
    }

    .sceneContainer {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;

        .sceneHeader {
            width: 100%;
            height: 77.2px;
            display: flex;
            justify-content: center;

            .sceneHeaderBg {
                width: 100%;
                height: 77.2px;
                border-top-right-radius: 20px;
                border-top-left-radius: 20px;
                object-fit: fill;
            }

            .sceneHeaderImage {
                height: 40.7px;
                width: 144.3px;
                margin-top: 5px;
                z-index: 10;
            }
        }

        .sceneStartBtn {
            width: 131.3px;
            height: 16px;
        }

        .sceneStart {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            align-self: center;

            .sceneStartImage {
                width: 147px;
                height: 34.3px;
            }

            .sceneStartText {
                text-align: center;
                font-size: 14px;
                color: #fff;
                font-weight: 600;
            }
        }

        .sceneContent {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            width: 100%;
            padding-top: 5px;
            padding-left: 15px;
            padding-right: 15px;
            margin-top: -36.5px;
            border-top-left-radius: 0;
            border-top-right-radius: 0;
            border-bottom-left-radius: 16px;
            border-bottom-right-radius: 16px;
        }
    }
}
