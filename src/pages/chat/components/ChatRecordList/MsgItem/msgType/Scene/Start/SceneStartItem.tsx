import React, { useMemo } from 'react';
import { View, Text, Image } from '@tarojs/components';
import { MsgDataType } from '@/types/im';
import IC_SCENE_START_HEADER_CHIP from '@/assets/chat/scene/ic_scene_start_chip.png';
import IC_SCENE_START_HEADER_IMMERSE from '@/assets/chat/scene/ic_scene_start_immerse.png';
import useDetailStore from '@/pages/chat/store/useDetailStore';
import { ChatType } from '@/pages/chat/type';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import './index.scss';
import TextFold from '../TextFold';

const SceneStartItem = ({ msgData, type }: { msgData: MsgDataType; type: string }) => {
    const chatMode = useDetailStore((state) => state.chatMode);

    const textMsg = useMemo(() => {
        let orginText = '';
        if (msgData?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_CUSTOM) {
            const content = msgData?.contentExt;
            if (content?.content?.type === 'sceneSettingStartMsg') {
                orginText = content?.content?.content?.settingContent;
            }
        }
        return orginText || '';
    }, [msgData]);

    return (
        <View className="sceneStartContainer">
            <Image
                className="sceneStartHeader"
                mode="aspectFill"
                src={
                    chatMode === ChatType.CHIP
                        ? IC_SCENE_START_HEADER_CHIP
                        : IC_SCENE_START_HEADER_IMMERSE
                }
            />
            {/* <Text className="sceneStartText">{textMsg}</Text> */}
            {type === ChatType.CHIP && <Text className="sceneStartText">{textMsg}</Text>}
            {type === ChatType.IMMERSE && (
                <TextFold
                    content={textMsg}
                    id={msgData.messageServerId}
                    type={type}
                    theme="start"
                />
            )}
        </View>
    );
};

export default React.memo(SceneStartItem);
