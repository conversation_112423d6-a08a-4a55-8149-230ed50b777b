import React from 'react';
import { View, Image } from '@tarojs/components';
import IC_SCENE_END_HEADER_CHIP from '@/assets/chat/scene/ic_scene_end_chip.png';
import IC_SCENE_END_HEADER_IMMERSE from '@/assets/chat/scene/ic_scene_end_immerse.png';
import useDetailStore from '@/pages/chat/store/useDetailStore';
import { ChatType } from '@/pages/chat/type';
import './index.scss';

const SceneEndItem = () => {
    const chatMode = useDetailStore((state) => state.chatMode);
    return (
        <View className="sceneEndContainer">
            <Image
                className="sceneEndHeader"
                mode="aspectFill"
                src={
                    chatMode === ChatType.CHIP
                        ? IC_SCENE_END_HEADER_CHIP
                        : IC_SCENE_END_HEADER_IMMERSE
                }
            />
        </View>
    );
};

export default React.memo(SceneEndItem);
