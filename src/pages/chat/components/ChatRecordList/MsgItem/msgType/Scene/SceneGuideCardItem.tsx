import React, { Fragment, useMemo } from 'react';
import { View, Text, Image } from '@tarojs/components';
import { MsgDataType } from '@/types/im';
import useScenarioStore from '@/pages/chat/store/useScenarioStore';
import NIMService from '@/hooks/useNewNIM';
import BH_SCENE_HEADER_IMAGE from '@/assets/chat/scene/bg_chat_scene_header.png';
import ICON_SCENE_HEADER_IMAGE from '@/assets/chat/scene/ic_chat_scene_header_image.png';
import ICON_SCENE_START_BTN from '@/assets/chat/scene/ic_chat_scene_start_btn.png';
import ICON_SCENE_START_BTN_CHIP from '@/assets/chat/scene/ic_chat_scene_start_btn_chip.png';
import BG_SCENE_IMMERSE from '@/assets/chat/scene/bg_chat_scene_immerse.png';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import Taro, { getCurrentInstance, showToast } from '@tarojs/taro';
import { addClickLog } from '@/utils/logTool';
import EventTrackView from '@/components/EventTrack';
import { ChatType } from '@/pages/chat/type';
import './index.scss';
import TextFold from './TextFold';

const SceneGuidCardItem = ({ msgData, type }: { msgData: MsgDataType; type: string }) => {
    const textMsg = useMemo(() => {
        let orginText = '';
        if (msgData?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_CUSTOM) {
            const content = msgData?.contentExt;
            if (content?.content?.type === 'sceneSettingNoticeMsg') {
                orginText = content?.content?.content?.settingContent;
            }
        }
        return orginText || '';
    }, [msgData]);

    const handleClick = () => {
        const robotUserId = getCurrentInstance().router?.params?.robotUserId;
        useScenarioStore
            .getState()
            .postStartScenario(robotUserId, '0', textMsg, true)
            .then((res) => {
                NIMService.deleteMessage(msgData);
            })
            .catch((error) => {
                console.log('开启场景异常error', error);
                showToast({
                    title: error?.message || '开启场景异常，请稍后再试',
                    icon: 'none',
                    duration: 2000,
                });
            });
        addClickLog('btn_ai_im_scenceguide|mod_ai_im_scenceguide|page_ai_im|page_h5_biz');
    };

    return (
        <Fragment>
            <EventTrackView
                params={{
                    _spm: 'mod_ai_im_scenceguide|page_ai_im|page_h5_biz',
                }}
                isPage={false}>
                <View className="sceneWrapper">
                    {type === ChatType.IMMERSE && (
                        <Image className="sceneImmerseBg" src={BG_SCENE_IMMERSE} />
                    )}
                    <View className="sceneContainer">
                        {type === ChatType.IMMERSE && (
                            <View className="sceneHeader">
                                <Image className="sceneHeaderImage" src={ICON_SCENE_HEADER_IMAGE} />
                            </View>
                        )}
                        {type === ChatType.CHIP && (
                            <View className="sceneHeader">
                                <Image className="sceneHeaderBg" src={BH_SCENE_HEADER_IMAGE} />
                                <Image className="sceneHeaderImage" src={ICON_SCENE_HEADER_IMAGE} />
                            </View>
                        )}
                        <View className="sceneContent">
                            <TextFold
                                content={textMsg}
                                id={msgData.messageServerId}
                                type={type}
                                theme={type}
                            />
                        </View>
                        <View className="sceneStart" onClick={handleClick}>
                            <EventTrackView
                                params={{
                                    _spm: 'btn_ai_im_scenceguide|mod_ai_im_scenceguide|page_ai_im|page_h5_biz',
                                }}
                                isPage={false}>
                                {type === ChatType.IMMERSE && (
                                    <Image className="sceneStartImage" src={ICON_SCENE_START_BTN} />
                                )}
                                {type === ChatType.CHIP && (
                                    <Image
                                        className="sceneStartImage"
                                        src={ICON_SCENE_START_BTN_CHIP}
                                    />
                                )}
                            </EventTrackView>
                        </View>
                    </View>
                </View>
            </EventTrackView>
        </Fragment>
    );
};

export default React.memo(SceneGuidCardItem);
