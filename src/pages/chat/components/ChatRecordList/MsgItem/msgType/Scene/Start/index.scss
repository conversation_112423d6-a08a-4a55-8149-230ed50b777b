.sceneStartContainer {
    width: calc(100vw - 40px);
    border-radius: 12px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    height: auto;
    padding-top: 12px;
    padding-bottom: 12px;
    padding-left: 17px;
    padding-right: 17px;
    background: rgba(3, 3, 3, 0.4);
    backdrop-filter: blur(50px);
    @supports (-webkit-touch-callout: none) {
        -webkit-backdrop-filter: none; // 确保iOS上不使用backdrop-filter
    }

    .sceneStartHeader {
        width: 256px;
        height: 21px;
        align-self: center;
    }

    .sceneStartText {
        width: 100%;
        display: block;
        word-break: break-all;
        font-size: 12px;
        line-height: 18px;
        margin-top: 7px;
        color: rgba(255, 255, 255, 0.72);
        text-align: start;
    }
}
