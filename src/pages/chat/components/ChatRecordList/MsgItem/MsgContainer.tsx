import React, { useCallback } from 'react';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import classNames from 'classnames';
import { View, Image } from '@tarojs/components';
import { MsgDataType } from '@/types/im';
import NIMService from '@/hooks/useNewNIM';
import Avatar from '@/components/avatar';
import messageSendError from '@/assets/chat/message_send_error.png';
import messageSendLoading from '@/assets/chat/message_send_loading.png';
import { jump2Profile } from '@/router';
import useDetailStore from '../../../store/useDetailStore';

import './index.scss';

const MsgContainer = ({
    msgData,
    avatar,
    children,
    className,
    type,
}: {
    msgData: MsgDataType;
    avatar: string | undefined;
    children: React.ReactNode;
    className?: string;
    type: string | undefined;
}) => {
    const isMySide = msgData?.isSelf === true;

    const clickAvatarAction = useCallback(() => {
        if (isMySide) {
            return;
        }
        const userId = useDetailStore.getState().userId;
        jump2Profile({
            robotUserId: userId,
            hasAiChapter: null,
            fromSource: 'chat',
        });
    }, [isMySide]);

    const clickSendErrorAction = () => {
        NIMService.resendMessage(msgData);
    };

    const isSending =
        msgData.sendingState ===
        V2NIMConst.V2NIMMessageSendingState.V2NIM_MESSAGE_SENDING_STATE_SENDING;
    const sendError =
        msgData.sendingState ===
        V2NIMConst.V2NIMMessageSendingState.V2NIM_MESSAGE_SENDING_STATE_FAILED;

    return (
        <View
            key={msgData.messageClientId}
            className={classNames('chat-msg-item', className, {
                my: !!isMySide,
            })}>
            <View
                className="msg"
                style={{
                    flexDirection: !isMySide ? 'row' : 'row-reverse',
                }}>
                {avatar && (
                    <Avatar lazyload={false} src={avatar || ''} onClick={clickAvatarAction} />
                )}
                <View className="msgContainer">{children}</View>
                {isMySide && (sendError || isSending) && (
                    <Image
                        src={sendError ? messageSendError : messageSendLoading}
                        className="w-14 h-14 mr-4"
                        style={{ alignSelf: 'center' }}
                        onClick={sendError ? clickSendErrorAction : undefined}
                    />
                )}
            </View>
        </View>
    );
};

export default React.memo(MsgContainer);
