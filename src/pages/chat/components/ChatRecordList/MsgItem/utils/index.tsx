import React from 'react';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import { View, Text } from '@tarojs/components';
import { MsgDataType } from '@/types/im';
import { TextContent } from '@/pages/system-notify';

export function getTextFromMessage(msgData: MsgDataType) {
    let orginText = '';
    if (msgData?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_CUSTOM) {
        const content = msgData?.contentExt;
        if (content?.content?.type === 'aigcCustomTextAudioMsg') {
            orginText = content?.content?.content?.text;
        } else if (content?.content?.type === 'secretaryText') {
            const textContent = content?.content?.content as TextContent;
            orginText = `${(textContent.template ?? '') + (textContent.bottom ?? '')}`;
        }
    } else if (msgData?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_TEXT) {
        orginText = msgData?.text ?? '';
    }
    return orginText;
}

export function renderRichText(orginText: string) {
    if (!orginText || orginText === '') {
        return null;
    }
    const parts = orginText.split(/[(（[【{]([^)）\]】]*)[)）\]】}]/g);
    const matches = orginText.match(/[(（[【{]([^)）\]】]*)[)）\]】}]/g);

    if (matches && parts.length === matches.length * 2 + 1) {
        return (
            <View className="richtext-msg">
                {parts.map((part, index) => {
                    if (index % 2 === 0) {
                        return (
                            // NOTE: 如果两个 part 都是 "" 空字符串，React 会报错 two children with the same key
                            // eslint-disable-next-line react/no-array-index-key
                            <Text key={index} className="richtext-msg-bold-text">
                                {part}
                            </Text>
                        );
                    }
                    return (
                        // eslint-disable-next-line react/no-array-index-key
                        <Text key={index} className="richtext-msg-moving-text">
                            {matches[Math.floor(index / 2)]}
                        </Text>
                    );
                })}
            </View>
        );
    }
    return orginText;
}

export default {};
