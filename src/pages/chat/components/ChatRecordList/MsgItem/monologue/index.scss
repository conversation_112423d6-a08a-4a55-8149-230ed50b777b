.monologueContainer {
    .custom-modal {
        .at-modal__overlay {
            background-color: rgba(0, 0, 0, 0.5);
        }

        .at-modal__container {
            width: 80%;
            max-width: 300px;
            border-radius: 20px;
        }

        .close-container {
            position: relative;
        }

        .close-icon {
            width: 26px;
            height: 26px;
            z-index: 1;
            position: absolute;
            right: 20px;
            top: 20px;
        }

        .modal-content {
            padding: 0 0 16px 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            background: linear-gradient(180deg, #ffc4d9, #ffe6ef, #ffe8f1);

            .loadImg {
                width: 30px;
                height: 30px;
            }

            .text-title {
                font-size: 16px;
                font-weight: 600;
                color: #26262a;
                margin-top: 40px;
                margin-left: 24px;
                margin-right: 16px;
                text-align: left;
                font-family: 'PingFang SC', sans-serif;
            }

            .text-container {
                width: calc(100% - 39px);
                margin-left: 18px;
                margin-right: 21px;
                background-color: white;
                border-radius: 12px;
                padding-left: 14px;
                padding-right: 14px;
                padding-top: 26px;
                padding-bottom: 16px;
                display: flex;
                flex-direction: row;
            }

            .text-content {
                font-size: 13px;
                color: #26262a;
                text-align: left;
                margin-right: 21px;
                font-family: 'PingFang SC', sans-serif;
                word-break: break-all;
            }

            .avatar-container {
                width: 100%;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
                z-index: 1;
                margin-bottom: -29px;
                margin-top: 12px;
            }

            .avatar {
                margin-left: 30px;
                border: 2px solid #fff;
                width: 52px;
                height: 52px;
                box-sizing: border-box;
                border-radius: 50%;
                box-shadow: 0 0 10px 0 rgba(147, 147, 147, 0.3);
                object-fit: cover;
            }

            .cloud-icon {
                width: 35px;
                height: 31px;
                margin-top: 2px;
                margin-right: 19px;
            }

            .button-container {
                width: calc(100% - 6px);
                margin-right: 6px;
                display: flex;
                flex-direction: row;
                justify-content: flex-end;
                align-items: center;
            }

            .button {
                width: 60px;
                height: 40px;
                margin-right: 12px;
                background-color: white;
                border-radius: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 10px;
            }

            .button-icon {
                width: 24px;
                height: 24px;
            }
        }
    }
}

.commonConfirmContainer {
    .custom-modal {
        .at-modal__overlay {
            background-color: rgba(0, 0, 0, 0.5);
        }

        .at-modal__container {
            width: 80%;
            max-width: 300px;
            border-radius: 20px;
        }

        .modal-content {
            padding-left: 20px;
            padding-right: 20px;
            padding-top: 50px;
            padding-bottom: 30px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: white;
        }

        .text-title {
            font-size: 18px;
            text-align: center;
            font-weight: 600;
            color: black;
        }

        .text-content {
            margin-top: 12px;
            font-size: 13px;
            text-align: center;
            color: rgba(0, 0, 0, 0.4);
        }

        .btn-group {
            display: flex;
            justify-content: space-between;
            width: 100%;
            margin-top: 30px;
        }

        .cancel,
        .confirm {
            width: 45%;
            height: 44px;
            font-size: 16px;
            border-radius: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }

        .cancel {
            background-color: #bfbfbf;
            color: white;
        }

        .confirm {
            background-color: #ff689e;
            color: white;
        }
    }
}
