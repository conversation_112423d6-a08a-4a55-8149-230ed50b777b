import React, { useCallback, useMemo } from 'react';
import { AtModal } from 'taro-ui';
import Taro from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { MsgDataType } from '@/types/im';
import { CreateModalProps } from '@/components/dialog';
import useDetailStore from '../../../../store/useDetailStore';
import './index.scss';

interface MonologueConfirmContentProps {
    isOpened?: boolean;
    dismiss: () => void;
    onClose?: () => void;
    data: {
        remainFreeCount: number;
        freeCount: number;
        amount: number;
        unlock?: boolean;
    };
    msgData: MsgDataType;
}
const MonologueConfirmContent = ({
    isOpened,
    dismiss,
    onClose,
    data,
    msgData,
}: MonologueConfirmContentProps) => {
    const gender = useDetailStore((state) => state.robotInfo?.robotBaseInfo.gender);

    const costText = useMemo(() => {
        if (data.remainFreeCount <= 0) {
            return `消耗${data.amount}`;
        }
        return '';
    }, [data]);

    const confirm = useCallback(() => {
        Taro.eventCenter.trigger('showMonologueDialog', {
            msgId: msgData?.messageClientId,
            type: useDetailStore.getState().chatMode,
        });
        dismiss();
    }, [dismiss, msgData?.messageClientId]);

    return (
        <View className="monologueConfirmContainer">
            <AtModal
                isOpened={isOpened}
                className="custom-modal"
                onClose={onClose}
                closeOnClickOverlay={false}>
                <View className="modal-content">
                    <Text className="text-title">
                        确定{costText}查看{gender === 1 ? '他' : '她'}的内心OS吗？
                    </Text>
                    <Text className="text-content">
                        今日免费次数{data.remainFreeCount}/{data.freeCount}
                    </Text>
                    <View className="btn-group">
                        <View className="cancel" onClick={dismiss}>
                            取消
                        </View>
                        <View className="confirm" onClick={confirm}>
                            确定
                        </View>
                    </View>
                </View>
            </AtModal>
        </View>
    );
};

const MonologueConfirm: CreateModalProps = {
    type: 'modal_custom',
    isModal: true,
    render(dismiss, msgData, data) {
        return (
            <MonologueConfirmContent
                dismiss={dismiss}
                isOpened={undefined}
                onClose={undefined}
                data={data}
                msgData={msgData}
            />
        );
    },
};

export default MonologueConfirm;
