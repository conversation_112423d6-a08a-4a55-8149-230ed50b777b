import React, { useCallback, useEffect } from 'react';
import { AtModal } from 'taro-ui';
import { showToast } from '@tarojs/taro';
import { View, Text, Image } from '@tarojs/components';

import { getImage } from '@/utils/image';
import { MessageErrorCode, MsgDataType } from '@/types/im';
import { CreateModalProps } from '@/components/dialog';
import Avatar from '@/components/avatar';

import cloudIcon from '@/assets/chat/ic_chat_monologue_cloud.png';
import upIcon from '@/assets/chat/ic_chat_monologue_up.png';
import upPinkIcon from '@/assets/chat/ic_chat_monologue_up_pink.png';
import downIcon from '@/assets/chat/ic_chat_monologue_down.png';
import downDarkIcon from '@/assets/chat/ic_chat_monologue_down_dark.png';
import closeIcon from '@/assets/common/ic_dialog_close.png';
import Spin_Loading_Chat from '@/assets/effect/spining_loading_chat.png';

import { jump2Market } from '@/router';
import useMonologueStore from './useMonologueStore';
import useDetailStore from '../../../../store/useDetailStore';
import './index.scss';

interface MonologueContentProps {
    isOpened?: boolean;
    dismiss: () => void;
    onClose?: () => void;
    msgData: MsgDataType;
    unlockData?: any; // 可以根据实际类型进一步定义
}

const MonologueContent = ({
    isOpened,
    dismiss,
    onClose,
    msgData,
    unlockData,
}: MonologueContentProps) => {
    const likeAction = () => {
        useMonologueStore.getState().likeAction(1);
    };

    const unlikeAction = () => {
        useMonologueStore.getState().likeAction(2);
    };

    const robotInfo = useDetailStore((state) => state.robotInfo?.robotBaseInfo);

    const monologueContent = useMonologueStore((state) => state.data?.monologueContent);

    const monologueState = useMonologueStore((state) => state.data?.feedBackStatus);

    const isShowEmpty = useMonologueStore((state) => state.loading);

    const handleDismiss = useCallback(() => {
        dismiss();
    }, [dismiss]);

    useEffect(() => {
        useMonologueStore.getState().userLoad(msgData, unlockData);
        if (!unlockData) {
            useMonologueStore
                .getState()
                .fetchMonologue(msgData)
                .catch((err) => {
                    if (err.code === MessageErrorCode.balaceInsufficient) {
                        showToast({
                            title: '体力不足',
                            icon: 'none',
                            duration: 2000,
                        });
                        jump2Market({});
                    } else {
                        showToast({
                            title: err.message || '内心想法获取失败',
                            icon: 'none',
                            duration: 2000,
                        });
                    }
                    handleDismiss();
                });
        }
    }, [msgData, dismiss, unlockData, handleDismiss]);

    return (
        <View className="monologueContainer">
            <AtModal
                isOpened={isOpened}
                className="custom-modal"
                onClose={onClose}
                closeOnClickOverlay={false}>
                <View className="close-container">
                    <Image src={closeIcon} className="close-icon" onClick={() => handleDismiss()} />
                    <View className="modal-content">
                        <Text className="text-title">{robotInfo?.nickname}此时的内心想法是…</Text>
                        <View className="avatar-container">
                            <Avatar
                                width={52}
                                height={52}
                                lazyload
                                className="avatar"
                                src={getImage(robotInfo?.avatarUrl || '')}
                            />
                            <Image src={cloudIcon} className="cloud-icon" />
                        </View>
                        <View className="text-container">
                            {isShowEmpty && <Image src={Spin_Loading_Chat} className="loadImg" />}
                            <Text className="text-content">{monologueContent || ''}</Text>
                        </View>
                        <View className="button-container">
                            <View className="button" onClick={() => likeAction()}>
                                <Image
                                    src={monologueState === 'like' ? upPinkIcon : upIcon}
                                    className="button-icon"
                                />
                            </View>
                            <View className="button" onClick={() => unlikeAction()}>
                                <Image
                                    src={monologueState === 'unlike' ? downDarkIcon : downIcon}
                                    className="button-icon"
                                />
                            </View>
                        </View>
                    </View>
                </View>
            </AtModal>
        </View>
    );
};

const Monologue: CreateModalProps = {
    type: 'modal_custom',
    isModal: true,
    render(dismiss, msgData, unlockData) {
        return (
            <MonologueContent
                dismiss={dismiss}
                isOpened={undefined}
                onClose={undefined}
                msgData={msgData}
                unlockData={unlockData}
            />
        );
    },
};

export default Monologue;
