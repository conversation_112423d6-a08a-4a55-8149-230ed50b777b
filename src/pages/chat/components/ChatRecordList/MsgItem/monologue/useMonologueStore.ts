import { create } from 'zustand';
import { MsgDataType } from '@/types/im';
import Taro, { showToast } from '@tarojs/taro';
import { getMonologue, getMonologuePermission } from '@/service/imApi';
import * as Api from '../../../../api';
import useDetailStore from '../../../../store/useDetailStore';
import type { MonologueData, MonologueStatusData } from './types';

interface MonologueState {
    msgData?: MsgDataType;
    data?: MonologueData;
    unlockData?: MonologueStatusData;
    showBubbleId: string;
    neesShowBubble: boolean;
    loading: boolean;
    userLoad: (msgData?: MsgDataType, unlock?: MonologueStatusData) => void;
    userLeave: () => void;
    closeHintBubble: () => void;
    fetchPermission: (msgData?: MsgDataType) => Promise<MonologueStatusData>;
    fetchMonologue: (msgData?: MsgDataType) => Promise<void>;
    likeAction: (action: number) => Promise<void>;
}

const useMonologueStore = create<MonologueState>((set, get, store) => {
    let currentUserId = '';
    useDetailStore.subscribe((state, prev) => {
        if (state.userId !== prev.userId || currentUserId !== state.userId) {
            if (currentUserId) {
                set(store.getInitialState());
            }
            currentUserId = state.userId;
        }
    });
    return {
        msgData: undefined,
        data: undefined,
        unlockData: undefined,
        showBubbleId: undefined,
        neesShowBubble: undefined,
        loading: true,
        userLoad: (msgData, unlock) => {
            if (unlock !== undefined) {
                set({
                    loading: false,
                    msgData,
                    data: {
                        monologueId: unlock.monologueId,
                        monologueContent: unlock.monologueContent,
                        feedBackStatus: unlock.feedBackStatus,
                    },
                });
            } else {
                set({
                    msgData,
                    data: undefined,
                    unlockData: undefined,
                    loading: true,
                });
            }
        },
        closeHintBubble: () => {
            set({
                showBubbleId: undefined,
            });
        },
        fetchPermission: async (data) => {
            const robotUserId = useDetailStore.getState().robotInfo?.robotBaseInfo?.userId;
            const res = await getMonologuePermission({
                monologueId: data?.customExt.serverExt.monologueId || '',
                robotUserId: robotUserId || '',
            });
            set({ unlockData: res });
            return res;
        },
        fetchMonologue: async (data) => {
            set({
                loading: true,
            });
            const userId = useDetailStore.getState().userId || currentUserId;
            const res = await getMonologue({
                userId,
                monologueId: data?.customExt.serverExt.monologueId || '',
            });
            set({
                data: res,
                msgData: data,
                loading: false,
            });
            Taro.eventCenter.trigger('powerEvent', {
                type: 'msgSendSuccess',
                time: new Date().getTime(),
            });
        },
        likeAction: async (action) => {
            try {
                const current = get().data?.feedBackStatus;
                let code = 'cancel';
                if (action === 1 && current !== 'like') {
                    code = 'like';
                } else if (action === 2 && current !== 'unlike') {
                    code = 'unlike';
                }
                await Api.apiFeebback({
                    sessionRecordId: get().msgData?.customExt.serverExt.monologueId || '',
                    feedBackLabelCode: code,
                    chatType: 'monologue',
                });
                const info = get().data;
                if (info) {
                    info.feedBackStatus = code;
                    set({ data: { ...info } });
                }
                if (code !== 'cancel') {
                    showToast({
                        title: '反馈成功',
                        icon: 'none',
                        duration: 2000,
                    });
                }
            } catch (e) {
                showToast({
                    title: e?.message || '反馈失败',
                    icon: 'none',
                    duration: 2000,
                });
            }
        },
    };
});

export default useMonologueStore;
