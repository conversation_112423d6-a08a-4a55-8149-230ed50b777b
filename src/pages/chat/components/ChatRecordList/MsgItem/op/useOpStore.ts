import { Rectangle } from '@music/ct-animation/lib/utils/math/Rectangle';
import { create } from 'zustand';
import { MsgDataType } from '@/types/im';
import { showToast } from '@tarojs/taro';
import NIMService from '@/hooks/useNewNIM';
import * as Api from '../../../../api';
import useDetailStore from '../../../../store/useDetailStore';

interface OpState {
    msgData?: MsgDataType;
    initState: number;
    currentState: number;
    fromAction: number;
    showGuide: boolean;
    showGuideRect?: Rectangle;
    toggleOp: (data?: MsgDataType, showGuide?: boolean) => void;
    closeOp: () => void;
    clear: () => void;
    likeAction: (action: number) => Promise<void>;
    likeActionWithoutBubble: (msgData: MsgDataType, action: number) => Promise<any>;
}

const useOpStore = create<OpState>((set, get, store) => {
    let currentUserId = '';
    useDetailStore.subscribe((state, prev) => {
        if (state.userId !== prev.userId || currentUserId !== state.userId) {
            if (currentUserId) {
                set(store.getInitialState());
            }
            currentUserId = state.userId;
        }
    });
    return {
        msgData: undefined,
        delayMsg: undefined,
        initState: 0,
        currentState: 0,
        fromAction: 0,
        showGuide: false,
        toggleOp: (data, showGuide) => {
            set({
                msgData: data,
                initState: data.customExt?.serverExt?.opType || 0,
                currentState: data.customExt?.serverExt?.opType || 0,
                fromAction: 0,
                showGuide: showGuide ?? false,
            });
        },
        closeOp: () => {
            set({
                msgData: undefined,
                showGuide: false,
            });
        },
        clear: () => {
            set({
                initState: 0,
                currentState: 0,
                fromAction: 0,
                showGuideRect: undefined,
            });
        },
        likeAction: async (action) => {
            try {
                const current = get().currentState;
                const from = get().fromAction;
                if (from === action) {
                    return;
                }
                let index = 0;
                let code = 'cancel';
                if (action === 1 && current !== 1) {
                    code = 'like';
                    index = 1;
                } else if (action === 2 && current !== 2) {
                    code = 'unlike';
                    index = 2;
                }
                set({ currentState: index, fromAction: action });
                await Api.apiFeebback({
                    sessionRecordId: get().msgData?.customExt.serverExt.messageRecordId || '',
                    feedBackLabelCode: code,
                    chatType: 'chat',
                });
                const info = get().msgData;
                if (info) {
                    info.customExt.serverExt.opType = index;
                    const json = JSON.stringify(info.customExt);
                    NIMService.modifyMessage(info, json);
                }
                if (code !== 'cancel' && code !== 'unlike') {
                    showToast({
                        title: '反馈成功',
                        icon: 'none',
                        duration: 2000,
                    });
                }
            } catch (e) {
                let msg = e?.message || '反馈失败';
                if (e.code === 'netfail') {
                    msg = '反馈失败';
                }
                showToast({
                    title: msg,
                    icon: 'none',
                    duration: 2000,
                });
            }
        },

        likeActionWithoutBubble: async (msgData, action): Promise<any> => {
            try {
                const current = msgData.customExt?.serverExt?.opType;
                if (current === action) {
                    return await Promise.reject(new Error('不能反馈和上次一致的结果'));
                }
                let index = 0;
                let code = 'cancel';
                if (action === 1 && current !== 1) {
                    code = 'like';
                    index = 1;
                } else if (action === 2 && current !== 2) {
                    code = 'unlike';
                    index = 2;
                }
                const res = await Api.apiFeebback({
                    sessionRecordId: msgData?.customExt.serverExt.messageRecordId || '',
                    feedBackLabelCode: code,
                    chatType: 'chat',
                });
                const info = msgData;
                if (info) {
                    info.customExt.serverExt.opType = index;
                    const json = JSON.stringify(info.customExt);
                    NIMService.modifyMessage(info, json);
                }
                if (code !== 'cancel' && code !== 'unlike') {
                    showToast({
                        title: '反馈成功',
                        icon: 'none',
                        duration: 2000,
                    });
                }
                return res;
            } catch (e) {
                let msg = e?.message || '反馈失败';
                if (e.code === 'netfail') {
                    msg = '反馈失败';
                }
                showToast({
                    title: msg,
                    icon: 'none',
                    duration: 2000,
                });
            }
        },
    };
});

export default useOpStore;
