import React, { useEffect, useMemo } from 'react';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import { View, Text, Image } from '@tarojs/components';
import { showToast, setClipboardData } from '@tarojs/taro';

import Rectangle from '@music/ct-animation/lib/utils/math/Rectangle';
import { useContextStore } from '@/components/storeContext/StoreContext';
import { GuideTipStoreCreator } from '@/pages/chat/store/useGuideTipStore';

import { MsgDataType } from '@/types/im';
import { isIOS } from '@/utils';
import { copy } from '@/utils/rpc';
import { ChatType } from '@/pages/chat/type';
import useDetailStore from '@/pages/chat/store/useDetailStore';
import ICON_OP_ARROW from '@/assets/chat/ic_chat_op_arrow.png';
import ICON_OP_ARROW_DRAK from '@/assets/chat/ic_chat_op_arrow_dark.png';
import ICON_OP_UP from '@/assets/chat/ic_chat_op_up.png';
import ICON_OP_UP_WHITE from '@/assets/chat/ic_chat_op_up_white.png';
import ICON_OP_UP_PINK from '@/assets/chat/ic_chat_op_up_pink.png';
import ICON_OP_DOWN from '@/assets/chat/ic_chat_op_down.png';
import ICON_OP_DOWN_WHITE from '@/assets/chat/ic_chat_op_down_white.png';
import ICON_OP_DOWN_WHITE_DARK from '@/assets/chat/ic_chat_op_down_white_dark.png';
import ICON_OP_DOWN_DARK from '@/assets/chat/ic_chat_op_down_dark.png';
import ICON_OP_COPY from '@/assets/chat/ic_chat_op_copy.png';
import ICON_OP_COPY_WHITE from '@/assets/chat/ic_chat_op_copy_white.png';

import { getTextFromMessage } from '../utils';

import useOpStore from './useOpStore';

import '../index.scss';

const OpBubble = ({
    msgData,
    cls,
    likeEnable = true,
    showDownReasonAlert,
}: {
    msgData: MsgDataType;
    cls?: string;
    likeEnable?: boolean;
    showDownReasonAlert?: () => void;
}) => {
    const chatMode = useDetailStore((state) => state.chatMode);
    const showGuide = useOpStore((state) => state.showGuide);
    const guideTipStore = useContextStore(GuideTipStoreCreator);
    const ref = React.useRef(null);

    useEffect(() => {
        return () => {
            useOpStore.getState().clear();
        };
    }, []);

    useEffect(() => {
        if (chatMode === ChatType.IMMERSE && showGuide) {
            setTimeout(() => {
                const absoluteRect = ref.current?.getBoundingClientRect();
                if (!absoluteRect) {
                    return;
                }
                useOpStore.setState({
                    showGuideRect: new Rectangle(absoluteRect?.left, absoluteRect?.top, 198, 32),
                });
            }, 2000);
        }
    }, [chatMode, showGuide, guideTipStore]);

    const currentState = useOpStore((state) => state.currentState);
    const initState = useOpStore((state) => state.initState);

    function getUpIcon(state: number, t: string) {
        if (state === 1) {
            return ICON_OP_UP_PINK;
        }
        return t === ChatType.CHIP ? ICON_OP_UP : ICON_OP_UP_WHITE;
    }

    function getDownIcon(state: number, t: string) {
        if (state === 2) {
            return t === ChatType.CHIP ? ICON_OP_DOWN_DARK : ICON_OP_DOWN_WHITE_DARK;
        }
        return t === ChatType.CHIP ? ICON_OP_DOWN : ICON_OP_DOWN_WHITE;
    }

    function upAction(e: any) {
        e.stopImmediatePropagation();
        e.preventDefault();
        useOpStore
            .getState()
            .likeAction(1)
            .then(() => {
                useOpStore.getState().closeOp();
            });
    }

    function downAction(e: any) {
        e.stopImmediatePropagation();
        e.preventDefault();

        useOpStore
            .getState()
            .likeAction(2)
            .then(() => {
                useOpStore.getState().closeOp();
                if (useOpStore.getState().currentState === 2 && showDownReasonAlert) {
                    showDownReasonAlert();
                }
            });
    }

    const textMsg = useMemo(() => {
        let orginText = getTextFromMessage(msgData);
        if (orginText === '') {
            if (msgData?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_CUSTOM) {
                const content = msgData?.contentExt;
                if (content?.content?.type === 'AIGC_USER_MULTIMOAL_MSG') {
                    orginText = content?.content?.content?.text;
                }
            }
        }
        return orginText;
    }, [msgData]);

    function copyAction(e: any) {
        e.stopImmediatePropagation();
        e.preventDefault();
        if (isIOS) {
            copy(textMsg || '');
            showToast({
                title: '消息已复制到剪切板',
                icon: 'none',
                duration: 2000,
            });
            useOpStore.getState().closeOp();
        } else {
            setClipboardData({
                data: textMsg || '',
                success() {
                    showToast({
                        title: '消息已复制到剪切板',
                        icon: 'none',
                        duration: 2000,
                    });
                    useOpStore.getState().closeOp();
                },
                fail() {
                    showToast({
                        title: '复制失败',
                        icon: 'none',
                        duration: 2000,
                    });
                    useOpStore.getState().closeOp();
                },
            });
        }
    }

    const isMySide = msgData.isSelf;

    return (
        <View className={`popContainer ${cls}`}>
            <View className="at-col">
                {/* {showGuide && <Image src={likeOrNotGuide} className="likeOrNotBubbleGuide" />} */}
                <View
                    ref={ref}
                    className="contentStrContainer"
                    onClick={(e) => {
                        e.stopPropagation();
                    }}>
                    {!isMySide && likeEnable === true && (
                        <View className="contentStrContainer">
                            {initState !== 2 && (
                                <View className="singleOpContainerUp" onTouchStart={upAction}>
                                    <Image
                                        src={getUpIcon(currentState, chatMode)}
                                        className="opImage"
                                    />
                                    <Text className="opTextUp">
                                        {currentState === 1 ? '取消点赞' : '点赞'}
                                    </Text>
                                </View>
                            )}
                            {initState !== 1 && (
                                <View className="singleOpContainerDown" onTouchStart={downAction}>
                                    <Image
                                        src={getDownIcon(currentState, chatMode)}
                                        className="opImage"
                                    />
                                    <Text className="opTextDown">
                                        {currentState === 2 ? '取消点踩' : '点踩'}
                                    </Text>
                                </View>
                            )}
                        </View>
                    )}
                    <View className="singleOpContainerCopy" onTouchStart={copyAction}>
                        <Image
                            src={chatMode === ChatType.CHIP ? ICON_OP_COPY : ICON_OP_COPY_WHITE}
                            className="opImage"
                        />
                        <Text className="opTextCopy">复制</Text>
                    </View>
                </View>
                <View className="arrowContainer">
                    <Image
                        src={chatMode === ChatType.CHIP ? ICON_OP_ARROW : ICON_OP_ARROW_DRAK}
                        className="arrowImg"
                    />
                </View>
            </View>
        </View>
    );
};

export default OpBubble;
