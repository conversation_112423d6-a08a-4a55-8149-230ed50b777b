.feedback-modal-container {
    background-color: #fff;
    border-radius: 20px 20px 0 0;
    padding: 18px 24px 0 24px;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    height: 680px;
    margin-bottom: -180px;

    .feedback-options-scroll-container {
        flex: 1;
    }

    .feedback-modal-title {
        font-size: 18px;
        font-weight: bold;
        margin-left: 6px;
    }

    .feedback-modal-subtitle {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.4);
        margin-top: 2px;
        margin-bottom: 4px;
        margin-left: 6px;
    }

    .feedback-modal-close {
        position: absolute;
        right: 25px;
        top: 27px;
        width: 26px;
        height: 26px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .feedback-option-item {
        display: flex;
        align-items: flex-start;
        position: relative;
        padding: 16px 0;
    }

    .feedback-divider {
        height: 0.5px;
        background-color: rgba(0, 0, 0, 0.1); // 可以根据需要调整颜色
        width: 100%;
        margin: 0;
    }

    .option-number {
        font-size: 14px;
        margin-right: 5px;
        font-weight: 600;
    }

    .option-text {
        font-size: 14px;
        flex: 1;
        padding-right: 40px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.8);
    }

    .checkbox-container {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .checkbox-button {
        width: 20px;
        height: 20px;
        border: 1px solid #ddd;
        border-radius: 50%;
    }

    .checkbox-icon {
        width: 20px;
        height: 20px;
    }

    .other-feedback-container {
        background-color: #f5f5f5;
        border-radius: 10px;
        padding: 10px;
        margin-bottom: 20px;
    }

    .other-feedback-input {
        .taro-textarea {
            background-color: #f5f5f5 !important;
            font-size: 14px;
        }

        background-color: #f5f5f5;

        width: 100%;
        height: 80px;
    }

    .input-placeholder {
        color: #999;
        font-size: 14px;
    }

    .submit-button-container {
        margin-top: 10px;
        margin-bottom: 190px;

        @supports (padding-bottom: env(safe-area-inset-bottom)) {
            margin-bottom: calc(190px + env(safe-area-inset-bottom));
        }

        width: 100%;
    }

    .submit-button {
        background: linear-gradient(to right, #f85f7e, #ff689e);
        color: white;
        border-radius: 25px;
        border: none;
        width: 100%;
        height: 50px;
        font-size: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .submit-button:disabled {
        opacity: 0.6;
    }
}
