import React, { useEffect, useRef, useState } from 'react';
import { Button, Image, ScrollView, Text, Textarea, View } from '@tarojs/components';
import { CreateModalProps } from '@/components/dialog';
import { apiFeedbackCommit, apiFeedbackInfo } from '@/pages/chat/api';
import { showToast } from '@tarojs/taro';
import KeyboardWrapper from '@music/mat-keyboard-h5';

import CloseBtn from '@/assets/common/ic_dialog_close.png';
import SelectIcon from '@/assets/chat/ic_chat_op_down_reason_select.png';

import './index.scss';

interface Props {
    dismiss: () => void;
    data: any;
}

// 定义反馈选项类型
interface FeedbackOption {
    tagCode: string;
    tagName: string;
}

const ModalContent = ({ dismiss, data }: Props) => {
    const childrenRef = useRef<any>(null);

    // 反馈选项列表
    const [feedbackOptions, setFeedbackOptions] = useState<FeedbackOption[]>([]);
    // 选中的选项ID数组
    const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
    // 其他问题输入内容
    const [otherFeedback, setOtherFeedback] = useState<string>('');
    // 处理选项点击
    const handleOptionClick = (optionId: string) => {
        setSelectedOptions((prev) => {
            // 如果已选中，则移除；否则添加
            if (prev.includes(optionId)) {
                return prev.filter((id) => id !== optionId);
            }
            return [...prev, optionId];
        });
    };

    // 检查选项是否被选中
    const isOptionSelected = (optionId: string) => {
        return selectedOptions.includes(optionId);
    };

    // 处理提交
    const handleSubmit = async () => {
        if (otherFeedback && otherFeedback.length > 1000) {
            showToast({ title: '问题字数不能超过1000', icon: 'none' });
            return;
        }

        try {
            await apiFeedbackCommit({
                sessionRecordId: data?.msgData?.customExt?.serverExt?.messageRecordId,
                feedBackTagCodes: selectedOptions.join(','),
                feedBackExt: otherFeedback ?? '',
            });
            dismiss();
            showToast({ title: '反馈成功', icon: 'none' });
        } catch (e) {
            showToast({ title: '反馈失败，请重试', icon: 'none' });
        }
    };

    const fechtData = async () => {
        try {
            const { chatBotFeedBackTagDtos } = await apiFeedbackInfo();
            chatBotFeedBackTagDtos.push({ tagCode: 'other_problems', tagName: '其他问题' });
            setFeedbackOptions(chatBotFeedBackTagDtos);
        } catch (error) {
            console.log(error);
        }
    };

    const commitDisble = () => {
        return selectedOptions.length === 0 && !otherFeedback.length;
    };

    useEffect(() => {
        fechtData();

        return () => {
            showToast({ title: '反馈成功', icon: 'none' });
        };
    }, []);

    return (
        <KeyboardWrapper action="cover" containers={[]} childrenRef={childrenRef} moveUp="input">
            <View className="feedback-modal-container" ref={childrenRef}>
                <Text className="feedback-modal-title">感谢您的反馈</Text>
                <Image src={CloseBtn} className="feedback-modal-close" onClick={dismiss} />
                {/* 副标题 */}
                <View className="feedback-modal-subtitle">
                    <Text>选择原因，帮助我们优化您的体验（可多选）</Text>
                </View>

                {/* 可滚动的选项列表区域 */}
                <ScrollView
                    className="feedback-options-scroll-container"
                    scrollY
                    enhanced
                    showScrollbar={false}>
                    <View className="feedback-options">
                        {feedbackOptions.map((option, index) => (
                            <React.Fragment key={option.tagCode}>
                                <View
                                    className="feedback-option-item"
                                    onClick={() => handleOptionClick(option.tagCode)}>
                                    <Text className="option-number">{index + 1}. </Text>
                                    <Text className="option-text">{option.tagName}</Text>
                                    {option.tagCode !== 'other_problems' && (
                                        <View className="checkbox-container">
                                            {isOptionSelected(option.tagCode) ? (
                                                <Image
                                                    className="checkbox-icon"
                                                    src={SelectIcon}
                                                    mode="aspectFit"
                                                />
                                            ) : (
                                                <View className="checkbox-button" />
                                            )}
                                        </View>
                                    )}
                                </View>
                                {/* {index < feedbackOptions.length - 1 && (
                                    <View className="feedback-divider" />
                                )} */}
                            </React.Fragment>
                        ))}
                    </View>

                    <View className="other-feedback-container">
                        <Textarea
                            className="other-feedback-input"
                            value={otherFeedback}
                            maxlength={1000}
                            onInput={(e) => setOtherFeedback(e.detail.value)}
                            placeholder="请输入..."
                            placeholderClass="input-placeholder"
                        />
                    </View>
                </ScrollView>

                {/* 提交按钮 */}
                <View className="submit-button-container">
                    <Button
                        className="submit-button"
                        onClick={() => {
                            if (commitDisble()) {
                                return;
                            }
                            handleSubmit();
                        }}
                        disabled={commitDisble()}>
                        提交
                    </Button>
                </View>
            </View>
        </KeyboardWrapper>
    );
};

const Modal: CreateModalProps = {
    type: 'no_scroll_float',
    render(dismiss, data) {
        return <ModalContent dismiss={dismiss} data={data} />;
    },
};

export default Modal;
