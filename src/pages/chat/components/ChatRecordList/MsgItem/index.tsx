import React from 'react';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import { MsgDataType } from '@/types/im';
import { View } from '@tarojs/components';
import MessageTypeUtils from '@/utils/messageTypeUtils';
import MsgContainer from './MsgContainer';

import TextItem from './msgType/TextItem';
import StreamTextItem from './msgType/StreamTextItem';
import AudioTextItem from './msgType/AudioTextItem';
import ImageItem from './msgType/ImageItem';
import IntimacyIncreaseItem from './msgType/IntimacyIncreaseItem';
import GiftItem from './msgType/GiftItem';
import SceneStartItem from './msgType/Scene/Start/SceneStartItem';
import SceneEndItem from './msgType/Scene/End/SceneEndItem';
import SceneGuideCardItem from './msgType/Scene/SceneGuideCardItem';

type ContainerProps = {
    msgData: MsgDataType;
    avatar: string | undefined;
    className?: string;
    type: string | undefined;
    showLikeOrNot?: boolean;
};
type Render = {
    container: React.FC<ContainerProps> | null;
    item: React.FC<{ msgData: MsgDataType; type: string; showLikeOrNot?: boolean }>;
};

const primitiveRenderMapper = new Map<V2NIMConst.V2NIMMessageType, Render>();
const customRenderMapper = new Map<string, Render>();

primitiveRenderMapper.set(V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_TEXT, {
    container: MsgContainer,
    item: TextItem,
});

primitiveRenderMapper.set(V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_IMAGE, {
    container: MsgContainer,
    item: ImageItem,
});

customRenderMapper.set('aigcCustomTextAudioMsg', {
    container: MsgContainer,
    item: AudioTextItem,
});

customRenderMapper.set('AIGC_USER_MULTIMOAL_MSG', {
    container: MsgContainer,
    item: ImageItem,
});

customRenderMapper.set('aigcIntimacyLimitIncr', {
    container: null,
    item: IntimacyIncreaseItem,
});

customRenderMapper.set('gift', {
    container: MsgContainer,
    item: GiftItem,
});

customRenderMapper.set('sceneSettingNoticeMsg', {
    container: null,
    item: SceneGuideCardItem,
});

customRenderMapper.set('sceneSettingEndMsg', {
    container: null,
    item: SceneEndItem,
});

customRenderMapper.set('sceneSettingStartMsg', {
    container: null,
    item: SceneStartItem,
});

// 创建消息组件解析器
const MessageComponentResolver = {
    resolve: (msgData: MsgDataType): Render | null => {
        // 优先处理流式消息
        if (MessageTypeUtils.isStreamingTextMessage(msgData)) {
            return { container: MsgContainer, item: StreamTextItem };
        }

        if (MessageTypeUtils.isStreamingAudioTextMessage(msgData)) {
            // 流式音频文本消息暂时也使用流式文本组件（后续可以创建专门的 StreamAudioTextItem）
            return { container: MsgContainer, item: StreamTextItem };
        }

        // 处理普通消息
        const primitiveRender = primitiveRenderMapper.get(msgData.messageType);
        if (primitiveRender) {
            return primitiveRender;
        }

        const customRender = customRenderMapper.get(msgData.contentExt?.content?.type);
        if (customRender) {
            return customRender;
        }

        return null;
    },
};

const MsgItem = ({ msgData, avatar, className, type, showLikeOrNot }: ContainerProps) => {
    const found = MessageComponentResolver.resolve(msgData);

    if (!found) {
        return null;
    }

    const Container = found.container;
    const Item = found.item;

    if (Container) {
        return (
            <Container
                msgData={msgData}
                avatar={avatar}
                key={msgData.messageClientId}
                className={className}
                type={type}>
                <Item msgData={msgData} type={type} showLikeOrNot={showLikeOrNot} />
            </Container>
        );
    }

    return (
        <View className={`chat-msg-item ${className}`} key={msgData.messageClientId}>
            <Item msgData={msgData} type={type} />
        </View>
    );
};

export default MsgItem;
