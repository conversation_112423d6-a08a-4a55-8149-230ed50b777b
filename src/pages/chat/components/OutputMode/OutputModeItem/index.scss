.output-mode-item-container {
    margin-top: 12px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 16px 20px;
    border-radius: 14px;
    border-width: 1.4px;
    position: relative;

    &.first {
        margin-top: 0;
    }

    &.normal {
        border-color: #d5d5d5;
    }

    &.select {
        border-color: #ff689e;
    }

    .mode-title-container {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: 100%;
        height: 100%;

        .mode-image {
            width: 16px;
            height: 16px;
        }

        .mode-name {
            margin-left: 2px;
            font-weight: 600;
            font-size: 14px;
            color: #26262a;
        }
    }

    .mode-desc {
        margin-top: 4px;
        margin-right: 35px;
        font-weight: 400;
        font-size: 12px;
        color: rgba(38, 38, 42, 0.6);
    }

    .mode-price-container {
        margin-top: 4px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: 100%;
        height: 100%;

        .mode-price {
            font-weight: 600;
            font-size: 12px;
            color: #ff689e;
        }

        .mode-coin {
            width: 16px;
            height: 16px;
        }

        .mode-price-text {
            font-weight: 400;
            font-size: 11px;
            color: rgba(255, 104, 158, 0.7);
        }
    }

    .select-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        width: 20px;
        height: 20px;
        transform: translateY(-50%);
    }
}
