import React, { useCallback } from 'react';
import { View, Text, Image } from '@tarojs/components';
import ErrorBoundary from '@/components/ErrorBoundary';

import { OutputModeItem } from '@/pages/chat/store/useOutputMode';

import ICON_STAR from '@/assets/market/ic_stamina.png';
import ICON_NORMAL_MODE from '@/assets/chat/outputMode/output_mode_normal_icon.png';
import ICON_LONG_MODE from '@/assets/chat/outputMode/output_mode_long_article_icon.png';

import ICON_SELECT from '@/assets/chat/outputMode/output_mode_select_icon.png';
import ICON_UNSELECT from '@/assets/chat/outputMode/output_mode_unselect_icon.png';

import './index.scss';

const OutputModeShowItem = (props: {
    item: OutputModeItem;
    index: number;
    clickAction: (item: OutputModeItem) => void;
}) => {
    const clickOutputMode = useCallback(() => {
        props.clickAction(props.item);
    }, [props]);

    return (
        <ErrorBoundary>
            <View
                className={`output-mode-item-container ${
                    props.item.currentMode ? 'select' : 'normal'
                } ${props.index === 0 ? 'first' : ''}`}
                onClick={clickOutputMode}>
                <View className="mode-title-container">
                    <Image
                        className="mode-image"
                        src={
                            props.item.chatTextModeCode === 'normal'
                                ? ICON_NORMAL_MODE
                                : ICON_LONG_MODE
                        }
                    />
                    <Text className="mode-name">{props.item.chatTextModeName}</Text>
                </View>
                <Text className="mode-desc">{props.item.chatTextModeDesc}</Text>
                <View className="mode-price-container">
                    <Text className="mode-price">{props.item.chatTextModePrice}</Text>
                    <Image className="mode-coin" src={ICON_STAR} />
                    <Text className="mode-price-text">/每条信息</Text>
                </View>
                <Image
                    className="select-icon"
                    src={props.item.currentMode ? ICON_SELECT : ICON_UNSELECT}
                />
            </View>
        </ErrorBoundary>
    );
};

export default OutputModeShowItem;
