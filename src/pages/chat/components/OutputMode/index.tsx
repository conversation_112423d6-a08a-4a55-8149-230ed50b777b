import React, { useEffect, useCallback } from 'react';
import { View, Text } from '@tarojs/components';
import ErrorBoundary from '@/components/ErrorBoundary';
import { useContextStore } from '@/components/storeContext/StoreContext';
import { CreateModalProps } from '@/components/dialog';
import { OutputModeItem, OutputModeStateStoreCreator } from '@/pages/chat/store/useOutputMode';
import EventTrackView from '@/components/EventTrack';
import { addClickLog } from '@/utils/logTool';

import OutputModeShowItem from './OutputModeItem';
import './index.scss';

const OutputMode = (props: { dismiss: () => void }) => {
    const outputModeStateStore = useContextStore(OutputModeStateStoreCreator);
    const allOutputMode = outputModeStateStore((state) => state.showAllModes);

    const clickOutputMode = (item: OutputModeItem) => {
        if (item.currentMode) return;
        outputModeStateStore.getState().selectOutputMode(item);
    };

    const clickSubmitAction = useCallback(() => {
        outputModeStateStore.getState().setOutputMode();
        props.dismiss();
        addClickLog('btn_ai_im_mode_pop_comfirm|mod_ai_im_mode_pop|page_ai_im|page_h5_biz');
    }, [outputModeStateStore, props]);

    useEffect(() => {
        outputModeStateStore.getState().loadOutputModeIfNeeded();
        return () => {
            outputModeStateStore.getState().loadOutputModeIfNeeded();
        };
    }, [outputModeStateStore]);

    return (
        <ErrorBoundary>
            <View className="output-mode-container normal">
                <EventTrackView
                    params={{
                        _spm: 'mod_ai_im_mode_pop|page_ai_im|page_h5_biz',
                    }}
                    isPage={false}
                />
                <View className="line" />
                <Text className="title">切换聊天模式</Text>
                <View className="chat-mode-item">
                    {allOutputMode.map((item: OutputModeItem, index) => (
                        <OutputModeShowItem
                            key={item.chatTextModeCode}
                            item={item}
                            index={index}
                            clickAction={clickOutputMode}
                        />
                    ))}
                </View>
                <View className="submit-button" onClick={clickSubmitAction}>
                    <Text className="submit-text">确定</Text>
                </View>
            </View>
        </ErrorBoundary>
    );
};

export const OutputModeModal: CreateModalProps = {
    type: 'float',
    render(dismiss) {
        return <OutputMode dismiss={dismiss} />;
    },
};

export default OutputMode;
