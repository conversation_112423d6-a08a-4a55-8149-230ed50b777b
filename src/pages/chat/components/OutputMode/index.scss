.output-mode-container {
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    width: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-bottom: 20px;
    @supports (padding-bottom: env(safe-area-inset-bottom)) {
        padding-bottom: calc(10px + env(safe-area-inset-bottom));
    }

    .line {
        margin-top: 5px;
        width: 30px;
        height: 3px;
        border-radius: 1.5px;
        background-color: #d9d9d9;
    }

    .title {
        margin-top: 19px;
        font-weight: 500;
        font-size: 17px;
        color: #26262a;
    }

    .chat-mode-item {
        width: 100%;
        padding: 0 28px;
        box-sizing: border-box;
        margin-top: 15px;
    }

    .submit-button {
        margin-top: 20px;
        width: calc(100% - 56px);
        margin-left: 28px;
        margin-right: 28px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 25px;
        background-color: #ff689e;

        .submit-text {
            font-weight: 600;
            font-size: 18px;
            color: #fff;
        }
    }
}
