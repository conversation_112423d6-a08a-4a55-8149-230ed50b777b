export interface MissionRewardDetail {
    imgUrl?: string;
    rewardName?: string;
    rewardNum?: number;
    rewardDesc?: string;
    id?: number;
}

export interface StageDetailReward {
    missionId?: number;
    missionName?: string;
    targetValue?: number;
    missionProgress?: number;
    rewardIcon?: string;
    rewardBoxId?: number;
    userMissionStatus?: number;
    rewardDetailList?: MissionRewardDetail[];
}

export interface StageDetailRewardList {
    detailRewardList?: StageDetailReward[];
    missionBoxId?: number;
}

/**
 * 用户任务状态
 */
export const UserMissionAppStatus = {
    NOT_FINISH: 0,
    FINISHED: 1,
    HAS_RECEIVE: 3, // 已领取
};
