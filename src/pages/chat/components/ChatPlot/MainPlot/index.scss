.main-plat-page {
    width: 100%;
    height: 100%;
    display: flex;
    position: relative;
    background-color: transparent;
    align-items: center;

    .page {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .award-img-bg {
            pointer-events: none;
            top: 0;
            right: 0;
            position: absolute;
            clip-path: inset(29px 0 0 0);
            overflow: hidden;
        }

        .award-img-bg-pepenetrate {
            pointer-events: none;
            top: 10px;
            right: 0;
            position: absolute;
            overflow: hidden;
        }

        .award-img-mask-gradual1 {
            pointer-events: none;
            top: 0;
            right: 0;
            position: absolute;
            overflow: hidden;
            height: 100%;
            width: 100%;
            background: radial-gradient(
                35.84% 71.71% at 5.6% 6.21%,
                rgba(255, 156, 191, 0.5) 0%,
                rgba(255, 255, 255, 0) 100%
            );
        }

        .award-img-mask-gradual2 {
            pointer-events: none;
            bottom: 0;
            right: 0;
            position: absolute;
            overflow: hidden;
            height: 100%;
            width: 100%;
            background: linear-gradient(
                282.97deg,
                rgba(122, 121, 121, 0.89) 3.75%,
                rgba(255, 255, 255, 0) 36.97%
            );
        }

        .award-img-bottom-pepenetrate {
            bottom: 0;
            right: 0;
            position: absolute;
            overflow: hidden;
        }

        .award-img-penetrate-container {
            top: 0;
            right: 0;
            position: absolute;
            clip-path: polygon(321px 13px, 400px 95px, 321px 175px, 241px 95.5px);
            overflow: hidden;
        }

        .content-container {
            display: flex;
            position: relative;
            padding-top: 10px;
            padding-bottom: 10px;
            min-height: 160px;
            flex-direction: column;
            padding-left: 16px;
            margin-top: 29px;
            padding-right: 145px;
            transform: scaleY(0);
            opacity: 0;
            transform-origin: center;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.5), inset 0 0 8.7px rgba(255, 255, 255, 1);
            background: linear-gradient(270deg, rgba(255, 255, 255, 0.5) 6.03%, #fff 49.88%),
                radial-gradient(
                    58.19% 107.31% at 10.79% 6.21%,
                    rgba(255, 156, 191, 0.2) 0%,
                    rgba(255, 255, 255, 0) 100%
                );

            .title {
                font-family: 'SourceHanSerifCN-Bold', sans-serif;
                font-weight: 700;
                font-size: 18px;
                line-height: 22px;
                position: absolute;
                top: 5px;
                left: 0;
                letter-spacing: 0;
                color: rgba(125, 109, 140, 1);
                transform: skewX(-10deg);
            }

            .desc-wrapper {
                overflow: hidden;
                display: flex;
                flex-direction: column;
                flex-shrink: 0;

                .desc-content {
                    font-size: 13px;
                    font-weight: normal;
                    line-height: 150%;
                    letter-spacing: 0;
                    color: #644f78;
                    opacity: 0.8;
                    margin-top: 3px;
                    order: 1;

                    &.two-line {
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        line-clamp: 2;
                        -webkit-line-clamp: 2;
                        overflow: hidden;
                        word-break: break-all;
                        text-overflow: ellipsis;
                    }
                }

                .desc-content-toggle-expand {
                    font-size: 13px;
                    font-weight: 600;
                    line-height: normal;
                    letter-spacing: 0;
                    color: rgba(0, 0, 0, 0.2);
                    order: 2;

                    &::after {
                        content: '';
                        display: inline-block;
                        width: 10px;
                        height: 10px;
                        background-image: url('../../../../../assets/profile/icon-expand.png');
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                        vertical-align: baseline;
                        margin-left: 2px;
                    }
                }

                .desc-content-toggle-fold {
                    font-size: 13px;
                    font-weight: 600;
                    line-height: normal;
                    letter-spacing: 0;
                    color: rgba(0, 0, 0, 0.2);
                    order: 3;

                    &::after {
                        content: '';
                        display: inline-block;
                        width: 10px;
                        height: 10px;
                        background-image: url('../../../../../assets/profile/icon-expand.png');
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                        vertical-align: baseline;
                        margin-left: 2px;
                        transform: rotate(180deg);
                    }
                }
            }

            .awardList {
                margin-top: 7px;
                width: 100%;
                height: 57px;
                display: flex;
                flex-direction: row;
                gap: 10px;

                &.hasReceived {
                    opacity: 0.4;
                }

                .item-container {
                    width: 40px;
                    height: 57px;
                    display: flex;
                    flex-direction: column;

                    .item-num {
                        position: absolute;
                        color: white;
                        height: 13px;
                        font-size: 10px;
                        right: 0;
                        bottom: 0;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        padding-left: 2.5px;
                        padding-right: 2.5px;
                        background: rgba(0, 0, 0, 0.2);
                        border-radius: 8px;
                    }
                }
            }
        }

        .func {
            width: 165px;
            height: 36px;
            border-width: 1px;
            gap: 10px;
            font-size: 16px;
            line-height: 36px;
            border-radius: 23.5px;
            text-align: center;
            font-family: 'SourceHanSerifCN-Bold', sans-serif;

            &.cancel {
                background: rgba(0, 0, 0, 0.5);
                color: white;
            }

            &.confirm {
                background: linear-gradient(90.44deg, #fff 8.35%, rgba(255, 255, 255, 0.54) 96.65%);
                color: black;
            }
        }
    }
}
