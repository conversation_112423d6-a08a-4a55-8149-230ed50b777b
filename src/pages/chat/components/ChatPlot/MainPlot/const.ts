// eslint-disable-next-line max-classes-per-file
/**
 * 抽奖奖品结果
 */
export interface RewardBoxCoreResourceDTO {
    name: string;
    imgUrl: string;
    resNum: number;
    resName: string;
}

export interface RewardBoxCoreDetailDTO {
    id: number;
    appProductType: string;
    coverImgUrl: string;
    name: string;
    descStr: string;
    resource: RewardBoxCoreResourceDTO[];
    price: number;
    control: number;
}

export interface GameChapterEndingRpcDto {
    endingCardId: string;
    endingName: string;
    endingDesc: string;
    bindChapterId: string;
    endingCardImg: string;
    endingCardBigImg: string;
    endingCardLevelImg: string;
}

export interface ChapterStorylineDetailDto {
    chapterId: string;
    roleId: string;
    chapterName: string;
    chapterDesc: string;
    chapterImg: string;
    relationTypeDesc: string;
    rewardBox: RewardBoxCoreDetailDTO;
    unlock: boolean;
    completed: boolean;
    endingCardInfoDtos: GameChapterEndingRpcDto[];
}

export class ChapterStorylineDetailDtoDefault implements ChapterStorylineDetailDto {
    chapterId: string;

    roleId: string;

    chapterName: string;

    chapterDesc: string;

    chapterImg: string;

    relationTypeDesc: string;

    rewardBox: RewardBoxCoreDetailDTO;

    unlock: boolean;

    completed: boolean;

    endingCardInfoDtos: GameChapterEndingRpcDto[];

    constructor(data: ChapterStorylineDetailDto) {
        Object.assign(this, data);
    }
}
