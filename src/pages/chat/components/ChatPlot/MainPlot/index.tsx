import mainPlot_detail_award_item_img_bg from '@/assets/main-plot/mainPlot_detail_award_item_img_bg.png';
import mainPlot_detail_award_received from '@/assets/main-plot/mainPlot_detail_award_received.png';
import mainPlot_detail_award_title from '@/assets/main-plot/mainPlot_detail_award_title.png';
import mainPlot_detail_award_title_penetrate from '@/assets/main-plot/mainPlot_detail_award_title_penetrate.png';
import mainPlot_detail_bg_title from '@/assets/main-plot/mainPlot_detail_bg_title.png';
import mainPlot_detail_bottom_penetrate from '@/assets/main-plot/mainPlot_detail_bottom_penetrate.png';
import mainPlot_detail_img_penetrate from '@/assets/main-plot/mainPlot_detail_img_penetrate.png';
import mainPlot_detail_img_penetrate_enter from '@/assets/main-plot/mainPlot_detail_img_penetrate_enter.png';
import mainPlot_detail_mask_penetrate from '@/assets/main-plot/mainPlot_detail_mask_penetrate.png';
import { ConfirmModalProvider } from '@/components/confirm-modal';
import { DialogController, useDialog } from '@/components/dialog';
import { usePageStore } from '@/components/storeContext/StoreContext';
import useDetailStore from '@/pages/chat/store/useDetailStore';
import { GameChapterStartResult, StartCode } from '@/pages/profile/GameChapterStartResult';
import { precheckChapterApi, startChapterApi } from '@/service/profileApi';
import { debounce } from '@/utils';
import { getImage } from '@/utils/image';
import { openKey, pageDidAppear } from '@/utils/rpc';
import { Image, Text, View } from '@tarojs/components';
import { createAnimation, showToast } from '@tarojs/taro';
import classNames from 'classnames';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { jump2ChapterInfo, jump2Market, jump2Epicplayer } from '@/router';
import MainPlotToast from '../MainPlotList/Toast';
import { MainPlotToastStore } from '../MainPlotList/Toast/MainPlotToastStore';
import {
    ChapterStorylineDetailDto,
    GameChapterEndingRpcDto,
    RewardBoxCoreResourceDTO,
} from './const';

import mainPlotStore from './mainPlotStore';

import './index.scss';

const EndingAwardItem = ({ item }: { item: GameChapterEndingRpcDto }) => {
    const awardImg =
        item.endingCardImg ??
        'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/59837700684/848d/dd73/8514/cf16afc5f5fb15db8adfbb650a5d763a.png';
    return (
        <View className="item-container">
            <View
                className="relative w-[40px] h-[40px] bg-cover bg-center flex justify-center items-center overflow-hidden rounded-[8px]"
                style={{ backgroundImage: `url(${mainPlot_detail_award_item_img_bg})` }}>
                <Image src={awardImg} className="w-[40px] h-[40px] " />
                <View className="item-num">结局</View>
            </View>
            <View
                className="h-[15px] w-[40px] text-[10px] text-[#7d6d8c] flex items-center justify-center mt-[2px] opacity-[0.6]"
                style={{
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    display: 'inline-block',
                    textAlign: 'center',
                }}>
                {item.endingName.slice(0, 4)}
            </View>
        </View>
    );
};

const PlotAwardItem = ({ item }: { item: RewardBoxCoreResourceDTO }) => {
    const awardImg =
        item.imgUrl ??
        'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/59837700684/848d/dd73/8514/cf16afc5f5fb15db8adfbb650a5d763a.png';
    return (
        <View className="item-container">
            <View
                className="relative w-[40px] h-[40px] bg-cover bg-center flex justify-center items-center"
                style={{ backgroundImage: `url(${mainPlot_detail_award_item_img_bg})` }}>
                <Image src={awardImg} className="w-[30px] h-[30px]" />
                {(item.resNum ?? 0) > 0 && <View className="item-num">x{item.resNum}</View>}
            </View>
            <View
                className="h-[15px] w-[40px] text-[10px] text-[#7d6d8c] flex items-center justify-center mt-[2px] opacity-[0.6]"
                style={{
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    display: 'inline-block',
                    textAlign: 'center',
                }}>
                {item.name.slice(0, 4)}
            </View>
        </View>
    );
};

const PloAwardListComponent = ({
    awardList,
    endingList,
}: {
    awardList: RewardBoxCoreResourceDTO[];
    endingList: GameChapterEndingRpcDto[];
}) => {
    return (
        <View className={classNames('awardList')}>
            {awardList?.map((item: RewardBoxCoreResourceDTO) => (
                <PlotAwardItem item={item} />
            ))}
            {endingList?.map((item: GameChapterEndingRpcDto) => (
                <EndingAwardItem item={item} />
            ))}
        </View>
    );
};

const PlotContentStrComponent = ({ content = '', maxLines = 2 }) => {
    const [showExpandtDesc, setShowExpandtDesc] = useState<{
        showAction: boolean;
        isExpand: boolean;
    }>({ showAction: false, isExpand: false });
    const onShowMoreDesc = useCallback(() => {
        setShowExpandtDesc({
            showAction: showExpandtDesc.showAction,
            isExpand: !showExpandtDesc.isExpand,
        });
    }, [showExpandtDesc]);

    const contentRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        if (contentRef.current) {
            const lineHeight = parseFloat(getComputedStyle(contentRef.current).lineHeight);
            const containerHeight = contentRef.current?.scrollHeight;
            const twoLineHeight = 2 * lineHeight;
            setShowExpandtDesc({
                showAction: containerHeight > twoLineHeight,
                isExpand: false,
            });
        }
    }, [content, maxLines]);
    return (
        <View className="desc-wrapper">
            <Text
                ref={contentRef}
                className={classNames('desc-content', {
                    'two-line': showExpandtDesc.showAction && !showExpandtDesc.isExpand,
                })}>
                {content}
            </Text>
            {showExpandtDesc.showAction && (
                <Text
                    className={classNames({
                        'desc-content-toggle-fold': showExpandtDesc.isExpand,
                        'desc-content-toggle-expand': !showExpandtDesc.isExpand,
                    })}
                    onClick={debounce(onShowMoreDesc)}>
                    {!showExpandtDesc.isExpand ? '展开' : '收起'}
                </Text>
            )}
        </View>
    );
};

const PlotContentComponent = ({ dto }: { dto: ChapterStorylineDetailDto }) => {
    const plotsResp = dto;
    const bg = getImage(plotsResp.chapterImg);
    const content = plotsResp?.chapterDesc;
    const title = plotsResp?.chapterName;
    const awardList = plotsResp?.rewardBox?.resource ?? [];
    const endingList = plotsResp?.endingCardInfoDtos;
    const hasAward = awardList?.length > 0 || endingList?.length > 0;

    const [bgAnimation, setBgAnimationData] = useState({});
    const [contentAnimation, setContentAnimationData] = useState({});
    const [penetrateAnimation, setPenetrateAnimationData] = useState({});
    const [penetrateEnterAnimation, setEnterPenetrateAnimationData] = useState({});

    const [content1Animation, setContent1AnimationData] = useState({});
    const [content2Animation, setContent2AnimationData] = useState({});
    const [content3Animation, setContent3AnimationData] = useState({});

    useEffect(() => {
        const animation = createAnimation({
            timingFunction: 'ease-in-out',
        });
        animation.opacity(1).scaleY(1).step({ duration: 350 });
        setBgAnimationData(animation.export());

        const animation2 = createAnimation({
            timingFunction: 'ease-in-out',
        });
        animation2.opacity(1).step({ delay: 110, duration: 100 });
        animation2.opacity(0).step({ delay: 100, duration: 50 });
        setEnterPenetrateAnimationData(animation2.export());

        const animation3 = createAnimation({
            timingFunction: 'ease-in-out',
        });
        animation3.opacity(1).step({ delay: 400, duration: 200 });
        setContentAnimationData(animation3.export());

        const animationContent1 = createAnimation({
            timingFunction: 'ease-in-out',
        });
        animationContent1.translateY(0).opacity(1).step({ delay: 400, duration: 100 });
        setContent1AnimationData(animationContent1.export());

        const animationContent2 = createAnimation({
            timingFunction: 'ease-in-out',
        });
        animationContent2.translateY(0).opacity(1).step({ delay: 430, duration: 100 });
        setContent2AnimationData(animationContent2.export());

        const animationContent3 = createAnimation({
            timingFunction: 'ease-in-out',
        });
        animationContent3.translateY(0).opacity(1).step({ delay: 450, duration: 100 });
        setContent3AnimationData(animationContent3.export());

        const animation4 = createAnimation({
            timingFunction: 'ease-in-out',
        });
        animation4.opacity(1).step({ delay: 550, duration: 200 });
        setPenetrateAnimationData(animation4.export());
    }, []);

    return (
        <View className="relative w-[100%] overflow-hidden">
            {/** 图片背景~ */}
            <View className="award-img-bg opacity-[0]" animation={contentAnimation}>
                <Image className="w-full h-full" src={bg} />
            </View>

            {/** 白色渐变背景 及 内容~ */}
            <View className="content-container" animation={bgAnimation}>
                {/** 背景（左上角）~ */}
                <View className="award-img-mask-gradual1" />
                {/** 背景（右下角）~ */}
                <View className="award-img-mask-gradual2" />

                <View className="opacity-[0]" animation={contentAnimation}>
                    {/** 左上角标题部分~ */}
                    <View
                        className="relative h-[37px] translate-y-[32px] opacity-0"
                        animation={content1Animation}>
                        <Image className="w-[136px] h-[100%]" src={mainPlot_detail_bg_title} />
                        <View className="title">{title}</View>
                    </View>
                    {/** 中间内容区域~ */}
                    <View className="translate-y-[32px] opacity-0" animation={content2Animation}>
                        <PlotContentStrComponent content={content} />
                    </View>
                    {/** 奖励区域~ */}
                    {hasAward && (
                        <View
                            className="flex flex-col w-[100%] translate-y-[32px] opacity-0"
                            animation={content3Animation}>
                            <View className="flex flex-row h-[16px] items-center">
                                <Image
                                    className="w-[88px] h-[16px] mt-[10px]"
                                    src={mainPlot_detail_award_title}
                                />
                                {dto.completed && (
                                    <Image
                                        className="w-[36px] h-[14px] mt-[10px] ml-[2px]"
                                        src={mainPlot_detail_award_received}
                                    />
                                )}
                            </View>
                            <PloAwardListComponent awardList={awardList} endingList={endingList} />
                        </View>
                    )}
                </View>
            </View>
            {/** 菱形进入时动效~ */}
            <View
                className="w-[188px] h-[189px] absolute top-[0px] left-[227px] opacity-[0] pointer-events-none"
                animation={penetrateEnterAnimation}>
                <Image
                    className="w-[100%] h-[100%]"
                    src={`${mainPlot_detail_img_penetrate_enter}?t=${Date.now()}`}
                />
            </View>
            <View
                className="absolute w-[100%] top-[0px] left-[0px] opacity-[0]"
                animation={penetrateAnimation}>
                <View className="relative">
                    {/** 菱形截取图片~ */}
                    <View className="award-img-penetrate-container">
                        <Image
                            className="w-full h-full"
                            src={bg}
                            onClick={debounce(() => {
                                if (dto) {
                                    const updatedDto: ChapterStorylineDetailDto = {
                                        ...dto,
                                    };
                                    const userId = useDetailStore.getState().userId;
                                    const chapterStr = encodeURIComponent(
                                        JSON.stringify(updatedDto)
                                    );
                                    jump2ChapterInfo({
                                        chapterId: updatedDto?.chapterId,
                                        robotUserId: userId,
                                        chapter: chapterStr,
                                        from: null,
                                    });
                                }
                            })}
                        />
                    </View>
                    {/** 菱形动效~ */}
                    <Image
                        className="w-[188px] h-[189px] absolute top-[0px] left-[227px] pointer-events-none"
                        src={mainPlot_detail_img_penetrate}
                    />
                    {/** 标题部分动效~ */}
                    <Image
                        className="absolute w-[170px] h-[60px] left-[0px] top-[29px] pointer-events-none"
                        src={`${mainPlot_detail_award_title_penetrate}?t=${Date.now()}`}
                    />
                </View>
            </View>
            {/** 蒙层动效（进入时，展示优先级高）~ */}
            <View className="award-img-bg-pepenetrate pointer-events-none">
                <Image
                    className="w-full h-full"
                    src={`${mainPlot_detail_mask_penetrate}?t=${Date.now()}`}
                />
            </View>
            {/** 底部动效~ */}
            <View className="award-img-bottom-pepenetrate pointer-events-none">
                <Image
                    className="w-full h-full"
                    src={`${mainPlot_detail_bottom_penetrate}?t=${Date.now()}`}
                />
            </View>
        </View>
    );
};

const PlotFuncComponent = ({
    dto,
    dialog,
}: {
    dto: ChapterStorylineDetailDto;
    dialog: DialogController;
}) => {
    const mainPlotToastStore = usePageStore(MainPlotToastStore);

    function handleClickForCancel() {
        dialog?.hide?.();
    }

    const startChapter = useCallback(() => {
        const targetChapterId = dto.chapterId;
        const targetRoleId = dto.roleId;
        startChapterApi({ chapterId: targetChapterId, roleId: targetRoleId })
            .then((res) => {
                const result = res as GameChapterStartResult;
                if (result.code === StartCode.SUCCESS) {
                    // useEpicPlayerStore.getState().showEpicPlayer({
                    //     roleId: targetRoleId,
                    //     chapterId: targetChapterId,
                    //     recordId: result.recordId,
                    //     backToChat: false,
                    //     showSkipButton: false,
                    // });
                    jump2Epicplayer({
                        chapterId: targetChapterId,
                        roleId: targetRoleId,
                        chapterPlayRecordId: result.recordId,
                    });
                } else if (result.code === StartCode.NO_STAMINA) {
                    jump2Market({});
                } else {
                    showToast({
                        title: result.message || '系统繁忙，请稍后再试',
                        icon: 'none',
                    });
                }
            })
            .catch((err) => {
                if (err?.message) {
                    showToast({
                        title: err.message,
                        icon: 'none',
                    });
                }
            });
    }, [dto.chapterId, dto.roleId]);

    const chapterDialog = useDialog(ConfirmModalProvider);
    const precheck = useCallback(async () => {
        if (!dto.unlock) {
            mainPlotToastStore
                .getState()
                .showToast({ scene: 'mainPlot', content: `请先完成上一等级剧情`, num: '' });
            return;
        }

        const targetChapterId = dto.chapterId;
        const targetRoleId = dto.roleId;
        try {
            const precheckResult = await precheckChapterApi({ chapterId: targetChapterId });
            if (precheckResult.recordId) {
                chapterDialog?.show({
                    title: '是否继续上次的探索',
                    cancelText: '重新探索',
                    closeOnClickOverlay: true,
                    cancelStyle: {
                        color: '#7F5C5B',
                        textAlign: 'center',
                        fontSize: '18px',
                        fontStyle: 'normal',
                        fontWeight: 600,
                        lineHeight: '18px',
                        borderRadius: '128px',
                        background: 'rgba(242, 220, 202, 0.3)',
                    },
                    confirmText: '确定',
                    confirmStyle: {
                        color: '#7F5C5B',
                        textAlign: 'center',
                        fontSize: '18px',
                        fontStyle: 'normal',
                        fontWeight: 600,
                        lineHeight: '18px',
                        borderRadius: '128px',
                        background: '#F2DCCA',
                    },
                    onCancel: () => {
                        startChapter();
                    },
                    onConfirm: () => {
                        // useEpicPlayerStore.getState().showEpicPlayer({
                        //     roleId: targetRoleId,
                        //     chapterId: targetChapterId,
                        //     recordId: precheckResult.recordId,
                        //     backToChat: false,
                        //     showSkipButton: false,
                        // });
                        jump2Epicplayer({
                            chapterId: targetChapterId,
                            roleId: targetRoleId,
                            chapterPlayRecordId: precheckResult.recordId,
                        });
                    },
                });
            } else {
                startChapter();
            }
        } catch (error) {
            showToast({
                title: error.message,
                icon: 'none',
            });
        }
    }, [chapterDialog, dto.chapterId, dto.roleId, dto.unlock, mainPlotToastStore, startChapter]);

    const confirmStr = dto.completed ? '再次回忆' : '开始回忆';

    const [funcAnimation, setFuncAnimationData] = useState({});
    useEffect(() => {
        const animation = createAnimation({
            timingFunction: 'ease-in-out',
        });
        animation.opacity(1).scaleY(1).step({ duration: 300 });
        setFuncAnimationData(animation.export());
    }, []);

    return (
        <View
            className="flex flex-row h-[50px] w-[100%] pr-[12px] pl-[12px] justify-between items-end opacity-[0]"
            animation={funcAnimation}>
            <View className="func cancel" onClick={handleClickForCancel}>
                下次再说
            </View>
            <View className="func confirm" onClick={precheck}>
                {confirmStr}
            </View>
        </View>
    );
};

function MainPlotPage({
    dto,
    dialog,
    onDismiss,
}: {
    dto: ChapterStorylineDetailDto;
    dialog: DialogController;
    // eslint-disable-next-line react/require-default-props
    onDismiss?: () => void;
}) {
    const closePlot = mainPlotStore((state) => state.closePlot);
    useEffect(() => {
        if (closePlot) {
            dialog?.hide?.();
        }
    }, [closePlot, dialog]);
    useEffect(() => {
        pageDidAppear(() => {
            mainPlotStore.getState().refreshStats(dto.chapterId);
        });
        return () => {
            mainPlotStore.getState().reset();
            onDismiss?.();
        };
    }, [dto, onDismiss]);
    return (
        <View>
            <View
                className="main-plat-page"
                onClick={(e) => {
                    e.stopPropagation();
                }}>
                <View className="relative w-[100%]" id={`${Date.now()}`}>
                    <View className="page">
                        <PlotContentComponent dto={dto} />
                        <PlotFuncComponent dto={dto} dialog={dialog} />
                    </View>
                </View>
            </View>
            <View className="mainPlotToastStyle">
                <MainPlotToast scene="mainPlot" />
            </View>
        </View>
    );
}

export default MainPlotPage;
