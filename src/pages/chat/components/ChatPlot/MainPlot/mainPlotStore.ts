import { create } from 'zustand';
import Taro from '@tarojs/taro';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { IEpicPageData } from '@/components/EpicPlayer';
import { aigcStoryDetail } from './mainPlotApi';
import { ChapterStorylineDetailDto } from './const';

interface MainPlotState {
    chapterId: string;
    closePlot: boolean;
    reset: () => void;
    refreshStats: (chapterId: string) => void;
    handleIntimacyUpgrade: (data: { type: number; content: Record<string, any> }) => void;
}

const mainPlotStore = create<MainPlotState>((set) => {
    return {
        chapterId: '',
        closePlot: false,
        reset: () => {
            set({
                chapterId: null,
                closePlot: false,
            });
        },
        refreshStats: async (chapterId: string) => {
            set({
                chapterId,
            });
            try {
                const res: ChapterStorylineDetailDto = await aigcStoryDetail(chapterId);
                const roleId = res?.roleId;
                if (res.completed) {
                    set({
                        closePlot: true,
                    });
                    Taro.eventCenter.trigger('EpicGameEnd', {
                        roleId,
                        chapterId,
                    } as IEpicPageData);
                }
            } catch (error) {
                //
            }
        },
        handleIntimacyUpgrade: async (data?: { type: number; content: Record<string, any> }) => {
            const content = data?.content ?? {};
            const chapterId = content?.chapterId;
            try {
                if (chapterId) {
                    const res = await aigcStoryDetail(chapterId);
                    if (res) {
                        content.storylineDetailDto = res;
                    }
                }
            } catch (err) {
                coronaWarnMessage(
                    '主线剧情',
                    `handleIntimacyUpgrade-aigcStoryDetail 接口：${JSON.stringify(err)}`
                );
            }
            Taro.eventCenter.trigger('taro_event_upgrade_manager_intimacy_upgrade', data);
        },
    };
});

export default mainPlotStore;
