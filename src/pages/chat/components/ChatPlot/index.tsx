import React, { useCallback, useEffect, useMemo } from 'react';
import classNames from 'classnames';
import { Image, Text, View } from '@tarojs/components';
import { ChatType } from '@/pages/chat/type';

import ICON_RIGHT_WHITE from '@/assets/common/icon_right_white.png';
import ICON_RIGHT_PINK from '@/assets/common/icon_right_pink.png';
import { DialogController, useDialog } from '@/components/dialog';
import { createSimpleModalProvider } from '@/components/SimpleModal';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import { pageDidAppear } from '@/utils/rpc';
import useDetailStore from '../../store/useDetailStore';
import MainPlotList from './MainPlotList';
import mainPlotListStore from './MainPlotList/mainPlotListStore';
import './index.scss';

function mainPlotList(dialog: DialogController) {
    return (
        <View className="w-[100%] h-[474.33px]">
            <MainPlotList dialog={dialog} />
        </View>
    );
}

const ChatPlot = ({ type }: { type: string }) => {
    const gender = useDetailStore((state) => state.robotInfo?.robotBaseInfo?.gender);

    const provider = useMemo(() => createSimpleModalProvider({ isCenter: false }), []);
    const dialog = useDialog(provider);

    const onGotoPlot = useCallback(() => {
        dialog?.show({
            children: mainPlotList(dialog),
        });
    }, [dialog]);

    const handleCustomEvent = useCallback(() => {
        const { robotUserId = '' } = getCurrentInstance().router?.params || {};
        mainPlotListStore.getState().getPlotsList(robotUserId);
    }, []);
    useEffect(() => {
        pageDidAppear(() => {
            handleCustomEvent();
        });
    }, [handleCustomEvent]);
    useEffect(() => {
        Taro.eventCenter.on('EpicPlayerPlayEnd', handleCustomEvent);
        return () => {
            Taro.eventCenter.off('EpicPlayerPlayEnd', handleCustomEvent);
        };
    }, [handleCustomEvent]);

    const hasUnCompletedPlot = mainPlotListStore((state) => state.hasUnCompletedPlot);
    return (
        <View className="chat-plot-panel">
            <View className={classNames('chat-plot', { [type]: true })} onClick={onGotoPlot}>
                <Text
                    className={classNames('tit', {
                        'red-dot': hasUnCompletedPlot,
                    })}>
                    {gender === 1 ? '他的故事' : '她的故事'}
                </Text>
                <Image
                    className="icon"
                    src={type === ChatType.CHIP ? ICON_RIGHT_PINK : ICON_RIGHT_WHITE}
                />
            </View>
        </View>
    );
};

export default React.memo(ChatPlot);
