import React, { useState, useRef, useEffect } from 'react';
import { createAnimation } from '@tarojs/taro';
import { View } from '@tarojs/components';

import ErrorBoundary from '@/components/ErrorBoundary';

import { usePageStore } from '@/components/storeContext/StoreContext';

import { MainPlotToastStore } from './MainPlotToastStore';
import './index.scss';

const MainPlotToast = ({ scene }: { scene: string }) => {
    const mainPlotToastStore = usePageStore(MainPlotToastStore);
    const toast = mainPlotToastStore((state) => state.toast);

    const [toastAnimationData, setToastAnimationData] = useState({});
    const toastNumContent = useRef('');
    const [toastContent, setToastContent] = useState('');
    const toastIntervalId = useRef<NodeJS.Timeout>();
    function hideToast() {
        const animation = createAnimation({
            duration: 10,
            timingFunction: 'ease',
        });
        animation.translateY(5).opacity(0).step();
        setToastAnimationData(animation.export());
    }
    function handleShowToast() {
        const animation = createAnimation({
            duration: 150,
            timingFunction: 'ease',
        });

        animation.translateY(-5).opacity(1).step();
        setToastAnimationData(animation.export());

        if (toastIntervalId.current !== undefined) {
            clearTimeout(toastIntervalId.current);
        }
        toastIntervalId.current = setTimeout(() => {
            hideToast();
        }, 2000);
    }

    useEffect(() => {
        if (scene !== toast?.scene) {
            return;
        }
        toastNumContent.current = toast?.num ?? '';
        setToastContent(toast?.content ?? '');
        handleShowToast();

        // eslint-disable-next-line consistent-return
        return () => {
            mainPlotToastStore.getState().clear(scene);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [toast]);

    return (
        <ErrorBoundary>
            <View
                style={{
                    position: 'absolute',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    top: '50',
                    width: '100%',
                }}>
                <View animation={toastAnimationData} style={{ opacity: 0 }}>
                    <View className="mainPlot-toastRoot">
                        <View className="toastBg">
                            <View className="toastStr">{toastContent}</View>
                            <View className="toastNumStr">{toastNumContent.current}</View>
                        </View>
                    </View>
                </View>
            </View>
        </ErrorBoundary>
    );
};
export default React.memo(MainPlotToast);
