import { ContextStore, StoreCreator, StoreLifecycle } from '@/components/storeContext/StoreContext';
import { create } from 'zustand';

class MainPlotToast {
    scene: string;

    content: string;

    num?: string;
}

export interface MainPlotListState extends StoreLifecycle {
    toast?: MainPlotToast;
    showToast: (toastValue: MainPlotToast) => void;
    clear: (scene: string) => void;
}

export const MainPlotToastStore: StoreCreator<MainPlotListState> = (contextStore: ContextStore) => {
    return create<MainPlotListState>((set, get) => ({
        toast: undefined,
        showToast: (toastValue) => {
            set({ toast: toastValue });
        },
        clear: (scene) => {
            if (scene === get().toast?.scene) {
                set({ toast: undefined });
            }
        },
    }));
};

export default MainPlotToastStore;
