import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Image, View } from '@tarojs/components';
import { createAnimation } from '@tarojs/taro';
import mainPlot_item_mask_finish from '@/assets/main-plot/mainPlot_item_mask_finish.png';
import mainPlot_item_mask_lock from '@/assets/main-plot/mainPlot_item_mask_lock.png';
import mainPlot_item_mask_opening from '@/assets/main-plot/mainPlot_item_mask_opening.png';

import { ChapterStorylineDto } from '../const';

const Unlock = ({
    item,
    checkNeedPlotOpenningAnimator,
}: {
    item: ChapterStorylineDto;
    checkNeedPlotOpenningAnimator: boolean;
}) => {
    const [animationData, setAnimationData] = useState({});
    const [imageSrc, setImageSrc] = useState('');
    const timer1 = useRef<NodeJS.Timeout>();
    const timer2 = useRef<NodeJS.Timeout>();

    const showDefault = (imageSrcValue: string) => {
        setImageSrc(`${imageSrcValue}?t=${Date.now()}`);
        const animation = createAnimation({
            timingFunction: 'linear',
        });
        animation.opacity(1).step({ duration: 50 });
        setAnimationData(animation.export());
    };

    const showUnlockAnimator = () => {
        setImageSrc('');
        timer1.current = setTimeout(() => {
            const animation = createAnimation({
                timingFunction: 'linear',
            });
            animation.opacity(0).step({ duration: 50 });
            setAnimationData(animation.export());
        }, 10);

        timer2.current = setTimeout(() => {
            setImageSrc(`${mainPlot_item_mask_opening}?t=${Date.now()}`);
            const animation = createAnimation({
                timingFunction: 'linear',
            });
            animation.opacity(1).step({ duration: 100 });
            setAnimationData(animation.export());
        }, 3000);
    };

    const showUnlockUI = useCallback(() => {
        if (checkNeedPlotOpenningAnimator) {
            showUnlockAnimator();
        } else {
            showDefault(`${mainPlot_item_mask_opening}`);
        }
    }, [checkNeedPlotOpenningAnimator]);

    useEffect(() => {
        if (!item.unlock) {
            showDefault(`${mainPlot_item_mask_lock}`);
        } else if (item.completed) {
            showDefault(`${mainPlot_item_mask_finish}`);
        } else {
            showUnlockUI();
        }
        return () => {
            if (timer1.current) {
                clearTimeout(timer1.current);
            }
            if (timer2.current) {
                clearTimeout(timer2.current);
            }
        };
    }, [item, showUnlockUI]);
    return (
        <React.Fragment>
            <View
                className="absolute top-[0px] left-[0px] w-[100%] h-[100%] opacity-[0]"
                animation={animationData}>
                {imageSrc && (
                    <View className="relative w-[100%] h-[100%] pl-[17px] pr-[13px]">
                        <Image
                            src={imageSrc}
                            className=" w-[100%] h-[100%] bg-center bg-cover bg-no-repeat"
                        />
                    </View>
                )}
            </View>
        </React.Fragment>
    );
};
export default React.memo(Unlock);
