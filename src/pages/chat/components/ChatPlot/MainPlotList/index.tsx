import mainPlot_icon_item_tag from '@/assets/main-plot/mainPlot_icon_item_tag.png';
import mainPlot_icon_item_tag_finish from '@/assets/main-plot/mainPlot_icon_item_tag_finish.png';
import mainPlot_icon_item_tag_finish_type_i from '@/assets/main-plot/mainPlot_icon_item_tag_finish_type_i.png';
import mainPlot_icon_item_tag_type_i from '@/assets/main-plot/mainPlot_icon_item_tag_type_i.png';
import mainPlot_item_bg_index from '@/assets/main-plot/mainPlot_item_bg_index.png';
import mainPlot_item_bg_index_type_i from '@/assets/main-plot/mainPlot_item_bg_index_type_i.png';
import mainPlot_item_favorability from '@/assets/main-plot/mainPlot_item_favorability.png';
import mainPlot_item_favorability_type_i from '@/assets/main-plot/mainPlot_item_favorability_type_i.png';
import mainPlot_item_icon_opening from '@/assets/main-plot/mainPlot_item_icon_opening.png';
import mainPlot_title_icon_center_subject from '@/assets/main-plot/mainPlot_title_icon_center_subject.png';
import mainPlot_title_icon_center_subject_type_i from '@/assets/main-plot/mainPlot_title_icon_center_subject_type_i.png';
import mainPlot_title_icon_left_gift from '@/assets/main-plot/mainPlot_title_icon_left_gift.png';
import mainPlot_title_icon_right_gacha from '@/assets/main-plot/mainPlot_title_icon_right_gacha.png';
import mainPlot_title_icon_right_gacha_type_i from '@/assets/main-plot/mainPlot_title_icon_right_gacha_type_i.png';
import mainPlot_dialog_cancel from '@/assets/main-plot/mainPlot_dialog_cancel.png';
import mainPlot_dialog_confirm from '@/assets/main-plot/mainPlot_dialog_confirm.png';
import mainPlote_bg from '@/assets/main-plot/mainPlote_bg.png';
import mainPlote_bg_type_i from '@/assets/main-plot/mainPlote_bg_type_i.png';
import { DialogController, useDialog } from '@/components/dialog';
import { createSimpleModalProvider } from '@/components/SimpleModal';
import { usePageStore } from '@/components/storeContext/StoreContext';
import MainAwardStoreCreator from '@/pages/chat/components/ChatPlot/MainAward/MainAwardStore';
import useDetailStore from '@/pages/chat/store/useDetailStore';
import { ChatType } from '@/pages/chat/type';
import { debounce } from '@/utils';
import { optimizeImage } from '@/utils/image';
import { pageDidAppear } from '@/utils/rpc';
import { Image, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import React, { useCallback, useEffect, useMemo } from 'react';
import useSelfStore from '@/store/useUserInfoStore';
import mainPlot_detail_award_item_img_bg from '@/assets/main-plot/mainPlot_detail_award_item_img_bg.png';
import { createAlertModalProvider } from '@/components/AlertModal';
import { useBalanceStore } from '@/store/balanceStore';
import { gotoRechargePage } from '@/pages/market/pay-panel/payPanelStore';
import { MessageErrorCode } from '@/types/im';
import { getCoinName } from '@/utils/appSourceAdapter';
import MainAward from '../MainAward';
import MainPlot from '../MainPlot';
import {
    ChapterStorylineDetailDto,
    ChapterStorylineDetailDtoDefault,
    GameChapterEndingRpcDto,
    RewardBoxCoreResourceDTO,
} from '../MainPlot/const';
import { ChapterStorylineDto } from './const';

import mainPlotListStore from './mainPlotListStore';
import MainPlotToast from './Toast';
import { MainPlotToastStore } from './Toast/MainPlotToastStore';

import { storylineUnlock } from './mainPlotListApi';

import './index.scss';
import UnlockTransform from './UnlockTransform';
import Unlock from './Unlock';

const EndingAwardItem = ({ item }: { item: GameChapterEndingRpcDto }) => {
    const awardImg =
        item.endingCardImg ??
        'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/59837700684/848d/dd73/8514/cf16afc5f5fb15db8adfbb650a5d763a.png';
    return (
        <View className="item-container">
            <View
                className="relative w-[20px] h-[20px] bg-cover bg-center flex justify-center items-center rounded-[4px] overflow-hidden"
                style={{ backgroundImage: `url(${mainPlot_detail_award_item_img_bg})` }}>
                <Image src={awardImg} className="w-[20px] h-[20px]" />
            </View>
        </View>
    );
};

const PlotAwardItem = ({ item }: { item: RewardBoxCoreResourceDTO }) => {
    const awardImg =
        item.imgUrl ??
        'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/59837700684/848d/dd73/8514/cf16afc5f5fb15db8adfbb650a5d763a.png';
    return (
        <View className="item-container">
            <View
                className="relative w-[20px] h-[20px] bg-cover bg-center flex justify-center items-center"
                style={{ backgroundImage: `url(${mainPlot_detail_award_item_img_bg})` }}>
                <Image src={awardImg} className="w-[15px] h-[15px]" />
            </View>
        </View>
    );
};

const PloAwardListComponent = ({
    awardList,
    hasReceived,
    endingList,
}: {
    awardList: RewardBoxCoreResourceDTO[];
    hasReceived: boolean;
    endingList: GameChapterEndingRpcDto[];
}) => {
    return (
        <View
            className={classNames('awardList', {
                hasReceived,
            })}>
            {awardList?.map((item: RewardBoxCoreResourceDTO) => (
                <PlotAwardItem item={item} />
            ))}
            {endingList?.map((item: GameChapterEndingRpcDto) => (
                <EndingAwardItem item={item} />
            ))}
        </View>
    );
};

function mainPlotGift() {
    return (
        <View className="absolute bottom-[0px] left-[0px] w-[100%]">
            <MainAward />
        </View>
    );
}

const unlockAlertDialog = ({ cost }: { cost: string }) => {
    const accountValue = useBalanceStore.getState().balance?.balance ?? 0;
    const costValue = `\u00A0${cost}\u00A0`;
    return (
        <View className="mainPlotUnlockStyle">
            <View className="title-container">
                <View className="title">确定使用</View>
                <View className="title-highlight">{costValue}</View>
                <View className="title">{getCoinName()}解锁</View>
            </View>
            <View className="title">该段剧情吗?</View>
            <View className="desc">当前持有：{accountValue}</View>
        </View>
    );
};

const TitleComponent: React.FC = () => {
    const dialogProvider = useMemo(() => createSimpleModalProvider({}), []);
    const dialog = useDialog(dialogProvider);
    const chatMode = useDetailStore((state) => state.chatMode);

    const giftIcon = mainPlot_title_icon_left_gift;
    const subjectIcon =
        chatMode === ChatType.IMMERSE
            ? mainPlot_title_icon_center_subject_type_i
            : mainPlot_title_icon_center_subject;
    const gachaIcon =
        chatMode === ChatType.IMMERSE
            ? mainPlot_title_icon_right_gacha_type_i
            : mainPlot_title_icon_right_gacha;

    function handleClickForGift() {
        dialog?.show({
            children: mainPlotGift(),
        });
    }

    function handleClickForAppreciate() {
        const userInfo = useSelfStore.getState().userBaseInfo;
        const key = `ShowGachaDialog_${userInfo?.userBase?.userId}`;
        const value = Taro.getStorageSync(key);
        let show = false;
        if (value !== '') {
            show = Boolean(value);
        } else {
            show = true;
        }
        Taro.setStorageSync(key, false);
        Taro.navigateTo({
            url: `/pages/appreciate/index?showGachaDialog=${show}&robotName=${
                useDetailStore.getState().robotInfo?.robotBaseInfo?.nickname
            }`,
        });
    }

    const mainAwardStore = usePageStore(MainAwardStoreCreator);
    const hasRedDot = mainAwardStore((state) => state.hasRedDot);
    useEffect(() => {
        mainAwardStore.getState().requestRewardList();
        useBalanceStore.getState().requestBalance();
    }, [mainAwardStore]);
    useEffect(() => {
        pageDidAppear(() => {
            mainAwardStore.getState().requestRewardList();
        });
    }, [mainAwardStore]);

    return (
        <View className="flex items-center h-full w-full  pl-[7px] pr-[10px]">
            {/* 左侧按钮 */}
            {/* 中间按钮 */}
            <Image src={subjectIcon} className="w-[98px] h-[35px]" />

            <Image
                src={giftIcon}
                className={classNames('w-[26px] h-[26px] ml-[2px]', {
                    'gift-icon-red-dot': hasRedDot,
                })}
                onClick={debounce(() => {
                    handleClickForGift();
                })}
            />

            {/* 右侧按钮 */}
            <Image
                src={gachaIcon}
                className="w-[90.5px] h-[44px] ml-auto"
                onClick={debounce(() => {
                    handleClickForAppreciate();
                })}
            />
        </View>
    );
};

export function mainPlot(
    dto: ChapterStorylineDetailDto,
    dialog: DialogController,
    onDismiss?: () => void
) {
    return (
        <View className="w-[100%]">
            <MainPlot dto={dto} dialog={dialog} onDismiss={onDismiss} />
        </View>
    );
}

const CardtemComponent: React.FC<{
    item: ChapterStorylineDto;
    endingList: GameChapterEndingRpcDto[];
    index: number;
    lastHasCompleted: boolean;
}> = ({
    item,
    endingList,
    index,
    lastHasCompleted,
}: {
    item: ChapterStorylineDto;
    endingList: GameChapterEndingRpcDto[];
    index: number;
    lastHasCompleted: boolean;
}) => {
    const mainPlotToastStore = usePageStore(MainPlotToastStore);
    const alertProvider = useMemo(
        () =>
            createAlertModalProvider({
                closeOnClickOverlay: true,
                fromBottom: false,
                styleType: 'v2',
            }),
        []
    );
    const alertDialog = useDialog(alertProvider);
    const provider = useMemo(() => createSimpleModalProvider({ isCenter: true }), []);
    const dialog = useDialog(provider);

    const chatMode = useDetailStore((state) => state.chatMode);
    const image = optimizeImage({
        src: item?.chapterImg,
        width: 375,
        height: 148,
    });
    const hasOpening = item.unlock;
    const imageFilter = hasOpening ? '' : 'blur(5px)';
    let title = '';
    if (hasOpening) {
        title = item.chapterName;
    }

    const descIcon = mainPlot_item_icon_opening;

    const iconMap = {
        tagIconFinish:
            chatMode === ChatType.IMMERSE
                ? mainPlot_icon_item_tag_finish_type_i
                : mainPlot_icon_item_tag_finish,
        tagIconUnComplete:
            chatMode === ChatType.IMMERSE ? mainPlot_icon_item_tag_type_i : mainPlot_icon_item_tag,
        indexIcon:
            chatMode === ChatType.IMMERSE ? mainPlot_item_bg_index_type_i : mainPlot_item_bg_index,
    };
    const tagIcon = item.completed ? iconMap.tagIconFinish : iconMap.tagIconUnComplete;
    const formatted = (index + 1).toString().padStart(2, '0');
    const milestone = item.relationTypeDesc;
    const intimacy = item.intimacy;
    const titleOpeninig = hasOpening ? 'opening' : '';

    const onGotoPlot = useCallback(() => {
        const dto = new ChapterStorylineDetailDtoDefault({
            chapterId: item.chapterId,
            roleId: item.roleId,
            chapterName: item.chapterName,
            chapterDesc: item.chapterDesc,
            chapterImg: item.chapterImg,
            relationTypeDesc: item.relationTypeDesc,
            rewardBox: item.rewardBox,
            unlock: true,
            completed: item.completed,
            endingCardInfoDtos: endingList,
        });
        dialog?.show({
            children: mainPlot(dto, dialog),
        });
    }, [item, dialog, endingList]);

    const buyInspirationConfirm = useCallback(async () => {
        try {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const res = await storylineUnlock(item.chapterId);
            mainPlotListStore.getState().getPlotsList(mainPlotListStore.getState().targetUserId);
            useBalanceStore.getState().requestBalance();
        } catch (error) {
            if (error.code === MessageErrorCode.balaceInsufficient) {
                mainPlotToastStore.getState().showToast({
                    scene: 'mainPlotList',
                    content: `${getCoinName()}不足，请充值后使用`,
                });
                setTimeout(() => {
                    gotoRechargePage();
                }, 1500);
                return;
            }
            mainPlotToastStore
                .getState()
                .showToast({ scene: 'mainPlotList', content: `${error.message}` });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    const handleItemClick = useCallback(() => {
        if (!item.unlock) {
            if (index !== 0 && !lastHasCompleted) {
                mainPlotToastStore
                    .getState()
                    .showToast({ scene: 'mainPlotList', content: `请先完成上一等级剧情`, num: '' });
                return;
            }

            if (item?.price?.price !== 0) {
                alertDialog?.show({
                    children: unlockAlertDialog({ cost: `${item?.price?.price}` }),
                    leftFuncImageRes: mainPlot_dialog_cancel,
                    rightFuncImageRes: mainPlot_dialog_confirm,
                    onRightFuncClick: buyInspirationConfirm,
                });
                return;
            }
            buyInspirationConfirm();
            return;
        }
        onGotoPlot();
    }, [
        item,
        onGotoPlot,
        index,
        lastHasCompleted,
        buyInspirationConfirm,
        mainPlotToastStore,
        alertDialog,
    ]);

    const userId = useDetailStore.getState().userId;
    const checkNeedPlotOpenningAnimator = mainPlotListStore
        .getState()
        .checkNeedPlotOpenningAnimator(userId, item.chapterId);

    return (
        <View
            className="item-container"
            onClick={debounce(() => {
                handleItemClick();
            })}>
            <View className="flex flex-col w-[100%] h-[100%]">
                <View className="flex flex-row w-[100%] h-[36.33px] items-center">
                    <View className="relative w-[35.67px] h-[35.33px] ml-[28px] mt-[1px]">
                        <Image className="w-[35.67px] h-[35.33px]" src={iconMap.indexIcon} />
                        <View
                            className={classNames('index absolute top-[4px] left-[7px]', {
                                [chatMode]: true,
                            })}>
                            {formatted}
                        </View>
                    </View>
                    <View className="flex flex-col h-[29] ml-[-1px]">
                        <View
                            className={classNames('milestone-text', {
                                [chatMode]: true,
                            })}>
                            {milestone}
                        </View>
                    </View>
                    <View className="flex flex-row h-[18px] ml-[10px] mt-[1px] items-center">
                        {chatMode === ChatType.IMMERSE && (
                            <Image
                                className="w-[18px] h-[18px]"
                                src={mainPlot_item_favorability_type_i}
                            />
                        )}
                        {chatMode !== ChatType.IMMERSE && (
                            <Image className="w-[12px] h-[12px]" src={mainPlot_item_favorability} />
                        )}
                        <View
                            className={classNames('favorability-text  ml-[4px]', {
                                [chatMode]: true,
                            })}>
                            {'好感度>'}
                            {intimacy}
                        </View>
                    </View>

                    <View className="flex flex-row h-[18px] ml-auto mr-[30px] mt-[1px] items-center">
                        <PloAwardListComponent
                            awardList={item?.rewardBox?.resource ?? []}
                            hasReceived={item.completed}
                            endingList={item?.endingCardInfoDtos}
                        />
                    </View>
                </View>
                <View className="relative w-[100%] h-[148px] mt-[-24.33px]">
                    <View className="absolute top-[0px] left-[0px] w-[100%] h-[100%]">
                        <View className="relative w-[100%] h-[100%] pl-[38px] pr-[32px] pt-[22px] pb-[19px]">
                            <View
                                style={{
                                    backgroundImage: image ? `url(${image})` : 'none',
                                    filter: imageFilter,
                                }}
                                className="w-[100%] h-[100%] rounded-tl-[28px] rounded-tr-[2px] rounded-bl-[2px] rounded-br-[28px] bg-center bg-cover bg-no-repeat"
                            />
                        </View>
                    </View>
                    <Unlock
                        item={item}
                        checkNeedPlotOpenningAnimator={checkNeedPlotOpenningAnimator}
                    />
                    <UnlockTransform
                        item={item}
                        checkNeedPlotOpenningAnimator={checkNeedPlotOpenningAnimator}
                    />
                </View>
            </View>
            {title && (
                <View className="absolute flex flex-row left-[46px] top-[123px] items-center">
                    <Image src={descIcon} className="flex flex-col w-[12px] h-[12px]" />
                    <View
                        className={classNames('title', {
                            [chatMode]: true,
                            [titleOpeninig]: true,
                        })}>
                        {title}
                    </View>
                </View>
            )}
            <Image src={tagIcon} className="w-[19.5px] h-[132px] absolute top-[6px] left-[6px]" />
        </View>
    );
};

const PlotListComponent: React.FC = () => {
    const plotsResp = mainPlotListStore((state) => state.plotsResp);
    return (
        <View className="flex-1 grid grid-cols-1 gap-y-[-4px] w-[100%]">
            {plotsResp?.chapterList?.map((item: ChapterStorylineDto, index: number) => (
                <CardtemComponent
                    item={item}
                    index={index}
                    lastHasCompleted={plotsResp?.chapterList?.[index - 1]?.completed}
                    endingList={item.endingCardInfoDtos}
                />
            ))}
        </View>
    );
};

function MainPlotListPage({ dialog }: { dialog: DialogController }) {
    const chatMode = useDetailStore((state) => state.chatMode);
    const bg = chatMode === ChatType.IMMERSE ? mainPlote_bg_type_i : mainPlote_bg;

    const handleCustomEvent = () => {
        mainPlotListStore.getState().getPlotsList(mainPlotListStore.getState().targetUserId);
        dialog?.hide?.();
    };
    useEffect(() => {
        Taro.eventCenter.on('EpicGameEnd', handleCustomEvent);
        return () => {
            Taro.eventCenter.off('EpicGameEnd');
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <View>
            <View
                className="main-plat-list-page"
                onClick={(e) => {
                    e.stopPropagation();
                }}>
                <View className="relative w-[100%] h-[100%] flex flex-col">
                    {chatMode === ChatType.IMMERSE && <View className="bg-mask" />}
                    {chatMode === ChatType.IMMERSE && <View className="bg-mask2" />}
                    <Image src={bg} className="main-plat-bg-img" />
                    <View className="absolute top-[12px] left-[0px] w-[100%] h-[69.67px] pl-[9px] pr-[10px]">
                        <TitleComponent />
                    </View>
                    <View className="absolute top-[80px] left-[0px] w-[100%] h-[395px] pb-[12px] overflow-auto">
                        <PlotListComponent />
                    </View>
                </View>
            </View>
            <View className="mainPlotToastStyle">
                <MainPlotToast scene="mainPlotList" />
            </View>
        </View>
    );
}

export default MainPlotListPage;
