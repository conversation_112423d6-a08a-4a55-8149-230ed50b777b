import { create } from 'zustand';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { getStorageSync, setStorageSync, showToast } from '@tarojs/taro';
import { deserializeNewCards, NewCardsType, serializeNewCards } from '@/pages/appreciate/utils';
import { getTypeKey } from '@/pages/appreciate/const';
import { ChapterStorylineVo } from './const';
import { aigcStoryline } from './mainPlotListApi';

interface MainPlotListState {
    targetUserId: string;
    hasAiChapter?: boolean;
    hasUnCompletedPlot?: boolean;
    plotsResp?: ChapterStorylineVo;
    getPlotsList: (targetUserId: string) => void;
    reset: () => void;
    handleIntimacyUpgrade: (data: { type: number; content: Record<string, any> }) => void;
    newCards: NewCardsType;
    checkNeedPlotOpenningAnimator: (robotUserId: string, chapterId: string) => boolean; // 校验是否需要播放解锁动效~
    clearNeedPlotOpenningAnimator: (robotUserId: string, chapterId: string) => void;
}

export const TAG_MAIN_PLOT_CHAPTION_UPDATED = 'mainPlot-chaptionUpdated';
const localCard = getStorageSync(TAG_MAIN_PLOT_CHAPTION_UPDATED) || '';
const mainPlotListStore = create<MainPlotListState>((set, get) => {
    return {
        targetUserId: '',
        hasAiChapter: false,
        hasUnCompletedPlot: false,
        plotsResp: null,
        getPlotsList: async (targetUserId) => {
            if (!targetUserId) {
                return;
            }
            set({
                targetUserId,
            });
            try {
                const res: ChapterStorylineVo = await aigcStoryline(targetUserId);
                set({
                    plotsResp: res,
                    hasAiChapter: res?.chapterList?.length > 0,
                    hasUnCompletedPlot: res?.chapterList?.some(
                        (chapter) => chapter.completed === false && chapter.unlock === true
                    ),
                });
            } catch (err) {
                set({
                    plotsResp: null,
                    hasAiChapter: false,
                    hasUnCompletedPlot: false,
                });
                coronaWarnMessage('主线剧情', `aigcStoryline 接口：${JSON.stringify(err)}`);
                showToast({
                    title: err.message,
                    icon: 'none',
                });
            }
        },
        reset: () => {
            set({
                plotsResp: null,
                hasAiChapter: false,
                hasUnCompletedPlot: false,
            });
        },
        handleIntimacyUpgrade: async (data?: { type: number; content: Record<string, any> }) => {
            const content = data?.content ?? {};
            const chapterId = content?.chapterId;
            if (chapterId) {
                get().getPlotsList(get().targetUserId);
            }
        },
        newCards: deserializeNewCards(localCard),
        checkNeedPlotOpenningAnimator(robotUserId, chapterId) {
            const typeKey = getTypeKey('mainPlot');
            const key = `${robotUserId}-${chapterId}`;
            const keySet = get().newCards?.get(typeKey)?.get(robotUserId);
            const result = keySet?.has(key) || false;
            return !result;
        },
        clearNeedPlotOpenningAnimator(robotUserId, chapterId) {
            const typeKey = getTypeKey('mainPlot');
            const chapterKey = `${robotUserId}-${chapterId}`;
            const cardsUpdated = get().newCards;
            const chapterRobot = cardsUpdated.get(typeKey) || new Map();
            const chapterSet = chapterRobot.get(robotUserId) || new Set();
            chapterSet.add(chapterKey);
            chapterRobot.set(robotUserId, chapterSet);
            cardsUpdated.set(typeKey, chapterRobot);
            set({
                newCards: cardsUpdated,
            });
            setStorageSync(TAG_MAIN_PLOT_CHAPTION_UPDATED, serializeNewCards(cardsUpdated));
        },
    };
});

export default mainPlotListStore;
