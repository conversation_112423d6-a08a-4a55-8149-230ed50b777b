.main-plat-list-page {
    height: 466px;
    width: 100%;
    display: flex;
    position: absolute;
    bottom: 0;
    left: 0;
    background-color: transparent;

    .bg-mask {
        width: 100%;
        height: 457px;
        position: absolute;
        bottom: 0;
        left: 0;
        backdrop-filter: blur(2px);
        box-sizing: border-box;
        border-top-right-radius: 45px;
        background-color: rgba(187, 187, 187, 0.2);
    }

    .bg-mask2 {
        width: 100%;
        height: 377px;
        position: absolute;
        bottom: 0;
        left: 0;
        background: linear-gradient(180.15deg, rgba(20, 20, 20, 0) 1.38%, #141414 99.87%);
    }

    .main-plat-bg-img {
        /* 布局定位 */
        width: 100%;
        height: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
    }

    .gift-icon-red-dot {
        &::after {
            content: '';
            position: absolute;
            top: 2px;
            right: 0;
            width: 5px;
            height: 5px;
            background-color: #ff561e;
            border-radius: 50%;
        }
    }

    .item-container {
        position: relative;
        width: 100%;
        height: 160px;

        .index {
            font-family: 'SourceHanSerifCN-Bold', sans-serif;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;
            letter-spacing: 0;
            text-align: center;
            color: white;
            text-shadow: 0 0 6px rgba(255, 138, 43, 0.24), 0 0 6px rgba(255, 104, 158, 1);

            &.immerse {
                text-shadow: 0 0 6px rgba(255, 138, 43, 0.24), 0 0 6px rgba(255, 104, 158, 1);
            }
        }

        .milestone-text {
            font-family: 'SourceHanSerifCN-Bold', sans-serif;
            font-weight: 400;
            font-size: 17px;
            line-height: 21px;
            letter-spacing: 0;
            text-align: center;
            color: rgba(255, 139, 179, 1);

            &.immerse {
                color: rgba(255, 255, 255, 1);
            }
        }

        .favorability-text {
            font-weight: 400;
            font-size: 11px;
            line-height: 15px;
            letter-spacing: 0;
            text-align: center;
            color: rgba(255, 104, 158, 1);

            &.immerse {
                color: rgba(255, 255, 255, 1);
            }
        }

        .title {
            font-family: 'SourceHanSerifCN-Bold', sans-serif;
            font-weight: 400;
            font-size: 13px;
            margin-left: 2.8px;
            line-height: 100%;
            letter-spacing: 0;
            text-align: center;
            color: white;
            opacity: 0.4;
            transform: skewX(-10deg);
            text-shadow: 0 0 6px rgba(255, 138, 43, 0.24), 0 0 6px rgba(255, 104, 158, 1);

            &.immerse {
                color: white;
            }

            &.opening {
                opacity: 1;
            }
        }

        .unlockInfo {
            display: flex;
            margin-top: 4px;
            flex-direction: row;
            justify-content: center;
            align-items: center;

            .cost {
                font-weight: 400;
                font-size: 8px;
                line-height: 100%;
                color: rgba(255, 104, 158, 1);
            }

            .free {
                font-weight: 400;
                font-size: 6px;
                line-height: 100%;
                text-decoration: line-through;
                color: rgba(255, 255, 255, 1);
            }

            .freeNotify {
                font-weight: 600;
                font-size: 8px;
                line-height: 100%;
                text-align: center;
                color: linear-gradient(90deg, #ffedb1 0%, #ffdc68 100%);
            }
        }

        .awardList {
            width: 100%;
            height: 20px;
            display: flex;
            flex-direction: row;
            gap: 5px;

            .item-container {
                width: 20px;
                height: 20px;
            }
        }
    }
}

.mainPlotToastStyle {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 120;
    pointer-events: none;
    top: calc(44px + var(--status-bar-height));
}

.mainPlotUnlockStyle {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-items: center;
    margin-top: 30px;

    .title-container {
        display: flex;
    }

    .title {
        font-weight: 700;
        font-size: 18px;
        line-height: 21.6px;
        text-align: center;
        color: rgba(72, 59, 86, 1);
    }

    .title-highlight {
        font-weight: 700;
        font-size: 18px;
        line-height: 21.6px;
        text-align: center;
        color: rgba(255, 118, 167, 1);
    }

    .desc {
        font-weight: 700;
        font-size: 13px;
        line-height: 13px;
        letter-spacing: 0;
        margin-top: 14px;
        text-align: center;
        color: rgba(72, 59, 86, 1);
        opacity: 0.4;
    }
}
