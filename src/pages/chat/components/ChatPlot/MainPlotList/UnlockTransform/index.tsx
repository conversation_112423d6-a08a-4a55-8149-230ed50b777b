import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Image, View } from '@tarojs/components';
import { createAnimation } from '@tarojs/taro';
import mainPlot_item_mask_lock_tag from '@/assets/main-plot/mainPlot_item_mask_lock_tag.png';
import mainPlot_item_mask_unlock_free_tag from '@/assets/main-plot/mainPlot_item_mask_unlock_free_tag.png';
import mainPlot_item_mask_opening_animator from '@/assets/main-plot/mainPlot_item_mask_opening_animator.png';

import useDetailStore from '@/pages/chat/store/useDetailStore';
import { getCoinIcon } from '@/utils/appSourceAdapter';
import { ChapterStorylineDto } from '../const';
import mainPlotListStore from '../mainPlotListStore';

const UnlockTransform = ({
    item,
    checkNeedPlotOpenningAnimator,
}: {
    item: ChapterStorylineDto;
    checkNeedPlotOpenningAnimator: boolean;
}) => {
    const imageSrcLock = useRef<string>(`${mainPlot_item_mask_opening_animator}?t=${Date.now()}`);
    const timer1 = useRef<NodeJS.Timeout>();
    const timer2 = useRef<NodeJS.Timeout>();
    const timer3 = useRef<NodeJS.Timeout>();
    const [animationLockInfoData, setAnimationLockInfoData] = useState({});
    const [animationUnlockData, setAnimationUnlockData] = useState({});

    const showDefault = () => {
        imageSrcLock.current = null;
        const animation = createAnimation({
            timingFunction: 'linear',
        });
        animation.opacity(0).step({ duration: 50 });
        setAnimationLockInfoData(animation.export());

        const animation1 = createAnimation({
            timingFunction: 'linear',
        });
        animation1.opacity(0).step({ duration: 50 });
        setAnimationUnlockData(animation1.export());
    };

    const showInfo = () => {
        const animation = createAnimation({
            timingFunction: 'linear',
        });
        animation.opacity(1).step({ duration: 50 });
        setAnimationLockInfoData(animation.export());
    };

    const showUnlockAnimator = () => {
        const lockInfoAnimation = createAnimation({
            timingFunction: 'linear',
        });
        lockInfoAnimation.opacity(0).step({ duration: 50 });
        setAnimationLockInfoData(lockInfoAnimation.export());

        timer1.current = setTimeout(() => {
            const unlockAnimation = createAnimation({
                timingFunction: 'linear',
            });
            unlockAnimation.opacity(1).step({ duration: 50 });
            setAnimationUnlockData(unlockAnimation.export());
        }, 10);

        timer2.current = setTimeout(() => {
            const animation = createAnimation({
                timingFunction: 'linear',
            });
            animation.opacity(0).step({ duration: 100 });
            setAnimationUnlockData(animation.export());
        }, 3000);

        timer3.current = setTimeout(() => {
            imageSrcLock.current = '';
        }, 3100);
    };

    const showUnlockUI = useCallback(() => {
        const userId = useDetailStore.getState().userId;
        if (checkNeedPlotOpenningAnimator) {
            showUnlockAnimator();
            mainPlotListStore.getState().clearNeedPlotOpenningAnimator(userId, item.chapterId);
        } else {
            showDefault();
        }
    }, [checkNeedPlotOpenningAnimator, item.chapterId]);

    useEffect(() => {
        if (!item.unlock) {
            showInfo();
        } else if (item.completed) {
            showDefault();
        } else {
            showUnlockUI();
        }
        return () => {
            if (timer1.current) {
                clearTimeout(timer1.current);
            }
            if (timer2.current) {
                clearTimeout(timer2.current);
            }
            if (timer3.current) {
                clearTimeout(timer3.current);
            }
        };
    }, [item, showUnlockUI]);

    const unlockFree = item.price?.price === 0 && item.price?.originalPrice > 0;
    const unlockCost = unlockFree ? item.price?.originalPrice : item.price?.price;
    const showLockInfo = !item.unlock || (!item.completed && checkNeedPlotOpenningAnimator);

    return (
        <React.Fragment>
            <View
                className="absolute top-[0px] left-[0px] w-[100%] h-[100%] flex flex-col justify-center items-center"
                animation={animationLockInfoData}>
                {showLockInfo && (
                    <React.Fragment>
                        <Image
                            src={mainPlot_item_mask_lock_tag}
                            className=" w-[156px] h-[72px] bg-center bg-cover bg-no-repeat"
                        />
                        <View className="unlockInfo">
                            <Image
                                src={getCoinIcon()}
                                className=" w-[10px] h-[10px] bg-center bg-cover bg-no-repeat"
                            />
                            {unlockFree && (
                                <React.Fragment>
                                    <View className="free">{unlockCost}</View>
                                    <Image
                                        src={mainPlot_item_mask_unlock_free_tag}
                                        className=" w-[32px] h-[8px] ml-[2px] bg-center bg-cover bg-no-repeat"
                                    />
                                </React.Fragment>
                            )}
                            {!unlockFree && <View className="cost">{unlockCost}</View>}
                        </View>
                    </React.Fragment>
                )}
            </View>

            <View
                className="absolute top-[0px] left-[0px] w-[100%] h-[100%] opacity-[0]"
                animation={animationUnlockData}>
                {imageSrcLock.current && (
                    <View className="relative w-[100%] h-[100%] pl-[17px] pr-[13px]">
                        <Image
                            src={imageSrcLock.current}
                            className=" w-[100%] h-[100%] bg-center bg-cover bg-no-repeat"
                        />
                    </View>
                )}
            </View>
        </React.Fragment>
    );
};
export default React.memo(UnlockTransform);
