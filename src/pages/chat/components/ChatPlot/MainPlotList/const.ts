// eslint-disable-next-line max-classes-per-file
import { GameChapterEndingRpcDto, RewardBoxCoreDetailDTO } from '../MainPlot/const';

/**
 * 提前解锁价格~
 */
export interface ChapterPriceDto {
    originalPrice: number; // 原价
    price: number; // 现价
}
/**
 * 抽奖奖品结果
 */
export interface ChapterStorylineDto {
    chapterId: string;
    chapterName: string;
    chapterImg: string;
    chapterDesc: string;
    relationTypeDesc: string;
    intimacy: number;
    unlock: boolean;
    completed: boolean;
    roleId: string;
    rewardBox: RewardBoxCoreDetailDTO;
    price?: ChapterPriceDto;
    endingCardInfoDtos: GameChapterEndingRpcDto[];
}

/**
 * 主线剧情信息
 */
export interface ChapterStorylineVo {
    chapterList: ChapterStorylineDto[];
}
