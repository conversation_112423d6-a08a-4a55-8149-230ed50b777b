import fetch from '@/utils/fetch';
import { StageDetailRewardList } from '@/pages/chat/components/ChatPlot/type/Reward';

/**
 * https://music-ox.hz.netease.com/ox/music/api/detail/4880516/basic
 * @param type 主线剧情~
 * @returns
 */
export const aigcStoryline = (targetUserIdValue: string): any =>
    fetch('/api/mirth/user/aigc/storyline', {
        method: 'POST',
        data: {
            targetUserId: targetUserIdValue,
        },
    });

export const storylineUnlock = (chapterIdValue: string): any =>
    fetch('/api/mirth/user/aigc/storyline/unlock', {
        method: 'POST',
        data: {
            chapterId: chapterIdValue,
        },
    });

export const rewardList = (): Promise<StageDetailRewardList> =>
    fetch('/api/mirth/home/<USER>/chapter/activity/reward/list', {
        method: 'POST',
    });

export const receiveReward = (missionId: number, missionBoxId: number): Promise<void> =>
    fetch('/api/mirth/home/<USER>/mission/reward/receive', {
        method: 'POST',
        data: {
            missionId,
            missionBoxId,
        },
    });
