.main-award-page {
    position: relative;
    width: 100%;
    height: 466px;
    display: flex;
    flex-direction: column;

    .bg-mask {
        width: 100%;
        height: 466px;
        position: absolute;
        bottom: 0;
        left: 0;
        backdrop-filter: blur(2px);
        box-sizing: border-box;
        border-top-right-radius: 45px;
        background-color: rgba(187, 187, 187, 0.2);
    }

    .bg-mask2 {
        width: 100%;
        height: 400px;
        position: absolute;
        bottom: 0;
        left: 0;
        background: linear-gradient(180.15deg, rgba(20, 20, 20, 0) 1.38%, #141414 99.87%);
    }

    .main-award-page-bg {
        width: 100%;
        height: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
    }

    .header-container {
        margin-top: 17px;
        display: flex;
        flex-direction: row;
        align-items: center;
        position: relative;

        .back-btn {
            width: 32px;
            height: 32px;
            background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_back.png');
            background-size: 100% 100%;
            margin-left: 18px;
        }

        .title-label {
            margin-left: 10px;
            width: 78px;
            height: 35px;
            background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_title.png');
            background-size: 100% 100%;

            &.immerse {
                background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_title_type_i.png');
            }
        }
    }

    .award-list {
        margin-top: 5px;
        width: 100%;
        flex: 1;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        display: flex;
        flex-direction: column;
        transform: translateZ(0);
        will-change: transform;

        .award-item {
            display: flex;
            flex-direction: row;
            width: 100%;
            align-items: center;
            margin-bottom: 10px;

            .step-icon {
                position: relative;
                width: 26px;
                height: 26px;
                background-size: 100% 100%;
                margin-left: 23px;
                flex-shrink: 0;
                background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_status_finished.png');

                &.finished {
                    background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_status_finished.png');

                    &.immerse {
                        background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_status_finished_type_i.png');
                    }
                }

                &.receive {
                    background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_status_receive.png');

                    &.immerse {
                        background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_status_receive_type_i.png');
                    }
                }

                &.not-finish {
                    background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_status_not_finish.png');

                    &.immerse {
                        background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_status_not_finish_type_i.png');
                    }
                }

                &::after {
                    content: '';
                    display: block;
                    position: absolute;
                    left: 50%;
                    transform: translateX(-50%);
                    bottom: -60px;
                    width: 2px;
                    height: 60px;
                    background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_line.png');
                    background-size: 100% 100%;
                }
            }

            .content-container {
                display: flex;
                flex-direction: row;
                margin-left: -7px;
                margin-right: 9px;
                flex: 1;
                padding: 12px 23px 12px 20px;
                height: 76px;
                align-items: center;
                background-size: 100% 100%;
                background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_status_bg_finished.png');

                &.finished {
                    background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_status_bg_finished.png');

                    &.immerse {
                        background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_status_bg_finished_type_i.png');
                    }
                }

                &.receive {
                    background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_status_bg_receive.png');
                    opacity: 0.4;

                    &.immerse {
                        background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_status_bg_receive_type_i.png');
                    }
                }

                &.not-finish {
                    background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_status_bg_not_finish.png');

                    &.immerse {
                        background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_status_bg_not_finish_type_i.png');
                    }
                }

                .award-icon-container {
                    width: 41px;
                    height: 41px;
                    position: relative;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;

                    &.red-dot {
                        &::after {
                            content: '';
                            position: absolute;
                            top: 4px;
                            right: 2px;
                            width: 5px;
                            height: 5px;
                            background-color: #ff561e;
                            border-radius: 50%;
                        }
                    }

                    .award-bg {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 41px;
                        height: 41px;
                        background-size: 100% 100%;

                        &.active {
                            background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_icon_bg_active.png');

                            &.immerse {
                                background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_icon_bg_active_type_i.png');
                            }
                        }

                        &.inactive {
                            background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_icon_bg_inactive.png.png');

                            &.immerse {
                                background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_icon_bg_inactive_type_i.png');
                            }
                        }
                    }

                    .award-icon {
                        width: 36px;
                        height: 36px;

                        &.inactive {
                            opacity: 0.4;
                        }
                    }
                }

                .text-container {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    margin-left: 11px;

                    .name-container {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        max-width: 150px;
                        overflow: hidden;

                        .name {
                            color: rgba(125, 109, 140, 0.8);
                            font-size: 14px;
                            font-style: normal;
                            font-weight: 600;
                            line-height: normal;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;

                            &.immerse {
                                color: rgba(255, 255, 255, 0.8);
                            }

                            .highlight {
                                color: rgba(125, 109, 140, 1);
                                font-size: 14px;
                                font-style: normal;
                                font-weight: 600;
                                line-height: normal;
                                display: inline-block;

                                &.immerse {
                                    color: rgba(255, 255, 255, 1);
                                }
                            }
                        }
                    }

                    .desc-container {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        margin-top: 1px;

                        .title {
                            color: #7d6d8c;
                            font-size: 11px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: normal;
                            opacity: 0.6;

                            &.immerse {
                                color: #fff;
                            }
                        }

                        .progress {
                            color: #ff689e;
                            font-size: 11px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: normal;
                            margin-left: 2px;

                            &.immerse {
                                color: #ff689e;
                            }
                        }

                        .total {
                            color: #7d6d8c;
                            font-size: 11px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: normal;

                            &.immerse {
                                color: #fff;
                            }
                        }
                    }
                }

                .award-receive-btn {
                    flex-shrink: 0;
                    width: 62px;
                    height: 26px;
                    background-image: url('../../../../../assets/main-plot/mainPlot_icon_award_get.png');
                    background-size: 100% 100%;
                }
            }
        }
    }

    .award-list .award-item:last-child .step-icon::after {
        display: none;
    }
}
