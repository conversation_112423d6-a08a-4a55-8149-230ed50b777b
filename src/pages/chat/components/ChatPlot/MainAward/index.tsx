import React, { useCallback, useContext, useEffect, useMemo } from 'react';
import { Image, Text, View } from '@tarojs/components';
import './index.scss';
import useDetailStore from '@/pages/chat/store/useDetailStore';
import { ChatType } from '@/pages/chat/type';
import classNames from 'classnames';
import { DialogAction } from '@/components/dialog/DialogActionProvider';
import { usePageStore } from '@/components/storeContext/StoreContext';
import {
    MissionRewardDetail,
    StageDetailReward,
    UserMissionAppStatus,
} from '@/pages/chat/components/ChatPlot/type/Reward';
import MainAwardStoreCreator from '@/pages/chat/components/ChatPlot/MainAward/MainAwardStore';
import { useDialog } from '@/components/dialog';
import { createSimpleModalProvider } from '@/components/SimpleModal';
import { PreviewModalContent } from '@/pages/task/components/PreviewModal';
import { IResource } from '@/pages/task/type';
import { ITouchEvent } from '@tarojs/components/types/common';
import { SuccessModalContent } from '@/pages/task/components/SuccessModal';
import mainPlote_bg_type_i from '@/assets/main-plot/mainPlote_bg_type_i.png';
import mainPlote_bg from '@/assets/main-plot/mainPlote_bg.png';

const MissionName = ({ missionName, value }: { missionName: string; value: number }) => {
    const parts = missionName.split('%s');
    const chatMode = useDetailStore((state) => state.chatMode);
    const isImmerse = chatMode === ChatType.IMMERSE;
    return (
        <Text className="name-container">
            <Text
                className={classNames('name', {
                    immerse: isImmerse,
                })}>
                {parts[0]}
                <Text
                    className={classNames('highlight', {
                        immerse: isImmerse,
                    })}>
                    {value}
                </Text>
                {parts[1]}
            </Text>
        </Text>
    );
};

const PreviewRewardDetail = ({ list }: { list: MissionRewardDetail[] }) => {
    const previewList: IResource[] = list.map(
        (item) =>
            ({
                id: item.id || 0,
                name: item.rewardName || '',
                imgUrl: item.imgUrl || '',
                desc: item.rewardDesc || '',
                resNum: item.rewardNum || 0,
            } as IResource)
    );
    return <PreviewModalContent previewList={previewList} />;
};

const SuccessRewardDetail = ({ list }: { list: MissionRewardDetail[] }) => {
    const previewList: IResource[] = list.map(
        (item) =>
            ({
                id: item.id || 0,
                name: item.rewardName || '',
                imgUrl: item.imgUrl || '',
                desc: item.rewardDesc || '',
                resNum: item.rewardNum || 0,
            } as IResource)
    );
    const { dismiss } = useContext(DialogAction);
    const onClickDismiss = useCallback(() => {
        dismiss?.();
    }, [dismiss]);
    return <SuccessModalContent previewList={previewList} onClick={onClickDismiss} />;
};

const MainAward: React.FC = () => {
    const mainAwardStore = usePageStore(MainAwardStoreCreator);
    const rewardList = mainAwardStore((state) => state.rewardList);

    useEffect(() => {
        mainAwardStore.getState().requestRewardList(true);
    }, [mainAwardStore]);

    const chatMode = useDetailStore((state) => state.chatMode);
    const isImmerse = chatMode === ChatType.IMMERSE;

    const { dismiss } = useContext(DialogAction);
    const onClickBack = useCallback(() => {
        dismiss?.();
    }, [dismiss]);

    const receiveDialogProvider = useMemo(
        () => createSimpleModalProvider({ isCenter: true, closeOnClickOverlay: false }),
        []
    );
    const receiveDialog = useDialog(receiveDialogProvider);
    const receiveReward = useCallback(
        (item: StageDetailReward) => {
            mainAwardStore
                .getState()
                .receiveReward(item.missionId)
                .then((success) => {
                    if (success) {
                        receiveDialog?.show({
                            children: <SuccessRewardDetail list={item.rewardDetailList} />,
                        });
                    }
                });
        },
        [mainAwardStore, receiveDialog]
    );
    const onClickReceive = useCallback(
        (event: ITouchEvent, item: StageDetailReward) => {
            event.stopPropagation();
            receiveReward(item);
        },
        [receiveReward]
    );

    const previewDialogProvider = useMemo(() => createSimpleModalProvider({ isCenter: true }), []);
    const previewDialog = useDialog(previewDialogProvider);
    const onClickIcon = useCallback(
        (item: StageDetailReward) => {
            if (item.userMissionStatus === UserMissionAppStatus.FINISHED) {
                receiveReward(item);
                return;
            }
            previewDialog?.show({
                children: <PreviewRewardDetail list={item.rewardDetailList} />,
            });
        },
        [previewDialog, receiveReward]
    );

    return (
        <View className="main-award-page">
            {chatMode === ChatType.IMMERSE && <View className="bg-mask" />}
            {chatMode === ChatType.IMMERSE && <View className="bg-mask2" />}
            <Image
                src={chatMode === ChatType.IMMERSE ? mainPlote_bg_type_i : mainPlote_bg}
                className="main-award-page-bg"
            />
            <View className="header-container">
                <View className="back-btn" onClick={onClickBack} />
                <View
                    className={classNames('title-label', {
                        immerse: isImmerse,
                    })}
                />
            </View>

            <View className="award-list">
                {rewardList?.detailRewardList.map((item) => (
                    <View className="award-item" key={item.missionId}>
                        <View
                            className={classNames('step-icon', {
                                immerse: isImmerse,
                                finished: item.userMissionStatus === UserMissionAppStatus.FINISHED,
                                receive:
                                    item.userMissionStatus === UserMissionAppStatus.HAS_RECEIVE,
                                'not-finish':
                                    item.userMissionStatus === UserMissionAppStatus.NOT_FINISH,
                            })}
                        />
                        <View
                            className={classNames('content-container', {
                                immerse: isImmerse,
                                finished: item.userMissionStatus === UserMissionAppStatus.FINISHED,
                                receive:
                                    item.userMissionStatus === UserMissionAppStatus.HAS_RECEIVE,
                                'not-finish':
                                    item.userMissionStatus === UserMissionAppStatus.NOT_FINISH,
                            })}>
                            <View
                                className={classNames('award-icon-container', {
                                    'red-dot':
                                        item.userMissionStatus === UserMissionAppStatus.FINISHED,
                                })}
                                onClick={() => onClickIcon(item)}>
                                <View
                                    className={classNames('award-bg', {
                                        active:
                                            item.userMissionStatus !==
                                            UserMissionAppStatus.NOT_FINISH,
                                        inactive:
                                            item.userMissionStatus ===
                                            UserMissionAppStatus.NOT_FINISH,
                                        immerse: isImmerse,
                                    })}
                                />
                                <Image
                                    src={item.rewardIcon}
                                    className={classNames('award-icon', {
                                        inactive:
                                            item.userMissionStatus ===
                                            UserMissionAppStatus.NOT_FINISH,
                                    })}
                                    mode="aspectFit"
                                />
                            </View>
                            <View className="text-container">
                                <MissionName
                                    missionName={item.missionName}
                                    value={item.targetValue}
                                />

                                <View className="desc-container">
                                    <Text
                                        className={classNames('title', {
                                            immerse: isImmerse,
                                        })}>
                                        已完成
                                    </Text>
                                    <Text
                                        className={classNames({
                                            immerse: isImmerse,
                                            progress:
                                                item.userMissionStatus !==
                                                UserMissionAppStatus.HAS_RECEIVE,
                                            total:
                                                item.userMissionStatus ===
                                                UserMissionAppStatus.HAS_RECEIVE,
                                        })}>
                                        {item.missionProgress}
                                    </Text>
                                    <Text
                                        className={classNames('total', {
                                            immerse: isImmerse,
                                        })}>
                                        /{item.targetValue}
                                    </Text>
                                </View>
                            </View>
                            {item.userMissionStatus === UserMissionAppStatus.FINISHED && (
                                <View
                                    className="award-receive-btn"
                                    onClick={(event) => onClickReceive(event, item)}
                                />
                            )}
                        </View>
                    </View>
                ))}
            </View>
        </View>
    );
};

export default MainAward;
