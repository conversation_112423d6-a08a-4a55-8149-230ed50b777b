import { ContextStore, StoreCreator, StoreLifecycle } from '@/components/storeContext/StoreContext';
import { create } from 'zustand';
import {
    StageDetailRewardList,
    UserMissionAppStatus,
} from '@/pages/chat/components/ChatPlot/type/Reward';

import Taro, { showLoading, showToast, hideLoading } from '@tarojs/taro';
import { receiveReward, rewardList } from '../MainPlotList/mainPlotListApi';

export interface MainAwardState extends StoreLifecycle {
    rewardList?: StageDetailRewardList;
    hasRedDot?: boolean;
    requestRewardList: (showToast?: boolean) => Promise<void>;
    receiveReward: (missionId: number) => Promise<boolean>;
}

export const MainAwardStoreCreator: StoreCreator<MainAwardState> = (contextStore: ContextStore) => {
    const intimacyUpgradeListener = () => {
        const mainAwardStore = contextStore.get(MainAwardStoreCreator);
        mainAwardStore.getState().requestRewardList();
    };
    return create<MainAwardState>((set, get) => ({
        requestRewardList: async (showT) => {
            try {
                const list = await rewardList();
                const hasRedDot = list?.detailRewardList?.some(
                    (item) => item.userMissionStatus === UserMissionAppStatus.FINISHED
                );
                set({ rewardList: list, hasRedDot });
            } catch (err) {
                if (showT) {
                    showToast({
                        title: err.message,
                        icon: 'none',
                    });
                }
            }
        },
        receiveReward: async (missionId) => {
            showLoading();
            try {
                await receiveReward(missionId, get().rewardList?.missionBoxId);
                await get().requestRewardList(true);
                Taro.eventCenter.trigger('powerEvent', {
                    type: 'msgSendSuccess',
                    time: Date.now(),
                });
                return true;
            } catch (err) {
                showToast({
                    title: err.message,
                    icon: 'none',
                });
                return false;
            } finally {
                hideLoading();
            }
        },
        onCreated: () => {
            Taro.eventCenter.on('EpicPlayerPlayEnd', intimacyUpgradeListener);
        },
        onCleared: () => {
            Taro.eventCenter.off('EpicPlayerPlayEnd', intimacyUpgradeListener);
        },
    }));
};

export default MainAwardStoreCreator;
