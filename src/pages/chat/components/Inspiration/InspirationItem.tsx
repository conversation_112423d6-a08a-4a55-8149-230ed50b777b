import React, { useCallback } from 'react';
import { View, Image, Text } from '@tarojs/components';
import { ChatType } from '@/pages/chat/type';
import ErrorBoundary from '@/components/ErrorBoundary';
import Taro from '@tarojs/taro';

import inspirationFailWhiteIcon from '@/assets/chat/ic_inspiration_fail_white.png';
import inspirationFailGreyIcon from '@/assets/chat/ic_inspiration_fail_grey.png';
import inspirationEditPinkIcon from '@/assets/chat/ic_chat_inspiration_pink.png';
import inspirationEditWhiteIcon from '@/assets/chat/ic_chat_inspiration_white.png';
import useInspirationStore from '../../store/useInspirationStore';
import './index.scss';

const InspirationItem = ({
    type,
    fromGuide,
    sendGuideMsg,
    index,
}: {
    type?: string;
    fromGuide?: boolean;
    sendGuideMsg?: (index: number) => void;
    index: number;
}) => {
    const list = useInspirationStore((state) => state.data?.list?.[index]);
    const { loading: isShowEmpty, error: isShowFail } = list || {};
    const theme = fromGuide ? 'guide' : '';

    const onSendText = (select: number, text: string) => {
        if (fromGuide) {
            // 新手引导模式
            if (sendGuideMsg) {
                sendGuideMsg(select);
            }
            return;
        }
        useInspirationStore.getState().sendText(text);
    };

    const onEdit = (text: string) => {
        if (fromGuide || !text) {
            return;
        }
        Taro.eventCenter.trigger('editInspiration', {
            msg: text,
        });
    };

    const fetchInspiration = useCallback(() => {
        useInspirationStore
            .getState()
            .fetchInspiration(index, useInspirationStore.getState().unlockId);
    }, [index]);

    return (
        <ErrorBoundary>
            <View>
                {isShowFail && (
                    <View className={`contentContainer ${theme}`} onClick={fetchInspiration}>
                        <Text className="fail">加载失败</Text>
                        <Image
                            src={
                                type === ChatType.CHIP
                                    ? inspirationFailWhiteIcon
                                    : inspirationFailGreyIcon
                            }
                            className="failImage"
                        />
                    </View>
                )}
                {isShowEmpty && (
                    <View className={`contentContainer ${theme}`}>
                        <View className={`contentEmpty shark-wrap ${theme}`}>
                            <Text> </Text>
                        </View>
                        <View className={`contentEmpty shark-wrap ${theme}`}>
                            <Text> </Text>
                        </View>
                        <View className={`contentEmpty shark-wrap ${theme}`}>
                            <Text> </Text>
                        </View>
                    </View>
                )}
                {!isShowEmpty && !isShowFail && (
                    <View className={`contentContainer ${theme}`}>
                        {list?.list?.length > 0 && (
                            <View className={`content-item-container ${theme}`}>
                                <Text
                                    className={`content ${theme}`}
                                    onClick={() => onSendText(0, list?.list[0])}>
                                    {list?.list[0]}
                                </Text>
                                {!fromGuide && (
                                    <View className="edit-container">
                                        <View className="dashed-line" />
                                        <Image
                                            src={
                                                type === ChatType.CHIP
                                                    ? inspirationEditPinkIcon
                                                    : inspirationEditWhiteIcon
                                            }
                                            className="edit-image"
                                            onClick={() => onEdit(list?.list[0])}
                                        />
                                    </View>
                                )}
                            </View>
                        )}
                        {list?.list?.length > 1 && (
                            <View className={`content-item-container ${theme}`}>
                                <Text
                                    className={`content ${theme}`}
                                    onClick={() => onSendText(1, list?.list[1])}>
                                    {list?.list[1]}
                                </Text>
                                {!fromGuide && (
                                    <View className="edit-container">
                                        <View className="dashed-line" />
                                        <Image
                                            src={
                                                type === ChatType.CHIP
                                                    ? inspirationEditPinkIcon
                                                    : inspirationEditWhiteIcon
                                            }
                                            className="edit-image"
                                            onClick={() => onEdit(list?.list[1])}
                                        />
                                    </View>
                                )}
                            </View>
                        )}
                        {list?.list?.length > 2 && (
                            <View className={`content-item-container ${theme}`}>
                                <Text
                                    className={`content ${theme}`}
                                    onClick={() => onSendText(2, list?.list[2])}>
                                    {list?.list[2]}
                                </Text>
                                {!fromGuide && (
                                    <View className="edit-container">
                                        <View className="dashed-line" />
                                        <Image
                                            src={
                                                type === ChatType.CHIP
                                                    ? inspirationEditPinkIcon
                                                    : inspirationEditWhiteIcon
                                            }
                                            className="edit-image"
                                            onClick={() => onEdit(list?.list[2])}
                                        />
                                    </View>
                                )}
                            </View>
                        )}
                    </View>
                )}
            </View>
        </ErrorBoundary>
    );
};

export default React.memo(InspirationItem);
