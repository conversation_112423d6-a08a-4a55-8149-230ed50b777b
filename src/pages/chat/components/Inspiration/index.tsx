import React, { useEffect, useCallback, forwardRef, useState, useRef } from 'react';
import { View, Image, Text } from '@tarojs/components';
import classNames from 'classnames';
import { ChatType } from '@/pages/chat/type';
import ErrorBoundary from '@/components/ErrorBoundary';
import { useDialog } from '@/components/dialog';
import { createAlertModalProvider } from '@/components/AlertModal';
import { getGlobalData } from '@/utils';

// 引入Swiper相关依赖
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';

import inspirationPinkIcon from '@/assets/chat/ic_inspiration_pink.png';
import inspirationWhiteIcon from '@/assets/chat/ic_inspiration_white.png';
import generateInspirationPinkIcon from '@/assets/chat/ic_inspiration_generate_pink.png';
import generateInspirationWhiteIcon from '@/assets/chat/ic_inspiration_generate_white.png';
import useInspirationStore from '../../store/useInspirationStore';
import './index.scss';
import InspirationItem from './InspirationItem';
import { InspirationItemData } from '../../type/inspiration';

interface InspirationProps {
    type?: string;
    fromGuide?: boolean;
    guideList?: any[];
    sendGuideMsg?: (index: number) => void;
}
const Inspiration = forwardRef(
    (
        { type = '', fromGuide = false, guideList = [], sendGuideMsg }: InspirationProps,
        ref: any
    ) => {
        const data = useInspirationStore((state) => state.data);
        const { list: dataList } = data || {};
        const location = useInspirationStore((state) => state.location);
        const theme = fromGuide ? 'guide' : '';

        const swiperRef = useRef(null);

        useEffect(() => {
            if (swiperRef.current && swiperRef.current.swiper) {
                swiperRef.current.swiper.slideTo(location, 300);
            }
        }, [location]);

        // useEffect(() => {
        //     Taro.nextTick(() => {
        //         setCur(location);
        //     });
        // }, [location]);

        const fetchInspiration = useCallback(() => {
            useInspirationStore.getState().fetchInspiration(0, undefined);
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, []);

        useEffect(() => {
            if (fromGuide) {
                useInspirationStore.getState().setGuideList(guideList);
                return;
            }
            const isLoading = useInspirationStore
                .getState()
                .data?.list?.filter((item: any = {}) => item.loading);
            if (!isLoading) {
                fetchInspiration();
            }
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, []);

        const inspirationDialog = () => {
            return (
                <div className="flex flex-col items-center justify-center mt-[30px]">
                    <Text className="text-[18px] text-[#000000] font-[600]">免费次数已耗尽，</Text>
                    <div className="flex flex-row items-center">
                        <Text className="text-[18px] text-[#000000] font-[600]">确定消耗</Text>
                        <Text className="text-[18px] text-[#FF689E] font-[600] pl-2 pr-2">
                            {data?.amount || 0}
                        </Text>
                        <Text className="text-[18px] text-[#000000] font-[600]">
                            体力重新生成吗？
                        </Text>
                    </div>
                    <div className="flex flex-row items-center mt-[10px]">
                        <Text className="text-[13px] text-[#00000040] font-[400]">当前持有：</Text>
                        <Text className="text-[13px] text-[#FF689E80] font-[400] ml-[2px] mr-[2px]">
                            {getGlobalData<number>('currentPowerConfig') || 0}
                        </Text>
                    </div>
                </div>
            );
        };

        const buyInspirationConfirm = useCallback(() => {
            useInspirationStore.getState().buyInspiration();
        }, []);

        const dialog = useDialog(createAlertModalProvider({ closeOnClickOverlay: true }));

        const generateClick = useCallback(() => {
            if (data?.useCount >= data?.totalCount) {
                return;
            }
            if (data?.pay) {
                dialog?.show({
                    children: inspirationDialog(),
                    leftFuncTxt: '取消',
                    rightFuncTxt: '确定',
                    onRightFuncClick: buyInspirationConfirm,
                });
            } else {
                useInspirationStore.getState().setLocation();
            }
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [data, dialog]);

        return (
            <ErrorBoundary>
                <View
                    ref={ref}
                    className={classNames('inspirationContainer', {
                        [type]: true,
                    })}>
                    <View className="rootBg">
                        <View className={`titleContainer ${theme}`}>
                            <View className="title-icon-container">
                                <Image
                                    src={
                                        type === ChatType.CHIP
                                            ? inspirationPinkIcon
                                            : inspirationWhiteIcon
                                    }
                                    className="image"
                                />
                                <View className="title">选择灵感回复</View>
                            </View>

                            {data?.totalCount > 0 && (
                                <View className="generation_container" onClick={generateClick}>
                                    <Image
                                        src={
                                            type === ChatType.CHIP
                                                ? generateInspirationPinkIcon
                                                : generateInspirationWhiteIcon
                                        }
                                        className="generate-image"
                                    />
                                    <Text className="generate-text">重新生成</Text>
                                    <Text className="generate-count">{data?.useCount || 0}</Text>
                                    <Text className="generate-total-count">
                                        {`/${data?.totalCount || 0}`}
                                    </Text>
                                </View>
                            )}
                        </View>
                        <Swiper
                            ref={swiperRef}
                            className={`inspiration-swiper ${type} ${theme}`}
                            modules={[Pagination]}
                            nested
                            pagination={dataList?.length > 1 ? { clickable: true } : false}
                            loop={dataList?.length > 1}>
                            {data?.list?.map(
                                (
                                    item: InspirationItemData = {
                                        id: 0,
                                        list: [],
                                        loading: false,
                                        error: false,
                                    }
                                ) => (
                                    <SwiperSlide key={`${item.id}-${JSON.stringify(item.list)}`}>
                                        <View>
                                            <InspirationItem
                                                type={type}
                                                fromGuide={fromGuide}
                                                sendGuideMsg={sendGuideMsg}
                                                index={item.id}
                                            />
                                        </View>
                                    </SwiperSlide>
                                )
                            )}
                        </Swiper>
                        {/* Taro swiper有bug：circular为true时，生成的内容第三页和第一页一样，节点拷贝出错 */}
                        {/* <Swiper
                            // key={data?.list?.length}
                            className="inspiration-swiper"
                            current={cur}
                            circular={dataList?.length > 1}
                            indicatorDots={dataList?.length > 1}
                            indicatorColor={type === ChatType.CHIP ? '#FF689E' : '#ffffff'}
                            indicatorActiveColor={type === ChatType.CHIP ? '#FF689E' : '#ffffff80'}>
                            {data?.list?.map(
                                (
                                    item: InspirationItemData = {
                                        id: 0,
                                        list: [],
                                        loading: false,
                                        error: false,
                                    }
                                ) => (
                                    <SwiperItem key={item.id}>
                                        <View>
                                            <InspirationItem
                                                type={type}
                                                fromGuide={fromGuide}
                                                sendGuideMsg={sendGuideMsg}
                                                index={item.id}
                                            />
                                        </View>
                                    </SwiperItem>
                                )
                            )}
                        </Swiper> */}
                    </View>
                </View>
            </ErrorBoundary>
        );
    }
);

export default React.memo(Inspiration);
