.inspirationContainer {
    display: flex;
    flex-direction: row-reverse;
    flex-shrink: 0;

    .rootBg {
        display: flex;
        flex-direction: column;
        background: rgba(38, 38, 38, 0.7);
        border-radius: 12px;
        padding-top: 9px;
        padding-left: 7.5px;
        padding-right: 7.5px;
        min-height: 180px;
        align-items: center;
    }

    .titleContainer {
        width: 260px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        &.guide {
            width: 224px;
        }

        .title-icon-container {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;

            .image {
                width: 12px;
                height: 12px;
            }

            .title {
                display: inline;
                font-size: 12px;
                margin-left: 2px;
                font-weight: 600;
                color: rgba(255, 255, 255, 0.4);
            }
        }

        .generation_container {
            width: 94px;
            height: 22px;
            display: flex;
            flex-direction: row;
            background-color: rgba(255, 255, 255, 0.1);
            align-items: center;
            border-radius: 22px;
            justify-content: center;

            .generate-image {
                width: 10px;
                height: 10px;
            }

            .generate-text {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.6);
                margin-left: 2px;
            }

            .generate-count {
                font-size: 12px;
                color: #fff;
                margin-left: 2px;
            }

            .generate-total-count {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.6);
            }
        }
    }

    .inspiration-swiper {
        height: auto;
        width: 275px;
        padding-bottom: 16px;

        &.guide {
            width: 239px;
        }

        &.immerse {
            .swiper-pagination .swiper-pagination-bullet {
                background: #fff;
            }
        }

        .swiper-container {
            overflow: visible;
        }

        .swiper-pagination {
            bottom: 0;

            .swiper-pagination-bullet {
                width: 4px;
                height: 4px;
                margin-left: 2px;
                background: #ff689e;
                opacity: 0.2;
            }

            .swiper-pagination-bullet-active {
                opacity: 1;
            }
        }
    }

    .contentContainer {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 275px;

        &.guide {
            width: 239px;
        }
    }

    .content-item-container {
        display: flex;
        flex-direction: row;
        width: 260px;
        align-items: center;
        background: rgba(0, 0, 0, 0.4);
        border-radius: 8px;
        margin-top: 8px;

        &.guide {
            width: 224px;
        }
    }

    .edit-container {
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .dashed-line {
        height: 27px;
        border-left: 1px dashed rgba(255, 255, 255, 0.2);
    }

    .edit-image {
        width: 16px;
        height: 16px;
        margin-left: 8.5px;
        margin-right: 14px;
    }

    .split-line {
        background-image: linear-gradient(
            to left,
            transparent 0%,
            transparent 50%,
            #ccc 50%,
            #ccc 100%
        );
        background-size: 10px 1px;
        background-repeat: repeat-x;
    }

    .contentEmpty {
        background: rgba(0, 0, 0, 0.4);
        border-radius: 8px;
        width: 260px;
        height: 38px;
        margin-top: 8px;
        display: block;

        &.guide {
            width: 224px;
        }
    }

    .content {
        font-size: 13px;
        font-weight: 600;
        color: #fff;
        width: 221px;
        padding-top: 9px;
        padding-bottom: 9px;
        padding-left: 11px;
        padding-right: 8.5px;
        min-height: 38px;
        display: block;

        &.guide {
            width: 224px;
        }
    }

    .fail {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.6);
        margin-top: 38px;
    }

    .failImage {
        width: 60px;
        height: 36px;
        margin-top: 12px;
    }

    .shark-wrap::after {
        content: '';
        position: absolute;
        inset: -20%;
        background: linear-gradient(
            105deg,
            rgba(255, 255, 255, 0) 50%,
            rgba(255, 255, 255, 0.3),
            rgba(255, 255, 255, 0) 60%
        );
        animation: shark-wrap 2s infinite;
        transform: translateX(-100%);
    }

    @keyframes shark-wrap {
        to {
            transform: translateX(100%);
        }
    }

    .shark-wrap {
        overflow: hidden;
        position: relative;
    }

    &.chip {
        .rootBg {
            background-color: #ffe5ee;
        }

        .title-icon-container {
            .title {
                color: rgba(94, 83, 103, 0.8);
            }
        }

        .content {
            color: #ff689e;
        }

        .content-item-container {
            background-color: #fff;
        }

        .contentEmpty {
            background: #ffeff5;
        }

        .shark-wrap::after {
            background: linear-gradient(
                105deg,
                rgba(255, 255, 255, 0) 50%,
                rgba(255, 255, 255, 0.7),
                rgba(255, 255, 255, 0) 60%
            );
        }

        .fail {
            color: rgba(38, 38, 42, 0.6);
        }

        .dashed-line {
            border-left: 1px dashed rgba(255, 104, 158, 0.2);
        }

        .generation_container {
            background-color: rgba(255, 255, 255, 0.6);

            .generate-text {
                color: #ff689e;
            }

            .generate-count {
                color: #ff689e;
            }

            .generate-total-count {
                color: rgba(255, 104, 158, 0.5);
            }
        }
    }
}
