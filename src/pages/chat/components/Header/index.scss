.chat-header-outter {
    position: sticky;
    top: 0;
    z-index: 3;
    padding-top: var(--status-bar-height);

    .chat-header-mask {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 160px;
        opacity: 0.6;
        background: linear-gradient(180deg, #000 0%, rgba(0, 0, 0, 0) 100%);
        z-index: 2;
    }

    .chat-header {
        position: relative;
        z-index: 9;
        width: 100%;
        height: 44px;
        color: #fff;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding-right: 18px;
        transition: all 300ms;

        .chat-header-back {
            width: 26px;
            height: 26px;
            margin-left: 14px;
        }

        .chat-name-container {
            width: 78px;
            display: flex;
            align-items: center;
        }

        .chat-header-name {
            max-width: 50px;
            font-size: 14px;
            font-weight: 600;
            line-height: 14px;
            margin-left: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .chat-header-arrow {
            width: 10px;
            height: 10px;
            margin-left: 0;
        }

        .chat-header-add {
            width: 20px;
            height: 16px;
            margin-left: 4px;
        }

        .check-header-mark {
            width: 20px;
            height: 16px;
            margin-left: 4px;
            animation: draw 1s forwards;
        }

        .chat-header-mode {
            border-radius: 45px;
            background: rgba(0, 0, 0, 0.2);
            width: 46px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-left: 61px;
            margin-right: -20px;

            .tag {
                font-size: 12px;
                white-space: nowrap;
                opacity: 0.6;
            }

            .icon {
                width: 12px;
                height: 12px;
            }
        }

        .chat-header-ellipses {
            width: 26px;
            height: 26px;
            margin-left: 6px;
        }

        @keyframes draw {
            0% {
                transform: scale(0.2);
                opacity: 0;
            }

            50% {
                transform: scale(0.8);
                opacity: 1;
            }

            100% {
                transform: scale(1);
                opacity: 0;
            }
        }

        &.chip {
            // background: #faf7f8;
            color: #000;
            position: unset;
            z-index: 0;

            .chat-header-mode {
                background: #ffe5ee;
                color: #ff689e;
                border: 0.5px solid #ffd1e1;

                .tag {
                    opacity: 1;
                }
            }

            .chat-header-back {
                filter: brightness(1) invert(1);
            }

            .chat-header-ellipses {
                filter: brightness(1) invert(1);
            }

            .powerViewInfo {
                border: 0.5px solid #ffd1e1;
            }
        }
    }
}

@import '~taro-ui/dist/style/components/float-layout.scss';
@import '~taro-ui/dist/style/components/switch.scss';

.voiceSettingContainer {
    .at-float-layout {
        .at-float-layout__container {
            min-height: 0;
            max-height: fit-content;
            background-color: transparent;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .layout-body {
                min-height: 0;
                max-height: fit-content;
                display: flex;
                flex-direction: column;
                justify-content: center;
                height: auto;
                padding: 0;

                .layout-body__content {
                    min-height: 0;
                    max-height: fit-content;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    border-top-left-radius: 20px;
                    border-top-right-radius: 20px;
                }
            }
        }

        .layout {
            &-body {
                padding: 0;
            }
        }
    }

    .at-switch {
        background-color: transparent;

        &__title {
            font-size: 14px;
            color: #26262a;
            font-weight: 600;
        }

        .weui-switch {
            width: 41px;
            height: 24px;

            &::before {
                background-color: #d8d8d8;
            }

            &::after {
                width: 16px;
                height: 16px;
                margin-top: 2.5px;
                margin-left: 1px;
                margin-right: 1px;
            }
        }
    }
}

.immerse-header-enter-anim {
    opacity: 0;

    &.active {
        opacity: 1;
        transition: opacity 400ms;
    }
}
