@import '~taro-ui/dist/style/components/nav-bar.scss';
@import '~taro-ui/dist/style/components/icon.scss';

.powerHistory {
    .powerViewInfo {
        border-radius: 18px;
        background-color: rgba(0, 0, 0, 0.2);
        min-width: 95px;
        max-width: 150px;
        height: 26px;
        position: relative;
        margin-left: 24px;

        .powerImageContainer {
            height: 26px;
            display: flex;
            margin-left: 6px;
            align-items: center;

            .powerImage {
                width: 18px;
                height: 18px;
            }
        }

        .powerImage2Container {
            height: 26px;
            display: flex;
            margin-right: 4px;
            align-items: center;

            .powerImage {
                margin-left: 4px;
                width: 16px;
                height: 16px;
            }
        }

        .powerInfoContainer {
            display: flex;
            height: 26px;
            align-items: center;
            justify-content: center;

            .powerNum {
                font-size: 12px;
                color: #fff;
            }

            .powerCount {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.5);
            }
        }

        .popContainer {
            position: absolute;
            height: 31px;
            left: 50%;
            transform: translate(-50%);
            z-index: 70;

            .arrowContainer {
                height: 5px;
                display: flex;
                justify-content: center;

                .arrowImg {
                    width: 14px;
                    height: 5px;
                }
            }

            .contentStrContainer {
                border-radius: 6px;
                height: 26px;
                padding-left: 10px;
                padding-right: 10px;
                display: flex;
                align-items: center;
                background-color: #fff;

                .contentStr {
                    font-size: 12px;
                    color: #000;
                    opacity: 0.8;
                }
            }
        }

        // 不同模式适配代码
        &.chip {
            background-color: #ffe5ee;

            .powerInfoContainer {
                .powerNum {
                    color: #ff689e;
                }

                .powerCount {
                    color: #ff689e;
                }
            }
        }
    }
}
