import React, { useEffect, useRef, useState } from 'react';
import { Image, View } from '@tarojs/components';
import './index.scss';
import Taro, { useDidHide, useDidShow, getStorageSync, setStorageSync } from '@tarojs/taro';
import { assetBalance, balanceQuery } from '@/service/physicalPowerApi';
import ICON_ADD1_PINK from '@/assets/common/icon_add_type_1_pink.png';
import ICON_ADD1_WHITE from '@/assets/common/icon_add_type_1_white.png';
import { ChatType } from '@/pages/chat/type';
import classNames from 'classnames';
import { getGlobalData, setGlobalData, debounce } from '@/utils';
import { addClickLog } from '@/utils/logTool';

import ICON_STAR from '@/assets/market/ic_stamina.png';
import ICON_POPUP_ARROW from '@/assets/self-power/icon_popup_arrow.png';

import usePopupStore from '@/pages/chat/store/usePopupStore';
import { jump2Market } from '@/router';

const PhysicalPowerView = ({ type }: { type: string }) => {
    const currentPowerConfigDefault = getGlobalData<number>('currentPowerConfig') || 0;
    const powerLimitConfigDefault = getGlobalData<number>('powerLimitConfig') || 0;
    const recoverPointConfigDefault = getGlobalData<number>('recoverPointConfigDefault') || 0;

    const showPhysicalPower = usePopupStore((state) => state.showPhysicalPower);
    const setShowPhysicalPower = usePopupStore((state) => state.setShowPhysicalPower);

    const hasHide = useRef(false);
    const powerLimitConfig = useRef(powerLimitConfigDefault);
    const recoverPoint = useRef(recoverPointConfigDefault);
    const recoverDur = useRef(60 * 3 * 1000 - 1);
    const [powerLimit, setPowerLimit] = useState(powerLimitConfigDefault);
    const currentPowerConfig = useRef(currentPowerConfigDefault);
    const [currentPower, setCurrentPower] = useState(currentPowerConfigDefault);
    const nextRecover = useRef(12000);
    const allRecover = useRef(1292000);
    const [allRecoverStr, setAllRecoverStr] = useState('已达体力上限');

    function jumpToRecharge(needShowToast: boolean) {
        jump2Market({ needShowToast: needShowToast });
    }

    function handleRecoverClick() {
        jumpToRecharge(false);

        addClickLog('btn_ai_im_power|page_ai_im|page_h5_biz');
    }

    function handleInfoClick(e: { stopPropagation: () => void }) {
        e.stopPropagation();
        if (powerLimitConfig.current === 0) {
            return;
        }
        setShowPhysicalPower(!showPhysicalPower);
    }

    const showPopupKey = 'firstExpendPower';
    function handleFirstExpendPower() {
        try {
            const value = getStorageSync(showPopupKey);
            if (value !== 'true') {
                setStorageSync(showPopupKey, 'true');
                setShowPhysicalPower(true);
            }
        } catch (e) {
            //
        }
    }

    function refreshCurrentPowerConfig(balance: number) {
        currentPowerConfig.current = balance;
        setGlobalData('currentPowerConfig', currentPowerConfig.current);
        setCurrentPower(balance);
    }

    const assetBalanceReq = () => {
        assetBalance('mirth_ai_stamina')
            .then((res: { balance: any }) => {
                const balance = Number.parseInt(res?.balance || '0', 10);
                refreshCurrentPowerConfig(balance);
            })
            .catch(() => {
                //
            });
    };

    useDidHide(() => {
        hasHide.current = true;
        Taro.eventCenter.off('powerEvent');
    });

    function refreshRecoverInfo() {
        if (currentPowerConfig.current < powerLimitConfig.current) {
            const all = allRecover.current;
            if (all > 0) {
                const hours = Math.floor(all / 3600000);
                const remainingMs = all % 3600000;
                const minutes = Math.floor(remainingMs / 60000);
                const seconds = Math.floor((remainingMs % 60000) / 1000);
                if (hours > 0) {
                    setAllRecoverStr(
                        `${hours.toString().padStart(2, '0')}:${minutes
                            .toString()
                            .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}后全部恢复`
                    );
                } else {
                    setAllRecoverStr(
                        `${minutes.toString().padStart(2, '0')}:${seconds
                            .toString()
                            .padStart(2, '0')}后全部恢复`
                    );
                }
            } else {
                setAllRecoverStr('已达体力上限');
            }
            allRecover.current = all - 1000;

            if (all > 0) {
                let next = nextRecover.current;
                if (next < 0) {
                    next = recoverDur.current;
                }
                const value = next - 1000;
                if (value < 0) {
                    const newPower = Math.min(
                        currentPowerConfig.current + recoverPoint.current,
                        powerLimitConfig.current
                    );
                    refreshCurrentPowerConfig(newPower);
                    if (newPower >= powerLimitConfig.current) {
                        setShowPhysicalPower(false);
                    }
                }
                nextRecover.current = value;
            }
        } else {
            setAllRecoverStr('已达体力上限');
        }
    }

    const showAutoShowPopup = useRef(false);

    function refreshBalanceQuery() {
        balanceQuery()
            .then((res: any) => {
                recoverDur.current = (res?.singleRecoveryMills || 0) - 1;
                recoverPoint.current = res?.singleRecoveryNum || 1;
                setGlobalData('recoverPointConfigDefault', recoverPoint.current);
                refreshCurrentPowerConfig(res?.balance || 0);
                powerLimitConfig.current = res?.autoRecoveryLimit || 0;
                setGlobalData('powerLimitConfig', powerLimitConfig.current);
                setPowerLimit(powerLimitConfig.current);
                nextRecover.current = res?.nextRecoveryRemainMills || 0;
                allRecover.current = res?.allRecoveryRemainMills || 0;
                refreshRecoverInfo();
                if (showAutoShowPopup.current) {
                    showAutoShowPopup.current = false;
                    handleFirstExpendPower();
                }
            })
            .catch(() => {
                showAutoShowPopup.current = false;
            });
    }

    const handleCustomEvent = (data: { type: string }) => {
        if (data.type === 'msgSendSuccess') {
            if (currentPowerConfig.current >= powerLimitConfig.current) {
                showAutoShowPopup.current = true;
                refreshBalanceQuery();
            } else {
                assetBalanceReq();
                handleFirstExpendPower();
            }
        }
        if (data.type === 'msgSendFiledForHypodynamia') {
            jumpToRecharge(true);
        }
    };

    useEffect(() => {
        Taro.eventCenter.on('powerEvent', handleCustomEvent);
        return () => {
            Taro.eventCenter.off('powerEvent');
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useDidShow(() => {
        if (hasHide.current) {
            refreshBalanceQuery();
            Taro.eventCenter.on('powerEvent', handleCustomEvent);
        }
        hasHide.current = false;
    });

    useEffect(() => {
        refreshBalanceQuery();

        const timer = setInterval(() => {
            refreshRecoverInfo();
        }, 1000);

        return () => clearInterval(timer);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <View className="powerHistory">
            <View
                className={classNames('powerViewInfo', {
                    [type]: true,
                })}>
                <View className="at-row">
                    <View className="at-col at-col-1 at-col--auto">
                        <View className="powerImageContainer">
                            <Image
                                src={ICON_STAR}
                                className="powerImage"
                                onClick={debounce((e) => {
                                    e.stopPropagation();
                                    handleInfoClick(e);
                                })}
                            />
                        </View>
                    </View>
                    <View
                        className="at-col"
                        onClick={debounce((e) => {
                            e.stopPropagation();
                            handleInfoClick(e);
                        })}>
                        <View className="powerInfoContainer">
                            <View className="powerNum">{currentPower}</View>
                            <View className="powerCount">/{powerLimit}</View>
                        </View>
                    </View>
                    <View className="at-col at-col-1 at-col--auto" onClick={handleRecoverClick}>
                        <View className="powerImage2Container">
                            <Image
                                src={type === ChatType.CHIP ? ICON_ADD1_PINK : ICON_ADD1_WHITE}
                                className="powerImage"
                            />
                        </View>
                    </View>
                </View>
                {showPhysicalPower && (
                    <View className="popContainer">
                        <View className="at-col">
                            <View className="arrowContainer">
                                <Image src={ICON_POPUP_ARROW} className="arrowImg" />
                            </View>
                            <View className="contentStrContainer">
                                <View className="contentStr">{allRecoverStr}</View>
                            </View>
                        </View>
                    </View>
                )}
            </View>
        </View>
    );
};

export default React.memo(PhysicalPowerView);
