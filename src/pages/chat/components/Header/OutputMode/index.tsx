import React, { useEffect, useCallback } from 'react';
import { Image, View, Text } from '@tarojs/components';
import classNames from 'classnames';
import ICON_POPUP_ARROW from '@/assets/self-power/icon_popup_arrow.png';
import { ChatType } from '@/pages/chat/type';
import EventTrackView from '@/components/EventTrack';
import { addClickLog } from '@/utils/logTool';

import ICON_SWITCH_PINK from '@/assets/common/icon_switch_pink.png';
import ICON_SWITCH_WHITE from '@/assets/common/icon_switch_white.png';
import { OutputModeStateStoreCreator } from '@/pages/chat/store/useOutputMode';
import { useContextStore } from '@/components/storeContext/StoreContext';
import { GuideTipStoreCreator, ChatGuideMode } from '@/pages/chat/store/useGuideTipStore';
import { useMessageStore } from '@/hooks/messageStore';

import { useDialog } from '@/components/dialog';
import { OutputModeModal } from '../../OutputMode';

import './index.scss';

const OutputModeHeader = ({ type }: { type: string }) => {
    const guideTipStore = useContextStore(GuideTipStoreCreator);
    const guideMode = guideTipStore((state) => state.guideMode);

    const outputModeDialog = useDialog(OutputModeModal);
    const outputModeStateStore = useContextStore(OutputModeStateStoreCreator);
    const showOutputMode = outputModeStateStore((state) => state.outputMode);

    useEffect(() => {
        let timer: NodeJS.Timeout;
        if (guideMode === ChatGuideMode.OutputMode) {
            /// 默认 3s 后关闭引导
            timer = setTimeout(() => {
                guideTipStore.getState().closeGuideTip();
            }, 3000);
        }
        return () => {
            if (timer) {
                clearTimeout(timer);
            }
        };
    }, [guideMode, guideTipStore]);

    const outputMode = showOutputMode?.chatTextModeName.replace(/模式$/, '');

    const onChangeOutputMode = useCallback(() => {
        outputModeDialog.show();
        addClickLog('btn_ai_im_mode|page_ai_im|page_h5_biz', {
            tab: outputMode,
        });
    }, [outputModeDialog, outputMode]);

    useEffect(() => {
        useMessageStore.getState().updateLoadingTimeout(outputMode === '长文' ? 35000 : 15000);
    }, [outputMode]);

    return (
        <View className="chat-output-mode">
            <EventTrackView
                params={{
                    _spm: 'btn_ai_im_mode|page_ai_im|page_h5_biz',
                    tab: outputMode,
                }}
                isPage={false}
            />
            <View className="chat-header-mode" onClick={onChangeOutputMode}>
                <Text
                    className={classNames('tag', {
                        [type]: true,
                    })}>
                    {outputMode}
                </Text>
                <Image
                    className="icon"
                    src={type === ChatType.CHIP ? ICON_SWITCH_PINK : ICON_SWITCH_WHITE}
                />
            </View>
            {guideMode === ChatGuideMode.OutputMode && (
                <View className="output-mode-pop-container">
                    <View className="at-col">
                        <View className="arrow-container">
                            <Image src={ICON_POPUP_ARROW} className="arrow-image" />
                        </View>
                        <View className="content-container">
                            <View className="content">随时切换聊天模式~</View>
                        </View>
                    </View>
                </View>
            )}
        </View>
    );
};

export default OutputModeHeader;
