import React, { useCallback, useEffect, useMemo } from 'react';
import classNames from 'classnames';
import Taro from '@tarojs/taro';
import { Image, Text, View } from '@tarojs/components';
import FavoirabilityHeadView from '@/pages/chat/components/Favoirability/favoirability-head-view';
import ErrorBoundary from '@/components/ErrorBoundary';

import ICON_BACK_GREY from '@/assets/common/chat_header_icon_back_grey.png';
import ICON_BACK_WHITE from '@/assets/common/chat_header_icon_back_white.png';
import ICON_ADD2_PINK from '@/assets/common/icon_add_type_2_pink.png';
import ICON_ADD2_WHITE from '@/assets/common/icon_add_type_2_white.png';
import ICON_BACK from '@/assets/common/icon_back.png';
import ICON_SELECT_PINK from '@/assets/common/icon_select_pink.png';
import ICON_SELECT_WHITE from '@/assets/common/icon_select_white.png';
import ICON_ELLIPSES from '@/assets/common/iocn_ellipses.png';

import { ChatType } from '@/pages/chat/type';
import { SystemMessage } from '@/types/im';
import { addClickLog } from '@/utils/logTool';

import MessageFlow from '@/hooks/message/MessageFlow';

import { useDialog } from '@/components/dialog';

import { useContextStore } from '@/components/storeContext/StoreContext';
import { ImmerseEnterAnimStoreCreator } from '@/pages/chat/store/useImmerseEnterAnim';
import { jump2Profile } from '@/router';
import useDetailStore from '../../store/useDetailStore';
import { VoiceSettingDialog } from '../VoiceSetting';
import PhysicalPowerView from './PhysicalPower';
import OutputModeHeader from './OutputMode';

import './index.scss';

function getSelectIcon(isFriend: boolean, type: string) {
    if (isFriend) {
        return type === ChatType.CHIP ? ICON_BACK_GREY : ICON_BACK_WHITE;
    }
    return type === ChatType.CHIP ? ICON_ADD2_PINK : ICON_ADD2_WHITE;
}

const ChatHeader = () => {
    const type = useDetailStore((state) => state.chatMode);
    const robotInfo = useDetailStore((state) => state.robotInfo?.robotBaseInfo);
    const isFriend = useDetailStore((state) => state.robotInfo?.friend);
    const checkMarkVisible = useDetailStore((state) => state.checkMarkVisible);
    const onAddFriendNotify = useDetailStore((state) => state.onAddFriendNotify);
    const settingsDialog = useDialog(VoiceSettingDialog);

    const onBack = useCallback(() => {
        if (Taro.getCurrentPages().length === 1) {
            // 新手引导要返回好友页面
            Taro.reLaunch({
                url: 'pages/friends/index',
            });
        } else {
            Taro.navigateBack();
        }
    }, []);

    const selectIcon = getSelectIcon(isFriend || false, type);
    const onClickAddIcon = async () => {
        useDetailStore.getState().addFriend();

        addClickLog('btn_ai_im_addition|page_ai_im|page_h5_biz');
    };

    const onClickArrowIcon = async () => {
        const userId = useDetailStore.getState().userId;
        jump2Profile({
            robotUserId: userId,
            hasAiChapter: null,
            fromSource: 'chat',
        });
    };

    const clickNickNameAction = () => {
        const userId = useDetailStore.getState().userId;
        jump2Profile({
            robotUserId: userId,
            hasAiChapter: null,
            fromSource: 'chat',
        });
    };

    const listener = useMemo(
        () => ({
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            onSystemMessage(message: SystemMessage) {
                onAddFriendNotify();
            },
        }),
        [onAddFriendNotify]
    );

    useEffect(() => {
        MessageFlow.addNotificationListener(4306, listener);
        return () => {
            MessageFlow.removeNotificationListener(4306, listener);
        };
    }, [listener]);

    const useImmerseEnterAnimStore = useContextStore(ImmerseEnterAnimStoreCreator);
    const shouldPlay = useImmerseEnterAnimStore((state) => state.shouldPlay);
    const playHeader = useImmerseEnterAnimStore((state) => state.playHeader);

    return (
        <ErrorBoundary>
            <View
                className={classNames('absolute top-0 left-0 w-full z-[2]', {
                    'immerse-header-enter-anim': shouldPlay,
                    active: playHeader,
                })}>
                <View
                    className={classNames('chat-header-outter', {
                        [type]: true,
                    })}>
                    <View
                        className={classNames('chat-header', {
                            [type]: true,
                        })}>
                        <Image
                            className="chat-header-back active:opacity-50"
                            src={ICON_BACK}
                            onClick={onBack}
                        />
                        <View className="chat-name-container">
                            <Text
                                className="chat-header-name"
                                onClick={() => clickNickNameAction()}>
                                {robotInfo?.nickname}
                            </Text>
                            {!checkMarkVisible && isFriend && (
                                <Image
                                    className="chat-header-arrow"
                                    onClick={onClickArrowIcon}
                                    src={selectIcon}
                                />
                            )}
                            {!checkMarkVisible && !isFriend && (
                                <Image
                                    className="chat-header-add"
                                    onClick={onClickAddIcon}
                                    src={selectIcon}
                                />
                            )}

                            {/* 对号 */}
                            {checkMarkVisible && (
                                <Image
                                    className="check-header-mark"
                                    src={
                                        type === ChatType.CHIP
                                            ? ICON_SELECT_PINK
                                            : ICON_SELECT_WHITE
                                    }
                                />
                            )}
                        </View>
                        <OutputModeHeader type={type} />
                        <PhysicalPowerView type={type} />
                        <Image
                            className="chat-header-ellipses active:opacity-50"
                            src={ICON_ELLIPSES}
                            onClick={() => {
                                settingsDialog.show();
                                addClickLog('btn_ai_im_set|page_ai_im|page_h5_biz');
                            }}
                        />
                    </View>
                </View>
                <FavoirabilityHeadView type={type} />
            </View>
        </ErrorBoundary>
    );
};

export default React.memo(ChatHeader);
