import { CreateModalProps } from '@/components/dialog';
import { useContextStore } from '@/components/storeContext/StoreContext';
import BGMSettingStore from '@/pages/chat/bgm/BGMSettingStore';
import useDetailStore from '@/pages/chat/store/useDetailStore';
import { View } from '@tarojs/components';
import React, { useCallback } from 'react';
import { AtSwitch } from 'taro-ui';
import useAudioStore from '../../store/useAudioStore';
import './index.scss';

const VoiceSetting = () => {
    const isAutoPlay = useAudioStore((state) => state.autoplay);

    const handleAutoPlayChange = (value) => {
        useAudioStore.getState().setAutoPlay(value);
    };

    const bgmUrl = useDetailStore((state) => state.robotInfo?.aigcChatResourceInfo?.bgmUrl);
    const useBgmSettingStore = useContextStore(BGMSettingStore);
    const isAutoPlayBGM = useBgmSettingStore((state) => state.isAutoPlay);
    const handleAutoPlayBGMChange = useCallback(() => {
        useBgmSettingStore.getState().setIsAutoPlay(!isAutoPlayBGM);
    }, [isAutoPlayBGM, useBgmSettingStore]);

    return (
        <View className="voiceSettingContainer">
            {bgmUrl && (
                <AtSwitch
                    title="播放聊天音乐"
                    checked={isAutoPlayBGM}
                    border={false}
                    onChange={handleAutoPlayBGMChange}
                    color="#FF689E"
                />
            )}
            <AtSwitch
                title="自动播放对话语音"
                checked={isAutoPlay}
                border={false}
                onChange={handleAutoPlayChange}
                color="#FF689E"
            />
        </View>
    );
};

export const VoiceSettingDialog: CreateModalProps = {
    type: 'float',
    render() {
        return <VoiceSetting />;
    },
};

export default VoiceSetting;
