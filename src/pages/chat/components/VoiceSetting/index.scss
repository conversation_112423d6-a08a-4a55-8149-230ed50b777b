@import '~taro-ui/dist/style/components/float-layout.scss';
@import '~taro-ui/dist/style/components/switch.scss';

.voiceSettingContainer {
    border-top-left-radius: 20px; /* 左上角圆角半径 */
    border-top-right-radius: 20px; /* 右上角圆角半径 */
    width: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    padding-top: 15px;
    padding-bottom: 20px;

    .at-float-layout {
        .at-float-layout__container {
            min-height: 0;
            max-height: fit-content;
            background-color: transparent;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .layout-body {
                min-height: 0;
                max-height: fit-content;
                display: flex;
                flex-direction: column;
                justify-content: center;
                height: auto;
                padding: 0;

                .layout-body__content {
                    min-height: 0;
                    max-height: fit-content;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    border-top-left-radius: 20px;
                    border-top-right-radius: 20px;
                }
            }
        }

        .layout {
            &-body {
                padding: 0;
            }
        }
    }

    .at-switch {
        background-color: transparent;
        padding: 17px 26px 17px 32px;
        margin-left: 0;

        &__title {
            font-size: 14px;
            color: #26262a;
            font-weight: 600;
        }

        .at-switch__switch {
            width: 41px;
            height: 24px;

            .weui-switch {
                width: 41px;
                height: 24px;

                &::before {
                    background-color: #d8d8d8;
                    width: 41px;
                    height: 24px;
                }

                &::after {
                    width: 16px;
                    height: 16px;
                    margin-top: 2.5px;
                    margin-left: 1px;
                    margin-right: 1px;
                }
            }
        }
    }
}
