import React, { memo, useCallback } from 'react';
import { Image, Text, View } from '@tarojs/components';
import { AtProgress } from 'taro-ui';
import classNames from 'classnames';
import { useDialog } from '@/components/dialog';
import { addClickLog } from '@/utils/logTool';

import message_favorability_icon from '@/assets/common/message_favorability_icon.png';
import message_favorability_arrow from '@/assets/common/message_favorability_arrow.png';

import { FavoirabilityMainModal } from '../favoirability-main';
import useIntimacyStore from '../store/useIntimacyStore';

import './index.scss';

const FavoirabilityHeadView = (props: { type: any; fromGuide?: boolean }) => {
    const favorability = useIntimacyStore((state) => state.info);
    const mainModal = useDialog(FavoirabilityMainModal);
    const handleFavorite = useCallback(() => {
        if (props.fromGuide) {
            return;
        }
        mainModal.show();

        addClickLog('mod_ai_im_favorability|page_ai_im|page_h5_biz');
    }, [mainModal, props.fromGuide]);

    const showIntimacy =
        (favorability?.newIntimacyValue ?? 0) > 0 ||
        (favorability !== null && favorability?.friend);

    if (!showIntimacy) {
        return null;
    }

    return (
        <View
            className={classNames('favorability', {
                [props.type]: true,
                'favorability-guide': props.fromGuide,
            })}
            onClick={handleFavorite}>
            <View className="favorability-left">
                <Image src={message_favorability_icon} className="favorability-left-icon" />
                <Text className="favorability-left-leftText">
                    {favorability.currentLevelRelationName}
                </Text>
                {favorability.nextLevelIntimacyAcquire > 0 && (
                    <Image src={message_favorability_arrow} className="favorability-left-arrow" />
                )}
                {favorability.nextLevelIntimacyAcquire > 0 && (
                    <Text className="favorability-left-rightText">
                        {favorability.nextLevelRelationName}
                    </Text>
                )}
            </View>
            <View className="favorability-middle">
                <AtProgress
                    percent={
                        favorability.nextLevelIntimacyAcquire > 0
                            ? Math.floor(
                                  (favorability.newIntimacyValue /
                                      favorability.nextLevelIntimacyAcquire) *
                                      100
                              )
                            : Math.floor(
                                  (favorability.newIntimacyValue /
                                      favorability.currentLevelIntimacyAcquire) *
                                      100
                              )
                    }
                    strokeWidth={14}
                />
            </View>
            <View className="favorability-right">
                <Text className="favorability-right-leftText">好感度:</Text>
                <Text className="favorability-right-rightText">
                    {favorability.newIntimacyValue}
                </Text>
            </View>
        </View>
    );
};

export default memo(FavoirabilityHeadView);
