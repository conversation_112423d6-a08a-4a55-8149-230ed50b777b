@import 'taro-ui/dist/style/components/progress.scss';
@import 'taro-ui/dist/style/components/icon.scss';

.favorability {
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 44px;
    padding-left: 20px;
    padding-right: 20px;

    &.favorability-guide {
        top: calc(44px + var(--status-bar-height));
    }

    &.chip {
        // background-image: linear-gradient(90deg, #342328 0%, #5e5055 100%);
        // border-bottom-left-radius: 10px;
        // border-bottom-right-radius: 10px;
    }

    &.immerse {
        background-image: linear-gradient(0deg, #0000004d 0%, #0000004d 100%);
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
    }

    &-left {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        // background-color: red;

        margin-left: 0;
        // margin-right: 40px;

        &-icon {
            width: 18px;
            height: 18px;
        }

        &-leftText {
            margin-left: 3px;
            font-size: 12px;
            color: #fff;
        }

        &-arrow {
            margin-left: 2px;
            width: 5px;
            height: 10px;
        }

        &-rightText {
            margin-left: 2px;
            font-size: 12px;
            color: #fff;
        }
    }

    &-middle {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 14px;
        // background-color: green;

        .at-progress {
            // background-color: aqua;
            width: 124px;
            margin-left: 0;
            margin-right: 0;

            &__outer {
                &-inner {
                    background-color: #ffffff20;
                    border-radius: 7px;
                    overflow: hidden;

                    &-background {
                        background-color: #ff689e;
                        border-radius: 7px;
                    }
                }
            }

            &__content {
                display: flex;
                justify-content: center;
                align-items: center;
                position: absolute;
                font-size: 10px;
                text-align: center;
                width: 102px;
                color: #fff;
            }
        }
    }

    &-right {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-right: 0;
        // background-color: blue;

        &-leftText {
            font-size: 12px;
            color: #fff6;
        }

        &-rightText {
            margin-left: 2px;
            font-size: 12px;
            color: #ffffffd0;
        }
    }
}
