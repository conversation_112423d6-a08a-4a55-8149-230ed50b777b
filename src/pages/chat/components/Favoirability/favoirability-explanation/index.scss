@import 'taro-ui/dist/style/components/float-layout.scss';

.favoirability-explanation-warp {
    border-top-left-radius: 20px; /* 左上角圆角半径 */
    border-top-right-radius: 20px; /* 右上角圆角半径 */
    width: 100%;
    height: 515px;
    background-color: #fff;
    overflow: hidden;

    .head-bg {
        position: absolute;
        width: 100%;
        height: 162px;
        border-top-left-radius: 20px; /* 左上角圆角半径 */
        border-top-right-radius: 20px; /* 右上角圆角半径 */
        background-image: linear-gradient(180deg, #ffdfea 0%, rgba(255, 223, 234, 0) 100%);
        z-index: 1;
        // background-color: #000;
    }

    .content {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
    }

    .head-view {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding-left: 28px;
        padding-right: 24px;
        margin-bottom: 23px;
        margin-top: 30px;
        background-color: transparent;
        z-index: 2;
        // background-color: #000;

        &-title {
            font-family: 'PingFang SC', sans-serif;
            font-size: 18px;
            font-weight: 600;
            line-height: normal;
            letter-spacing: 0;
            color: #000;
        }

        &-close-btn {
            width: 26px;
            height: 26px;
        }
    }

    .favoirability-explanation-scrollview {
        z-index: 2;
        padding-bottom: 20px;
        height: 430px;
    }
    @supports (padding-bottom: calc(100px + env(safe-area-inset-bottom))) {
        .favoirability-explanation-scrollview {
            z-index: 2;
            padding-bottom: calc(10px + env(safe-area-inset-bottom));
            height: 430px;
        }
    }

    .relationship {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        background-image: linear-gradient(90deg, #ffecf3 3%, #ffd0e1 98%);
        padding-left: 14px;
        padding-right: 14px;
        padding-top: 20px;
        padding-bottom: 20px;
        margin-left: 24px;
        margin-right: 24px;
        margin-bottom: 12px;
        border-radius: 12px;

        &-head-view {
            // background-color: aqua;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 24px;
            margin-top: 0;

            &-left {
                display: flex;
                justify-content: flex-start;
                align-items: center;
            }

            &-icon {
                width: 18px;
                height: 18px;
            }

            &-leftText {
                margin-left: 3px;
                font-size: 16px;
                color: #26262a;
            }

            &-arrow {
                width: 12px;
                height: 12px;
            }
        }

        &-middle-view {
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 38px;
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.72);
            padding: 10px;

            &-text {
                font-family: 'PingFang SC', sans-serif;
                font-size: 13px;
                font-weight: 600;
                color: #26262a;
            }

            &-content {
                font-family: 'PingFang SC', sans-serif;
                font-size: 12px;
                font-weight: 600;
                line-height: normal;
                text-align: right;
                letter-spacing: 0;
                font-variation-settings: 'opsz' auto;
                color: #26262a;
            }
        }

        &-bottom-content {
            width: 100%;
            height: auto;
            margin-top: 11px;

            font-family: 'PingFang SC', sans-serif;
            font-size: 12px;
            font-weight: normal;
            line-height: normal;
            letter-spacing: 0;
            font-variation-settings: 'opsz' auto;
            color: #26262a;
            opacity: 0.4;
        }
    }

    .at-tabs {
        margin-top: 15px;

        .at-tabs__header {
            .at-tabs__item {
                color: #c51414;
                opacity: 0.3;
                font-size: 16px;
                font-weight: 600;
                line-height: normal;
                letter-spacing: 0;
                padding: 0 0 4px 0;
                flex: none;
                margin-right: 20px;
            }

            .at-tabs__item--active {
                color: #000;
                opacity: 0.8;
                font-size: 16px;
                font-weight: 600;
                line-height: normal;
                letter-spacing: 0;
            }

            .at-tabs__item-underline {
                border-radius: 28px;
                background: #000;
                width: 10px;
                height: 2px;
                position: absolute;
                left: 50%;
                bottom: 0;
                transform: translateX(-50%) scaleX(0);
                transition: none;
            }

            .at-tabs__item--active .at-tabs__item-underline {
                transform: translateX(-50%) scaleX(1);
            }
        }

        .at-tabs__body {
            .at-tabs__underline {
                background-color: transparent;
            }
        }
    }
}
