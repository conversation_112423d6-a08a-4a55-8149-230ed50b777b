import React, { useCallback } from 'react';
import { Image, ScrollView, Text, View } from '@tarojs/components';
import { CreateModalProps } from '@/components/dialog';

import message_favorability_close from '@/assets/common/message_favorability_close.png';
import message_favorability_icon from '@/assets/common/message_favorability_icon.png';
import message_favorability_down_arrow from '@/assets/common/message_favorability_down_arrow.png';
import message_favorability_up_arrow from '@/assets/common/message_favorability_up_arrow.png';

import useIntimacyStore from '../store/useIntimacyStore';

import './index.scss';

const FavoirabilityExplanation = (props: { dismiss: () => void }) => {
    const items = useIntimacyStore((state) => state.levels);
    const handleFloatLayoutChange = () => {
        props.dismiss();
    };

    const handleClose = useCallback((index) => {
        useIntimacyStore.getState().toggleClose(index);
    }, []);

    return (
        <View className="favoirability-explanation-warp">
            <View className="head-bg" />
            <View className="content">
                <View className="head-view">
                    <Text className="head-view-title">
                        通过积累好感
                        <Text className="head-view-title" style={{ display: 'block' }}>
                            探索你们的亲密关系
                        </Text>
                    </Text>
                    <Image
                        src={message_favorability_close}
                        className="head-view-close-btn"
                        onClick={handleFloatLayoutChange}
                    />
                </View>
                <ScrollView
                    className="favoirability-explanation-scrollview"
                    style={{ height: '430px' }}
                    scrollY
                    scrollWithAnimation>
                    {items &&
                        items.map((item, index) => (
                            <View className="relationship" key={index}>
                                <View
                                    className="relationship-head-view"
                                    onClick={() => handleClose(index)}>
                                    <View className="relationship-head-view-left">
                                        <Image
                                            src={message_favorability_icon}
                                            className="relationship-head-view-icon"
                                        />
                                        <Text className="relationship-head-view-leftText">
                                            {item.label}
                                        </Text>
                                    </View>
                                    <Image
                                        src={
                                            item.isOpen === true
                                                ? message_favorability_up_arrow
                                                : message_favorability_down_arrow
                                        }
                                        className="relationship-head-view-arrow"
                                    />
                                </View>
                                {item.isOpen && (
                                    <View className="relationship-middle-view">
                                        <Text className="relationship-middle-view-text">
                                            好感度
                                        </Text>
                                        <Text className="relationship-middle-view-content">{`${item.intimacyAcquire}分以上`}</Text>
                                    </View>
                                )}
                                {item.isOpen && (
                                    <Text className="relationship-bottom-content">
                                        {item.levelDesc}
                                    </Text>
                                )}
                            </View>
                        ))}
                </ScrollView>
            </View>
        </View>
    );
};

export const FavoirabilityExplanationModal: CreateModalProps = {
    type: 'float',
    render(dismiss) {
        return <FavoirabilityExplanation dismiss={dismiss} />;
    },
};

export default FavoirabilityExplanation;
