import { create } from 'zustand';
import { FavoriteDetailDataType, FavoriteLevelExtensionDataType } from '@/types/favorite';
import * as Api from '@/service/favoirabilityApi';
import MessageFlow from '@/hooks/message/MessageFlow';
import { SystemMessage } from '@/types/im';
import useDetailStore from '@/pages/chat/store/useDetailStore';
import cardCollectStore from '@/pages/appreciate/store/cardCollectStore';

interface IntimacyState {
    info?: FavoriteDetailDataType;
    levels: FavoriteLevelExtensionDataType[];
    refresh: () => Promise<void>;
    toggleClose: (index: number) => void;
    refreshintimacyValue: (intimacy: number) => void;
}

const useIntimacyStore = create<IntimacyState>((set, get, store) => {
    let currentUserId = '';
    let lastIntimacyUpdateTime = 0;
    let pendingLevel: FavoriteLevelExtensionDataType[] | undefined;

    const updateLevelInfo = (levelInfo: FavoriteLevelExtensionDataType[]) => {
        const nLevelInfo = [...levelInfo];
        const { info } = get();
        if (info === undefined || nLevelInfo === undefined) {
            return;
        }
        for (let i = 0; i < nLevelInfo.length; i++) {
            const currentLevel = nLevelInfo[i];
            const nextLevel = i >= nLevelInfo.length - 1 ? null : nLevelInfo[i + 1];

            if (nextLevel === null) {
                nLevelInfo[i].isOpen = true;
                break;
            }

            const newIntimacyValue = info.newIntimacyValue;
            if (
                newIntimacyValue >= currentLevel.intimacyAcquire &&
                newIntimacyValue < nextLevel?.intimacyAcquire
            ) {
                nLevelInfo[i].isOpen = true;
                break;
            }
        }
        set({ levels: nLevelInfo });
    };
    const refreshIntimacyInfo = async () => {
        if (!currentUserId) {
            console.warn('userId 不存在');
            return;
        }
        try {
            const info = await Api.intimacyDetailApi({ robotUserId: currentUserId });
            set({ info });
            if (pendingLevel) {
                updateLevelInfo(pendingLevel);
                pendingLevel = undefined;
            }
        } catch (e) {
            console.error(e);
        }
    };
    const refreshLevelInfo = async () => {
        try {
            const levels = await Api.intimacyLevelApi({});
            const { info } = get();
            if (info) {
                updateLevelInfo(levels);
            } else {
                pendingLevel = levels;
            }
        } catch (e) {
            console.error(e);
        }
    };
    useDetailStore.subscribe((state, prev) => {
        if (state.userId !== prev.userId || state.userId !== currentUserId) {
            if (currentUserId) {
                set(store.getInitialState());
            }
            currentUserId = state.userId;
            lastIntimacyUpdateTime = 0;
            pendingLevel = undefined;
            if (currentUserId) {
                refreshIntimacyInfo();
                refreshLevelInfo();
            }
        }
    });
    MessageFlow.addGlobalListener(
        {
            onSystemMessage: (message: SystemMessage) => {
                const serverExt = message.contentExt?.serverExt;
                if (serverExt?.type === 4306) {
                    if (serverExt?.content?.bindUserId.toString() !== currentUserId.toString()) {
                        return;
                    }
                    const target: boolean = serverExt?.content?.bind;
                    const { info } = get();
                    if (info?.friend !== target) {
                        if (info) {
                            set({ info: { ...info, friend: target } });
                        }
                        refreshIntimacyInfo();
                    }
                } else if (serverExt?.type === 4304) {
                    const content = serverExt?.content;
                    const taregetUserId = serverExt?.content?.targetUserId;
                    if (taregetUserId.toString() !== currentUserId.toString()) {
                        return;
                    }
                    const timestamp = content.timestamp || 0;
                    if (timestamp <= lastIntimacyUpdateTime) {
                        return;
                    }
                    lastIntimacyUpdateTime = timestamp;
                    const { info } = get();
                    if (info) {
                        set({
                            info: {
                                ...info,
                                newIntimacyValue: content.newIntimacyValue,
                                currentLevelRelationName: content.currentLevelRelationName,
                                currentLevelIntimacyAcquire: content.currentLevelIntimacyAcquire,
                                nextLevelRelationName: content.nextLevelRelationName,
                                nextLevelIntimacyAcquire: content.nextLevelIntimacyAcquire,
                            },
                        });
                    }
                } else if (serverExt?.type === 4308) {
                    const endingId = serverExt?.content?.endingId;
                    const firstUnlock = serverExt?.content?.firstUnlock;
                    const robotUserId = serverExt?.content?.robotUserId;
                    if (firstUnlock) {
                        cardCollectStore.getState().addEnding(robotUserId, endingId);
                        cardCollectStore.getState().getEndingCards();
                    }
                }
            },
        },
        'useIntimacyStore'
    );
    return {
        info: undefined,
        levels: [],
        refresh: async () => {
            await refreshIntimacyInfo();
            refreshLevelInfo();
        },
        toggleClose: (index: number) => {
            const { levels } = get();
            if (!levels) {
                return;
            }
            const item = levels[index];
            item.isOpen = !item.isOpen;
            levels[index] = item;
            set({ levels: [...levels] });
        },
        refreshintimacyValue: (intimacy: number) => {
            const { info } = get();
            if (info) {
                set({
                    info: {
                        ...info,
                        newIntimacyValue: intimacy,
                    },
                });
            }
        },
    };
});

export default useIntimacyStore;
