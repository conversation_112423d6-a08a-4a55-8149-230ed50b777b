@import 'taro-ui/dist/style/components/float-layout.scss';

.how-to-promote-warp {
    border-top-left-radius: 20px; /* 左上角圆角半径 */
    border-top-right-radius: 20px; /* 右上角圆角半径 */
    max-height: 450px;
    min-height: 450px;
    width: 100%;
    background-color: #fff;
}

.how-to-promote {
    &-container {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        margin-top: 15px;
        padding: 0;
    }

    &-title-view {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding-left: 28px;
        padding-right: 24px;
        margin-bottom: 40px;

        &-left {
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            flex-direction: column;

            .find-more {
                display: flex;
                justify-items: center;
                align-items: center;
                margin-top: 0;

                &-title {
                    font-family: 'PingFang SC', sans-serif;
                    font-size: 12px;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: 0;
                    font-variation-settings: 'opsz' auto;
                    color: #000;
                    opacity: 0.4;
                }

                &-arrow {
                    width: 10px;
                    height: 10px;
                }
            }
        }
    }

    &-title {
        font-family: 'PingFang SC', sans-serif;
        font-size: 18px;
        font-weight: 600;
        color: #000;
    }

    &-title-icon {
        width: 26px;
        height: 26px;
    }

    &-avatars-view {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 70px;
        margin-bottom: 50px;
        position: relative;
        // background-color: #2d2626;

        &-left-avatar {
            width: 66px;
            height: 66px;
            border-radius: 33px;
            box-sizing: border-box;
            border: 2px solid #fff;
            box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
            margin-right: -20px;
            z-index: 2;
        }

        &-middle-avatar {
            width: 150px;
            height: 77px;
            z-index: 1;
        }

        &-love {
            position: absolute;
            top: 3px;
            left: 50%;
            transform: translateX(calc(-50% + 2.5px));
            width: 70px;
            height: 70px;

            .love-up-mask {
                z-index: 2;
                width: 65px;
            }

            .love-up-ani {
                position: absolute;
                left: 50%;
                width: 65px;
                transform: translateX(-50%);
                background: transparent;
            }
        }

        &-right-avatar {
            width: 66px;
            height: 66px;
            border-radius: 33px;
            box-sizing: border-box;
            border: 2px solid #fff;
            box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
            margin-left: -20px;
        }

        .score {
            display: flex;
            justify-items: flex-start;
            align-items: center;
            flex-direction: column;
            position: absolute;
            width: 100px;
            height: 100px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 3;

            &-title {
                font-family: 'PingFang SC', sans-serif;
                font-size: 14px;
                font-weight: 600;
                line-height: normal;
                letter-spacing: 0;
                font-variation-settings: 'opsz' auto;
                color: #ff7cab;
            }

            &-score {
                margin-top: 55px;
                font-family: 'PingFang SC', sans-serif;
                font-size: 14px;
                font-weight: 600;
                line-height: normal;
                letter-spacing: 0;
                font-variation-settings: 'opsz' auto;
                color: #ff6198;
            }
        }
    }

    &-items-view {
        // display: flex;
        // justify-content: space-between;
        // align-items: center;
        width: calc(100% - 56px);
        padding-left: 12px;
        padding-right: 12px;
        padding-top: 26px;
        padding-bottom: 0;
        margin: 28 28px;
        margin-bottom: 35px;
        background-color: #fafafa;
        border-radius: 10px;
        position: relative;
    }

    &-tips {
        position: absolute;
        top: 0%;
        transform: translateY(-50%);
        width: 35px;
        height: 21px;
    }

    &-item-view {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        // height: 23px;
        margin-bottom: 20px;
        // background-color: aqua;
    }

    &-item-title {
        font-family: 'PingFang SC', sans-serif;
        font-size: 13px;
        font-weight: 600;
        color: #000000c0;
    }

    &-item-btn-title {
        font-family: 'PingFang SC', sans-serif;
        font-size: 12px;
        font-weight: 600;
        line-height: normal;
        letter-spacing: 0;
        font-variation-settings: 'opsz' auto;
        color: #ff6198;
        background-color: #ff619810;
        border-radius: 14px;
        height: 28px;
        padding-left: 12px;
        padding-right: 12px;
        padding-top: 5px;
        padding-top: 5px;
    }
}
