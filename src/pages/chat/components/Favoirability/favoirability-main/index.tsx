import React, { useCallback, useMemo } from 'react';
import { getSystemInfoSync } from '@tarojs/taro';
import { Image, Text, View } from '@tarojs/components';
import useUserInfoStore from '@/store/useUserInfoStore';
import { CreateModalProps, useDialog } from '@/components/dialog';
import { GiftPanelModal } from '@/pages/gift-panel';
import useDetailStore from '@/pages/chat/store/useDetailStore';
import message_favorability_close from '@/assets/common/message_favorability_close.png';
import message_favorability_find_more_arrow from '@/assets/common/message_favorability_find_more_arrow.png';
import message_favoirability_score_bg from '@/assets/common/message_favorability_score_bg.png';
import message_favoirability_tips from '@/assets/common/message_favoirability_tips.png';
import LoveMask from '@/assets/common/love.png';

import { jump2Profile } from '@/router';
import useIntimacyStore from '../store/useIntimacyStore';
import { FavoirabilityExplanationModal } from '../favoirability-explanation';

import './index.scss';

const LoveUpAniSrc =
    'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/58314869277/6907/4d53/458c/1ccf82b80bd5828af16c5688fe44c801.apng?imageView';

const FavoirabilityMain = (props: { dismiss: () => void }) => {
    const targetAvatar = useDetailStore((state) => state.robotInfo?.robotBaseInfo?.avatarUrl);
    const favorability = useIntimacyStore((state) => state.info);
    const userInfo = useUserInfoStore.getState().userBaseInfo;
    const explanationModal = useDialog(FavoirabilityExplanationModal);
    const giftPanel = useDialog(GiftPanelModal);

    const rootFontSize = getSystemInfoSync().screenWidth / 18.75;

    const fullLoveHeight = 40;

    const scoreRatio = useMemo(() => {
        if (!favorability?.nextLevelIntimacyAcquire || favorability?.nextLevelIntimacyAcquire === 0)
            return 0;
        if (favorability?.newIntimacyValue >= favorability?.nextLevelIntimacyAcquire) return 1;
        return favorability?.newIntimacyValue / favorability?.nextLevelIntimacyAcquire;
    }, [favorability?.nextLevelIntimacyAcquire, favorability?.newIntimacyValue]);

    const handleFloatLayoutChange = () => {
        props.dismiss();
    };

    const handleFindMore = useCallback(() => {
        explanationModal.show();
    }, [explanationModal]);

    const handleGotoChat = () => {
        props.dismiss();
    };

    const handleGotoGift = () => {
        giftPanel.show();
    };

    const handleGotoProfile = () => {
        const userId = useDetailStore.getState().userId;
        props.dismiss();
        jump2Profile({
            robotUserId: userId,
            hasAiChapter: null,
            fromSource: 'chat',
        });
    };

    return (
        <View className="how-to-promote-warp">
            <View className="how-to-promote-container">
                <View className="how-to-promote-title-view">
                    <View className="how-to-promote-title-view-left">
                        <Text className="how-to-promote-title">如何提升好感度</Text>
                        <View className="find-more" onClick={handleFindMore}>
                            <Text className="find-more-title">查看详情</Text>
                            <Image
                                className="find-more-arrow"
                                src={message_favorability_find_more_arrow}
                            />
                        </View>
                    </View>
                    <Image
                        src={message_favorability_close}
                        className="how-to-promote-title-icon"
                        onClick={handleFloatLayoutChange}
                    />
                </View>
                <View className="how-to-promote-avatars-view">
                    <Image
                        className="how-to-promote-avatars-view-left-avatar"
                        src={targetAvatar ?? ''}
                        mode="aspectFill"
                    />
                    <Image
                        className="how-to-promote-avatars-view-middle-avatar"
                        src={message_favoirability_score_bg}
                    />
                    <View className="how-to-promote-avatars-view-love">
                        <Image className="love-up-mask" src={LoveMask} />
                        <Image
                            className="love-up-ani"
                            src={LoveUpAniSrc}
                            style={{
                                top: `${(fullLoveHeight * (1 - scoreRatio)) / rootFontSize}rem`,
                            }}
                        />
                    </View>
                    <Image
                        className="how-to-promote-avatars-view-right-avatar"
                        src={userInfo?.userBase?.avatarImgUrl}
                        mode="aspectFill"
                    />
                    <View className="score">
                        <Text className="score-title">
                            {favorability?.currentLevelRelationName}
                        </Text>
                        <Text className="score-score">
                            {!favorability?.nextLevelIntimacyAcquire ||
                            favorability?.nextLevelIntimacyAcquire === 0
                                ? `${favorability?.newIntimacyValue}`
                                : `${favorability?.newIntimacyValue}/${favorability?.nextLevelIntimacyAcquire}`}
                        </Text>
                    </View>
                </View>

                <View className="how-to-promote-items-view">
                    <Image className="how-to-promote-tips" src={message_favoirability_tips} />
                    <View className="how-to-promote-item-view">
                        <Text className="how-to-promote-item-title">和 TA 多多聊天互动</Text>
                        <Text className="how-to-promote-item-btn-title" onClick={handleGotoChat}>
                            去聊天
                        </Text>
                    </View>
                    <View className="how-to-promote-item-view">
                        <Text className="how-to-promote-item-title">给TA赠送信物</Text>
                        <Text className="how-to-promote-item-btn-title" onClick={handleGotoGift}>
                            去送礼
                        </Text>
                    </View>
                    <View className="how-to-promote-item-view">
                        <Text className="how-to-promote-item-title">探索剧情，收集结局卡面</Text>
                        <Text className="how-to-promote-item-btn-title" onClick={handleGotoProfile}>
                            去探索
                        </Text>
                    </View>
                </View>
            </View>
        </View>
    );
};

export const FavoirabilityMainModal: CreateModalProps = {
    type: 'float',
    render(dismiss) {
        return <FavoirabilityMain dismiss={dismiss} />;
    },
};

export default FavoirabilityMain;
