import React, { useEffect, useState, useRef } from 'react';
import { getSystemInfoSync } from '@tarojs/taro';
import { View, Image, Text } from '@tarojs/components';

import { MsgDataType } from '@/types/im';
import TOP_ICON from '@/assets/guide/guide_tip_top_icon.png';
import Monologue_ICON from '@/assets/guide/guide_monologue_icon.png';

import { GuideRoleInfo } from '../../../store/useGuideStore';
import MsgItem from '../../ChatRecordList/MsgItem';
import useDetailStore from '../../../store/useDetailStore';

import './index.scss';

const MonologueTipPanel = ({
    message,
    info,
    clickAction,
}: {
    message: MsgDataType;
    info: GuideRoleInfo;
    clickAction: () => void;
}) => {
    const rootFontSize = getSystemInfoSync().screenWidth / 18.75;

    const hasAiChapter = useDetailStore((state) => state.robotInfo?.hasAiChapter) || false;
    const [monologueImageRight, setMonologueImageRight] = useState(0);
    const monologueImageRef = useRef(null);

    useEffect(() => {
        if (monologueImageRef.current) {
            const rect = monologueImageRef.current.getBoundingClientRect();
            const distanceToRight = window.innerWidth - rect.right;
            setMonologueImageRight(distanceToRight);
        }
    }, []);

    return (
        <View className="chat-guide-monologue-tip-panel" onClick={clickAction}>
            <View
                className="monologue-tip-container"
                style={{ marginBottom: hasAiChapter ? '137px' : '102px' }}>
                <View className="monologue-tip-top-container">
                    <View className="monologue-tip-top">
                        <View className="monologue-tip-bg-content">
                            <Image className="monologue-tip-top-image" src={info.avatarUrl || ''} />
                            <Text className="monologue-tip-top-content">{info.content || ''}</Text>
                            <Text className="monologue-tip-top-subtitle">
                                {info.subTitle || ''}
                            </Text>
                        </View>
                        <View className="monologue-tip-bottom-container">
                            <Image
                                className="monologue-tip-bottom-image"
                                src={TOP_ICON}
                                style={{
                                    right: `${
                                        (monologueImageRight + 53 / 2 - 20 / 2) / rootFontSize
                                    }rem`,
                                }}
                            />
                        </View>
                    </View>
                </View>

                <View className="monologue-tip-message-container">
                    <MsgItem msgData={message} avatar={null} />
                    <Image
                        className="monologue-tip-image"
                        src={Monologue_ICON}
                        ref={monologueImageRef}
                        onClick={clickAction}
                    />
                </View>
            </View>
        </View>
    );
};

export default React.memo(MonologueTipPanel);
