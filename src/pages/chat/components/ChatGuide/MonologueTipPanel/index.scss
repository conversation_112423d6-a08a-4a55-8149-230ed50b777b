.chat-guide-monologue-tip-panel {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);

    .monologue-tip-container {
        position: absolute;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: flex-start;
        bottom: constant(safe-area-inset-bottom);
        bottom: env(safe-area-inset-bottom);

        .monologue-tip-top-container {
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            align-items: flex-end;
            width: 100%;
            padding-right: 15px;

            .monologue-tip-top {
                display: inline-flex;
                flex-direction: column;
                justify-content: flex-end;
                align-items: center;
                max-width: 250px;
                margin-bottom: -10px;

                .monologue-tip-bg-content {
                    background: white;
                    border-radius: 12px;
                    top: 9px;

                    display: inline-flex;
                    flex-direction: column;
                    align-items: flex-start;

                    .monologue-tip-top-image {
                        border-radius: 20px;
                        top: -15px;
                        width: 40px;
                        height: 40px;
                        left: 14px;
                        background: white;
                        border: 2px solid #fff;
                        box-shadow: 0 0 10px 0 rgba(147, 147, 147, 0.3);
                    }

                    .monologue-tip-top-content {
                        display: block;
                        margin: 0 20px;
                        font-size: 14px;
                        line-height: 150%;
                        letter-spacing: 0;
                        font-variation-settings: 'opsz' auto;
                        color: #000;
                        white-space: normal;
                        word-wrap: break-word;
                        word-break: normal;
                    }

                    .monologue-tip-top-subtitle {
                        display: block;
                        margin: 0 20px 24px 20px;
                        font-size: 14px;
                        line-height: 150%;
                        letter-spacing: 0;
                        font-variation-settings: 'opsz' auto;
                        color: rgba(0, 0, 0, 0.5);
                        white-space: normal;
                        word-wrap: break-word;
                        word-break: normal;
                    }
                }

                .monologue-tip-bottom-container {
                    height: 9px;

                    .monologue-tip-bottom-image {
                        position: absolute;
                        width: 20px;
                        height: 9px;
                        transform: scaleY(-1);
                    }
                }
            }
        }

        .monologue-tip-message-container {
            width: 100%;
            bottom: 100px;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            align-items: flex-end;
            padding-right: 15px;

            .monologue-tip-image {
                margin-left: -13px;
                width: 53px;
                height: 53px;
                align-self: center;
            }
        }
    }
}
