import React, { useEffect, useRef, useMemo } from 'react';
import { View } from '@tarojs/components';
import MessageFlow from '@/hooks/message/MessageFlow';
import { SystemMessage } from '@/types/im';

import './index.scss';

const GodotPanel = ({ completeAction }: { completeAction: () => void }) => {
    // 保存定时器 ID
    const timerRef = useRef(null);
    // 用来标记倒计时是否被取消
    const canceledRef = useRef(false);

    const listener = useMemo(
        () => ({
            onSystemMessage(message: SystemMessage) {
                if (timerRef.current) {
                    clearTimeout(timerRef.current);
                    timerRef.current = null;
                    canceledRef.current = true;
                }
            },
        }),
        [timerRef.current, canceledRef.current]
    );

    // 页面加载后启动倒计时
    useEffect(() => {
        // 启动 2 秒倒计时
        timerRef.current = setTimeout(() => {
            if (!canceledRef.current) {
                // 倒计时结束后执行操作
                completeAction();
            }
        }, 2000);

        // 添加升级动效监听
        MessageFlow.addNotificationListener(4305, listener);
        // 组件卸载时清除定时器和事件监听
        return () => {
            if (timerRef.current) {
                clearTimeout(timerRef.current);
            }
            MessageFlow.removeNotificationListener(4305, listener);
        };
    }, []);

    return <View className="godot-background" />;
};

export default React.memo(GodotPanel);
