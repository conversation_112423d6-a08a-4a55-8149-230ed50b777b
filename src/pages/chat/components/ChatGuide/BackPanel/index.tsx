import React from 'react';
import { Image, ITouchEvent, View } from '@tarojs/components';
import BACK_ICON from '@/assets/guide/guide_back_icon.png';
import ANIMATION_ICON from '@/assets/guide/guide_animation.png';
import './index.scss';

const BackPanel = ({ clickAction }: { clickAction: () => void }) => {
    const clickBackAction = (e: ITouchEvent) => {
        e.stopPropagation();
        clickAction();
    };

    return (
        <View className="back-guide-background">
            <Image className="back-icon" src={BACK_ICON} onClick={clickBackAction} />
            <Image
                className="back-animation"
                src={ANIMATION_ICON}
                mode="aspectFit"
                onClick={clickBackAction}
            />
        </View>
    );
};

export default React.memo(BackPanel);
