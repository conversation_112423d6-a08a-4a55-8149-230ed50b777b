import ErrorBoundary from '@/components/ErrorBoundary';
import useFriendStore, { FriendGuideMeta } from '@/components/Friend/useFriendStore';
import useIntimacyStore from '@/pages/chat/components/Favoirability/store/useIntimacyStore';
import { ImDetail, MsgDataType } from '@/types/im';
import { getGlobalData, isAndroid, isIOS } from '@/utils';
import fetch from '@/utils/fetch';
import { View } from '@tarojs/components';
import Taro, { setStorageSync, getCurrentPages } from '@tarojs/taro';
import { getAppName } from '@/utils/appSourceAdapter';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import React, { useCallback, useEffect, useState } from 'react';
import useHomeStore, { HomeTabIndex } from '@/components/Home/useHomeStore';
import { OutputModeStateStoreCreator } from '@/pages/chat/store/useOutputMode';
import { useContextStore } from '@/components/storeContext/StoreContext';
import SwipeBack from '@/utils/adapter/swipeBack';
import useDetailStore from '../../store/useDetailStore';
import useGuideStore, {
    GuideChatMsgInfo,
    GuideInspirationMeta,
    GuideInspirationReplyDto,
    GuideMonologueMeta,
    GuidePageInfo,
    GuidePageType,
    GuidePopupMeta,
    GuideRoleInfo,
    IceChatInfo,
} from '../../store/useGuideStore';
import BackPanel from './BackPanel';
import FavorabilityPanel from './FavorabilityPanel';
import MonologuePanel from './MonologuePanel';
import MonologueTipPanel from './MonologueTipPanel';
import PopupPanel from './PopupPanel';
import SendPanel from './SendPanel';
import GodotPanel from './WaitingForGodot';
import { apiCompleteGuide } from './api';
import './index.scss';

const eventTrackRequest = (data: any) =>
    fetch('/api/mirth/home/<USER>/log', {
        method: 'post',
        data,
    });

const iOSString = () => {
    if (isIOS) {
        return 'iphone';
    }
    if (isAndroid) {
        return 'android';
    }
    return 'h5';
};

const ChatGuide = () => {
    const userIMInfo = getGlobalData<ImDetail>('imDetail');

    const guideInfo = useGuideStore((state) => state.guideInfo);
    const isGuide = useGuideStore((state) => state.isGuide);
    const isRestart = useGuideStore((state) => state.isRestart);
    const outputModeStateStore = useContextStore(OutputModeStateStoreCreator);
    const [showing, setShowing] = useState(false);
    const [godot, setGodot] = useState(false);

    const [selectMsgIds, setSelectMsgIds] = useState<string[]>([]);
    const [currentGuide, setCurrentGuide] = useState<GuidePageInfo | null>(null);
    /// 上次用户选择新手引导的亲密度值
    const [userIntimacyValue, setUserIntimacyValue] = useState(0);
    /// 当前用户选择的新手引导的总亲密度值
    const [totalIntimacyValue, setTotalIntimacyValue] = useState(0);
    /// 上次AI回复的消息体
    const [lastAIMsg, setLastAIMsg] = useState<MsgDataType | null>(null);

    const relanuchApp = useCallback(() => {
        Taro.reLaunch({
            url: 'pages/message/index',
        });
    }, []);

    const showLastDanceAction = useCallback(() => {
        if (godot) {
            return;
        }
        setGodot(true);
        setCurrentGuide({
            pageType: GuidePageType.USER_BACK,
            pageorder: 99,
            guideInfo: {},
            showInfo: undefined,
        });
    }, [godot]);

    const showAiReplay = (guidePageInfo: GuidePageInfo, targetAccid: string) => {
        // 开始显示 AI 真实回复
        const chatMsgInfo = guidePageInfo.showInfo as GuideChatMsgInfo;
        const info = chatMsgInfo.guideMsgMeta[0];
        let incrType = 0;
        if (userIntimacyValue < 2) {
            incrType = 100;
        } else if (userIntimacyValue < 5) {
            incrType = 101;
        } else if (userIntimacyValue < 8) {
            incrType = 102;
        } else {
            incrType = 103;
        }
        const numberString = [
            {
                numberType: 1,
                number: userIntimacyValue,
                incrType,
            },
        ];

        const msgId = `96${new Date().getTime()}`;
        const favoirabilityAnimationKey = `kfavoirabilityAnimationKey_${targetAccid}_${msgId}`;
        setStorageSync(favoirabilityAnimationKey, 'needPlay');

        const numberInfo = JSON.stringify(numberString);
        const aiMsg = {
            contentExt: {
                content: {
                    type: 'aigcCustomTextAudioMsg',
                    content: {
                        text: info.content,
                        audioUrl: info.voiceUrl,
                        duration: info.duration,
                    },
                },
            },
            customExt: {
                serverExt: {
                    sessionPeriodId: targetAccid,
                    numberInfo,
                },
            },
            messageType: V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_CUSTOM,
            time: new Date().getTime(),
            from: targetAccid,
            to: userIMInfo?.accId || '',
            text: info.content,
            flow: 'in',
            type: 'custom',
            scene: 'p2p',
            idClient: `86${new Date().getTime()}`,
            messageClientId: msgId,
            sessionId: `p2p-${targetAccid}`,
        };
        Taro.eventCenter.trigger('showGuideMessage', aiMsg);
        setLastAIMsg(aiMsg);
        // 显示人物立绘动效
        if (incrType >= 101) {
            Taro.eventCenter.trigger('intimacy_increase_greater_2_event');
        }
        // 再倒计时显示下一步
        setTimeout(() => {
            startNextGuide();
        }, info.duration + 1000);
    };

    const startNextGuide = async () => {
        const nextGuide = useGuideStore.getState().nextGuide();
        if (!nextGuide) {
            // 新手模式结束了，需要上报
            if (selectMsgIds.length < 1) {
                relanuchApp();
                return;
            }
            // 先展示
            setCurrentGuide({
                pageType: GuidePageType.USER_WAIT,
                pageorder: 88,
                guideInfo: {},
                showInfo: undefined,
            });
            // 再请求
            const targetUid = guideInfo?.baseInfo.baseInfoDto.userId as string;
            try {
                await apiCompleteGuide({
                    type: 4,
                    body: {
                        userId: targetUid,
                        aigcTouchMsgIds: selectMsgIds,
                        aigcVersion: useGuideStore.getState().aigcVersion,
                    },
                });
            } catch (e) {
                console.error('完成新手引导失败', e);
            }
            return;
        }
        const guidePageInfo = nextGuide as GuidePageInfo;
        addLog(guidePageInfo.pageorder);
        const targetAccid = guidePageInfo.guideInfo.baseInfo.baseInfoDto.accId;
        setCurrentGuide(guidePageInfo);
        if (guidePageInfo.pageType === GuidePageType.AI_START) {
            // 先显示 AI 欢迎语
            const info = guidePageInfo.showInfo as IceChatInfo;
            const msgId = `96${new Date().getTime()}`;
            Taro.eventCenter.trigger('showGuideMessage', {
                contentExt: {
                    content: {
                        type: 'aigcCustomTextAudioMsg',
                        content: {
                            text: info.content,
                            audioUrl: info.voiceUrl,
                            duration: info.duration,
                        },
                    },
                },
                messageType: V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_CUSTOM,
                time: new Date().getTime(),
                from: targetAccid,
                to: userIMInfo?.accId || '',
                text: info.content,
                flow: 'in',
                type: 'custom',
                scene: 'p2p',
                idClient: `86${new Date().getTime()}`,
                messageClientId: msgId,
                sessionId: `p2p-${targetAccid}`,
            });
            // 再倒计时显示下一步
            setTimeout(() => {
                startNextGuide();
            }, info.duration + 1000);
        } else if (guidePageInfo.pageType === GuidePageType.AI_REPLY) {
            // 机器人回复
            // 先显示 loading
            setTimeout(() => {
                Taro.eventCenter.trigger('showGuideMessage', {
                    messageClientId: 'loading',
                });
            }, 100);
            // 再显示提示语
            setTimeout(() => {
                showAiReplay(guidePageInfo, targetAccid);
            }, 2000);
        } else if (guidePageInfo.pageType === GuidePageType.USER_SELECT) {
            // 用户选择好感度回复
            const info = guidePageInfo.showInfo as GuideInspirationReplyDto;
            const intimacy = info.inspirationReplyMeta[0].intimacy;
            setUserIntimacyValue(intimacy);
        } else if (guidePageInfo.pageType === GuidePageType.USER_FAVORABILITY) {
            // 用户好感度提升
        } else if (guidePageInfo.pageType === GuidePageType.AI_SEE_OS) {
            // 用户内心 OS 引导
        } else if (guidePageInfo.pageType === GuidePageType.AI_LOOK_OS) {
            // 用户查看内心 OS
        } else if (guidePageInfo.pageType === GuidePageType.AI_POPUP) {
            // 用户查看内心 OS 弹窗
        }
    };

    useEffect(() => {
        useGuideStore.getState().initGuide();
    }, []);

    useEffect(() => {
        Taro.eventCenter.on('guideAnimationCompleted', showLastDanceAction);
        return () => {
            Taro.eventCenter.off('guideAnimationCompleted');
        };
    }, [showLastDanceAction]);

    useEffect(() => {
        if (isGuide) {
            useDetailStore
                .getState()
                .refreshGuideInfo(
                    guideInfo?.baseInfo.baseInfoDto.userId || '',
                    guideInfo?.baseInfo.baseInfoDto.accId || ''
                );
            outputModeStateStore
                .getState()
                .queryChatOutputMode(guideInfo?.baseInfo.baseInfoDto.userId);
        }
    }, [isGuide, guideInfo, outputModeStateStore]);

    useEffect(() => {
        SwipeBack.enabled = !isGuide;
    }, [isGuide]);

    useEffect(() => {
        if (showing) {
            // 新手引导中，不显示
            return;
        }
        if (isRestart) {
            // 当前报错，需要重启
            relanuchApp();
            return;
        }
        if (isGuide) {
            // 开始显示新手引导
            setShowing(true);
            startNextGuide();
        }
    }, [isGuide, isRestart, guideInfo, showing, relanuchApp]);

    useEffect(() => {
        if (totalIntimacyValue > 0) {
            useIntimacyStore.getState().refreshintimacyValue(totalIntimacyValue);
        }
    }, [totalIntimacyValue]);

    const clickUserSelect = (meta: GuideInspirationMeta) => {
        setTotalIntimacyValue(totalIntimacyValue + meta.intimacy);
        setSelectMsgIds([...selectMsgIds, meta.msgId]);
        //
        const fromInfo = currentGuide as GuidePageInfo;
        const targetAccid = fromInfo.guideInfo.baseInfo.baseInfoDto.accId;
        const msgId = `96${new Date().getTime()}`;
        Taro.eventCenter.trigger('showGuideMessage', {
            messageType: V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_TEXT,
            time: new Date().getTime(),
            to: targetAccid,
            from: userIMInfo?.accId || '',
            text: meta.content,
            isSelf: true,
            flow: 'out',
            type: 'text',
            scene: 'p2p',
            idClient: `86${new Date().getTime()}`,
            messageClientId: msgId,
            sessionId: `p2p-${targetAccid}`,
        });
        startNextGuide();
    };

    const clickFavorability = () => {
        startNextGuide();
    };

    const clickBackAction = () => {
        // 先保存跳转信息
        const useInfo = guideInfo?.baseInfo.baseInfoDto;
        const roleInfo = guideInfo?.endInfo.roleInfo;
        const mockFriendGuide: FriendGuideMeta = {
            topAvatar: roleInfo?.avatarUrl || '',
            topContent: roleInfo?.content || '',
            userId: Number(useInfo?.userId || ''),
            userAvatar: useInfo?.avatarUrl || '',
            userName: useInfo?.nickname || '',
            userSign: useInfo?.intro || '',
        };
        useFriendStore.getState().setGuide(mockFriendGuide);
        useGuideStore.getState().resetGuide();
        // 根据用户选择决定跳转页面
        // 跳转到好友页面
        if (getCurrentPages().length > 1) {
            useHomeStore.getState().setCurrent(HomeTabIndex.friend);
            Taro.navigateBack();
        } else {
            Taro.reLaunch({
                url: 'pages/friends/index',
            });
        }
    };

    const addLog = (step: number) => {
        const appearTime = Date.now();
        const requestParams = {
            _spm: 'btn_ai_chat_guide_step|page_ai_chat_guide|page_h5_biz',
            action: '_ec',
            userid: '',
            log_time: appearTime,
            os: iOSString(),
            appname: getAppName(),
            step,
        };
        const paramsString = JSON.stringify(requestParams);
        try {
            eventTrackRequest({
                log: paramsString,
            });
        } catch (e) {
            console.error('埋点失败', e);
        }
    };

    const renderChildComponent = () => {
        if (!currentGuide) {
            return null;
        }
        switch (currentGuide.pageType) {
            case GuidePageType.AI_START:
            case GuidePageType.AI_REPLY:
                return null;
            case GuidePageType.USER_SELECT:
                return (
                    <SendPanel
                        showInfo={currentGuide.showInfo as GuideInspirationReplyDto}
                        selectIndex={clickUserSelect}
                    />
                );
            case GuidePageType.USER_FAVORABILITY:
                return (
                    <FavorabilityPanel
                        guideInfo={currentGuide.showInfo as GuideRoleInfo}
                        clickAction={clickFavorability}
                    />
                );
            case GuidePageType.AI_SEE_OS:
                return (
                    <MonologueTipPanel
                        message={lastAIMsg}
                        info={currentGuide.showInfo as GuideRoleInfo}
                        clickAction={startNextGuide}
                    />
                );
            case GuidePageType.AI_POPUP:
                return (
                    <PopupPanel
                        info={currentGuide.showInfo as GuidePopupMeta}
                        clickAction={startNextGuide}
                    />
                );
            case GuidePageType.AI_LOOK_OS:
                return (
                    <MonologuePanel
                        info={currentGuide.showInfo as GuideMonologueMeta}
                        clickAction={startNextGuide}
                    />
                );
            case GuidePageType.USER_WAIT:
                return <GodotPanel completeAction={showLastDanceAction} />;
            case GuidePageType.USER_BACK:
                return <BackPanel clickAction={clickBackAction} />;
            default:
                return null;
        }
    };

    return isGuide ? (
        <ErrorBoundary>
            <View className="chat-guide">{renderChildComponent()}</View>
        </ErrorBoundary>
    ) : null;
};

export default React.memo(ChatGuide);
