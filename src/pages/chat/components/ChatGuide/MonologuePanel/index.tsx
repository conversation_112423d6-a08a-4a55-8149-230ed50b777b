import React, { useEffect, useState, useCallback, useRef } from 'react';
import Taro, { getSystemInfoSync, createSelectorQuery } from '@tarojs/taro';
import { AtModal } from 'taro-ui';
import { View, Image, Text } from '@tarojs/components';

import { throttle } from '@/utils';
import Avatar from '@/components/avatar';

import closeIcon from '@/assets/common/ic_dialog_close.png';
import cloudIcon from '@/assets/chat/ic_chat_monologue_cloud.png';
import upIcon from '@/assets/chat/ic_chat_monologue_up.png';
import downIcon from '@/assets/chat/ic_chat_monologue_down.png';
import Spin_Loading_Chat from '@/assets/effect/spining_loading_chat.png';
import CLOSE_ICON from '@/assets/guide/guide_monologue_close_icon.png';
import ANIMATION_ICON from '@/assets/guide/guide_animation.png';
import TOP_ICON from '@/assets/guide/guide_tip_top_icon.png';

import { GuideMonologueMeta } from '../../../store/useGuideStore';

import './index.scss';

const MonologuePanel = ({
    info,
    clickAction,
}: {
    info: GuideMonologueMeta;
    clickAction: () => void;
}) => {
    const closeIconRef = useRef(null);
    const [showStatus, setShowStatus] = useState(0);
    const [position, setPosition] = useState({ top: 0, right: 0 });

    useEffect(() => {
        setTimeout(() => {
            setShowStatus(1);
        }, 1000);
    }, []);

    useEffect(() => {
        Taro.nextTick(() => {
            const query = createSelectorQuery();
            query
                .select('.close-icon')
                .boundingClientRect((rect) => {
                    if (rect) {
                        // 计算元素距离顶部和右侧的距离
                        setPosition({
                            top: rect.top - 18,
                            right: getSystemInfoSync().windowWidth - rect.right - 9,
                        });
                    }
                })
                .exec();
        });
    }, []);

    // 使用useRef保存throttle函数，确保它在组件生命周期内保持不变
    const throttledClickRef = useRef(
        throttle((currentStatus) => {
            console.log('ysl clickBackgroundAction ', currentStatus);
            if (currentStatus === 2) {
                return;
            }
            setShowStatus(2);
        }, 1000)
    );

    const clickBackgroundAction = useCallback(() => {
        throttledClickRef.current(showStatus);
    }, [showStatus]);

    return (
        <View
            className="chat-guide-monologue-panel"
            style={{
                backgroundColor: showStatus === 2 ? 'rgba(0, 0, 0, 0)' : 'rgba(0, 0, 0, 0.7)',
            }}>
            <View className="monologueContainer">
                <AtModal isOpened className="custom-modal" closeOnClickOverlay={false}>
                    <View className="close-container">
                        <Image src={closeIcon} className="close-icon" ref={closeIconRef} />
                        <View className="modal-content">
                            <Text className="text-title">{info.title}</Text>
                            <View className="avatar-container">
                                <Avatar
                                    width={52}
                                    height={52}
                                    lazyload
                                    className="avatar"
                                    src={info.avatarUrl || ''}
                                />
                                <Image src={cloudIcon} className="cloud-icon" />
                            </View>
                            <View className="text-container">
                                {showStatus === 0 && (
                                    <Image src={Spin_Loading_Chat} style={{ width: '30px' }} />
                                )}
                                <Text className="text-content">
                                    {showStatus === 0 ? '' : info.content}
                                </Text>
                            </View>
                            <View className="button-container">
                                <View className="button">
                                    <Image src={upIcon} className="button-icon" />
                                </View>
                                <View className="button">
                                    <Image src={downIcon} className="button-icon" />
                                </View>
                            </View>
                        </View>
                    </View>
                </AtModal>
            </View>
            {showStatus === 1 ? (
                <View
                    className="chat-guide-monologue-popup-container"
                    onClick={clickBackgroundAction}>
                    <View
                        className="chat-guide-monologue-popup-tip"
                        style={{ height: position.top + 18 }}>
                        <View className="chat-guide-monologue-popup-bg-content">
                            <Image
                                className="chat-guide-monologue-popup-tip-image"
                                src={info.roleInfo?.avatarUrl || ''}
                            />
                            <Text className="chat-guide-monologue-popup-tip-content">
                                {info.roleInfo?.content || ''}
                            </Text>
                            <Text className="chat-guide-monologue-popup-tip-subtitle">
                                {info.roleInfo?.subTitle || ''}
                            </Text>
                        </View>
                        <View className="chat-guide-monologue-popup-image-container">
                            <Image className="chat-guide-monologue-popup-image" src={TOP_ICON} />
                        </View>
                    </View>
                </View>
            ) : null}
            {showStatus === 2 && (
                <View className="chat-guide-monologue-close-container">
                    <View
                        className="chat-guide-monologue-close-panel"
                        style={{ top: position.top, right: position.right }}
                        onClick={clickAction}>
                        <Image className="chat-guide-monologue-close-bg-image" src={CLOSE_ICON} />
                        <Image className="chat-guide-monologue-close-image" src={closeIcon} />
                        <Image
                            className="chat-guide-monologue-animation-image"
                            src={ANIMATION_ICON}
                            mode="aspectFit"
                        />
                    </View>
                </View>
            )}
        </View>
    );
};

export default React.memo(MonologuePanel);
