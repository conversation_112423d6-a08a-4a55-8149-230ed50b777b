import React, { useEffect, useState, useRef } from 'react';
import Taro from '@tarojs/taro';
import { View, Image, Text } from '@tarojs/components';
import { ChatType } from '@/pages/chat/type';
import TOP_ICON from '@/assets/guide/guide_tip_top_icon.png';
import BG_ICON from '@/assets/guide/guide_send_background_icon.png';
import ANIMATION_ICON from '@/assets/guide/guide_animation.png';
import Inspiration from '../../Inspiration';
import { GuideInspirationReplyDto, GuideInspirationMeta } from '../../../store/useGuideStore';

import './index.scss';

const SendPanel = ({
    showInfo,
    selectIndex,
}: {
    showInfo: GuideInspirationReplyDto;
    selectIndex: (meta: GuideInspirationMeta) => void;
}) => {
    const [inspirationHeight, setInspirationHeight] = useState(0);
    const inspirationRef = useRef(null);
    const hasTip = showInfo.roleInfo !== undefined;

    useEffect(() => {
        Taro.nextTick(() => {
            if (inspirationRef.current) {
                const height = inspirationRef.current.offsetHeight;
                setInspirationHeight(height);
            }
        });
    }, []);

    const newList = showInfo.inspirationReplyMeta.map((item) => {
        return {
            reply: item.content,
        };
    });

    const changeInspirationAction = () => {};

    const scrollToBottom = () => {};

    const sendGuideMsg = (index: number) => {
        selectIndex(showInfo.inspirationReplyMeta[index]);
    };

    return (
        <View className="send-guide-background">
            <View className="send-panel">
                {hasTip ? (
                    <View className="send-top-tip">
                        <View className="send-top-bg-content">
                            <Image
                                className="send-top-tip-image"
                                src={showInfo.roleInfo?.avatarUrl || ''}
                            />
                            <Text className="send-top-tip-content">
                                {showInfo.roleInfo?.content || ''}
                            </Text>
                            <Text className="send-top-tip-subtitle">
                                {showInfo.roleInfo?.subTitle || ''}
                            </Text>
                        </View>
                        <Image className="send-bottom-image" src={TOP_ICON} />
                    </View>
                ) : null}
                <Inspiration
                    ref={inspirationRef}
                    type={ChatType.IMMERSE}
                    fromGuide
                    guideList={newList}
                    sendGuideMsg={sendGuideMsg}
                />
                <Image
                    className="send-animation-image"
                    src={BG_ICON}
                    style={{ height: inspirationHeight + 68 }}
                />
                <View className="send-panel-bottom">
                    <Text className="send-panel-bottom-content">选择心仪的回复</Text>
                </View>
            </View>
            <Image className="send-webp-animation" src={ANIMATION_ICON} mode="aspectFit" />
        </View>
    );
};

export default React.memo(SendPanel);
