.send-guide-background {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);

    @keyframes scaleUpDown {
        0% {
            transform: scale(1.02);
        }

        50% {
            transform: scale(1);
        }

        100% {
            transform: scale(1.02);
        }
    }

    .send-panel {
        position: absolute;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: flex-end;
        padding-right: 21px;
        bottom: constant(safe-area-inset-bottom);
        bottom: env(safe-area-inset-bottom);

        .send-top-tip {
            display: inline-flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: center;
            max-width: 80%;
            margin-right: 36px;
            margin-bottom: 20px;

            .send-top-bg-content {
                background: white;
                border-radius: 12px;
                top: 9px;

                display: inline-flex;
                flex-direction: column;
                align-items: flex-start;

                .send-top-tip-image {
                    border-radius: 20px;
                    top: -15px;
                    width: 40px;
                    height: 40px;
                    left: 14px;
                    background: white;
                    border: 2px solid #fff;
                    box-shadow: 0 0 10px 0 rgba(147, 147, 147, 0.3);
                }

                .send-top-tip-content {
                    display: block;
                    margin: 0 20px;
                    font-size: 14px;
                    line-height: 150%;
                    letter-spacing: 0;
                    font-variation-settings: 'opsz' auto;
                    color: #000;
                    white-space: normal;
                    word-wrap: break-word;
                    word-break: normal;
                }

                .send-top-tip-subtitle {
                    display: block;
                    margin: 0 20px 24px 20px;
                    font-size: 14px;
                    line-height: 150%;
                    letter-spacing: 0;
                    font-variation-settings: 'opsz' auto;
                    color: rgba(0, 0, 0, 0.5);
                    white-space: normal;
                    word-wrap: break-word;
                    word-break: normal;
                }
            }

            .send-bottom-image {
                width: 20px;
                height: 9px;
                transform: scaleY(-1);
            }
        }

        .send-animation-image {
            resize: stretch;
            position: absolute;
            width: 325px;
            right: -14px;
            bottom: 32px;
            display: inline-block;
            animation: scaleUpDown 1s infinite;
            pointer-events: none;
        }

        .send-panel-bottom {
            height: 30px;
            margin-bottom: 24px;
            display: inline-flex;
            flex-direction: column;
            justify-content: center;
            margin-top: 12px;
            margin-right: 22px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;

            .send-panel-bottom-content {
                font-size: 12px;
                color: #fff8f3;
                padding-left: 12px;
                padding-right: 12px;
            }
        }
    }

    .send-webp-animation {
        position: absolute;
        transform: scaleX(-1);
        bottom: calc(30px + constant(safe-area-inset-bottom));
        bottom: calc(30px + env(safe-area-inset-bottom));
        width: 101px;
        height: 79px;
        right: 38px;
        pointer-events: none;
    }
}
