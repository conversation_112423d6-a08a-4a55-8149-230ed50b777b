import React from 'react';
import { View, Image, Text } from '@tarojs/components';

import { ChatType } from '@/pages/chat/type';
import { getImage } from '@/utils/image';
import FavoirabilityHeadView from '@/pages/chat/components/Favoirability/favoirability-head-view';

import BG_ICON from '@/assets/guide/guide_favorability_background_icon.png';
import TOP_ICON from '@/assets/guide/guide_tip_top_icon.png';

import { GuideRoleInfo } from '../../../store/useGuideStore';

import './index.scss';

const FavorabilityPanel = ({
    guideInfo,
    clickAction,
}: {
    guideInfo: GuideRoleInfo;
    clickAction: () => void;
}) => {
    return (
        <View className="favorability-guide-background" onClick={clickAction}>
            <Image className="favorability-bg-image" src={BG_ICON} mode="scaleToFill" />
            <FavoirabilityHeadView type={ChatType.IMMERSE} fromGuide />
            <View className="favorability-text-container">
                <Image className="favorability-top-image" src={TOP_ICON} />
                <View className="favorability-bg-content">
                    <Image className="favorability-tip-iamge" src={getImage(guideInfo.avatarUrl)} />
                    <Text className="favorability-tip-content">{guideInfo.content}</Text>
                    <Text className="favorability-tip-guide">{guideInfo.subTitle || ''}</Text>
                </View>
            </View>
        </View>
    );
};

export default React.memo(FavorabilityPanel);
