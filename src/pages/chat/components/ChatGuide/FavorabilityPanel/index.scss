.favorability-guide-background {
    position: fixed;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;

    .favorability-bg-image {
        position: absolute;
        width: 100%;
        height: 70px;
        top: var(--status-bar-height);
        margin-top: 31px;
    }

    .favorability-text-container {
        position: absolute;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        top: var(--status-bar-height);
        margin-top: 100px;

        .favorability-top-image {
            position: absolute;
            width: 20px;
            height: 9px;
            top: 0;
        }

        .favorability-bg-content {
            position: absolute;
            background: white;
            border-radius: 12px;
            top: 9px;
            max-width: 80%;
            display: inline-flex;
            flex-direction: column;
            align-items: flex-start;

            .favorability-tip-iamge {
                position: absolute;
                border-radius: 421px;
                top: -15px;
                width: 40px;
                height: 40px;
                left: 14px;
                background: white;
                border: 2px solid #fff;
                box-shadow: 0 0 10px 0 rgba(147, 147, 147, 0.3);
            }

            .favorability-tip-content {
                display: block;
                margin: 36px 20px 0 20px;
                font-size: 14px;
                line-height: 150%;
                letter-spacing: 0;
                font-variation-settings: 'opsz' auto;
                color: #000;
                white-space: normal;
                word-wrap: break-word;
                word-break: normal;
            }

            .favorability-tip-guide {
                display: block;
                margin: 0 20px 24px 20px;
                font-size: 14px;
                line-height: 150%;
                letter-spacing: 0;
                font-variation-settings: 'opsz' auto;
                color: rgba(0, 0, 0, 0.5);
                white-space: normal;
                word-wrap: break-word;
                word-break: normal;
            }
        }
    }
}
