.chat-guide-popup-panel {
    position: absolute;
    width: 100%;
    height: 100%;

    .commonConfirmContainer {
        .custom-modal {
            .at-modal__overlay {
                background-color: rgba(0, 0, 0, 0.5);
            }

            .at-modal__container {
                width: 80%;
                max-width: 300px;
                border-radius: 20px;
            }

            .modal-content {
                padding-left: 20px;
                padding-right: 20px;
                padding-top: 50px;
                padding-bottom: 30px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                background: white;
            }

            .text-title {
                font-size: 18px;
                text-align: center;
                font-weight: 600;
                color: black;
            }

            .text-content {
                margin-top: 12px;
                font-size: 13px;
                text-align: center;
                color: rgba(0, 0, 0, 0.4);
            }

            .btn-group {
                display: flex;
                justify-content: space-between;
                width: 100%;
                margin-top: 30px;
            }

            .cancel,
            .confirm {
                width: 45%;
                height: 44px;
                font-size: 16px;
                border-radius: 22px;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0;
            }

            .cancel {
                background-color: #bfbfbf;
                color: white;
            }

            .confirm {
                background-color: #ff689e;
                color: white;
            }
        }
    }

    .guide-webp-animation-container {
        position: absolute;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 2000;
        background: transparent;

        .guide-webp-animation {
            position: absolute;
            top: 50%;
            margin-top: 30px;
            right: 0;
            width: 101px;
            height: 79px;
            transform: scaleX(-1);
        }
    }
}
