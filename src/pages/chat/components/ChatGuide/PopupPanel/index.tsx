import React from 'react';
import { AtModal } from 'taro-ui';
import { View, Text, Image } from '@tarojs/components';
import ANIMATION_ICON from '@/assets/guide/guide_animation.png';
import { GuidePopupMeta } from '../../../store/useGuideStore';

import './index.scss';

const PopupPanel = ({ info, clickAction }: { info: GuidePopupMeta; clickAction: () => void }) => {
    return (
        <View className="chat-guide-popup-panel">
            <View className="monologueConfirmContainer">
                <AtModal isOpened className="custom-modal" closeOnClickOverlay={false}>
                    <View className="modal-content">
                        <Text className="text-title">{info.title}</Text>
                        <Text className="text-content">{info.subTitle}</Text>
                        <View className="btn-group">
                            <View className="cancel">取消</View>
                            <View className="confirm" onClick={clickAction}>
                                确定
                            </View>
                        </View>
                    </View>
                </AtModal>
            </View>
            <View className="guide-webp-animation-container">
                <Image className="guide-webp-animation" src={ANIMATION_ICON} mode="aspectFit" />
            </View>
        </View>
    );
};

export default React.memo(PopupPanel);
