import React, { useEffect } from 'react';
import { View, Image } from '@tarojs/components';
import ErrorBoundary from '@/components/ErrorBoundary';
import ICON_MODE_GUIDE from '@/assets/chat/guide/icon_mode_guide_finger.png';
import { useContextStore } from '@/components/storeContext/StoreContext';
import './index.scss';
import { ChatGuideMode, GuideTipStoreCreator } from '../../store/useGuideTipStore';

const ModeGuide = () => {
    const guideTipStore = useContextStore(GuideTipStoreCreator);
    const guideMode = guideTipStore((state) => state.guideMode);

    useEffect(() => {
        if (guideMode === ChatGuideMode.Swipe) {
            setTimeout(() => {
                guideTipStore.getState().closeGuideTip();
            }, 5000);
        }
    }, [guideMode, guideTipStore]);

    return guideMode === ChatGuideMode.Swipe ? (
        <ErrorBoundary>
            <View className="mode-guide-mask">
                <View className="mode-guide-content">
                    <Image className="mode-guide-image" src={ICON_MODE_GUIDE} />
                </View>
            </View>
        </ErrorBoundary>
    ) : null;
};

export default React.memo(ModeGuide);
