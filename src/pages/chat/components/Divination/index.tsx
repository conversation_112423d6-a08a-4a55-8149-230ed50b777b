import React, { useEffect, useMemo, useRef, useState } from 'react';
import { View, Text, ITouchEvent, Picker } from '@tarojs/components';
import MessageFlow from '@/hooks/message/MessageFlow';
import { SystemMessage } from '@/types/im';
import FullScreenModal from '@/components/FullScreenModal';
import { showToast, getCurrentInstance } from '@tarojs/taro';
import domAdapter from '@/utils/adapter/domAdapter';
import { useMessageStore } from '@/hooks/messageStore';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { postDivinationUploadTime } from '../../api';
import './index.scss';

const DivinationInvite = () => {
    const [visible, setVisible] = useState(false);
    const [dateSel, setDateSel] = useState('2000-01-01');
    const [timeSel, setTimeSel] = useState('00:00');
    const [title, setTitle] = useState('');
    const [content, setContent] = useState('');
    const ref = useRef<any>();
    const ref2 = useRef<any>();

    const hideKeyboard = () => {
        domAdapter.blurActiveElement();
    };

    const listener = useMemo(
        () => ({
            onSystemMessage(message: SystemMessage) {
                coronaWarnMessage(
                    'ChatPageLog',
                    `收到占卜消息 时间戳${message.timestamp}, serverExt: ${JSON.stringify(
                        message.contentExt?.serverExt
                    )}`
                );
                hideKeyboard();
                const serverExtContent = message.contentExt?.serverExt?.content;
                const msgTitle = serverExtContent.title || '是否需要进行占卜';
                const msgContent = serverExtContent.content || '请选择您的生日';
                setTitle(msgTitle);
                setContent(msgContent);
                setDateSel('2000-01-01');
                setTimeSel('00:00');
                setVisible(true);
            },
        }),
        []
    );

    const requestDivinationUploadTime = async (time: string) => {
        coronaWarnMessage(
            'ChatPageLog',
            `占卜 开始请求上传时间 时间戳${Date.now()}, dateSel: ${dateSel}, time: ${time}`
        );
        const dateWithHHMM = `${dateSel} ${time}`;
        const sessionPeriod = useMessageStore.getState().periodId;
        const { robotUserId } = getCurrentInstance().router?.params || {};
        showToast({
            title: '命锁已启，快来与我互动，看看你的命途如何展开。',
            icon: 'none',
        });
        try {
            await postDivinationUploadTime({
                userInputTime: dateWithHHMM,
                sessionPeriod,
                robotUserId,
            });
            coronaWarnMessage('ChatPageLog', `占卜 请求上传时间成功`);
        } catch (error) {
            console.error('占卜上传时间失败', error);
            coronaWarnMessage('ChatPageLog', `占卜 请求上传时间失败，${error}`);
        }
    };

    const onDateChange = (e: any) => {
        const date = e.detail.value;
        setDateSel(date);

        if (ref2.current) {
            ref2.current.click();
        }
    };

    const onTimeChange = (e: any) => {
        const time = e.detail.value;
        setTimeSel(time);
        setVisible(false);

        requestDivinationUploadTime(time);
    };

    const onDateCancel = (e: any) => {
        setVisible(false);
    };

    const onTimeCancel = (e: any) => {
        if (ref.current) {
            ref.current.click();
        }
    };

    const onCancel = () => {
        setVisible(false);
    };

    const onConfirm = () => {
        if (ref.current) {
            ref.current.click();
        }
    };

    useEffect(() => {
        MessageFlow.addNotificationListener(6000, listener);
        return () => {
            MessageFlow.removeNotificationListener(6000, listener);
        };
    }, [listener]);

    return (
        <View>
            <FullScreenModal onClose={() => setVisible(false)} visible={visible}>
                <View
                    className="custom-modal-divination"
                    onClick={(e: ITouchEvent) => {
                        e.stopPropagation();
                    }}>
                    <View className="modal-content">
                        <Text className="text-title">{title}</Text>
                        <Text className="text-content">{content}</Text>
                        <View className="btn-group">
                            <View className="cancel" onClick={onCancel}>
                                取消
                            </View>
                            {/* <Picker
                                mode="date"
                                value={dateSel}
                                onChange={onDateChange}
                                onCancel={onDateCancel}
                                end={new Date().toISOString().split('T')[0]}> */}
                            <View className="confirm" onClick={onConfirm}>
                                确认
                            </View>
                            {/* </Picker> */}
                        </View>
                    </View>
                </View>
            </FullScreenModal>
            <Picker
                mode="date"
                value={dateSel}
                onChange={onDateChange}
                onCancel={onDateCancel}
                end={new Date().toISOString().split('T')[0]}>
                <View style={{ display: 'none' }} ref={ref} />
            </Picker>
            <Picker mode="time" value={timeSel} onChange={onTimeChange} onCancel={onTimeCancel}>
                <View style={{ display: 'none' }} ref={ref2} />
            </Picker>
        </View>
    );
};

export default DivinationInvite;
