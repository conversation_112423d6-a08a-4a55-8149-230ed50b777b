@import '~taro-ui/dist/style/components/list.scss';

.custom-modal-divination {
    display: flex;
    align-items: center;
    justify-content: center;

    .modal-content {
        width: 80%;
        border-radius: 16px;
        padding: 30px 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: #fff;
        min-width: 300px;
    }

    .text-title {
        font-size: 18px;
        text-align: center;
        font-weight: 600;
        color: rgba(0, 0, 0, 1);
    }

    .text-content {
        margin-top: 12px;
        font-size: 13px;
        text-align: center;
        color: rgba(0, 0, 0, 0.4);
    }

    .btn-group {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: 30px;
    }

    .cancel,
    .confirm {
        width: 110px;
        height: 44px;
        font-size: 18px;
        font-weight: 600;
        border-radius: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        color: white;
    }

    .cancel {
        background-color: #bfbfbf;
    }

    .confirm {
        background-color: #ff689e;
    }
}

// .divination-pickers {
.weui-picker__overlay {
    .weui-picker__hd {
        height: 50px;
        padding: 0;

        .weui-picker__action {
            // background-color: #ff689e;
            font-size: 18px;
            padding-top: 12px;
            padding-right: 10px;
            padding-left: 10px;
        }
    }
}
// }
