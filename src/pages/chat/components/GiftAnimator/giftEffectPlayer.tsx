import Taro from '@tarojs/taro';
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, Image } from '@tarojs/components';
import { AlphaVideo, PositionMode, SizeMode } from '@music/ct-animation';
import ErrorBoundary from '@/components/ErrorBoundary';
import { GiftMeta } from '../../store/useGiftResourcesStore';
import './index.scss';

// 定义动效接口
interface GiftEffect {
    type: 'video' | 'image';
    src: string;
    priority: number;
}

// 动效队列类
class EffectQueue {
    private queue: GiftEffect[] = [];

    add(effect: GiftEffect) {
        this.queue.push(effect);
        this.queue.sort((a, b) => b.priority - a.priority);
    }

    next(): GiftEffect | undefined {
        return this.queue.shift();
    }

    isEmpty(): boolean {
        return this.queue.length === 0;
    }
}

// 礼物动效播放组件
const GiftEffectPlayer: React.FC = () => {
    const [currentEffect, setCurrentEffect] = useState<GiftEffect | null>(null);
    const isPlayingRef = useRef(false);
    const [animationState, setAnimationState] = useState('initial');
    const effectQueue = useRef(new EffectQueue());

    const startImageAnimation = () => {
        setAnimationState('fadeIn-moveUp-fadeOut');
    };

    const playNextEffect = useCallback(() => {
        const nextEffect = effectQueue.current.next();
        if (nextEffect) {
            isPlayingRef.current = true;
            setCurrentEffect(nextEffect);
            if (nextEffect.type === 'image') {
                startImageAnimation();
            }
        }
    }, []);

    // 外部调用的方法，添加新的动效到队列
    const addEffect = useCallback(
        (effect: GiftEffect) => {
            effectQueue.current.add(effect);
            if (!isPlayingRef.current) {
                playNextEffect();
            }
        },
        [playNextEffect]
    );

    const addGiftAnimationEffect = useCallback(
        (giftMeta: GiftMeta) => {
            if (giftMeta.basicResource?.commonMaterialFile?.url) {
                const effect: GiftEffect = {
                    type: 'video',
                    src: giftMeta.basicResource?.commonMaterialFile?.url,
                    priority: 2,
                };
                addEffect(effect);
            } else {
                const url =
                    giftMeta.basicResource?.previewImg?.url ??
                    giftMeta.basicResource?.thumbnailImg?.url;

                if (url) {
                    const effect: GiftEffect = {
                        type: 'image',
                        src: url,
                        priority: 1,
                    };
                    addEffect(effect);
                }
            }
        },
        [addEffect]
    );

    useEffect(() => {
        Taro.eventCenter.on('addGiftAnimationEffect', addGiftAnimationEffect);
        return () => {
            Taro.eventCenter.off('addGiftAnimationEffect', addGiftAnimationEffect);
        };
    }, [addGiftAnimationEffect]);

    const onPlayEnded = () => {
        isPlayingRef.current = false;
        setCurrentEffect(null);
        setAnimationState('initial');
        playNextEffect();
    };

    return (
        <ErrorBoundary>
            <View
                className="gift-effect-player"
                hidden={!currentEffect}
                style={{ pointerEvents: currentEffect?.type === 'video' ? 'all' : 'none' }}>
                {currentEffect && currentEffect.type === 'video' && (
                    <AlphaVideo
                        src={currentEffect.src}
                        style={{ width: '100%', height: '100%' }}
                        autoplay
                        loop={false}
                        sizeMode={SizeMode.Cover}
                        positionMode={PositionMode.CenterCenter}
                        onComplete={onPlayEnded}
                        onError={onPlayEnded}
                        onDataError={onPlayEnded}
                    />
                )}
                {currentEffect && currentEffect.type === 'image' && (
                    <Image
                        src={currentEffect.src}
                        className={`gift-image ${animationState}`}
                        onAnimationEnd={onPlayEnded}
                    />
                )}
            </View>
        </ErrorBoundary>
    );
};

export default React.memo(GiftEffectPlayer);
