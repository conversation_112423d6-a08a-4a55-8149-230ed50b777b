.gift-effect-player {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 999;
}

.gift-image {
    position: absolute;
    left: 50%;
    bottom: 50%;
    transform: translate(-50%, 50%);
    width: 80px;
    height: 80px;
    opacity: 0;
}

.fadeIn-moveUp-fadeOut {
    animation: total 2.5s;
}

@keyframes total {
    0% {
        opacity: 0;
        transform: translate(-50%, 0%) scale(1);
    }

    10% {
        opacity: 1;
        transform: translate(-50%, 0%) scale(1.3);
    }

    30% {
        opacity: 1;
        transform: translate(-50%, 0%) scale(1.3);
    }

    85% {
        opacity: 1;
        transform: translate(-50%, -200%) scale(1.2);
    }

    100% {
        opacity: 0;
        transform: translate(-50%, -200%) scale(1.2);
    }
}
