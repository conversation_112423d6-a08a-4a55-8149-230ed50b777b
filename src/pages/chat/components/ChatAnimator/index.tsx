import React, { useEffect, useState, useRef } from 'react';
import Taro, {
    getCurrentInstance,
    useDidHide,
    useDidShow,
    getStorageSync,
    createAnimation,
} from '@tarojs/taro';
import { View, Image } from '@tarojs/components';

import { ChatType } from '@/pages/chat/type';
import UpgradeAlertManager from '@/utils/managers/UpgradeAlertManager';
import CenterAnimatorContainer from '@/components/animator/container';
import ErrorBoundary from '@/components/ErrorBoundary';

import bgBorderAnimator from '@/assets/chat/bg_border_animator.png';

import { useDialog } from '@/components/dialog';
import { createSimpleModalProvider } from '@/components/SimpleModal';
import useDetailStore from '../../store/useDetailStore';
import UpgradeAlert, { IScoreQueueItemContent } from '../UpgradeAlert';
import './index.scss';
import { mainPlot } from '../ChatPlot/MainPlotList';
import mainPlotListStore from '../ChatPlot/MainPlotList/mainPlotListStore';
import mainPlotStore from '../ChatPlot/MainPlot/mainPlotStore';

export interface IScoreQueueItem {
    incrType?: number;
    content?: IScoreQueueItemContent;
    idServer?: number;
}

const favorabilitAnimatorVideo =
    '//nos.netease.com/vodkgeyttp8/c37420e888a07a98e77639ce5e935c8c.mp4';
const surroundAnimatorVideo =
    '//d1.music.126.net/dmusic/fe94/a90c/f2fc/4ecefb05f025ebfa2ff440fa0380a7ee.mp4?infoId=2612187';
const upgradeAnimatorVideo =
    '//d1.music.126.net/dmusic/02ea/acdf/9ccd/d76074cea25ea2b5b45952693303bdb8.mp4?infoId=2611219';
const ChatAnimator = () => {
    const { robotUserId = '' } = getCurrentInstance().router?.params || {};
    const chatMode = useRef(getStorageSync('Chat_Mode_STORE') || ChatType.CHIP);
    const chatModeStore = useDetailStore((state) => state.chatMode);
    const [upgradeAlertInfo, setUpgradeAlertInfo] = useState<IScoreQueueItemContent>(null);
    const [upgradeAnimator, setUpgradeAnimator] = useState('');
    const [refreshCurrentAnimatorLevel, setRefreshCurrentAnimatorLevel] = useState(-1);
    const [scoreRefreshAnimator, setScoreRefreshAnimator] = useState('');
    const [scoreRefreshFullAnimator, setScoreRefreshFullAnimator] = useState('');
    const scoreQueue = useRef<IScoreQueueItem[]>([]);
    const hasHide = useRef(false);
    const idSet = useRef(new Set());
    const dialog = useDialog(createSimpleModalProvider({ isCenter: true }));

    const [animationData, setAnimationData] = useState({});
    const [showBorderAnimator, setShowBorderAnimator] = useState(false);

    function handleScoreAnimator() {
        if (scoreQueue.current.length > 0 && hasHide.current === false) {
            setRefreshCurrentAnimatorLevel(Date.now());
        }
    }

    function resetAnimator() {
        scoreQueue.current = scoreQueue.current.slice(1) || [];
        setScoreRefreshFullAnimator('');
        setScoreRefreshAnimator('');
        setUpgradeAnimator('');
        setShowBorderAnimator(false);
        handleScoreAnimator();
        setUpgradeAlertInfo(null);
    }

    const isIdAdded = (id: number) => {
        return idSet.current.has(id);
    };
    const addId = (id: number) => {
        idSet.current.add(id);
    };
    const handleCustomEvent = (data: IScoreQueueItem) => {
        const incrType: number = data.incrType;
        const content: IScoreQueueItemContent = data.content;
        if (incrType === undefined) {
            return;
        }

        if (chatMode.current !== ChatType.IMMERSE && incrType !== -9998) {
            return;
        }
        const idServer = data.idServer;
        if (isIdAdded(idServer)) {
            return;
        }
        addId(idServer);

        scoreQueue.current = scoreQueue.current.concat({ incrType, content });
        if (scoreQueue.current.length === 1) {
            handleScoreAnimator();
        }
    };

    const handleUpgradeAnimator = (data: IScoreQueueItem) => {
        const content = data.content;
        const targetUserId = content?.targetUserId || '';
        if (robotUserId.length > 0 && `${targetUserId}` !== `${robotUserId}`) {
            return;
        }
        if (chatMode.current !== ChatType.IMMERSE) {
            handleCustomEvent({
                incrType: -9998,
                content,
                idServer: new Date().getTime(),
            });
        } else {
            handleCustomEvent({
                incrType: -9999,
                content,
                idServer: new Date().getTime(),
            });
        }
        UpgradeAlertManager.getInstance().deleteIntimacyUpgradeContent(targetUserId);
    };

    useEffect(() => {
        chatMode.current = chatModeStore;
        scoreQueue.current = [];
        resetAnimator();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [chatModeStore]);

    useEffect(() => {
        const content = UpgradeAlertManager.getInstance().getIntimacyUpgradeContent(robotUserId);
        if (content) {
            handleUpgradeAnimator({ content });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [robotUserId, chatMode.current]);

    const handleUpgradeAnimatorStart = (data) => {
        mainPlotListStore.getState().handleIntimacyUpgrade(data);
        mainPlotStore.getState().handleIntimacyUpgrade(data);
    };

    useDidShow(() => {
        hasHide.current = false;
        Taro.eventCenter.on('taro_event_intimacy_increase_4304_score_animator', handleCustomEvent);
        Taro.eventCenter.on('taro_event_upgrade_manager_intimacy_upgrade', handleUpgradeAnimator);
        Taro.eventCenter.on(
            'taro_event_upgrade_manager_intimacy_upgrade_start',
            handleUpgradeAnimatorStart
        );
        handleScoreAnimator();

        const content = UpgradeAlertManager.getInstance().getIntimacyUpgradeContent(robotUserId);
        if (content) {
            handleUpgradeAnimator({ content });
        }
    });

    useDidHide(() => {
        hasHide.current = true;
        Taro.eventCenter.off('taro_event_intimacy_increase_4304_score_animator');
        Taro.eventCenter.off('taro_event_upgrade_manager_intimacy_upgrade');
        Taro.eventCenter.off('taro_event_upgrade_manager_intimacy_upgrade_start');
    });

    useEffect(() => {
        return () => {
            Taro.eventCenter.off('taro_event_intimacy_increase_4304_score_animator');
            Taro.eventCenter.off('taro_event_upgrade_manager_intimacy_upgrade');
            Taro.eventCenter.off('taro_event_upgrade_manager_intimacy_upgrade_start');
            scoreQueue.current = [];
        };
    }, []);

    function handleMainPlotDialogDismiss() {
        resetAnimator();
    }

    const handleUpgradeAnimatorClose = () => {
        const storylineDetailDto = scoreQueue.current?.[0]?.content?.storylineDetailDto;
        if (storylineDetailDto) {
            dialog?.show({
                children: mainPlot(storylineDetailDto, dialog, handleMainPlotDialogDismiss),
            });
            return;
        }
        resetAnimator();
    };
    const handleUpgradeAnimatorFinish = () => {
        setUpgradeAnimator('');
        const item = scoreQueue.current[0];
        if (item === undefined) {
            resetAnimator();
            return;
        }
        setUpgradeAlertInfo(item.content);
    };

    function handleShowBorderAnimator() {
        setTimeout(() => {
            if (chatMode.current !== ChatType.IMMERSE) {
                setShowBorderAnimator(false);
                resetAnimator();
                return;
            }

            const animation = createAnimation({
                timingFunction: 'linear',
            });

            animation.opacity(0.85).step({ duration: 900 });
            animation.opacity(0.5).step({ duration: 700 });
            animation.opacity(0.85).step({ duration: 700 });
            animation.opacity(0).step({ duration: 700 });
            setShowBorderAnimator(true);
            setAnimationData(animation.export());
            setTimeout(() => {
                resetAnimator();
            }, 3100);
        }, 100);
    }

    useEffect(() => {
        const item = scoreQueue.current[0];
        if (item === undefined) {
            return;
        }
        const incrType = item.incrType;
        if (incrType === undefined) {
            return;
        }
        if (incrType === -9999) {
            setUpgradeAnimator(upgradeAnimatorVideo);
        } else if (incrType === -9998) {
            handleUpgradeAnimatorFinish();
        } else {
            let currentUrl = surroundAnimatorVideo;
            setScoreRefreshAnimator(() => {
                if (incrType <= 101) {
                    currentUrl = '';
                    resetAnimator();
                }
                if (incrType >= 103) {
                    handleShowBorderAnimator();
                    setScoreRefreshFullAnimator(favorabilitAnimatorVideo);
                }
                // NOTE: 监听亲密度提升，播放背景视频
                if (incrType >= 101 && chatMode.current === ChatType.IMMERSE) {
                    Taro.eventCenter.trigger('intimacy_increase_greater_2_event');
                }
                return currentUrl;
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [refreshCurrentAnimatorLevel]);

    const handleScoreAnimatorFinish = () => {
        resetAnimator();
    };

    function showAnimator(type: number) {
        if (type === 1) {
            handleCustomEvent({ incrType: 101, idServer: new Date().getTime() });
        } else if (type === 2) {
            handleCustomEvent({ incrType: 102, idServer: new Date().getTime() });
        } else if (type === 3) {
            handleCustomEvent({ incrType: 103, idServer: new Date().getTime() });
        } else if (type === 4) {
            handleCustomEvent({
                incrType: -9999,
                content: {
                    targetUserId: '435633321',
                    curIntimacyLevelLabel: '朋友',
                    nickName: '黎川',
                    timestamp: 1737375717931,
                    curResourceUrl:
                        'https://p2.wave.126.net/mirth/442a0087d06f9fad1053f045e147d24a.png',
                    lastResourceUrl:
                        'https://p6.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/58313483398/be9c/ab5e/a54c/1e75603adab03261db258a143b4bc2a7.png',
                },
                idServer: new Date().getTime(),
            });
        } else if (type === 5) {
            handleCustomEvent({
                incrType: -9998,
                content: {
                    targetUserId: '435633321',
                    curIntimacyLevelLabel: '朋友',
                    nickName: '黎川',
                    timestamp: 1737375717931,
                    curResourceUrl:
                        'https://p2.wave.126.net/mirth/442a0087d06f9fad1053f045e147d24a.png',
                    lastResourceUrl:
                        'https://p6.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/58313483398/be9c/ab5e/a54c/1e75603adab03261db258a143b4bc2a7.png',
                },
                idServer: new Date().getTime(),
            });
        } else if (type === 6) {
            handleCustomEvent({
                incrType: -9999,
                content: {
                    targetUserId: '435633321',
                    curIntimacyLevelLabel: '朋友',
                    nickName: '黎川',
                    timestamp: 1737375717931,
                    curResourceUrl:
                        'https://p2.wave.126.net/mirth/442a0087d06f9fad1053f045e147d24a.png',
                    lastResourceUrl:
                        'https://p6.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/58313483398/be9c/ab5e/a54c/1e75603adab03261db258a143b4bc2a7.png',
                    guideRoleInfo: {
                        content: '恭喜，你们的关系更近一步',
                        avatarUrl:
                            'https://img2.baidu.com/it/u=1195773901,4039087122&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=750',
                    },
                },
                idServer: new Date().getTime(),
            });
        }
    }

    return (
        <ErrorBoundary>
            <View>
                <View
                    style={{
                        position: 'absolute',
                        zIndex: '110',
                        top: '30%',
                        backgroundColor: 'red',
                        pointerEvents: 'all',
                        display: 'none',
                    }}>
                    <View style={{ width: '500px' }} onClick={() => showAnimator(1)}>
                        执行动画（普通）
                    </View>
                    <View style={{ width: '500px' }} onClick={() => showAnimator(2)}>
                        执行动画（中级）
                    </View>
                    <View style={{ width: '500px' }} onClick={() => showAnimator(3)}>
                        执行动画（高级）
                    </View>
                    <View style={{ width: '500px' }} onClick={() => showAnimator(4)}>
                        执行动画（升级-沉浸式）
                    </View>
                    <View style={{ width: '500px' }} onClick={() => showAnimator(5)}>
                        执行动画（升级-闲聊）
                    </View>
                    <View style={{ width: '500px' }} onClick={() => showAnimator(6)}>
                        执行动画（升级-引导）
                    </View>
                </View>
                <View
                    style={{
                        width: '100%',
                        height: '100%',
                        pointerEvents: 'none',
                        overflow: 'none',
                    }}>
                    <View className="scoreRefreshFullAnimatorStyle">
                        <View className="content">
                            <CenterAnimatorContainer
                                videoUrl={scoreRefreshFullAnimator}
                                loop={false}
                                fade
                                topZIndex={false}
                            />
                        </View>
                    </View>

                    <CenterAnimatorContainer
                        videoUrl={scoreRefreshAnimator}
                        loop={false}
                        fade
                        topZIndex={false}
                        top="40%"
                        finish={handleScoreAnimatorFinish}
                    />
                    <CenterAnimatorContainer
                        videoUrl={upgradeAnimator}
                        loop={false}
                        fade
                        topZIndex={false}
                        top="40%"
                        finish={handleUpgradeAnimatorFinish}
                    />
                    {upgradeAlertInfo !== null && (
                        <UpgradeAlert data={upgradeAlertInfo} close={handleUpgradeAnimatorClose} />
                    )}
                    {showBorderAnimator && (
                        <View
                            className="borderAnimator"
                            animation={animationData}
                            style={{ opacity: 0 }}>
                            <Image src={bgBorderAnimator} className="image" />
                            <View />
                        </View>
                    )}
                </View>
            </View>
        </ErrorBoundary>
    );
};
export default React.memo(ChatAnimator);
