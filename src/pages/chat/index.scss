.chat {
    width: 100vw;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;

    .chat-container {
        overflow: hidden;
        z-index: 20;
        flex: 1;
        top: 0;
    }
}

.custom-modal {
    .at-modal__overlay {
        background-color: rgba(0, 0, 0, 0.5);
    }

    .at-modal__container {
        width: 80%;
        max-width: 300px;
        border-radius: 20px;
    }

    .modal-content {
        padding: 30px 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .text-title {
        font-size: 18px;
        text-align: center;
        font-weight: 600;
        color: black;
    }

    .text-content {
        margin-top: 12px;
        font-size: 13px;
        text-align: center;
        color: rgba(0, 0, 0, 0.4);
    }

    .btn-group {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: 30px;
    }

    .cancel,
    .confirm {
        width: 45%;
        height: 44px;
        font-size: 16px;
        border-radius: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
    }

    .cancel {
        background-color: #bfbfbf;
        color: white;
    }

    .confirm {
        background-color: #ff689e;
        color: white;
    }
}

.footer-wraper-style {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 50;
    flex-shrink: 0;
    font-size: 0;
    box-sizing: border-box;
    padding-bottom: calc(env(safe-area-inset-bottom) + 10px);
}

.taro_router > .taro_page:has(> .immerse-enter-anim) {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    transform: none !important;
    transition: opacity 200ms !important;
    z-index: 0;
    opacity: 0;
}

.taro_router > .taro_page.taro_page_show:has(> .immerse-enter-anim) {
    transform: none !important;
    transition: none !important;
    opacity: 1;
}

.taro_router .taro_page.taro_page_show.taro_page_stationed:has(> .immerse-enter-anim) {
    transform: none !important;
    transition: none !important;
    opacity: 1;
}

.immerse-enter-anim {
    opacity: 0;
    transform: scale(1.05);

    &.active {
        opacity: 1;
        transform: scale(1);
        transition: opacity 200ms, transform 200ms;
    }
}
