import Taro, {
    getStorageSync,
    setStorageSync,
    showToast,
    createInnerAudioContext,
    InnerAudioContext,
    getCurrentInstance,
} from '@tarojs/taro';
import { create } from 'zustand';
import { MsgDataType } from '@/types/im';
import { rpc } from '@music/mat-base-h5';
import MessageFlow from '@/hooks/message/MessageFlow';
import { useMessageStore } from '@/hooks/messageStore';
import { isIOS } from '@/utils';
import { getAudioUrl } from '../utils/getAudioUrl';

const SETTINGS_KEY = 'voice-auto-playing';

interface AudioState {
    autoplay?: boolean;
    playingItem?: MsgDataType;
    setAutoPlay: (value: boolean) => void;
    useLoad: () => void;
    useUnload: () => void;
    manualPlay(item: MsgDataType, autoPlay?: boolean): void;
    jsbPlayAudio(): void;
}

// taro 类型没有暴露Instance， 但是 audio 是有 Instance， 所以在这里加上Instance
let audioPlayer: (InnerAudioContext & { Instance?: HTMLAudioElement }) | undefined;

const destroyAudioPlayer = () => {
    if (audioPlayer) {
        audioPlayer.stop();
        // https://github.com/NervJS/taro/pull/14130
        try {
            audioPlayer.destroy();
        } catch (e) {
            console.log(e);
        }
        audioPlayer = undefined;
    }
};

const useAudioStore = create<AudioState>((set, get) => {
    let isManualPlay = false;
    let waitingList: MsgDataType[] = [];
    const setPlayItem = (item: MsgDataType | null) => {
        set({ playingItem: item === null ? undefined : item });
    };
    const ensureAudioPlayer = () => {
        if (!audioPlayer) {
            audioPlayer = createInnerAudioContext();
            // Instance 一定会有的，但是健壮性在这里判断下
            if (audioPlayer?.Instance) {
                audioPlayer.Instance.id = `taro-audio-${Date.now()}`;
                // https://github.com/NervJS/taro/pull/14130
                // destroy调用失败是因为实例没有添加到 body 中， 在这里手动添加下，保证destroy中实例可以销毁
                document.body.appendChild(audioPlayer.Instance);
            }
            audioPlayer.onPlay(() => {
                console.log('播放开始');
            });
            audioPlayer.onEnded(() => {
                console.log('播放结束', isManualPlay);
                setPlayItem(null);
                if (isManualPlay !== true) {
                    // eslint-disable-next-line @typescript-eslint/no-use-before-define
                    autoPlayAudios(null);
                }
            });
            audioPlayer.onError(() => {
                console.log('播放出错');
                setPlayItem(null);
            });
            audioPlayer.onStop(() => {
                console.log('播放停止');
                setPlayItem(null);
            });
        }
    };
    const autoPlayAudios = (data: MsgDataType[] | null) => {
        const { playingItem } = get();
        const list = data ?? waitingList;
        if (playingItem || list.length === 0) {
            console.log('正在播放中或者没有可播放的');
            return;
        }
        const item = list[0];
        waitingList = list.slice(1);
        if (item === undefined || item === null) {
            console.log('未找到语音');
            return;
        }
        const url = getAudioUrl(item);
        if (!url) {
            console.log('未找到语音');
            return;
        }
        setPlayItem(item);
        isManualPlay = false;
        ensureAudioPlayer();
        if (audioPlayer) {
            audioPlayer.src = url;
            if (isIOS) {
                get().jsbPlayAudio();
            } else {
                audioPlayer.play();
            }
        }
    };

    MessageFlow.addGlobalListener(
        {
            onMessage(receive, from) {
                const conversationId = useMessageStore.getState().conversationId;
                const autoplay = get().autoplay;
                if (autoplay !== true) {
                    return;
                }
                if (receive.conversationId !== conversationId || from !== 'receive') {
                    return;
                }
                if (receive.isSelf) {
                    return;
                }
                const audioUrl = getAudioUrl(receive);
                if (!audioUrl) {
                    return;
                }
                if (getCurrentInstance().router?.path.includes('chat/index') !== true) {
                    return;
                }
                const newWaitingList = [...waitingList, receive];
                waitingList = newWaitingList;
                autoPlayAudios(newWaitingList);
            },
        },
        'useAudioStore'
    );

    return {
        autoplay: undefined,
        playingItem: undefined,
        useLoad: () => {
            isManualPlay = false;
            waitingList = [];
            const { autoplay } = get();
            if (autoplay === undefined) {
                let auto = false;
                try {
                    const value = getStorageSync(SETTINGS_KEY);
                    if (value !== '') {
                        auto = Boolean(value);
                    }
                } catch (e) {
                    console.log(e);
                }
                set({
                    autoplay: auto,
                });
            }
        },
        useUnload: () => {
            destroyAudioPlayer();
        },
        setAutoPlay: (value) => {
            const current = get().autoplay;
            if (current === value) {
                return;
            }
            showToast({
                title: value ? '开启成功' : '关闭成功',
                icon: 'none',
                duration: 2000,
            });
            setStorageSync({
                key: SETTINGS_KEY,
                data: value,
            });
            set({
                autoplay: value,
            });
        },
        manualPlay: (item, autoPlay = false) => {
            waitingList = [];
            const { playingItem } = get();
            if (item.messageClientId === playingItem?.messageClientId) {
                setPlayItem(null);
                audioPlayer?.stop();
            } else {
                isManualPlay = true;
                audioPlayer?.stop();
                setPlayItem(item);
                const audioUrl = getAudioUrl(item);
                if (!audioUrl) {
                    return;
                }
                ensureAudioPlayer();
                if (audioPlayer) {
                    audioPlayer.src = audioUrl;
                }

                if (autoPlay) {
                    get().jsbPlayAudio();
                } else {
                    audioPlayer.play();
                }
            }
        },
        jsbPlayAudio() {
            // 调用 JSB 自动播放，绕过浏览器限制12
            rpc.caller('html.audio.play', { id: audioPlayer?.Instance.id })
                .then(() => {
                    console.log('播放成功', audioPlayer?.Instance.id);
                })
                .catch((err) => {
                    console.log(err?.message || '播放失败');
                });
        },
    };
});

export default useAudioStore;
