import { create } from 'zustand';

interface SceneInfo {
    // 场景长文折叠和收起逻辑，沉浸式和闲聊式需要同步收起和展开
    // 使用Map存储不同id的折叠状态
    textFoldCheckedMap: Map<string, boolean>;

    // 获取指定id的折叠状态
    getTextFoldChecked: (id: string) => boolean;

    // 设置指定id的折叠状态
    setTextFoldChecked: (id: string, checked: boolean) => void;
}

const useSceneStore = create<SceneInfo>((set, get) => {
    return {
        textFoldCheckedMap: new Map<string, boolean>(),

        getTextFoldChecked: (id: string) => {
            return get().textFoldCheckedMap.get(id) || false;
        },

        setTextFoldChecked: (id: string, checked: boolean) => {
            const newMap = new Map(get().textFoldCheckedMap);
            newMap.set(id, checked);
            set({ textFoldCheckedMap: newMap });
        },
    };
});

export default useSceneStore;
