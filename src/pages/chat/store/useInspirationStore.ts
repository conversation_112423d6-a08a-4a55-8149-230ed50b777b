import { create } from 'zustand';
import { buyInspiration, fetchInspiration } from '@/service/imApi';
import NIMService from '@/hooks/useNewNIM';
import MessageFlow from '@/hooks/message/MessageFlow';
import { useMessageStore } from '@/hooks/messageStore';
import Taro, { showToast } from '@tarojs/taro';
import { MsgDataType } from '@/types/im';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import useDetailStore from './useDetailStore';
import { InspirationState } from '../type/inspiration';

const useInspirationStore = create<InspirationState>((set, get, store) => {
    let currentUserId = '';
    useDetailStore.subscribe((state, prev) => {
        if (state.userId !== prev.userId || currentUserId !== state.userId) {
            if (currentUserId) {
                set(store.getInitialState());
            }
            currentUserId = state.userId;
        }
    });

    function isAiSendType(msg: MsgDataType) {
        if (msg?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_CUSTOM) {
            return msg?.contentExt?.content?.type === 'aigcCustomTextAudioMsg';
        }
        return (
            msg?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_TEXT ||
            msg?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_IMAGE
        );
    }

    MessageFlow.addGlobalListener(
        {
            onMessage(receive, from) {
                const conversationId = useMessageStore.getState().conversationId;
                if (receive.conversationId !== conversationId || from !== 'receive') {
                    return;
                }
                if (receive.isSelf) {
                    return;
                }
                set({ data: undefined, location: 0, unlockId: undefined });
                if (get().open !== true) {
                    return;
                }
                setTimeout(() => {
                    Taro.eventCenter.trigger('receiveNewMsgHandleInspiration');
                }, 200);
            },
        },
        'useInspirationStore'
    );

    return {
        open: false,
        show: false,
        data: undefined,
        location: 0,
        unlockId: undefined,
        count: 0,
        fetchInspiration: async (loc: number, id?: number) => {
            const msgList = useMessageStore.getState().messageList;
            const robotAccId = useDetailStore.getState().accId || '';
            const findLastMsg = msgList.find((item: any = {}) => {
                return item.senderId === robotAccId && isAiSendType(item);
            });
            const lastMsgId = findLastMsg?.messageServerId;
            if (get().data) {
                if (get().data.lastMsgId === lastMsgId) {
                    const error = get().data?.list?.filter((item: any = {}) => item.error);
                    let newData;
                    if (error?.length > 0) {
                        const newArray = get().data?.list?.map((item: any = {}, index) =>
                            index === get().data?.list?.length - 1
                                ? { ...item, error: false, loading: true }
                                : item
                        );
                        newData = {
                            ...get().data,
                            list: newArray,
                        };
                    } else {
                        newData = {
                            ...get().data,
                            list: [
                                ...get().data.list,
                                { id: loc, list: [], loading: true, error: false },
                            ],
                        };
                    }
                    set({
                        show: true,
                        location: loc,
                        data: newData,
                    });
                } else {
                    set({
                        show: true,
                        location: loc,
                        data: {
                            lastMsgId,
                            list: [{ id: loc, list: [], loading: true, error: false }],
                            useCount: 0,
                            totalCount: 0,
                            pay: false,
                            amount: '',
                        },
                    });
                }
            } else {
                set({
                    show: true,
                    location: loc,
                    data: {
                        lastMsgId,
                        list: [{ id: loc, list: [], loading: true, error: false }],
                        useCount: 0,
                        totalCount: 0,
                        pay: false,
                        amount: '',
                    },
                });
            }
            const userId = useDetailStore.getState().userId || currentUserId;
            try {
                const res = await fetchInspiration({
                    toUserId: userId,
                    messageId: lastMsgId || '',
                    unlockId: id || 0,
                    location: loc || 0,
                    sync: true,
                });
                if (res?.replyList?.length > 0) {
                    const realData = res?.replyList?.map((item: any = {}, index = 0) => ({
                        id: index,
                        list: item || [],
                        loading: false,
                        error: !(item.length > 0),
                    }));
                    set({
                        show: true,
                        data: {
                            lastMsgId,
                            list: realData,
                            useCount: res?.useCount || 0,
                            totalCount: res?.totalCount || 0,
                            pay: res?.pay || false,
                            amount: res?.amount || '',
                        },
                    });
                } else {
                    const newArray = get().data?.list?.map((item: any = {}, index = 0) =>
                        index === get().data.list.length - 1
                            ? { ...item, error: true, loading: false }
                            : item
                    );
                    const newData = {
                        ...get().data,
                        list: newArray,
                    };
                    set({
                        show: true,
                        data: newData,
                    });
                }
            } catch (e) {
                const newArray = get().data?.list?.map((item: any = {}, index) =>
                    index === get().data?.list?.length - 1
                        ? { ...item, error: true, loading: false }
                        : item
                );
                const newData = {
                    ...get().data,
                    list: newArray,
                };
                set({
                    show: true,
                    data: newData,
                });
                coronaWarnMessage('fetchInspiration error', e);
            } finally {
                set({ count: get().count + 1 });
            }
        },
        buyInspiration: async () => {
            try {
                const userId = useDetailStore.getState().userId || currentUserId;
                const res = await buyInspiration({
                    toUserId: userId,
                    messageId: get().data.lastMsgId || '',
                });
                if (res?.unLockId) {
                    set({
                        unlockId: res?.unLockId,
                    });
                    get().setLocation(res?.unLockId);
                    Taro.eventCenter.trigger('powerEvent', {
                        type: 'msgSendSuccess',
                        time: new Date().getTime(),
                    });
                }
            } catch (e) {
                showToast({
                    title: e?.message || '操作失败，请稍后再试',
                    icon: 'none',
                });
                coronaWarnMessage('BuyInspiration error', e);
            }
        },
        setLocation: (unLockId?: number) => {
            const current = get().data?.useCount || 0;
            const total = get().data?.totalCount || 0;
            const isLoading = get().data?.list?.filter((item: any = {}) => item.loading);
            if (isLoading?.length > 0) {
                return;
            }
            if (current < total) {
                get().fetchInspiration(current + 1, unLockId);
            }
        },
        toggleInspiration: () => {
            set((state) => ({
                show: !state.open,
                open: !state.open,
            }));
        },
        toggleInspirationShow: (needShow) => {
            set(() => ({
                show: needShow,
            }));
        },
        closeInspiration: () => {
            if (get().show || get().open) {
                set({
                    show: false,
                    open: false,
                    data: undefined,
                    location: 0,
                    unlockId: undefined,
                });
            }
        },
        sendText: (text: string) => {
            if (!text) {
                return;
            }
            NIMService.sendText(text);
            set({
                show: false,
                data: undefined,
                location: 0,
                unlockId: undefined,
            });
        },
        setGuideList: (list) => {
            const newData = [
                {
                    id: 0,
                    list: list.map((item: any = {}) => item.reply),
                    loading: false,
                    error: false,
                },
            ];
            set({
                data: {
                    lastMsgId: '',
                    list: newData,
                    useCount: 0,
                    totalCount: 0,
                    pay: false,
                    amount: '',
                },
            });
        },
        enterChat: () => {
            set(store.getInitialState());
        },
    };
});

export default useInspirationStore;
