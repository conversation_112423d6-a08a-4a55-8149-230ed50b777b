import Taro, { getStorageSync, setStorageSync } from '@tarojs/taro';
import { create } from 'zustand';
import {
    giftPanelsQuery,
    giftResourcesQuery,
    giftsByPanelQuery,
    giftSendRequest,
    backpackQuery,
    packSendRequest,
} from '@/service/giftApi';

import domAdapter from '@/utils/adapter/domAdapter';
import { useBalanceStore } from '@/store/balanceStore';

import { coronaWarnMessage, rpc } from '@music/mat-base-h5';
import { harborLogger } from '@/utils/appSourceAdapter';

export enum GiftItemType {
    GIFT = 'gift',
    PACK = 'package',
    LOTTERY = 'lottery',
}

export interface BaseResource {
    previewImg?: CommonResource;
    thumbnailImg?: CommonResource;
    commonMaterialFile?: CommonMaterialFile;
    displaySlot?: number;
}

export interface CommonResource {
    url?: string;
    md5?: string;
}

export interface CommonMaterialFile {
    id?: string;
    md5?: string;
    url?: string;
    type?: number;
    terminal?: number;
    bottom?: number;
}

export interface BatchResource {
    level?: number;
    num?: number;
    remark?: string;
    showTime?: number;
    commonMaterialFile?: CommonMaterialFile;
}

export interface GiftMeta {
    id: number;
    giftType?: number;
    giftFlag?: number;
    name?: string;
    description?: string;
    tag?: string;
    worth?: number;
    worthUnit?: number;
    basicResource?: BaseResource;
    giftTags?: GiftTag[];
    giftTagsV2?: GiftTag[];
    attrsMap?: {
        [key: string]: string;
    };
    batchResource?: BatchResource[];
}

/**
 * 礼物标签
 */
export interface GiftTag {
    tagType?: string;
    tagName?: string;
    // backgroundAttr?: BackgroundAttr;
    panelCode?: string;
    tagRgb?: string;
}

export interface GiftItemMeta {
    type: GiftItemType;
    gift?: GiftMeta;
    // extra?: GiftItemExtra;
}

interface GiftPanelMeta {
    panelCode: string;
    panelName: string;
}

export interface PackGiftMeta extends GiftMeta {
    number: number;
    resourceType: number;
}

interface GiftResourceState {
    // 礼物的所有资源
    giftItems: GiftMeta[];
    // 礼物面板的信息
    giftPanel: GiftPanelMeta[];
    // 礼物面板对应的礼物资源
    giftPanelItems: {
        [panelKey: string]: GiftMeta[];
    };
    // 背包礼物内容
    packbackItems: PackGiftMeta[];

    initGiftResources: () => Promise<void>;
    fetchGiftResources: () => Promise<void>;
    fetchGiftPanels: () => Promise<[GiftPanelMeta] | undefined>;
    fetchGiftsByPanel: (panelCode: string) => Promise<void>;
    postGiftSend: (
        panelCode: string,
        targetUserIds: string[],
        giftId: number,
        number: number,
        batchLevel: number,
        sessionPeriodId: string
    ) => Promise<any>;
    postPackSend: (
        targetUserIds: string[],
        giftId: number,
        number: number,
        resourceType: number,
        batchLevel: number,
        sessionPeriodId: string
    ) => Promise<any>;
    fetchBackpack: (robotUserId: number) => Promise<void>;

    updatePackbackItems: (packbackId: string, number: number) => void;
}

const kGiftLastUpdateTimeKey = 'kGiftLastUpdateTimeKey';
const kGiftsInfoStorageKey = 'kGiftsInfoStorageKey';

const giftLogger = (info: string) => {
    harborLogger('gift', info);
};

const useGiftStore = create<GiftResourceState>((set, get) => {
    const initGiftResources = async (): Promise<void> => {
        giftLogger(`🔅 初始化礼物资源`);
        const oldItemsString = getStorageSync(kGiftsInfoStorageKey) || '[]';
        const oldItems = JSON.parse(oldItemsString);
        set({ giftItems: oldItems });

        giftLogger(
            `✅ 初始化礼物资源完成，获取到礼物信息:${oldItems.length}条，${oldItems
                .map((item: any) => {
                    return `${item?.id} : ${item?.name}`;
                })
                .join(',')}`
        );
    };

    const fetchGiftResources = async (): Promise<void> => {
        try {
            const lastUpdateTime = getStorageSync(kGiftLastUpdateTimeKey) || 0;
            giftLogger(`🔅 开始请求礼物资源: lastUpdateTime: ${lastUpdateTime}`);
            const res = await giftResourcesQuery(lastUpdateTime);
            giftLogger(
                `✅ 请求礼物资源完成，获取到礼物信息:${res?.records?.length}条，${res?.records
                    .map((item: any) => {
                        return `${item?.id} : ${item?.name}`;
                    })
                    .join(',')}`
            );
            const oldItemsString = getStorageSync(kGiftsInfoStorageKey) || '[]';
            const oldItems = JSON.parse(oldItemsString);
            const newItems = res.records;
            const oldItemsFilter = oldItems.filter(
                (item: any) => !newItems.find((newItem: any) => newItem.id === item.id)
            );
            const totalItems = [...oldItemsFilter, ...newItems] as any;
            giftLogger(
                `✅ 整合礼物资源完成，获取到礼物信息:${totalItems?.length}条，${totalItems
                    ?.map((item: any) => {
                        return `${item?.id} : ${item?.name}`;
                    })
                    .join(',')}`
            );
            set({ giftItems: totalItems });
            setStorageSync(kGiftsInfoStorageKey, JSON.stringify(totalItems));

            const newLastUpdateTime = res.lastUpdateTime;
            setStorageSync(kGiftLastUpdateTimeKey, newLastUpdateTime);
            giftLogger(`✅ 缓存礼物时间戳:${newLastUpdateTime}`);
        } catch (e) {
            giftLogger(`❌ 获取礼物资源失败 ${e}`);
            coronaWarnMessage('Gift模块', `获取礼物资源失败 ${e}`);
        }
    };

    const fetchGiftPanels = async (): Promise<[GiftPanelMeta] | undefined> => {
        try {
            giftLogger(`🔅 开始请求面板信息`);
            const res = await giftPanelsQuery();
            giftLogger(`✅ 面板信息:${res?.records}`);
            set({ giftPanel: res.records });
            return res.records;
        } catch (e) {
            giftLogger(`❌ 获取礼物面板失败 ${e}`);
            coronaWarnMessage('Gift模块', `获取礼物面板失败 ${e}`);
            return undefined;
        }
    };

    const fetchGiftsByPanel = async (panelCode: string): Promise<void> => {
        try {
            giftLogger(`🔅 开始请求${panelCode}面板对应的礼物`);
            const res = await giftsByPanelQuery(panelCode);
            giftLogger(`✅ ${panelCode}面板礼物信息:${res?.records}条`);
            const giftItems = get().giftItems;
            const giftInPanel = res
                .map((record: any) =>
                    giftItems.find(
                        (item) => item?.id?.toString() === record?.resourceId?.toString()
                    )
                )
                .filter((value: any) => value !== undefined && value !== null);
            const giftPanelItems = get().giftPanelItems || {};
            giftPanelItems[panelCode] = giftInPanel;
            set({ giftPanelItems });
        } catch (e) {
            giftLogger(`❌ 获取礼物面板资源失败 ${e}`);
            coronaWarnMessage('Gift模块', `获取礼物面板资源失败 ${e}`);
        }
    };

    const postGiftSend = async (
        panelCode: string,
        targetUserIds: string[],
        giftId: number,
        number: number,
        batchLevel: number,
        sessionPeriodId: string
    ): Promise<any> => {
        try {
            giftLogger(
                `🔅 开始送礼请求: ${giftId}, ${panelCode}, ${targetUserIds}, ${number}, ${batchLevel}, ${sessionPeriodId}`
            );
            const res = await giftSendRequest(
                panelCode,
                targetUserIds,
                giftId,
                number,
                batchLevel,
                sessionPeriodId
            );
            giftLogger(`✅ 成功送礼: ${res.balance}`);
            if (res.balance) {
                useBalanceStore.setState({ balance: { balance: res.balance } });
            }
            const giftMeta = get().giftItems.find((item) => item.id === giftId);
            if (giftMeta) {
                giftLogger(`✅ 触发送礼动效: ${giftId}`);
                Taro.eventCenter.trigger('addGiftAnimationEffect', giftMeta);
            }
            return res;
        } catch (e) {
            giftLogger(
                `❌ 送礼失败  ${giftId}, ${panelCode}, ${targetUserIds}, ${number}, ${batchLevel}, ${sessionPeriodId}`
            );
            coronaWarnMessage(
                'Gift模块',
                `===送礼失败  ${giftId}, ${panelCode}, ${targetUserIds}, ${number}, ${batchLevel}, ${sessionPeriodId}`
            );
            return Promise.reject(e);
        }
    };

    const postPackSend = async (
        targetUserIds: string[],
        giftId: number,
        number: number,
        resourceType: number,
        batchLevel: number,
        sessionPeriodId: string
    ): Promise<any> => {
        try {
            giftLogger(
                `🔅 开始送背包请求: ${giftId}, ${targetUserIds}, ${number}, ${batchLevel}, ${sessionPeriodId}`
            );
            const res = await packSendRequest(
                targetUserIds,
                giftId,
                number,
                resourceType,
                batchLevel,
                sessionPeriodId
            );
            giftLogger(`✅ 成功送背包`);
            const giftMeta = get().packbackItems.find((item) => item.id === giftId);
            if (giftMeta) {
                giftLogger(`✅ 触发送背包动效: ${giftId}`);
                Taro.eventCenter.trigger('addGiftAnimationEffect', giftMeta);
            }
            return res;
        } catch (e) {
            giftLogger(
                `❌ 送背包失败  ${giftId}, ${targetUserIds}, ${number}, ${batchLevel}, ${sessionPeriodId} ${e}`
            );
            coronaWarnMessage(
                'Gift模块',
                `===送背包失败  ${giftId}, ${targetUserIds}, ${number}, ${batchLevel}, ${sessionPeriodId} ${e}`
            );
            return Promise.reject(e);
        }
    };

    const fetchBackpack = async (robotUserId: number): Promise<void> => {
        try {
            giftLogger(`🔅 开始请求背包资源`);
            const res = await backpackQuery(robotUserId);
            const packGift = res.map((item: any = {}) => {
                const gift = item.giftResourceDTO;
                return { ...gift, number: item.resourceNum, resourceType: item.resourceType };
            });
            giftLogger(`✅ 背包礼物信息:${packGift?.length}条`);
            set({ packbackItems: packGift });
            return packGift;
        } catch (e) {
            giftLogger(`❌ 请求背包资源失败`);
            coronaWarnMessage('Gift模块', `请求背包资源失败`);
            return Promise.reject(e);
        }
    };

    const updatePackbackItems = (packbackId: string, number: number): void => {
        const packbackItems = get().packbackItems;
        const index = packbackItems.findIndex((item) => item.id.toString() === packbackId);
        if (index >= 0) {
            if (number <= 0) {
                packbackItems.splice(index, 1);
            } else {
                packbackItems[index].number = number;
                set({ packbackItems });
            }
        }
    };

    return {
        giftItems: [],
        giftPanel: [],
        giftPanelItems: {},

        packbackItems: [],
        initGiftResources,
        fetchGiftPanels,
        fetchGiftResources,
        fetchGiftsByPanel,
        postGiftSend,
        postPackSend,
        fetchBackpack,
        updatePackbackItems,
    };
});

export default useGiftStore;
