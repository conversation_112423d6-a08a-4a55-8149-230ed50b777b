import { create } from 'zustand';

export enum InputPanelStatus {
    normal,
    send,
    more,
}

interface ChangeStore {
    panelStatus: number;
    chipScrollTop: number;
    immerseScrollTop: number;
    // 用来判断是否滚动到顶，要分页请求消息
    currentScrollToTop: boolean;
    setPanelStatus: (num: number) => void;
    setChipScrollTop: (num: number) => void;
    setImmerseScrollTop: (num: number) => void;
    setCurrentScrollToTop: (top: boolean) => void;
}

const useScrollStore = create<ChangeStore>((set) => ({
    panelStatus: 0,
    chipScrollTop: 0,
    immerseScrollTop: 0,
    currentScrollToTop: false,
    setPanelStatus: (num: number) => {
        set({
            panelStatus: num,
        });
    },
    setChipScrollTop: (num: number) => {
        set({
            chipScrollTop: num,
        });
    },
    setImmerseScrollTop: (num: number) => {
        set({
            immerseScrollTop: num,
        });
    },
    setCurrentScrollToTop: (top: boolean) => {
        set({
            currentScrollToTop: top,
        });
    },
}));

export default useScrollStore;
