import { StoreCreator, StoreLifecycle } from '@/components/storeContext/StoreContext';
import { create } from 'zustand';
import Taro, { getCurrentInstance, showToast } from '@tarojs/taro';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { apiGetChatOutputMode, apiSetChatOutputMode } from '../api';

export interface OutputModeItem {
    chatTextModeCode: string;
    chatTextModeName: string;
    chatTextModeDesc: string;
    chatTextModePrice: number;
    currentMode: boolean;
}

export interface OutputModeState extends StoreLifecycle {
    outputMode: OutputModeItem | undefined; // 当前的输出模式
    allMode: OutputModeItem[]; // 所有的输出模式
    showAllModes: OutputModeItem[]; // 展示所有的输出模式
    loadOutputModeIfNeeded: () => void; // 加载输出模式
    queryChatOutputMode: (uid: string) => void; // 查询输出模式
    selectOutputMode: (item: OutputModeItem) => void; // 选择输出模式
    setOutputMode: () => void; // 设置输出模式
}

export const OutputModeStateStoreCreator: StoreCreator<OutputModeState> = () =>
    create<OutputModeState>((set, get) => ({
        outputMode: undefined,
        allMode: [],
        showAllModes: [],
        onCreated: () => {
            get().loadOutputModeIfNeeded();
        },
        loadOutputModeIfNeeded: async () => {
            const currentAllMode = get().allMode;
            if (currentAllMode.length > 0) {
                // 当前已经有输出模式，重置显示模式并返回
                set({ showAllModes: currentAllMode });
                return;
            }
            // 开始请求输出模式
            const { robotUserId = '' } = getCurrentInstance().router?.params || {};
            get().queryChatOutputMode(robotUserId);
        },
        queryChatOutputMode: async (uid: string) => {
            if (!uid) {
                return;
            }
            try {
                const res = (await apiGetChatOutputMode({
                    targetUserId: uid,
                })) as OutputModeItem[];
                if (!res) {
                    return;
                }
                // 查找当前的模式
                const currentMode = res.find((item: OutputModeItem) => item?.currentMode === true);
                // 设置当前的模式
                set({
                    outputMode: currentMode,
                    allMode: res,
                    showAllModes: res,
                });
            } catch (error) {
                coronaWarnMessage('聊天模式', `获取聊天模式失败 ${JSON.stringify(error)}`);
            }
        },
        selectOutputMode: (item: OutputModeItem) => {
            const allMode = get().allMode;
            const showAllModes = allMode.map((mode: any = {}) => {
                return { ...mode, currentMode: mode.chatTextModeCode === item.chatTextModeCode };
            });
            set({ showAllModes });
        },
        setOutputMode: async () => {
            const { robotUserId = '' } = getCurrentInstance().router?.params || {};
            if (robotUserId) {
                try {
                    const showAllModes = get().showAllModes;
                    const currentMode = showAllModes.find(
                        (item: OutputModeItem) => item?.currentMode === true
                    );
                    if (!currentMode) {
                        return;
                    }
                    await apiSetChatOutputMode({
                        targetUserId: robotUserId,
                        mode: currentMode.chatTextModeCode,
                    });
                    set({
                        outputMode: currentMode,
                        allMode: showAllModes,
                        showAllModes,
                    });
                    showToast({
                        title: '切换成功',
                        icon: 'none',
                        duration: 2000,
                    });
                } catch (error) {
                    showToast({
                        title: error.message || '切换失败，请重试',
                        icon: 'none',
                        duration: 2000,
                    });
                    coronaWarnMessage('聊天模式', `设置聊天模式失败 ${JSON.stringify(error)}`);
                }
            }
        },
    }));
