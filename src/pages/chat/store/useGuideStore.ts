import { create } from 'zustand';
import { getCurrentInstance } from '@tarojs/taro';
import useHomeStore from '@/components/Home/useHomeStore';
import { coronaWarnMessage } from '@music/mat-base-h5';
import * as Api from '../api';

export interface AigcRobotBaseInfoDto {
    userId: string; // 机器人 id
    accId: string; // 云信 id
    age: number; // 年龄
    nickname: string; // 昵称
    avatarUrl: string; // 头像
    intro: string; // 个性签名
}

export interface GuideBaseInfo {
    botBackgroundUrl: string; // 背景图
    baseInfoDto: AigcRobotBaseInfoDto; // 机器人基础信息
}

export interface IceChatInfo {
    content: string; // 招呼语
    voiceUrl: string; // 语音资源
    duration: number; // 语音时长
    order: number; // 排序
}

export enum GuideRoleType {
    guide = 'guide', // 引导
    intimacy = 'intimacy', // 亲密度
    monologue = 'monologue', // 内心 OS
    monologue_popup = 'monologue_popup', // 内心 OS弹窗引导文案
}

export interface GuideRoleInfo {
    avatarUrl: string; // 头像
    content: string; // 文案
    subTitle?: string; // 副标题
    type: GuideRoleType; // 引导类型
    order: number; // 排序
}

export interface GuideMsgMeta {
    content: string; // 文案
    voiceUrl: string; // 语音文案
    duration: number; // 语音时长
}

export interface GuideChatMsgInfo {
    guideMsgMeta: [GuideMsgMeta];
    order: number; // 排序
}

export interface GuideInspirationMeta {
    content: string; // 文案
    intimacy: number; // 回复增长亲密度
    msgId: string; // 消息id
}

export interface GuideInspirationReplyDto {
    inspirationReplyMeta: [GuideInspirationMeta];
    roleInfo?: GuideRoleInfo; // 角色信息
    order: number; // 排序
}

export interface GuideMonologueMeta {
    avatarUrl: string; // 头像
    content: string; // 文案
    title: string; // 标题
    roleInfo?: GuideRoleInfo; // 角色信息
    order: number; // 排序
}

export interface GuidePopupMeta {
    title: string; // 标题
    subTitle: string; // 副标题
    type: GuideRoleType; // 引导类型
    order: number; // 排序
}

export interface GuideEndMeta {
    roleInfo: GuideRoleInfo; // 角色信息
}

export interface GuideAllInfo {
    sort: [number]; // 展示顺序
    baseInfo: GuideBaseInfo; // 基础信息（机器人信息、背景）
    iceChatInfo: [IceChatInfo]; // 破冰信息（招呼语、语音）
    roleInfo: [GuideRoleInfo]; // 引导角色信息
    chatMsgInfo: [GuideChatMsgInfo]; // 对话信息
    inspirationReplyInfo: [GuideInspirationReplyDto]; // 灵感回复
    monologueInfo: [GuideMonologueMeta]; // 内心 OS
    guidePopupInfo: [GuidePopupMeta]; // 引导弹窗信息
    endInfo: GuideEndMeta; // 结束信息
}

export interface GuideState {
    aigcVersion: number; // 当前引导的版本号
    isGuide: boolean; // 是否处于新手引导模式
    isRestart: boolean; // 是否重启小程序
    guideInfo: GuideAllInfo | undefined; // 新手引导信息
    initGuide: () => Promise<void>; // 初始化引导信息
    nextGuide: () => GuidePageInfo | undefined; // 下一步引导
    resetGuide: () => void; // 重置引导信息
}

export const GuidePageType = {
    AI_START: 0, // AI 破冰语
    AI_REPLY: 1, // 机器人回复
    USER_SELECT: 2, // 用户选择灵感回复
    USER_FAVORABILITY: 3, // 用户好感度提升
    AI_SEE_OS: 4, // 内心 OS
    AI_POPUP: 5, // 弹窗
    AI_LOOK_OS: 6, // 查看内心 OS
    USER_WAIT: 7, // 等待好感度消息
    USER_BACK: 8, // 用户返回
} as const;

export type GuidePageType = (typeof GuidePageType)[keyof typeof GuidePageType];

export interface GuidePageInfo {
    pageType: GuidePageType; // 页面类型
    pageorder: number; // 页面索引
    guideInfo: GuideAllInfo; // 引导信息
    showInfo: any; // 展示信息
}

const useGuideStore = create<GuideState>((set, get, store) => {
    let currentIndex = -1;

    const initUser = async () => {
        try {
            const { aigcVersion } = get();
            const res = await Api.apiGetGuideInfo({ aigcVersion });
            currentIndex = -1;
            set({ isGuide: true, isRestart: false, guideInfo: res });
        } catch (e) {
            coronaWarnMessage('UseGuide', `加载新手引导资源信息失败 ${JSON.stringify(e)}`);
        }
        useHomeStore.setState({ canHideLoading: true });
        useHomeStore.getState().hideLoading();
    };

    return {
        aigcVersion: 101,
        isGuide: false,
        isRestart: false,
        guideInfo: undefined,
        initGuide: async () => {
            const { robotAccid = '', robotUserId = '' } = getCurrentInstance().router?.params || {};
            if (!!robotAccid || !!robotUserId) {
                console.warn('ysl 非新手引导模式');
                return;
            }
            initUser();
        },
        nextGuide: () => {
            const { guideInfo } = get();
            if (!guideInfo) {
                console.warn('ysl 没有引导信息哦');
                return undefined;
            }
            currentIndex += 1;
            if (currentIndex >= guideInfo.sort.length) {
                console.warn('ysl 超出位置了');
                return undefined;
            }
            const order = guideInfo.sort[currentIndex];
            console.warn('ysl 下一步引导', order);
            // 先取破冰语
            const iceChatInfo = guideInfo.iceChatInfo.find((info) => info.order === order);
            if (iceChatInfo != undefined) {
                return {
                    pageType: GuidePageType.AI_START,
                    pageorder: order,
                    guideInfo,
                    showInfo: iceChatInfo,
                };
            }
            // 再取AI回复
            const chatMsgInfo = guideInfo.chatMsgInfo.find((info) => info.order === order);
            if (chatMsgInfo != undefined) {
                return {
                    pageType: GuidePageType.AI_REPLY,
                    pageorder: order,
                    guideInfo,
                    showInfo: chatMsgInfo,
                };
            }
            // 再取引导角色【目前有灵感度提升+灵感回复】
            const roleInfo = guideInfo.roleInfo.find((info) => info.order === order);
            if (roleInfo != undefined) {
                switch (roleInfo.type) {
                    case GuideRoleType.intimacy:
                        return {
                            pageType: GuidePageType.USER_FAVORABILITY,
                            pageorder: order,
                            guideInfo,
                            showInfo: roleInfo,
                        };
                    case GuideRoleType.monologue:
                        return {
                            pageType: GuidePageType.AI_SEE_OS,
                            pageorder: order,
                            guideInfo,
                            showInfo: roleInfo,
                        };
                }
            }
            // 最后取灵感回复
            const inspirationReplyInfo = guideInfo.inspirationReplyInfo.find(
                (info) => info.order === order
            );
            if (inspirationReplyInfo != undefined) {
                inspirationReplyInfo.roleInfo = roleInfo;
                return {
                    pageType: GuidePageType.USER_SELECT,
                    pageorder: order,
                    guideInfo,
                    showInfo: inspirationReplyInfo,
                };
            }
            // 取内心 OS
            const monologueInfo = guideInfo.monologueInfo.find((info) => info.order === order);
            if (monologueInfo != undefined) {
                if (roleInfo != undefined) {
                    monologueInfo.roleInfo = roleInfo;
                }
                return {
                    pageType: GuidePageType.AI_LOOK_OS,
                    pageorder: order,
                    guideInfo,
                    showInfo: monologueInfo,
                };
            }
            // 最后取弹窗
            const guidePopupInfo = guideInfo.guidePopupInfo.find((info) => info.order === order);
            if (guidePopupInfo != undefined) {
                return {
                    pageType: GuidePageType.AI_POPUP,
                    pageorder: order,
                    guideInfo,
                    showInfo: guidePopupInfo,
                };
            }
            return undefined;
        },
        resetGuide: () => {
            set({ isGuide: false, isRestart: false, guideInfo: undefined });
        },
    };
});

export default useGuideStore;
