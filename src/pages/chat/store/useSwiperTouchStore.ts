import { create } from 'zustand';

export default create((set) => ({
    touchStart: false,
    touchMove: false,
    direction: 0, // -1: 左滑，1：右滑
    progress: 0,
    setProgress: (value: number) => {
        set({ progress: value });
    },
    setTouchStart: (value: number) => {
        set({ touchStart: value });
    },
    setTouchMove: (value: number) => {
        set({ touchMove: value });
    },
    setDirection: (value: number) => {
        set({ direction: value });
    },
}));
