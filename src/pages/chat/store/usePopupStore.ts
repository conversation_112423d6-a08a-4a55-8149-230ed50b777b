/**
 * @file 聊天页统一的浮层管理store
 * 聊天页所有的浮层都用store统一管理，在最外层onclick点击处理浮层消失逻辑。
 * 不需要在子组件中绑定dom事件处理浮层消失，否则小程序下无法兼容
 *
 * 涉及功能：点赞点踩浮层【未迁移】、体力值浮层
 */
import { create } from 'zustand';

interface PopupInfo {
    showPhysicalPower: boolean;
    setShowPhysicalPower: (showPhysicalPower: boolean) => void;
}

const usePopupStore = create<PopupInfo>((set) => {
    return {
        showPhysicalPower: false,

        setShowPhysicalPower: (showPhysicalPower: boolean) => {
            set({ showPhysicalPower });
        },
    };
});

export default usePopupStore;
