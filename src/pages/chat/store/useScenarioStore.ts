import {
    scenarioRecommendQuery,
    scenarioEndRequest,
    scenarioStartRequest,
    scenarioStatusQuery,
} from '@/service/scenarioApi';
import { create } from 'zustand';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { LastMsgStatus, useMessageStore } from '@/hooks/messageStore';
import MessageFlow from '@/hooks/message/MessageFlow';
import { getCurrentInstance } from '@tarojs/taro';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import useDetailStore from './useDetailStore';

export enum ScenarioRecommendFetchStatus {
    idle,
    loading,
    success,
    error,
}

export const scenarioRecommendNeedFetch = (result: string[] | ScenarioRecommendFetchStatus) => {
    if (
        result === ScenarioRecommendFetchStatus.idle ||
        result === ScenarioRecommendFetchStatus.error
    ) {
        return true;
    }
    return false;
};

interface ScenarioState {
    runing: boolean;
    scenarioRecommendResult: string[] | ScenarioRecommendFetchStatus;
    chatMode?: string;
    messageCountWhenStartScene: number;

    fetchScenarioState: (robotUserId: string, storyId: string) => void;
    fetchScenarioAigc: (robotUserId: string, storyId: string, position: number) => void;
    postStartScenario: (
        robotUserId: string,
        storyId: string,
        settingContent: string,
        aiGenerate: boolean
    ) => Promise<any>;
    postEndScenario: (robotUserId: string, storyId: string) => Promise<any>;
    fetchAllScenarioAigcIfNeed: (robotUserId: string, storyId: string, focus: boolean) => void;
    cleanCurrentScenarioRecommendContent: () => void;
}

const useScenarioStore = create<ScenarioState>((set, get) => {
    MessageFlow.addGlobalListener(
        {
            onMessage(receive, from) {
                const conversationId = useMessageStore.getState().conversationId;
                if (receive.conversationId !== conversationId || from !== 'receive') {
                    return;
                }
                if (receive.isSelf) {
                    return;
                }
                if (
                    receive?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_CUSTOM
                ) {
                    if (receive?.contentExt?.content?.type === 'sceneSettingEndMsg') {
                        const robotUserId = getCurrentInstance().router?.params?.robotUserId;
                        get().fetchScenarioState(robotUserId, '0');
                    }
                }
            },
        },
        'useScenarioStore'
    );

    const fetchScenarioState = async (robotUserId: string, storyId = '0') => {
        if (!robotUserId) {
            return;
        }
        try {
            const res = await scenarioStatusQuery(robotUserId, storyId);
            set({
                runing: res.status === 1,
                chatMode: res.status === 1 ? 'sceneSetting' : undefined,
            });
            if (res.status === 1) {
                setTimeout(() => {
                    const messageCount = useMessageStore.getState().messageList.filter((item) => {
                        return item.isSelf;
                    }).length;
                    set({
                        messageCountWhenStartScene: messageCount,
                    });
                }, 1000);
            }
        } catch (e) {
            coronaWarnMessage('Scenario模块', `获取ai场景状态 ${e}`);
        }
    };

    const fetchScenarioAigc = async (robotUserId: string, storyId: string, position = 1) => {
        if (!robotUserId) {
            return;
        }
        set({
            scenarioRecommendResult: ScenarioRecommendFetchStatus.loading,
        });
        try {
            const res = await scenarioRecommendQuery(robotUserId, storyId, position);
            const robotId = useDetailStore.getState().userId;
            if (robotId === robotUserId && res.length > 0) {
                const contents = res.map((item: any) => {
                    return item.recommendContent;
                });
                set({
                    scenarioRecommendResult: contents,
                });
            } else {
                set({ scenarioRecommendResult: ScenarioRecommendFetchStatus.error });
            }
        } catch (e) {
            set({
                scenarioRecommendResult: ScenarioRecommendFetchStatus.error,
            });
            coronaWarnMessage('Scenario模块', `获取ai场景推荐错误 ${e}`);
        }
    };

    const postStartScenario = async (
        robotUserId: string,
        storyId: string,
        settingContent: string,
        aiGenerate: boolean
    ): Promise<any> => {
        if (!robotUserId) {
            return Promise.reject(new Error('postStartScenario robotUserId 不能为空'));
        }
        try {
            const res = await scenarioStartRequest(
                robotUserId,
                storyId,
                settingContent,
                aiGenerate
            );
            const messageCount = useMessageStore.getState().messageList.filter((item) => {
                return item.isSelf;
            }).length;
            set({
                runing: true,
                chatMode: 'sceneSetting',
                messageCountWhenStartScene: messageCount,
            });
            return res;
        } catch (e) {
            coronaWarnMessage('Scenario模块', `ai场景开始错误 ${e}`);
            return Promise.reject(e);
        }
    };

    const postEndScenario = async (robotUserId: string, storyId: string): Promise<any> => {
        if (!robotUserId) {
            return Promise.reject(new Error('postEndScenario robotUserId 不能为空'));
        }
        if (
            useMessageStore.getState().lastMsgStatus === LastMsgStatus.sent &&
            useMessageStore.getState().showLoading
        ) {
            return Promise.reject(new Error('角色消息输出中，等等再点吧'));
        }
        try {
            const res = await scenarioEndRequest(robotUserId, storyId);
            set({
                runing: false,
                chatMode: undefined,
            });
            return res;
        } catch (e) {
            coronaWarnMessage('Scenario模块', `ai场景结束错误 ${e}`);
            return Promise.reject(e);
        }
    };

    const fetchAllScenarioAigcIfNeed = (robotUserId: string, storyId: string, focus: boolean) => {
        if (!robotUserId) {
            return;
        }
        if (focus) {
            fetchScenarioAigc(robotUserId, storyId);
        } else if (scenarioRecommendNeedFetch(get().scenarioRecommendResult)) {
            fetchScenarioAigc(robotUserId, storyId);
        }
    };

    const cleanCurrentScenarioRecommendContent = () => {
        set({
            scenarioRecommendResult: ScenarioRecommendFetchStatus.idle,
            messageCountWhenStartScene: Math.max(),
            runing: false,
        });
    };

    return {
        fetchScenarioState,
        fetchScenarioAigc,
        postStartScenario,
        postEndScenario,
        fetchAllScenarioAigcIfNeed,
        cleanCurrentScenarioRecommendContent,
        runing: false,
        messageCountWhenStartScene: Math.max(),
        scenarioRecommendResult: ScenarioRecommendFetchStatus.idle,
        chatMode: undefined,
    };
});

export default useScenarioStore;
