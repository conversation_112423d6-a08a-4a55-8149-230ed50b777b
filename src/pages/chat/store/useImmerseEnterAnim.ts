import { StoreCreator, StoreLifecycle } from '@/components/storeContext/StoreContext';
import { create } from 'zustand';
import useDetailStore from '@/pages/chat/store/useDetailStore';
import Taro from '@tarojs/taro';
import useChapterGuideStore from '@/store/useChapterGuideStore';

/**
 * 沉浸模式下的进场动效
 */
export interface ImmerseEnterAnimState extends StoreLifecycle {
    shouldPlay: boolean; // 是否需要播放沉浸模式下的进场动效
    playPage: boolean; // 整个页面动效
    playHeader: boolean; // 头部动效
    playFooter: boolean; // 底部动效
    playBgVideo: boolean; // 视频背景动效
    playRecordList: boolean; // 聊天列表动效
}

export const ImmerseEnterAnimStoreCreator: StoreCreator<ImmerseEnterAnimState> = () => {
    let timer: ReturnType<typeof setTimeout> | undefined;
    return create<ImmerseEnterAnimState>((set) => ({
        shouldPlay: false,
        playPage: false,
        playHeader: false,
        playFooter: false,
        playBgVideo: false,
        playRecordList: false,
        onCreated: () => {
            const shouldPlay = useDetailStore.getState().chatMode === 'immerse';
            set({ shouldPlay });

            if (shouldPlay) {
                // 先播放背景动效，耗时200ms
                Taro.nextTick(() => {
                    set({ playPage: true });
                });
                // 200ms以后一起播放头部、底部,动效耗时400ms
                timer = setTimeout(() => {
                    if (useChapterGuideStore.getState().hasGuide) {
                        Taro.eventCenter.once('chapter_guide_finish', () => {
                            setTimeout(() => {
                                set({ playHeader: true, playFooter: true });
                                // 200ms 播放聊天列表动效
                                setTimeout(() => {
                                    set({ playBgVideo: true });
                                    // 200ms 播放聊天列表动效
                                    setTimeout(() => {
                                        set({ playRecordList: true });
                                    }, 200);
                                }, 200);
                            }, 200);
                        });
                        return;
                    }
                    set({ playHeader: true, playFooter: true });
                    // 200ms以后播放背景视频动效，耗时200ms
                    setTimeout(() => {
                        set({ playBgVideo: true });
                        // 200ms 播放聊天列表动效
                        setTimeout(() => {
                            set({ playRecordList: true });
                        }, 200);
                    }, 200);
                }, 200);
            }
        },
        onCleared: () => {
            if (timer !== undefined) {
                clearTimeout(timer);
                timer = undefined;
            }
        },
    }));
};
