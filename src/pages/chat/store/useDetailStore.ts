import { create } from 'zustand';
import { MsgDataType, RobotInfoType, SystemMessage } from '@/types/im';
import Taro, { getCurrentInstance, getStorageSync, setStorageSync, showToast } from '@tarojs/taro';
import NIMService from '@/hooks/useNewNIM';
import MessageFlow from '@/hooks/message/MessageFlow';
import { ChatType } from '@/pages/chat/type';
import { deleteFavorilityStorage } from '@/utils/storageUtils';
import * as Api from '../api';

interface DetailState {
    /** 聊天模式 chip: 闲聊 immerse: 沉浸 */
    chatMode: string;
    firstPage: boolean;
    /** 当前会话消息列表 */
    messages: MsgDataType[];
    /** 机器人 id */
    userId: string;
    /** 云信 id */
    accId: string;
    /** 机器人信息 */
    robotInfo?: RobotInfoType;
    /** 当前会话periodId */
    periodId: string;
    /** 对号ICON可见性 */
    checkMarkVisible: boolean;
    /** 是否是更多模式 */
    isMoreBounce: boolean;
    /** 是否是键盘弹起 */
    isKeyboardBounce: boolean;
    setFirstPage: (firstPage: boolean) => void;
    /** 初始化聊天页面 */
    initPage: () => Promise<void>;
    /** 关闭聊天页面 */
    endPage: () => void;
    /** 重启会话 */
    refreshSession: () => Promise<void>;
    /** 获取机器人信息 */
    fetchRobotInfo: () => Promise<RobotInfoType | undefined>;
    /** 添加好友 */
    addFriend: () => Promise<void>;
    /** 切换聊天模式 */
    toggleChatMode: () => void;
    /** 添加好友成功通知 */
    onAddFriendNotify: () => void;
    /** 刷新新手引导信息 */
    refreshGuideInfo: (userId: string, accId: string) => void;
    /** 更新会话信息 */
    updateSession: () => Promise<void>;
    /** 设置更多模式 */
    setIsMoreBounce: (isMoreBounce: boolean) => void;
    /** 设置键盘弹起 */
    setIsKeyboardBounce: (isKeyboardBounce: boolean) => void;
}

const initialState = {
    firstPage: true,
    chatMode: getStorageSync('Chat_Mode_STORE') || ChatType.IMMERSE,
    messages: [] as MsgDataType[],
    userId: '',
    accId: '',
    robotInfo: {} as RobotInfoType,
    periodId: '',
    checkMarkVisible: false,
    isMoreBounce: false,
    isKeyboardBounce: false,
};

const useDetailStore = create<DetailState>((set, get) => {
    const friendAdded = async () => {
        get().fetchRobotInfo();
        set({ checkMarkVisible: true });
        setTimeout(() => {
            set({ checkMarkVisible: false });
        }, 1000);
    };

    const initSession = async () => {
        const { userId } = get();
        try {
            const res = await Api.apiInitSession({ userId });
            const periodId = res?.sessionPeriodId || '';
            NIMService.updatePeriodId(periodId);
        } catch (e) {
            showToast({
                title: e?.message || '初始化会话失败',
                icon: 'none',
                duration: 2000,
            });
        }
    };

    MessageFlow.addGlobalListener(
        {
            onSystemMessage: (message: SystemMessage) => {
                const serverExt = message.contentExt?.serverExt;
                if (serverExt?.type === 4306) {
                    if (serverExt?.content?.bindUserId.toString() !== get().userId.toString()) {
                        return;
                    }
                    const { robotInfo } = get();
                    if (robotInfo?.friend) {
                        get().fetchRobotInfo();
                        return;
                    }
                    friendAdded();
                }
            },
            onMessage(receive, from) {
                if (
                    receive?.callbackExtension &&
                    JSON.parse(receive?.callbackExtension).responseCode === 20030
                ) {
                    console.log('收到好友请求');
                }
            },
        },
        'useDetailStore'
    );

    return {
        ...initialState,

        setFirstPage: (firstPage: boolean) => {
            set({ firstPage });
        },
        // 初始化页面：设置 userId 和 accId，拉取机器人和聊天信息
        initPage: () => {
            const { userId: currentUserId } = get();
            const { userId, accId, chatMode, ...reset } = initialState;
            const { robotAccid = '', robotUserId = '' } = getCurrentInstance().router?.params || {};

            if (robotAccid.length > 0) {
                if (currentUserId !== robotUserId) {
                    set({ userId: robotUserId, accId: robotAccid, ...reset });
                }
                get().fetchRobotInfo();
            }

            if (robotUserId.length > 0) {
                initSession();
            } else {
                set({ chatMode: ChatType.IMMERSE });
            }
        },

        endPage: () => {
            const { chatMode, ...reset } = initialState;
            set({ ...reset });
        },

        refreshSession: async () => {
            const { userId, periodId: lastPeriodId } = get();
            if (!userId) {
                return;
            }
            let res;
            try {
                res = await Api.apiRefreshSession({ userId });
                deleteFavorilityStorage(lastPeriodId);
                const periodId = res?.sessionPeriodId || '';
                NIMService.updatePeriodId(periodId);
                Taro.eventCenter.trigger('resetInputPanel');
                showToast({
                    title: '会话已重启',
                    icon: 'none',
                    duration: 2000,
                });
            } catch (e) {
                showToast({
                    title: e?.message || '重启会话失败',
                    icon: 'none',
                    duration: 2000,
                });
            }
        },

        // 获取机器人信息
        fetchRobotInfo: async () => {
            const { userId } = get();
            if (!userId) {
                console.warn('userId 不存在');
                return;
            }
            try {
                const { chatMode } = get();
                const res = await Api.apiGetRobotInfo({ robotUserId: userId });

                if (!chatMode) {
                    set({
                        robotInfo: res as RobotInfoType,
                        chatMode:
                            (res as RobotInfoType)?.chatStyle === 1
                                ? ChatType.CHIP
                                : ChatType.IMMERSE,
                    });
                } else {
                    set({ robotInfo: res as RobotInfoType });
                }
            } catch (e) {
                showToast({
                    title: e?.message || '获取机器人信息失败',
                    icon: 'none',
                    duration: 2000,
                });
            }
        },

        addFriend: async () => {
            const { userId } = get();
            if (!userId) {
                console.warn('userId 不存在');
                return;
            }
            try {
                await Api.apiAddFriend({
                    robotUserId: userId,
                    operationType: 1,
                });
                const info = get().robotInfo;
                if (info) {
                    info.friend = true;
                    set({ robotInfo: { ...info } });
                }
                friendAdded();
            } catch (e) {
                showToast({
                    title: e?.message || '添加好友失败',
                    icon: 'none',
                    duration: 2000,
                });
            }
        },

        toggleChatMode: () => {
            const { chatMode } = get();
            const newMode = chatMode === ChatType.CHIP ? ChatType.IMMERSE : ChatType.CHIP;
            set({ chatMode: newMode });
            setStorageSync('Chat_Mode_STORE', newMode);
            showToast({
                title: newMode === ChatType.CHIP ? '已切换到闲聊模式' : '已切换到沉浸模式',
                icon: 'none',
                duration: 2000,
            });
        },

        onAddFriendNotify: () => {
            if (!get().robotInfo?.friend) {
                set({ checkMarkVisible: true });

                setTimeout(() => set({ checkMarkVisible: false }), 1000); // 1秒后隐藏对号
                get().fetchRobotInfo();
            }
        },

        refreshGuideInfo: (userId: string, accId: string) => {
            set({ userId, accId });
            get().fetchRobotInfo();
        },

        updateSession: async () => {
            const { userId, periodId: lastPeriodId } = get();
            if (!userId) {
                console.warn('userId 不存在');
                return;
            }
            try {
                const res = await Api.apiInitSession({ userId });
                deleteFavorilityStorage(lastPeriodId);
                const periodId = res?.sessionPeriodId || '';
                NIMService.updatePeriodId(periodId);
                // eslint-disable-next-line no-empty
            } catch (e) {}
        },

        setIsMoreBounce: (isMoreBounce: boolean) => {
            set({ isMoreBounce });
        },
        setIsKeyboardBounce: (isKeyboardBounce: boolean) => {
            set({ isKeyboardBounce });
        },
    };
});

export default useDetailStore;
