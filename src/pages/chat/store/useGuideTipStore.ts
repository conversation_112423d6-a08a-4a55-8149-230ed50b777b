import { StoreCreator, StoreLifecycle } from '@/components/storeContext/StoreContext';
import { create } from 'zustand';
import { getCurrentInstance, getStorageSync, setStorageSync, setStorage } from '@tarojs/taro';

import useChapterGuideStore from '@/store/useChapterGuideStore';

import { isToday } from '@/utils/timeHelper';
import useUserInfoStore from '@/store/useUserInfoStore';
import { useMessageStore } from '@/hooks/messageStore';
import useDetailStore from './useDetailStore';
import { ChatType } from '../type';
import useOpStore from '../components/ChatRecordList/MsgItem/op/useOpStore';

export enum ChatGuideMode {
    NONE, // 无引导
    Chapter, // 剧情引导
    Swipe, // 滑动引导
    OutputMode, // 聊天模式引导
    Vote, // 点赞点踩引导
}

export interface GuideTipState extends StoreLifecycle {
    guideMode: ChatGuideMode; // 当前的引导模式
    checkGuideTipStatus: () => void; // 检查私聊引导状态
    closeGuideTip: () => void; // 关闭聊天引导
    hasShowedGuide: boolean; // 是否展示过引导
}

/// 管理私聊引导优先级
/// 主线剧情 > 沉浸模式引导 > 聊天模式引导 > 点赞点踩引导

export const GuideTipStoreCreator: StoreCreator<GuideTipState> = () => {
    const userInfo = useUserInfoStore.getState().userBaseInfo;
    /// 私聊进入次数
    let enterCount = 0;
    /// 取消订阅剧情引导状态
    let unsubscribeFromChapter: () => void | null;
    /// 是否已经显示过初遇剧情引导
    let hasShowedEPICGuide = false;

    const hasSwipeGuide = () => {
        const chatMode = useDetailStore.getState().chatMode;
        if (chatMode === ChatType.IMMERSE) {
            let show = false;
            const { robotUserId = '' } = getCurrentInstance().router?.params || {};
            if (robotUserId === '' || userInfo === undefined || userInfo === null) {
                return false;
            }
            const key = `ShowModeGuide_${userInfo?.userBase?.userId}`;
            const value = getStorageSync(key);
            if (value !== '') {
                show = Boolean(value);
            } else {
                show = true;
            }
            setStorageSync(key, false);
            return show;
        }
        return false;
    };

    const hasLikeOrNotGuide = () => {
        const chatMode = useDetailStore.getState().chatMode;
        if (chatMode !== ChatType.IMMERSE) {
            return false;
        }

        const storageLikeOrNotGuideShowed = getStorageSync('storage.chat.likeOrNotGuideShowed');

        if (storageLikeOrNotGuideShowed === true) {
            return false;
        }

        const initTime = useUserInfoStore.getState().userBaseInfo?.userBase?.initTime;
        if (isToday(initTime)) {
            return false;
        }

        return true;
    };

    // 记录私聊进入次数
    const saveEnterCount = () => {
        // 获取当前日期
        const currentDate = new Date();
        const currentDay = currentDate.toLocaleDateString();
        // 获取当前日期的进入次数
        const enterCountSet = getStorageSync('chat_enter_count_set') || {};
        let count = enterCountSet[currentDay] || 0;
        count += 1;
        // 保存当前日期的进入次数
        if (enterCount < 10) {
            // 避免无意义的记录
            setStorage({ key: 'chat_enter_count_set', data: { [currentDay]: count } });
        }
        return count;
    };

    const hasOutputModeGuide = (count: number) => {
        // 先判断当前是否有引导
        const hasGuide = getStorageSync('chat_output_mode_guide') || false;
        if (hasGuide) {
            /// 已经引导过就不显示了
            return false;
        }
        // 再判断注册时间
        if (userInfo && userInfo.userBase?.initTime) {
            const initTime = userInfo.userBase.initTime;
            const today = new Date();
            const initDate = new Date(initTime);
            if (initDate.getDate() === today.getDate()) {
                /// 已经是今天，不显示引导
                return false;
            }
        } else {
            /// 用户信息获取失败，不显示引导
            return false;
        }
        // 再判断第二次进入才显示引导
        return count >= 2;
    };

    return create<GuideTipState>((set, get) => ({
        guideMode: ChatGuideMode.NONE,
        hasShowedGuide: false,
        onCreated: () => {
            const { robotUserId = '' } = getCurrentInstance().router?.params || {};
            if (!robotUserId) {
                /// 当前为新手引导模式，不执行
                return;
            }
            // 记录进入私聊次数
            enterCount = saveEnterCount();
            // 订阅剧情引导状态
            unsubscribeFromChapter = useChapterGuideStore.subscribe((state, prevState) => {
                if (hasShowedEPICGuide === true || prevState.hasGuide === true) {
                    /// 剧情引导显示时，下次再显示其他引导
                    hasShowedEPICGuide = true;
                    return;
                }
                if (state.hasGuide === false) {
                    /// 剧情引导不显示时，检查其他引导
                    get().checkGuideTipStatus();
                }
            });
        },
        onCleared: () => {
            /// 清除订阅
            if (unsubscribeFromChapter) {
                unsubscribeFromChapter?.();
                unsubscribeFromChapter = null;
            }
        },
        checkGuideTipStatus: () => {
            // 先判断滑动引导
            if (hasSwipeGuide()) {
                set({ guideMode: ChatGuideMode.Swipe, hasShowedGuide: true });
                return;
            }
            // 判断聊天模式
            if (hasOutputModeGuide(enterCount)) {
                set({ guideMode: ChatGuideMode.OutputMode, hasShowedGuide: true });
                return;
            }
            // 判断点赞点踩引导
            if (hasLikeOrNotGuide()) {
                set({ guideMode: ChatGuideMode.Vote, hasShowedGuide: true });
                // eslint-disable-next-line no-useless-return
                return;
            }
            // 判断其他模式...
        },
        closeGuideTip: () => {
            const currentGuideMode = get().guideMode;
            if (currentGuideMode === ChatGuideMode.NONE) {
                return;
            }
            // 先重置状态
            set({ guideMode: ChatGuideMode.NONE });
            // 再保存数据
            switch (currentGuideMode) {
                case ChatGuideMode.Swipe:
                    break;
                case ChatGuideMode.Vote: {
                    const messageList = useMessageStore.getState().messageList;
                    const hasAudioMsg =
                        messageList.filter((item) => {
                            return item.contentExt?.content?.type === 'aigcCustomTextAudioMsg';
                        }).length > 0;
                    if (hasAudioMsg) {
                        // 如果有音频消息，才保存
                        setStorageSync('storage.chat.likeOrNotGuideShowed', true);
                    }
                    useOpStore.getState().closeOp();
                    break;
                }
                case ChatGuideMode.OutputMode:
                    setStorage({ key: 'chat_output_mode_guide', data: true });
                    break;
                case ChatGuideMode.Chapter:
                    break;
                default:
                    break;
            }
        },
    }));
};
