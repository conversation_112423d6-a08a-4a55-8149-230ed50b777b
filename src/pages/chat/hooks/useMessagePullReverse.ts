import { useCallback, useRef, useEffect } from 'react';
import Taro from '@tarojs/taro';
import NIMService from '@/hooks/useNewNIM';
import { useMessageStore } from '@/hooks/messageStore';
import useDetailStore from '../store/useDetailStore';
import useScrollStore from '../store/useScrollStore';
import { ChatType } from '../type';

interface UseMessagePullProps {
    robotAccid: string;
}

const useMessagePull = ({ robotAccid }: UseMessagePullProps) => {
    const refPulling = useRef(false);
    const refMore = useRef(true);
    const setFirstPage = useDetailStore((state) => state.setFirstPage);
    const messageList = useMessageStore((state) => state.messageList);

    const chatMode = useDetailStore((state) => state.chatMode);

    const setCurrentScrollToTop = useScrollStore((state) => state.setCurrentScrollToTop);

    // 处理消息拉取
    const handleMessagePull = useCallback(() => {
        if (refPulling.current === false && refMore.current && messageList.length > 10) {
            refPulling.current = true;

            // 记录当前滚动位置和内容高度
            const chipContainer = document.getElementById('chatContainer_chip'); // ALLOW document
            const immerseContainer = document.getElementById('chatContainer_immerse'); // ALLOW document

            // 根据当前模式选择正确的容器
            const currentContainer =
                chatMode === ChatType.IMMERSE ? immerseContainer : chipContainer;

            // 记录滚动前的位置信息
            let scrollTop = 0;
            let scrollHeight = 0;

            if (currentContainer) {
                const scrollView = currentContainer.querySelector('.chat-record');
                if (scrollView) {
                    scrollTop = scrollView.scrollTop;
                    scrollHeight = scrollView.scrollHeight;
                }
            }

            setFirstPage(false);
            setCurrentScrollToTop(false);
            NIMService.getMessageList(robotAccid ?? '')
                .then((res) => {
                    refMore.current = res;
                    refPulling.current = false;

                    // 在消息加载完成后，恢复滚动位置
                    Taro.nextTick(() => {
                        setTimeout(() => {
                            if (currentContainer) {
                                const scrollView = currentContainer.querySelector('.chat-record');
                                if (scrollView) {
                                    // 计算新增内容的高度
                                    const newScrollHeight = scrollView.scrollHeight;
                                    const heightDiff = newScrollHeight - scrollHeight;

                                    requestAnimationFrame(() => {
                                        // 调整滚动位置，保持相对位置不变
                                        scrollView.scrollTop = scrollTop + heightDiff;
                                    });
                                }
                            }
                        }, 100);
                    });
                })
                .catch((_err) => {
                    refPulling.current = false;
                });
        }
    }, [chatMode, messageList.length, robotAccid, setCurrentScrollToTop, setFirstPage]);

    useEffect(() => {
        Taro.eventCenter.on('scrollToUpper', handleMessagePull);

        return () => {
            Taro.eventCenter.off('scrollToUpper', handleMessagePull);
        };
    }, [handleMessagePull]);

    return {
        handleMessagePull,
        hasMore: refMore,
        isPulling: refPulling,
    };
};

export default useMessagePull;
