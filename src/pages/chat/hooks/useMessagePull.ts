import { useCallback, useRef, useEffect } from 'react';
import Taro from '@tarojs/taro';
import NIMService from '@/hooks/useNewNIM';
import { useMessageStore } from '@/hooks/messageStore';
import useDetailStore from '../store/useDetailStore';

interface UseMessagePullProps {
    robotAccid: string;
}

const useMessagePull = ({ robotAccid }: UseMessagePullProps) => {
    const refPulling = useRef(false);
    const refMore = useRef(true);
    const setFirstPage = useDetailStore((state) => state.setFirstPage);
    const messageList = useMessageStore((state) => state.messageList);

    // 处理消息拉取
    const handleMessagePull = useCallback(() => {
        if (refPulling.current === false && refMore.current && messageList.length > 10) {
            refPulling.current = true;
            setFirstPage(false);
            NIMService.getMessageList(robotAccid ?? '')
                .then((res) => {
                    refMore.current = res;
                    refPulling.current = false;
                })
                .catch((_err) => {
                    refPulling.current = false;
                });
        }
    }, [messageList.length, robotAccid, setFirstPage]);

    useEffect(() => {
        Taro.eventCenter.on('scrollToPullMessage', handleMessagePull);

        return () => {
            Taro.eventCenter.off('scrollToPullMessage', handleMessagePull);
        };
    }, [handleMessagePull]);

    return {
        handleMessagePull,
        hasMore: refMore,
        isPulling: refPulling,
    };
};

export default useMessagePull;
