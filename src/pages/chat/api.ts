import fetch from '@/utils/fetch';

// 获取机器人信息
export const apiGetRobotInfo = (data: { robotUserId: string }) =>
    fetch('/api/mirth/user/aigc/chat/robot/info', {
        method: 'post',
        data,
    });

export const apiGetGuideInfo = (data: { aigcVersion: number }) =>
    fetch('/api/mirth/user/aigc/guide/resource', {
        method: 'post',
        data,
    });

// 添加机器人为好友
export const apiAddFriend = (data: { robotUserId: string; operationType: number }) =>
    fetch('/api/mirth/user/aigc/relation/operation', {
        method: 'post',
        data,
    });

// 会话初始化
export const apiInitSession = (data: { userId: string }) =>
    fetch('/api/sociallive/chatbot/session/init', {
        method: 'post',
        data,
    });

// 会话重启
export const apiRefreshSession = (data: { userId: string }) =>
    fetch('/api/sociallive/chatbot/session/refresh', {
        method: 'post',
        data,
    });

// 消息操作（点赞、点踩等）
export const apiFeebback = (data: {
    sessionRecordId: string;
    feedBackLabelCode: string;
    chatType: string;
}) =>
    fetch('/api/sociallive/chatbot/feedback/label/tag', {
        method: 'post',
        data,
    });

// 占卜数据上报
export const postDivinationUploadTime = (data: {
    userInputTime: string;
    sessionPeriod: string;
    robotUserId: string;
}) => {
    return fetch('/api/sociallive/aigc/divination/upload/time', {
        method: 'post',
        data,
    });
};

// 点踩反馈列表
export const apiFeedbackInfo = () => {
    return fetch('/api/sociallive/chatbot/feedback/info', {
        method: 'post',
    });
};

// 点踩反馈提交
export const apiFeedbackCommit = (data: {
    sessionRecordId: string;
    feedBackTagCodes: string;
    feedBackExt: string;
}) => {
    return fetch('/api/sociallive/chatbot/feedback/label/tag/info', {
        method: 'post',
        data: {
            sessionRecordId: data.sessionRecordId,
            feedBackTagCodes: data.feedBackTagCodes,
            feedBackExt: data.feedBackExt,
            feedBackLabelCode: 'unlike',
        },
    });
};

// 获取AI输出模式
export const apiGetChatOutputMode = (data: { targetUserId: string }) => {
    return fetch('/api/mirth/chat/text/mode', {
        method: 'post',
        data,
    });
};

// 设置AI输出模式
export const apiSetChatOutputMode = (data: { targetUserId: string; mode: string }) => {
    return fetch('/api/mirth/chat/text/mode/set', {
        method: 'post',
        data,
    });
};
