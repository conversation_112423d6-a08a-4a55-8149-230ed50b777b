import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import { MsgDataType } from '@/types/im';

export function getAudioUrl(message?: MsgDataType): string | undefined {
    if (!message) {
        return undefined;
    }
    if (message.messageType !== V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_CUSTOM) {
        return undefined;
    }
    const content = message.contentExt;
    if (content?.content?.type === 'aigcCustomTextAudioMsg') {
        const url: string = content?.content?.content?.audioUrl;
        if (url === undefined || url === '' || url === null) {
            return undefined;
        }
        return url;
    }
    return undefined;
}

export default {};
