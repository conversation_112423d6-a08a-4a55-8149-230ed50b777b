import { isAndroid, isIOS } from '@/utils';
import Env, { compareVersion } from '@music/mobile-env';

const getChromeVersion = () => {
    const ua = navigator.userAgent || '';
    // 常见 UA 里会有 “Chrome/83.0.4103.106” 或 “Chrome/96” 这样的片段
    // 下面的正则抓取 Chrome 后面的数字（主版本号或完整版本号）
    const match = ua.match(/Chrome\/(\d+(\.\d+)?(\.\d+)?(\.\d+)?)/i);
    if (match) {
        // match[1] 里可能是 “83.0.4103.106” 或 “96.0.4664.45” 等
        const ver = match[1];
        return parseInt(ver?.split('.')[0] || '0', 10);
    }
    return null;
};

export const isIosLower = isIOS && +Env.getIosLargeVersion() < 14;

// 在 flex-direction: column-reverse 布局下，不同设备对 scrollTop 和 scrollTo 的处理存在差异。
// 安卓 8 上的行为与其他机型不同，目前的做法是对安卓8及以下低端机型禁止滚动，保证核心功能
export const isAndroidLower = isAndroid && compareVersion(Env.getAndroidVersion(), '9') < 0;

// https://stackoverflow.com/questions/74617048/chrome-wont-render-items-when-flex-direction-row-reverse-column-reverse-wit#:~:text=This%20issue%20is%20related%20to,25%20of%20Chrome
// chrome109之前的webview上column-reverse布局会有bug
// 缓存transform：rotate（180deg）布局后在不同安卓上滚动方向不一致
// 策划不同意牺牲部分版本安卓用户的遮罩层和自动滚动到底体验
// 所以只能用正向布局
export const isChromeLower = isAndroid && getChromeVersion() < 109;

export default {};
