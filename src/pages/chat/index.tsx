import React, { Fragment, useEffect, useRef, useState } from 'react';
import classNames from 'classnames';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import { View } from '@tarojs/components';
import ErrorBoundary from '@/components/ErrorBoundary';
import EventTrackView from '@/components/EventTrack';
import { DialogRootLayer, useDialog } from '@/components/dialog';
import { getSafeAreaHeight } from '@/components/safe-area-view';
import StoreProvider, { useContextStore } from '@/components/storeContext/StoreContext';
import NIMService from '@/hooks/useNewNIM';
import DivinationInvite from '@/pages/chat/components/Divination';
import { ImmerseEnterAnimStoreCreator } from '@/pages/chat/store/useImmerseEnterAnim';
import { isIOS } from '@/utils';
import { useSessionStore } from '@/hooks/sessionStore';
import { useMessageStore } from '@/hooks/messageStore';
import EpicPage from '@/components/EpicPlayer/index';
import ChatBGM from '@/pages/chat/bgm';
import RealNameAuthModal from '@/pages/modals/real-name-modal';
import useChapterGuideStore from '@/store/useChapterGuideStore';
import domAdapter from '@/utils/adapter/domAdapter';
import { needRegisterKeyboardRPC } from '@/utils/appSourceAdapter';
import { useKeyboardWrapResponseStore } from '@music/mat-keyboard-h5/dist/esm/KeyboardWrapper/useKeyboardWrapper';
import KeyboardWrapper from '@music/mat-keyboard-h5';
import PullMessageView from './PullMessageView';
import PullMessageViewReverse from './PullMessageViewReverse';
import ModeSwiper from './Swiper';
import ChatAnimator from './components/ChatAnimator';
import ChatGuide from './components/ChatGuide';
import useOpStore from './components/ChatRecordList/MsgItem/op/useOpStore';
import Footer from './components/Footer';
import GiftEffectPlayer from './components/GiftAnimator/giftEffectPlayer';
import Header from './components/Header';
import ModeGuide from './components/ModeGuide/ModeGuide';
import ScenarioEntranceView from './components/Scenario/Entrance';
import UserGuide from './components/UserGuide';
import './index.scss';
import useAudioStore from './store/useAudioStore';
import useDetailStore from './store/useDetailStore';
import useGiftStore from './store/useGiftResourcesStore';
import { GuideTipStoreCreator } from './store/useGuideTipStore';
import useInspirationStore from './store/useInspirationStore';
import usePopupStore from './store/usePopupStore';
import useScenarioStore from './store/useScenarioStore';
import { ChatType } from './type';
import { isChromeLower } from './utils';

const ChatContent = () => {
    const guideTipStore = useContextStore(GuideTipStoreCreator);

    const loginstatus = useSessionStore((state) => state.loginstatus);
    const messageList = useMessageStore((state) => state.messageList);
    const chatMode = useDetailStore((state) => state.chatMode);
    const setFirstPage = useDetailStore((state) => state.setFirstPage);
    const { robotAccid = '', robotUserId = '' } = getCurrentInstance().router?.params || {};
    const [chipRecordList, setChipRecordList] = useState<HTMLElement>(null);
    const [immerseRecordList, setImmerseRecordList] = useState<HTMLElement>(null);

    useEffect(() => {
        useChapterGuideStore.getState().startGuide();
        useDetailStore.getState().initPage();
        useAudioStore.getState().useLoad();
        useInspirationStore.getState().enterChat();

        return () => {
            useAudioStore.getState().useUnload();
            useDetailStore.getState().endPage();
            useScenarioStore.getState().cleanCurrentScenarioRecommendContent();
            NIMService.leaveSession();
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        // 等云信登陆成功才能发起第一次消息拉取，否则消息拉不到
        if (loginstatus === 1 && messageList.length === 0) {
            if (robotAccid) {
                NIMService.enterSession(robotAccid);
                setFirstPage(true);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [loginstatus, messageList.length]);

    useEffect(() => {
        const fetchGiftData = async () => {
            // 礼物相关
            useGiftStore.getState().initGiftResources();
            useGiftStore.getState().fetchGiftResources();
            const panel = await useGiftStore.getState().fetchGiftPanels();
            if (!panel) {
                return;
            }
            for (const item of panel) {
                useGiftStore.getState().fetchGiftsByPanel(item.panelCode);
            }
        };

        if (useGiftStore.getState().giftItems.length === 0) {
            fetchGiftData();
        }
    }, []);

    useEffect(() => {
        const chipDom = document.getElementById('chatContainer_chip');
        const immerseDom = document.getElementById('chatContainer_immerse');
        setChipRecordList(chipDom);
        setImmerseRecordList(immerseDom);

        // 下面代码后续要删掉，2025/05/12上线之后，监控下版本，然后删掉。
        if (needRegisterKeyboardRPC()) {
            useKeyboardWrapResponseStore.setState({
                registerNeeded: true,
            });
        }
    }, []);

    const childrenRef = useRef(null);

    const safeAreaHeight = getSafeAreaHeight();
    const realNameModal = useDialog(RealNameAuthModal);

    useEffect(() => {
        Taro.eventCenter.on('kShowRealNameModal', () => {
            domAdapter.blurActiveElement();
            realNameModal.show();
        });
        return () => {
            Taro.eventCenter.off('kShowRealNameModal');
        };
    }, [realNameModal]);

    const useImmerseEnterAnimStore = useContextStore(ImmerseEnterAnimStoreCreator);
    const shouldPlay = useImmerseEnterAnimStore((state) => state.shouldPlay);
    const playPage = useImmerseEnterAnimStore((state) => state.playPage);

    return (
        <Fragment>
            <EventTrackView
                params={
                    robotUserId
                        ? {
                              _spm: 'page_ai_im|page_h5_biz',
                              botid: robotUserId,
                              mode: chatMode === ChatType.IMMERSE ? 'full' : 'chat',
                          }
                        : {
                              _spm: 'page_ai_chat_guide|page_h5_biz',
                          }
                }
            />
            {isChromeLower ? (
                <PullMessageViewReverse robotAccid={robotAccid} />
            ) : (
                <PullMessageView robotAccid={robotAccid} />
            )}
            <View
                id="ai_chat_container_view"
                onTouchStart={() => {
                    // 这里是为了点击整个页面任意区域，点赞点踩浮层消失
                    // 不在OpBubble组件内部实现是为了避免使用dom对全局进行监听，兼容小程序场景实现
                    useOpStore.getState().closeOp();
                    usePopupStore.getState().setShowPhysicalPower(false);
                    guideTipStore.getState().closeGuideTip();
                }}
                className={classNames('relative w-full h-full', {
                    'immerse-enter-anim': shouldPlay,
                    active: playPage,
                })}>
                <View className="relative w-full h-full z-1 overflow-hidden bg-[#faf7f8]">
                    <Header />
                    <ModeSwiper />
                    <View className="footer-wraper-style">
                        <KeyboardWrapper
                            action="cover"
                            transformOffset={isIOS ? safeAreaHeight + 10 : 0}
                            containers={[chipRecordList, immerseRecordList]}
                            childrenRef={childrenRef}>
                            <Footer ref={childrenRef} type={chatMode} />
                        </KeyboardWrapper>
                    </View>
                    <ScenarioEntranceView type={chatMode} />
                    <ChatAnimator />
                    <GiftEffectPlayer />
                    <ModeGuide />
                    <UserGuide />
                    <ChatGuide />
                    <ChatBGM />
                </View>
            </View>
            <DivinationInvite />
        </Fragment>
    );
};

const ChatNew = () => {
    return (
        <ErrorBoundary text="系统繁忙，请点击任意区域退出" needback>
            <StoreProvider type="page">
                <ChatContent />
                <DialogRootLayer />
                <EpicPage />
            </StoreProvider>
        </ErrorBoundary>
    );
};

export default ChatNew;
