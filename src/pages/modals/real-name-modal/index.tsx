import React, { useCallback } from 'react';
import { Text, View, Image } from '@tarojs/components';
import { CreateModalProps } from '@/components/dialog';
import RealNameTipIcon from '@/assets/real-name/real-name-tip-image.png';
import { openKey } from '@/utils/rpc';
import './index.scss';
import Taro from '@tarojs/taro';

const RealNameAuthContent = (props: { dismiss: () => void }) => {
    const closeAction = useCallback(() => {
        props.dismiss();
    }, [props]);

    const realNameAuthAction = useCallback(() => {
        closeAction();
        Taro.nextTick(() => {
            openKey('st_nameauth');
        });
    }, [closeAction]);

    return (
        <View className="real-name-auth-modal">
            <View className="modal-content">
                <View className="background-image" />
                <Image className="top-image" src={RealNameTipIcon} mode="aspectFill" />
                <Text className="modal-text">
                    系统检测到您的账号存在风险，部分功能需要通过实名认证才能使用，感谢您的支持与理解!
                </Text>
                <View className="button-container">
                    <Text className="modal-button confirm-button" onClick={realNameAuthAction}>
                        去认证
                    </Text>
                    <Text className="modal-button cancel-button" onClick={closeAction}>
                        已知并关闭
                    </Text>
                </View>
            </View>
        </View>
    );
};

export const RealNameAuthModal: CreateModalProps = {
    type: 'modal',
    isModal: true,
    render(dismiss: () => void) {
        return <RealNameAuthContent dismiss={dismiss} />;
    },
};

export default RealNameAuthModal;
