.real-name-auth-modal {
    height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .modal-content {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        border-radius: 30px;
        background-color: white;
        border-radius: 30px;
        overflow: hidden;
        margin: 30px 30px 0;

        .background-image {
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, #ffbed5, #fff);
            z-index: 0;
        }

        .top-image {
            margin-top: 12px;
            width: 193px;
            height: 193px;
            z-index: 1;
        }

        .modal-text {
            margin: 2px 35px 0 35px;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            color: #000;
            z-index: 2;
        }

        .button-container {
            display: flex;
            flex-direction: column;
            width: 100%;
            padding: 0 20px;
            margin-top: 20px;
            margin-bottom: 30px;
            z-index: 3;

            .modal-button {
                width: 100%;
                height: 50px;
                text-align: center;
                border-radius: 25px;
                font-size: 16px;
                font-weight: 500;
                line-height: 50px;

                &.confirm-button {
                    background-color: #ff689e;
                    color: white;
                    margin-bottom: 15px;
                }

                &.cancel-button {
                    background-color: #fff;
                    color: #ff689e;
                    border: 1px solid #ff689e;
                }
            }
        }
    }
}
