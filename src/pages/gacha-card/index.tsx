import React, { Component } from 'react';
import { View } from '@tarojs/components';
import CardPage from './card';

export default class GachaCard extends Component<any, any> {
    constructor(props: any) {
        super(props);
    }

    render() {
        return (
            <View style={{ position: 'relative', width: '100%', height: '100%' }}>
                <CardPage />
            </View>
        );
    }
}
