import React, { useEffect, useRef, useState } from 'react';
import { View, Image } from '@tarojs/components';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import ICON_BACK from '@/assets/game/gacha/icon_back_white.png';
import gacha_card_info_icon_jump_explore from '@/assets/game/gacha/gacha_card_info_icon_jump_explore.png';
import Header from '@/components/Header';
import { jump2ChapterInfo } from '@/router';
import CardsView from '../gacha/gacha-animator-container/cards';
import { ChapterRewardItem, LotteryRewardDetailImpl } from '../gacha/const';

function CardPage() {
    const itemRef = useRef<ChapterRewardItem>(null);
    const robotUserIdRef = useRef('');
    const [detail, setDetail] = useState(null);

    useEffect(() => {
        const { infoJsonStr = '', robotUserId = '' } = getCurrentInstance().router?.params || {};
        const decodeJsonStr = decodeURIComponent(infoJsonStr);
        const chapterRewardItem: ChapterRewardItem = JSON.parse(decodeJsonStr);
        const detailImpl = new LotteryRewardDetailImpl();

        detailImpl.chapterInfoItem = chapterRewardItem;
        detailImpl.decomposedReward = null;
        detailImpl.firstReceive = false;
        itemRef.current = chapterRewardItem;
        robotUserIdRef.current = robotUserId;
        setDetail(detailImpl);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    function handleFinish() {
        Taro.navigateBack({});
    }
    function jumpToExplore() {
        jump2ChapterInfo({
            chapterId: itemRef.current.chapterId,
            robotUserId: robotUserIdRef.current,
            chapter: null,
            from: null,
        });
    }

    return (
        <View style={{ position: 'relative', width: '100%', height: '100%' }}>
            {itemRef.current && (
                <View className="absolute w-[100%] h-[100%] top-[0] left-[0] pointer-events-none">
                    <CardsView detail={detail} finish={handleFinish} />
                </View>
            )}
            <View className="absolute w-[100%] full-[100%] top-[0px] left-[0px]">
                <Header backConfig={{ src: ICON_BACK }} />
            </View>
            {itemRef.current && (
                <View
                    className="absolute fixed w-full flex justify-center bottom-[34px]"
                    onClick={jumpToExplore}>
                    <Image src={gacha_card_info_icon_jump_explore} className="w-[316px] h-[48px]" />
                </View>
            )}
        </View>
    );
}

export default CardPage;
