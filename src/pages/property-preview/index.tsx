import { CreateModalProps } from '@/components/dialog';
import { Image, Text, View } from '@tarojs/components';
import React, { CSSProperties } from 'react';
import './index.scss';
import ICON_CLOSE from '@/assets/property/icon-close.png';

export interface PropertyInfoProps {
    title: string;
    info: string;
    confirmText?: string;
    confirmStyle?: CSSProperties;
    cancelText?: string;
    cancelStyle?: CSSProperties;
    onCancel?: () => void;
    onConfirm?: () => void;
}

type PropertyInfoModalProps = PropertyInfoProps & {
    dismiss?: () => void;
};

const PropertyInfo = ({ dismiss }: PropertyInfoModalProps) => {
    // console.log('ConfirmModal-->render');

    return (
        <View className="property-info-dialog">
            <View className="dialog-content">
                <View className="content">
                    <View className="title-wrapper">
                        <Text className="title">高维透镜</Text>
                        <Text className="info">已持有888个 </Text>
                    </View>
                    <View className="line" />
                    <View className="content-wrapper">
                        <Image
                            src="https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/32279397497/4a10/0a40/ae12/efc318fadac5b67dc09c11d5a72769dd.png"
                            className="content-img"
                        />
                        <Text className="content-text">高维生物的视觉模块，可用于升维观测</Text>
                    </View>
                </View>
                <Image src={ICON_CLOSE} className="close-img" />
            </View>
        </View>
    );
};

export const PropertyInfoModal: CreateModalProps = {
    type: 'modal',
    isModal: true,
    render(dismiss: () => void, props: PropertyInfoProps) {
        return <PropertyInfo {...props} dismiss={dismiss} />;
    },
};

export const PropertyInfoTest = () => {
    return (
        <View
            style={{
                position: 'absolute',
                top: 0,
                left: 0,
            }}>
            <PropertyInfo />
        </View>
    );
};

export default React.memo(PropertyInfo);
