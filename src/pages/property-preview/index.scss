.property-info-dialog {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    background: rgba(0, 0, 0, 0.7);

    .dialog-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .content {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 295px;
            padding: 10px 24px 36px 20px;
            background-image: url('../../assets/property/icon-bg.png');
            background-size: 100% 100%;

            .title-wrapper {
                display: flex;
                flex-direction: row;
                align-items: center;
                width: 100%;
                height: 37px;

                .title {
                    color: #000;
                    text-align: center;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 21px;
                }

                .info {
                    color: #000;
                    text-align: center;
                    font-size: 11px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                    opacity: 0.6;
                }
            }

            .line {
                width: 100%;
                height: 0.5px;
                opacity: 0.1;
                background: #000;
            }

            .content-wrapper {
                display: flex;
                flex-direction: row;
                align-items: center;
                width: 100%;
                margin-top: 13px;

                .content-img {
                    width: 68px;
                    height: 68px;
                    flex-shrink: 0;
                }

                .content-text {
                    color: #000;
                    font-size: 13px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 150%;
                    opacity: 0.8;
                    padding: 10px;
                    align-items: flex-start;
                    gap: 10px;
                    border-radius: 7px;
                    background: linear-gradient(
                        180deg,
                        rgba(255, 255, 255, 0.6) 0%,
                        rgba(255, 255, 255, 0) 100%
                    );
                    margin-left: 8px;
                }
            }
        }

        .close-img {
            margin-top: 20px;
            width: 26px;
            height: 26px;
        }
    }
}
