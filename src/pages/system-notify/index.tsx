import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import TaroSafeAreaView from '@/components/safe-area-view';
import './index.scss';
import Page from '@/components/lifecycle/Page';
import TopBg from '@/components/Self/components/TopBg';
import { Image, ScrollView, Text, View } from '@tarojs/components';
import NIMService from '@/hooks/useNewNIM';
import { useMessageStore } from '@/hooks/messageStore';
import { MsgDataType } from '@/types/im';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import { officialAccountLastTimestamp, useSessionStore } from '@/hooks/sessionStore';
import OpBubble from '@/pages/chat/components/ChatRecordList/MsgItem/op/OpBubble';
import useOpStore from '@/pages/chat/components/ChatRecordList/MsgItem/op/useOpStore';
import { jumpLink } from '@/router';
import ICON_OFFICIAL_ACCOUNT from '@/assets/common/official_account_logo.png';

export interface ItemProps {
    data: MsgDataType;
}

export interface TextContent {
    template?: string;
    bottom?: string;
    url?: string;
}

function parseTimeText(time: number) {
    const dateObj = new Date(time);

    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');

    return `${year}年${month}月${day}日 ${hours}:${minutes}`;
}

function supportMessage(message: MsgDataType) {
    if (message.contentExt?.content?.type === 'secretaryText') {
        const textContent = message.contentExt?.content?.content as TextContent;
        return textContent.template || textContent.bottom;
    }
    return false;
}

const Item = ({ data }: ItemProps) => {
    const textContent = data.contentExt?.content?.content as TextContent;

    const [opshow, setOpshow] = useState(false);
    const showBubbleItem = useOpStore((state) => state.msgData);
    const show =
        showBubbleItem !== undefined && showBubbleItem?.messageClientId === data?.messageClientId;

    const onClickLink = useCallback(() => {
        jumpLink(textContent.url);
    }, [textContent]);

    const onLongClick = useCallback(() => {
        useOpStore.getState().toggleOp(data);
        setOpshow(true);
    }, [data]);

    return (
        <View className="item-wrapper" id={`notify-item-${data.messageClientId}`}>
            <Text className="time">{parseTimeText(data.createTime)}</Text>
            <View className="item-content">
                <Image className="avatar" src={ICON_OFFICIAL_ACCOUNT} />
                <View className="text-content" onLongPress={onLongClick}>
                    <Text className="text">
                        {textContent?.template}
                        {textContent.bottom && (
                            <Text className="link" onClick={onClickLink}>
                                {textContent.bottom}
                            </Text>
                        )}
                    </Text>
                    {(show || opshow) && (
                        <OpBubble
                            msgData={data}
                            cls={`${show ? 'show' : 'hide'}`}
                            likeEnable={false}
                        />
                    )}
                </View>
            </View>
        </View>
    );
};

const Content = () => {
    const { robotAccid } = useMemo(() => {
        const params = getCurrentInstance().router?.params || {};
        return {
            robotAccid: params.robotAccid as string,
        };
    }, []);

    const loginstatus = useSessionStore((state) => state.loginstatus);
    const messageList = useMessageStore((state) => state.messageList);

    useEffect(() => {
        return () => {
            NIMService.leaveSession();
            useOpStore.getState().closeOp();
            useOpStore.getState().clear();
        };
    }, []);

    useEffect(() => {
        // 等云信登陆成功才能发起第一次消息拉取，否则消息拉不到
        if (loginstatus === 1 && messageList.length === 0) {
            if (robotAccid) {
                // 官方推送消息在过滤时没有sessionPeriodId, 所以开启忽略PeriodId的过滤
                useMessageStore.setState({ filterIgnorePeriodId: true });
                NIMService.updatePeriodId(robotAccid);
                NIMService.enterSession(robotAccid, {
                    beginTime: officialAccountLastTimestamp,
                    limit: 100,
                });
            }
        }
    }, [loginstatus, messageList.length, robotAccid]);

    const systemNotifyList = useMemo(() => {
        return messageList
            .filter((item) => supportMessage(item) && item.createTime)
            .sort((a, b) => a.createTime - b.createTime);
    }, [messageList]);

    const onClickBack = useCallback(() => {
        Taro.navigateBack();
    }, []);

    const [scrollToView, setScrollToView] = useState('');

    const hasScrollBottom = useRef(false);
    useEffect(() => {
        if (!hasScrollBottom.current && systemNotifyList && systemNotifyList.length > 0) {
            // 设置滚动到最后一项
            const lastItemId = `notify-item-${
                systemNotifyList[systemNotifyList.length - 1].messageClientId
            }`;
            setScrollToView(lastItemId);
            hasScrollBottom.current = true;
        }
    }, [systemNotifyList]);

    const onScrollToUpper = useCallback(() => {
        if (hasScrollBottom.current) {
            NIMService.getMessageList(robotAccid ?? '', {
                beginTime: officialAccountLastTimestamp,
                limit: 100,
            });
        }
    }, [robotAccid]);

    return (
        <TaroSafeAreaView className="system-notify-page">
            <TopBg />
            <View className="header-wrapper">
                <View className="back-icon" onClick={onClickBack} />
                <Text className="title">
                    系统通知
                    <Text className="logo">官方</Text>
                </Text>
            </View>
            <ScrollView
                scrollY
                className="content-wrapper"
                scrollIntoView={scrollToView}
                onScrollToUpper={onScrollToUpper}
                onTouchStart={() => {
                    useOpStore.getState().closeOp();
                }}>
                {systemNotifyList.map((item) => {
                    return <Item key={item.messageClientId} data={item} />;
                })}
            </ScrollView>
        </TaroSafeAreaView>
    );
};

const SystemNotify = () => {
    return (
        <Page>
            <Content />
        </Page>
    );
};

export default React.memo(SystemNotify);
