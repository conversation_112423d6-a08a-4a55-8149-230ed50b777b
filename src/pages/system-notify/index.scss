.system-notify-page {
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #fff7f9 -21.67%, #fff7fa 58.02%);
    display: flex;
    flex-direction: column;

    .header-wrapper {
        height: 44px;
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        position: relative;

        .back-icon {
            position: absolute;
            left: 14px;
            top: 7px;
            width: 30px;
            height: 30px;
            background: url('../../assets/system-notify/icon-back.png');
            background-size: 100% 100%;
        }

        .title {
            color: #1f201d;
            text-align: center;
            font-size: 18px;
            font-style: normal;
            font-weight: 600;
            line-height: 18px;
            display: flex;
            flex-direction: row;
            align-items: center;

            .logo {
                color: #fff;
                margin-left: 3px;
                font-size: 10px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                flex-shrink: 0;
                border-radius: 3px;
                background: #ff689e;
                padding: 1px 2px;
            }
        }
    }

    .content-wrapper {
        flex: 1;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        isolation: isolate;
        padding-top: 8px;
        margin-bottom: 30px;

        .item-wrapper {
            padding: 0 16px;
            display: flex;
            flex-direction: column;
            margin-top: 15px;

            .time {
                color: #000;
                text-align: center;
                font-size: 11px;
                font-style: normal;
                font-weight: 400;
                line-height: 16.5px;
                opacity: 0.2;
            }

            .item-content {
                display: flex;
                flex-direction: row;
                margin-top: 15px;

                .avatar {
                    width: 40px;
                    height: 40px;
                    flex-shrink: 0;
                }

                .text-content {
                    margin-left: 5px;
                    display: flex;
                    max-width: 230px;
                    padding: 12px 14px;
                    align-items: center;
                    gap: 10px;
                    flex-shrink: 0;
                    border-radius: 0 12px 12px 12px;
                    background: #fff;
                    position: relative;

                    .text {
                        color: #000;
                        font-size: 13px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 19.5px;

                        .link {
                            color: #ff689e;

                            &:active {
                                opacity: 0.6;
                            }
                        }
                    }
                }
            }
        }

        .popContainer {
            position: absolute;
            height: 51px;
            left: 50%;
            bottom: 100%;
            transform: translate(-50%);
            z-index: 70;
            margin-bottom: 2px;
            transition: opacity 0.5s ease-in-out;

            &.show {
                opacity: 1;
            }

            &.hide {
                opacity: 0;
            }

            .arrowContainer {
                height: 5px;
                display: flex;
                justify-content: center;

                .arrowImg {
                    width: 14px;
                    height: 5px;
                }
            }

            .contentStrContainer {
                border-radius: 10px;
                height: 46px;
                padding: 5px;
                display: flex;
                align-items: center;
                background-color: #474747;
                backdrop-filter: blur(10px);
                // iOS特定样式
                @supports (-webkit-touch-callout: none) {
                    -webkit-backdrop-filter: none;
                }
                flex-direction: row;

                .singleOpContainerUp,
                .singleOpContainerDown,
                .singleOpContainerCopy {
                    width: 50px;
                    height: 38px;
                    flex-direction: column;
                    display: flex;
                    align-items: center;
                }

                .opTextUp,
                .opTextDown,
                .opTextCopy {
                    font-size: 10px;
                    color: #fff;
                    font-weight: 600;
                    margin-top: 2px;
                    font-family: 'PingFang SC', sans-serif;
                }

                .opImage {
                    width: 20px;
                    height: 20px;
                }
            }
        }
    }
}
