.gacha-page-history {
    height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    background-color: black;
}

body.appreciate-page-show,
body.gacha-page-show {
    background-color: rgb(34, 44, 107);

    .taro_router > .taro_page.taro_page_show {
        opacity: 1;
        display: block !important;
    }

    .taro_router .taro_page.taro_page_show.taro_page_stationed {
        opacity: 1;
        display: block !important;
    }

    .taro_router > .taro_page {
        transform: none !important;
        background-color: rgb(34, 44, 107) !important;
        // transition: opacity 100ms !important;
        display: block !important;
        opacity: 1 !important;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
    }
}

body.appreciate-page-show {
    background: center / 100% 100% repeat url('../../../assets/game/gacha/preview_bg.jpg');

    .taro_router > .taro_page {
        // transform: none !important;
        background: center / 100% 100% repeat url('../../../assets/game/gacha/preview_bg.jpg') !important;
    }
}
