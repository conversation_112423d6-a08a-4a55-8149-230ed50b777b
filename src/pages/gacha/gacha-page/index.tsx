import { useContextStore } from '@/components/storeContext/StoreContext';
import { GACHA_BGM } from '@/const/gacha';
import { BGMStore } from '@/pages/gacha/store/useBgmStore';
import { View } from '@tarojs/components';
import {
    createAnimation,
    offAppHide,
    offAppShow,
    onAppHide,
    onAppShow,
    useDidShow,
} from '@tarojs/taro';
import React, { useEffect, useState } from 'react';
import { pageShowDur } from '../const';
import GachaAnimatorContainer from '../gacha-animator-container';
import GachaBgContainer from '../gacha-bg-container';
import GachaFuncContainer, { QuickEntrance } from '../gacha-func-container';
import './index.scss';

const GachaPage = () => {
    const useBgmStore = useContextStore(BGMStore);
    const [animationData, setAnimationData] = useState({});

    useEffect(() => {
        const animation = createAnimation({
            timingFunction: 'linear',
        });
        animation.opacity(1).step({ duration: pageShowDur });
        setAnimationData(animation.export());

        const musicTimer = setTimeout(() => {
            useBgmStore.getState().playMusic(GACHA_BGM);
        }, 20);

        return () => {
            clearTimeout(musicTimer);
            useBgmStore.getState().stopMusic();
        };
    }, []);

    useDidShow(() => {
        if (!useBgmStore.getState().pauseByUser) {
            useBgmStore.getState().resumeMusic();
        }
    });

    useEffect(() => {
        document.body.classList.add('gacha-page-show');

        return () => {
            document.body.classList.remove('gacha-page-show');
        };
    }, []);

    useEffect(() => {
        const handleAppShow = () => {
            if (useBgmStore.getState().pauseByUser) {
                return;
            }
            useBgmStore.getState().resumeMusic();
        };
        onAppShow((options) => {
            // 如果 options.path 包含 gacha/index ，才不播放
            if (options.path.includes('pages/gacha/index')) {
                handleAppShow();
            }
        });
        const handleAppHide = () => {
            if (useBgmStore.getState().isPlaying) {
                useBgmStore.getState().pauseMusic(false);
            }
        };
        onAppHide(handleAppHide);

        return () => {
            offAppShow(handleAppShow);
            offAppHide(handleAppHide);
        };
    }, []);

    return (
        <View
            className="relative w-[100%] h-[100%] bg-white overflow-hidden opacity-[0]"
            id="appreciateList"
            animation={animationData}>
            <View className="absolute w-[100%] h-[100%] top-[0] left-[0] pointer-events-none">
                <GachaBgContainer />
            </View>
            <View className="absolute w-[100%] h-[100%] top-[0] left-[0]">
                <GachaFuncContainer />
            </View>
            <QuickEntrance classNames="absolute bottom-[152.16px] right-[0] z-[0]" />
            <View className="absolute w-[100%] h-[100%] top-[0] left-[0] pointer-events-none">
                <GachaAnimatorContainer />
            </View>
        </View>
    );
};
export default React.memo(GachaPage);
