import React, { useEffect } from 'react';
import { View } from '@tarojs/components';
import EventTrackView from '@/components/EventTrack';
import Taro from '@tarojs/taro';
import { DialogRootLayer, useDialog } from '@/components/dialog';
import { getCurrentInstance } from '@tarojs/runtime';
import RealNameAuthModal from '@/pages/modals/real-name-modal';
import StoreProvider from '@/components/storeContext/StoreContext';
import GachaPage from './gacha-page';
import useDetailStore from '../chat/store/useDetailStore';

// 定义明确类型
// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface GachaProps {}

const Gacha: React.FC<GachaProps> = () => {
    const realNameModal = useDialog(RealNameAuthModal);

    useEffect(() => {
        Taro.eventCenter.on('kShowRealNameModal', () => {
            realNameModal.show();
        });
        return () => {
            Taro.eventCenter.off('kShowRealNameModal');
        };
    }, [realNameModal]);

    useEffect(() => {
        const { jumpPage = '' } = getCurrentInstance().router?.params || {};
        if (jumpPage === 'appreciate') {
            Taro.nextTick(() => {
                // todo 参数
                Taro.redirectTo({
                    url: `/pages/appreciate/index?showGachaDialog=false&robotName=${
                        useDetailStore.getState().robotInfo?.robotBaseInfo?.nickname
                    }`,
                });
            });
        }
    }, []);

    return (
        <StoreProvider type="page">
            <View
                style={{
                    position: 'relative',
                    width: '100%',
                    height: '100%',
                    backgroundColor: '#222c6b',
                }}>
                <EventTrackView params={{ _spm: 'page_ai_lottery|page_h5_biz' }}>
                    <View style={{ width: '10px', height: '10px', display: 'none' }} />
                </EventTrackView>
                <GachaPage />
            </View>
            <DialogRootLayer />
        </StoreProvider>
    );
};

export default Gacha;
