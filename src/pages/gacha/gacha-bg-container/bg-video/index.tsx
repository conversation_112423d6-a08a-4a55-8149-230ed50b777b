import React, { useEffect, useMemo, useRef, useState } from 'react';
import { View, Image, Video } from '@tarojs/components';
import { GACHA_BG_VIDEO } from '@/const/gacha';
import { coronaWarnMessage } from '@music/mat-base-h5';
import gacha_bg_default from '@/assets/game/gacha/gacha_bg_default.png';
import './index.scss';

/**
 * 卡面层~
 * @returns
 */
const GachaBgVideoContainer = () => {
    const targetImage = useRef(gacha_bg_default);
    const [targetVideo, setTargetVideo] = useState('');

    const [bgVideoZIndex, setBgVideoZIndex] = useState(false);
    const bgVideoZIndexPlay = useMemo(() => {
        return bgVideoZIndex;
    }, [bgVideoZIndex]);

    useEffect(() => {
        const timer = setTimeout(() => {
            setTargetVideo(GACHA_BG_VIDEO);
        }, 20);
        return () => {
            clearTimeout(timer);
        };
    });

    return (
        <View className="absolute w-full h-full top-[0] left-[0]">
            <View className="relative w-[100%] h-[100%]">
                <View className="absolute w-[100%] h-[100%]">
                    {targetImage.current && (
                        <Image src={targetImage.current} className="w-[100%] h-[100%]" />
                    )}
                </View>
                <View className="absolute w-[100%] h-[100%]">
                    {targetVideo && (
                        <Video
                            id="gachaBgVideo"
                            className={`${bgVideoZIndexPlay ? 'bg-video-up' : 'bg-video-normal'}`}
                            controls={false}
                            showCenterPlayBtn={false}
                            showPlayBtn={false}
                            src={targetVideo}
                            muted
                            autoplay
                            custom-cache={false}
                            loop
                            objectFit="cover"
                            onError={(e) => {
                                coronaWarnMessage(
                                    'Gacha流程',
                                    `抽卡页动效报错(GachaBgVideoContainer) error:${e}`
                                );
                            }}
                            onPlay={() => {
                                setBgVideoZIndex(true);
                            }}
                            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                            // @ts-ignore
                            sizeMode="cover"
                            style={{ width: '100%', height: '100%' }}
                        />
                    )}
                </View>
            </View>
        </View>
    );
};
export default React.memo(GachaBgVideoContainer);
