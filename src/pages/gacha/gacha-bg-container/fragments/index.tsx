import React, { useEffect, useRef, useState } from 'react';
import { View, Image } from '@tarojs/components';

import GachaFragments1 from '@/assets/game/gacha/gacha_fragments_1.png';
import GachaFragments2 from '@/assets/game/gacha/gacha_fragments_2.png';
import GachaFragments3 from '@/assets/game/gacha/gacha_fragments_3.png';
import Taro, { createAnimation, Animation } from '@tarojs/taro';
import useGachaStore from '../../useGachaStore';
/**
 * 碎片动效层~
 */
const GachaFragmentsContainer = () => {
    const isFetchGachaLoading = useGachaStore((state) => state.isFetchGachaLoading);
    const timer = useRef<NodeJS.Timeout>();
    const [animationData1, setAnimationData1] = useState({});
    const [animationData2, setAnimationData2] = useState({});
    const [animationData3, setAnimationData3] = useState({});

    function loadAnimator() {
        // 创建动画实例
        const animation1 = createAnimation({
            timingFunction: 'ease-in-out',
        });

        const animation2 = createAnimation({
            timingFunction: 'ease-in-out',
        });

        const animation3 = createAnimation({
            timingFunction: 'ease-in-out',
        });

        const loopAnimation = (
            x: number,
            y: number,
            setAnimationData: any,
            animation: Animation
        ) => {
            // 第一个动画阶段：0.4 秒内透明度从 1 变为 0.9，x 和 y 方向各移动
            animation.opacity(0.7).translate(x, y).step({ duration: 1200 });
            // 停留 2 秒
            setTimeout(() => {
                // 第二个动画阶段：0.4 秒内透明度从 0.9 变为 1，x 和 y 恢复
                animation.opacity(1).translate(0, 0).step({ duration: 1200 });
                // 更新动画样式
                setAnimationData(animation.export());
            }, 1500);
            // 更新动画样式
            setAnimationData(animation.export());
        };

        const allLoopAnimation = () => {
            loopAnimation(-6, -8, setAnimationData1, animation1);
            loopAnimation(4, 8, setAnimationData2, animation2);
            loopAnimation(-8, 6, setAnimationData3, animation3);
        };

        timer.current = setInterval(allLoopAnimation, 4000);

        allLoopAnimation();
    }

    useEffect(() => {
        if (timer.current) {
            clearInterval(timer.current);
        }
        if (!isFetchGachaLoading) {
            loadAnimator();
        }
        return () => {
            // 组件卸载时清除定时器
            clearInterval(timer.current);
        };
    }, [isFetchGachaLoading]);

    return (
        <View className="absolute w-[100%] h-[100%] top-[0] right-[0]">
            <Image
                src={GachaFragments1}
                className="w-[89px] h-[48px] absolute top-[398px] left-[62px]"
                animation={animationData1}
            />
            <Image
                src={GachaFragments2}
                className="w-[78px] h-[96px] absolute top-[414px] left-[-8px]"
                animation={animationData2}
            />
            <Image
                src={GachaFragments3}
                className="w-[119px] h-[54px] absolute top-[483px] left-[108px]"
                animation={animationData3}
            />
        </View>
    );
};
export default React.memo(GachaFragmentsContainer);
