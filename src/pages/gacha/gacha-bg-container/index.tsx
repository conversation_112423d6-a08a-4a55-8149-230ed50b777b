import React, { useEffect, useState } from 'react';
import { View } from '@tarojs/components';
import { createAnimation } from '@tarojs/taro';
import GachaBgVideoContainer from './bg-video';
import GachaFragmentsContainer from './fragments';
import GachaExtraInfoContainer from './extraInfo';
import { pageShowDur, pageShowDelayDur } from '../const';

const GachaBgContainer = () => {
    const [animationData, setAnimationData] = useState({});
    useEffect(() => {
        const timer = setTimeout(() => {
            const animation = createAnimation({
                timingFunction: 'linear',
            });
            animation.scaleX(1).scaleY(1).step({ duration: pageShowDur });
            setAnimationData(animation.export());
        }, pageShowDelayDur);
        return () => {
            clearTimeout(timer);
        };
    }, []);

    const [subjectAnimationData, setSubjectAnimationData] = useState({});
    useEffect(() => {
        const timer = setTimeout(() => {
            const animation = createAnimation({
                timingFunction: 'linear',
            });
            animation.translateX(0).step({ duration: pageShowDur });
            setSubjectAnimationData(animation.export());
        }, pageShowDelayDur);
        return () => {
            clearTimeout(timer);
        };
    }, []);

    return (
        <View className="relative  w-[100%] h-[100%] bg-[black]">
            <View
                className="absolute w-[100%] h-[100%] scale-[1.05] top-[0] left-[0]"
                animation={animationData}>
                <View className="absolute w-[100%] h-[100%] top-[0] right-[0]">
                    <GachaBgVideoContainer />
                </View>
                <View className="absolute w-[100%] h-[100%] top-[0] right-[0]">
                    <GachaFragmentsContainer />
                </View>
            </View>
            <View
                className="absolute w-[100%] h-[100%] bottom-[0] left-[0] translate-x-[-223px]"
                animation={subjectAnimationData}>
                <GachaExtraInfoContainer />
            </View>
        </View>
    );
};
export default React.memo(GachaBgContainer);
