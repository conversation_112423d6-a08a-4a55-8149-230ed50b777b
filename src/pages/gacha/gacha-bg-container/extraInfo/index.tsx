import React from 'react';
import { View, Image } from '@tarojs/components';
import gacha_icon_subject from '@/assets/game/gacha/gacha_icon_subject.png';
import icon_gacha_ssr from '@/assets/game/gacha/icon_gacha_ssr.png';
import icon_gacha_ssr_pre from '@/assets/game/gacha/icon_gacha_ssr_pre.png';
import useGachaStore from '../../useGachaStore';
import './index.scss';

const GachaExtraContainer = () => {
    const ssrRemainCount = useGachaStore((state) => state.exchangeInfoResp?.ssrRemainCount || 0);
    return (
        <React.Fragment>
            {ssrRemainCount > 0 ? (
                <View className="gacha-get-ssr-container">
                    <View className="flex flex-row items-end justify-center">
                        <View className="gacha-get-ssr-num">{ssrRemainCount}</View>
                        <Image
                            className="w-[58px] h-[13px] ml-[1px] mb-[5.5px]"
                            src={icon_gacha_ssr_pre}
                        />
                        <Image
                            className="w-[65px] h-[36px] ml-[-5px] mb-[-2px]"
                            src={icon_gacha_ssr}
                        />
                    </View>
                </View>
            ) : null}
            <Image
                src={gacha_icon_subject}
                className="w-[201px] h-[111px] absolute bottom-[168.83px] left-[19px]"
            />
        </React.Fragment>
    );
};
export default React.memo(GachaExtraContainer);
