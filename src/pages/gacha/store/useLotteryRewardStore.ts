import { create } from 'zustand';
import { lotteryRewardPoolApi, lotteryRecord<PERSON>pi } from '../gachaApi';

export interface LotteryRewardAward {
    /** 奖励物品等级 */
    rewardLevel?: 'SSR' | 'SR' | 'R';
    /** 奖励物品名称 */
    rewardName?: string;
    /** 奖励物品图标 */
    rewardIcon?: string;
    /** 奖励物品数量 */
    rewardNumber?: number;
    /** 奖励物品ID(卡面id) */
    rewardId?: string;
    /** 抽奖时间 */
    recordTime?: string;
    /** 奖励描述 */
    rewardDesc?: string;
    /** 是否锁定 */
    isLock?: boolean;
    /** 角色名称 */
    roleName?: string;
    /** 角色ID */
    roleUserId?: string;
    /** 章节ID */
    chapterId?: string;
}

interface LotteryReward {
    R?: LotteryRewardAward[];
    SR?: LotteryRewardAward[];
    SSR?: LotteryRewardAward[];
}

interface LotteryRewardState {
    lotteryRewards: LotteryReward;
    lotteryRecord: any[];
    uuid: string;
    cursor: string;
    loading: boolean;
    more: boolean;
    setLotteryRewardPool: (value: LotteryReward) => void;
    getLotteryRewardPool: () => Promise<void>;
    getLotteryRecord: () => Promise<void>;
    resetStore: () => void;
}

const useLotteryRewardStore = create<LotteryRewardState>((set, get) => ({
    uuid: `${Date.now()}`,
    cursor: '0',
    loading: false,
    more: true,
    lotteryRewards: {},
    lotteryRecord: [],
    setLotteryRewardPool: (value) => set({ lotteryRewards: value }),
    getLotteryRewardPool: async () => {
        const res = await lotteryRewardPoolApi();
        set({ lotteryRewards: res || {} });
    },
    getLotteryRecord: async () => {
        if (get().cursor === '0') {
            set({ uuid: `${Date.now()}` });
        }
        const uuid = get().uuid;
        if (!get().more) return;
        set({ loading: true });
        try {
            const { page, records } = await lotteryRecordApi({
                size: 10,
                cursor: get().cursor,
            });
            if (uuid !== get().uuid) {
                return;
            }
            const { cursor: newCursor, more: newMore } = page || {};
            if (!newMore) {
                set({ more: false });
            }
            let currentLotteryRecord = get().lotteryRecord;
            if (get().cursor === '0') {
                currentLotteryRecord = [];
            }
            if (records?.length) {
                set({ lotteryRecord: currentLotteryRecord.concat(records) });
            }
            set({ cursor: newCursor });
        } catch (error) {
            //
        } finally {
            if (uuid === get().uuid) {
                set({ loading: false });
            }
        }
    },
    resetStore: () => {
        set({
            cursor: '0',
            loading: false,
            more: true,
            lotteryRewards: {},
            lotteryRecord: [],
        });
    },
}));

export default useLotteryRewardStore;
