import { create } from 'zustand';
import { createInnerAudioContext, InnerAudioContext } from '@tarojs/taro';
import { coronaWarnMessage } from '@music/mat-base-h5';

interface AudioState {
    audioContext: InnerAudioContext | null;
    isPlaying: boolean;
    currentSrc: string;
    volume: number;
    pauseByUser: boolean;

    // 方法
    initAudio: () => void;
    playMusic: (src: string) => void;
    pauseMusic: (byUser: boolean) => void;
    resumeMusic: () => void;
    stopMusic: () => void;
    setVolume: (volume: number) => void;
    fadeIn: (duration?: number) => void;
    fadeOut: (duration?: number) => Promise<void>;
}

export const BGMStore = () => {
    return create<AudioState>((set, get) => ({
        audioContext: null,
        isPlaying: false,
        currentSrc: '',
        volume: 0.7,
        pauseByUser: false,

        initAudio: () => {
            const audioContext = createInnerAudioContext();
            audioContext.loop = true;
            audioContext.onPlay(() => {
                set({ isPlaying: true });
            });

            audioContext.onPause(() => {
                set({ isPlaying: false });
            });

            audioContext.onStop(() => {
                set({ isPlaying: false });
            });

            audioContext.onEnded(() => {
                set({ isPlaying: false });
            });

            audioContext.onError(() => {
                set({ isPlaying: false });
            });

            set({ audioContext });
        },

        playMusic: (src) => {
            const { currentSrc } = get();
            let { audioContext } = get();
            if (!audioContext) {
                get().initAudio();
                audioContext = get().audioContext;
            }

            if (currentSrc !== src) {
                audioContext?.stop();
                audioContext.src = src;
                set({ currentSrc: src });
            }

            try {
                audioContext.volume = get().volume;
                audioContext?.play();
            } catch (error) {
                coronaWarnMessage('audioContext.play() error', error);
            }
            set({ isPlaying: true });
        },

        pauseMusic: async (byUser?: boolean) => {
            const { audioContext } = get();
            if (audioContext) {
                audioContext.pause();
                set({ isPlaying: false, pauseByUser: byUser });
            }
        },

        resumeMusic: () => {
            const { audioContext } = get();
            if (audioContext) {
                try {
                    audioContext.volume = get().volume;
                    audioContext.play();
                } catch (error) {
                    coronaWarnMessage('audioContext.play() error', error);
                }
                set({ isPlaying: true, pauseByUser: false });
            }
        },

        stopMusic: async () => {
            const { audioContext } = get();
            if (audioContext) {
                audioContext.stop();
                try {
                    audioContext.destroy();
                } catch (error) {
                    coronaWarnMessage('audioContext.destroy() error', error);
                }
            }
        },

        setVolume: (volume) => {
            set({ volume });
            const { audioContext } = get();
            if (audioContext) {
                audioContext.volume = volume;
            }
        },

        // 音量渐强效果
        fadeIn: (duration = 0.7) => {
            const { audioContext } = get();
            if (!audioContext) return;

            const steps = 20; // 将渐变分为20个步骤
            const interval = (duration * 1000) / steps;
            let currentStep = 0;

            const fadeInterval = setInterval(() => {
                currentStep++;
                const volume = Math.min(currentStep / steps, 1);
                audioContext.volume = volume;
                set({ volume });

                if (currentStep >= steps) {
                    clearInterval(fadeInterval);
                }
            }, interval);
        },

        // 音量渐弱效果 - 返回Promise以便在渐弱后执行操作
        fadeOut: (duration = 0.7) => {
            return new Promise<void>((resolve) => {
                const { audioContext, volume } = get();
                if (!audioContext || volume === 0) {
                    resolve();
                    return;
                }

                const initialVolume = volume;
                const steps = 20; // 将渐变分为20个步骤
                const interval = (duration * 1000) / steps;
                let currentStep = 0;

                const fadeInterval = setInterval(() => {
                    currentStep++;
                    const progress = currentStep / steps;
                    const newVolume = initialVolume * (1 - progress);
                    audioContext.volume = newVolume;
                    set({ volume: newVolume });

                    if (currentStep >= steps) {
                        clearInterval(fadeInterval);
                        audioContext.volume = 0;
                        set({ volume: 0 });
                        resolve();
                    }
                }, interval);
            });
        },
    }));
};

export default { BGMStore };
