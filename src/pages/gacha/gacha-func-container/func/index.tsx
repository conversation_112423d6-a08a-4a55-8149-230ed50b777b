import React, { useEffect, useRef, useState } from 'react';
import { Image, View } from '@tarojs/components';
import { createAnimation } from '@tarojs/taro';
import ICON_GACHA_ONE from '@/assets/game/gacha/gacha_icon_one.png';
import ICON_GACHA_ONE_FREE from '@/assets/game/gacha/gacha_icon_one_free.png';
import ICON_GACHA_TEN from '@/assets/game/gacha/gacha_icon_ten.png';
import ICON_GACHA_MUST_GET_SSR from '@/assets/game/gacha/icon_gacha_must_get_ssr.png';
import gacha_icon_cost_ticket from '@/assets/game/gacha/gacha_icon_cost_ticket.png';
import { debounce } from '@/utils';
import { pageShowDelayDur, pageShowDur } from '../../const';
import useGachaStore from '../../useGachaStore';

const GachaFuncView = () => {
    const exchangeInfoResp = useGachaStore((state) => state.exchangeInfoResp);
    const setGachaClick = useGachaStore((state) => state.setGachaClick);
    let oneCost = 0;
    let tenCost = 0;
    exchangeInfoResp?.exchangeInfos?.forEach((item) => {
        if (item.rounds === 1) {
            oneCost = item.amount;
        }
        if (item.rounds === 10) {
            tenCost = item.amount;
        }
    });

    // 请求完调用 setRspData()
    function handleOneClick() {
        if (exchangeInfoResp?.lotteryCardPoint) {
            useGachaStore.getState().setGachaFreeClick(1);
        } else {
            setGachaClick(1);
        }
    }

    function handleTenClick() {
        setGachaClick(10);
    }

    const [animationData, setAnimationData] = useState({});
    const hasShowAnimator = useRef(false);
    useEffect(() => {
        if (hasShowAnimator.current) {
            return;
        }
        hasShowAnimator.current = true;

        const timer = setTimeout(() => {
            const animation = createAnimation({
                timingFunction: 'linear',
            });
            animation.scaleX(1).scaleY(1).step({ duration: pageShowDur });
            setAnimationData(animation.export());
        }, pageShowDelayDur);
        // eslint-disable-next-line consistent-return
        return () => {
            clearTimeout(timer);
        };
    }, []);

    return (
        <View className="h-full flex flex-col pb-[81.83px] justify-end">
            <View
                className="flex justify-between w-full pl-[19px] pr-[19px] scale-[1.05]"
                animation={animationData}>
                <View className="flex-row">
                    <View className="h-[25px]">
                        {exchangeInfoResp && (
                            <View className="flex ">
                                <Image src={gacha_icon_cost_ticket} className="w-[25px] h-[25px]" />
                                <View className="flex h-[25px] items-center justify-start ml-[3px] text-[10px] text-[white]">
                                    <View
                                        className={`text-[10px] text-[white] ${
                                            exchangeInfoResp?.lotteryCardPoint ? 'line-through' : ''
                                        }`}>
                                        {oneCost}
                                    </View>
                                </View>
                            </View>
                        )}
                    </View>
                    <View className="relative">
                        <Image
                            src={
                                exchangeInfoResp?.lotteryCardPoint
                                    ? ICON_GACHA_ONE_FREE
                                    : ICON_GACHA_ONE
                            }
                            className="w-[159px] h-[41px] mt-[2px] max-[320px]:w-[135px] max-[320px]:h-[35px]"
                            onClick={debounce(() => {
                                handleOneClick();
                            })}
                        />
                        <View>
                            {exchangeInfoResp?.lotteryCardPoint && (
                                <View className="absolute right-[5px] top-[2px] w-[6px] h-[6px] bg-[#FF561E] rounded-full" />
                            )}
                        </View>
                    </View>
                </View>
                <View className="flex-row">
                    <View className="h-[25px]">
                        {exchangeInfoResp && (
                            <View className="flex">
                                <Image src={gacha_icon_cost_ticket} className="w-[25px] h-[25px]" />
                                <View className="flex h-[25px] items-center justify-start ml-[3px] text-[10px] text-[white]">
                                    <View className="text-[10px] text-[white]">{tenCost}</View>
                                </View>
                            </View>
                        )}
                    </View>
                    <View className="relative">
                        <Image
                            src={ICON_GACHA_TEN}
                            className="w-[159px] h-[41px] mt-[2px] max-[320px]:w-[135px] max-[320px]:h-[35px]"
                            onClick={debounce(() => {
                                handleTenClick();
                            })}
                        />
                        <Image
                            src={ICON_GACHA_MUST_GET_SSR}
                            className="w-[40px] h-[40px] absolute mt-[-12px] mr-[-8px] right-0"
                        />
                    </View>
                </View>
            </View>
        </View>
    );
};

export default React.memo(GachaFuncView);
