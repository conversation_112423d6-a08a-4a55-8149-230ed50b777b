import React, { useEffect, useRef, useState } from 'react';
import { Image, View } from '@tarojs/components';
import { useDidShow } from '@tarojs/taro';
import gacha_icon_account_ticket from '@/assets/game/gacha/gacha_icon_account_ticket.png';
import ICON_ADD1_PINK from '@/assets/game/gacha/gacha_icon_add.png';
import classNames from 'classnames';
import { debounce } from '@/utils';
import { TabType } from '@/pages/market/store/store';
import { jump2Market } from '@/router';
import useGachaStore from '../../useGachaStore';

import './index.scss';

const TicketsView = () => {
    const exchangeInfoResp = useGachaStore((state) => state.exchangeInfoResp);
    const [currentTickets, setCurrentTickets] = useState(0);
    const inEnRechargePage = useRef(false);
    function handleRecoverClick() {
        if (inEnRechargePage.current === true) {
            return;
        }
        inEnRechargePage.current = true;
        jump2Market({ type: TabType.lottery });
    }

    useEffect(() => {
        setCurrentTickets(exchangeInfoResp?.ticketAmount || 0);
    }, [exchangeInfoResp]);

    useDidShow(() => {
        inEnRechargePage.current = false;
    });

    return (
        <View className="ticketHistory">
            <View
                className={classNames('ticketViewInfo')}
                onClick={debounce(() => {
                    handleRecoverClick();
                })}>
                <View className="at-row">
                    <View className="at-col at-col-1 at-col--auto">
                        <View className="ticketImageContainer">
                            <Image src={gacha_icon_account_ticket} className="ticketImage" />
                        </View>
                    </View>
                    <View className="at-col">
                        <View className="ticketInfoContainer">
                            <View className="ticketNum">{currentTickets}</View>
                        </View>
                    </View>
                    <View className="at-col at-col-1 at-col--auto">
                        <View className="ticketImage2Container">
                            <Image src={ICON_ADD1_PINK} className="ticketImage" />
                        </View>
                    </View>
                </View>
            </View>
        </View>
    );
};

export default React.memo(TicketsView);
