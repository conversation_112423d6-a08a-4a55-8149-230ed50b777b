@import '~taro-ui/dist/style/components/nav-bar.scss';
@import '~taro-ui/dist/style/components/icon.scss';

.ticketHistory {
    .ticketViewInfo {
        border-radius: 45px;
        border: 0.5px solid rgba(255, 255, 255, 0.1);
        background: rgba(0, 0, 0, 0.2);
        width: 92px;
        height: 24px;
        position: relative;
        margin-left: 6px;

        .ticketImageContainer {
            height: 24px;
            display: flex;
            margin-left: 4px;
            align-items: center;

            .ticketImage {
                width: 18px;
                height: 18px;
            }
        }

        .ticketImage2Container {
            height: 24px;
            display: flex;
            margin-right: 4px;
            align-items: center;

            .ticketImage {
                margin-left: 4px;
                width: 16px;
                height: 16px;
            }
        }

        .ticketInfoContainer {
            display: flex;
            height: 24px;
            margin-left: 3px;
            align-items: center;
            justify-content: flex-start;

            .ticketNum {
                width: 39px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                font-size: 12px;
                color: #fff;
                margin-left: 3px;
            }
        }
    }
}
