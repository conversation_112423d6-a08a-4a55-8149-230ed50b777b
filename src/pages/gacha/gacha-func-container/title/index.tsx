import gacha_bgm_off from '@/assets/game/gacha/gacha_bgm_off.png';
import gacha_bgm_on from '@/assets/game/gacha/gacha_bgm_on.png';
import gacha_explain from '@/assets/game/gacha/gacha_explain.png';
import ICON_BACK from '@/assets/game/gacha/icon_back_white.png';
import { GoldView, TicketsView } from '@/components';
import { useContextStore } from '@/components/storeContext/StoreContext';
import { BGMStore } from '@/pages/gacha/store/useBgmStore';
import { jump2Explain } from '@/router';
import { debounce } from '@/utils';
import { Image, View } from '@tarojs/components';
import Taro, { createAnimation } from '@tarojs/taro';
import React, { useEffect, useRef, useState } from 'react';
import { pageShowDelayDur, pageShowDur } from '../../const';
import './index.scss';

const GachaTitleView = () => {
    const useBgmStore = useContextStore(BGMStore);
    function handleBackClick() {
        Taro.navigateBack({});
    }

    function handleExplainClick() {
        jump2Explain();
    }

    function handleBgmClick() {
        const { isPlaying } = useBgmStore.getState();
        if (isPlaying) {
            useBgmStore.getState().pauseMusic(true);
        } else {
            useBgmStore.getState().resumeMusic();
        }
    }

    const [animationData, setAnimationData] = useState({});

    const isPlaying = useBgmStore((state) => state.isPlaying);
    const [bgmAnimationData, setBgmAnimationData] = useState({});
    const rotationRef = useRef(0);
    const intervalRef = useRef(null);

    useEffect(() => {
        const timer = setTimeout(() => {
            const animation = createAnimation({
                timingFunction: 'linear',
            });
            animation.translateX(0).step({ duration: pageShowDur });
            setAnimationData(animation.export());
        }, pageShowDelayDur);
        // eslint-disable-next-line consistent-return
        return () => {
            clearTimeout(timer);
        };
    }, []);

    const [accountAnimationData, setAccountAnimationData] = useState({});
    useEffect(() => {
        const timer = setTimeout(() => {
            const animation = createAnimation({
                timingFunction: 'linear',
            });
            animation.translateX(0).scaleX(1).scaleY(1).step({ duration: pageShowDur });
            setAccountAnimationData(animation.export());
        }, pageShowDelayDur);
        // eslint-disable-next-line consistent-return
        return () => {
            clearTimeout(timer);
        };
    }, []);

    // 音乐图标旋转动画
    useEffect(() => {
        // 播放状态发生变化时
        if (isPlaying) {
            // 开始旋转
            const rotateIcon = () => {
                rotationRef.current += 2; // 每次增加的角度
                if (rotationRef.current >= 360) {
                    rotationRef.current = 0;
                }

                const animation = createAnimation({
                    duration: 0, // 立即更新角度
                    transformOrigin: '50% 50%',
                });
                animation.rotate(rotationRef.current).step();
                setBgmAnimationData(animation.export());
            };

            // 清除旧定时器
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }

            // 创建新定时器，频率高一些使旋转更平滑
            intervalRef.current = setInterval(rotateIcon, 50);
        } else if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }

        // 组件卸载时清理
        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
        };
    }, [isPlaying]);

    return (
        <View className="gacha-titleContainer">
            <View className="at-row">
                <View className="at-col at-col-1 at-col--auto">
                    <View className="flex h-[44px] mx-[20px] items-center">
                        <View
                            className="w-[26px] h-[26px] translate-x-[-46px]"
                            animation={animationData}>
                            <Image
                                src={ICON_BACK}
                                className="w-[26px] h-[26px]"
                                onClick={debounce(() => {
                                    handleBackClick();
                                })}
                            />
                        </View>
                        <View
                            className="w-[26px] h-[26px] flex items-center justify-center"
                            onClick={debounce(() => {
                                handleExplainClick();
                            })}>
                            <Image src={gacha_explain} className="w-[20px] h-[20px]" />
                        </View>
                    </View>
                </View>
                <View className="at-col" />
                <View className="at-col at-col-1 at-col--auto">
                    <View
                        className="flex h-[44px] mx-[20px] items-center scale-[1.05] translate-x-[210px]"
                        animation={accountAnimationData}>
                        <View
                            className="w-[26px] h-[26px] flex items-center justify-center"
                            onClick={debounce(() => {
                                handleBgmClick();
                            })}>
                            <Image
                                src={isPlaying ? gacha_bgm_on : gacha_bgm_off}
                                className="w-[24px] h-[24px]"
                                animation={bgmAnimationData}
                            />
                        </View>
                        <TicketsView />
                        <GoldView />
                    </View>
                </View>
            </View>
        </View>
    );
};

export default React.memo(GachaTitleView);
