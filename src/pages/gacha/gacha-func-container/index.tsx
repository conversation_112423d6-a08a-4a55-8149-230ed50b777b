import React, { useCallback, useEffect, useState } from 'react';
import { View, Image } from '@tarojs/components';
import classnames from 'classnames';
import { useDidShow, createAnimation } from '@tarojs/taro';
import gacha_func_icon_appreciate from '@/assets/game/gacha/gacha_func_icon_appreciate.png';
import gacha_func_icon_enchange from '@/assets/game/gacha/gacha_func_icon_enchange.png';
import gacha_func_icon_preview from '@/assets/game/gacha/gacha_func_icon_preview.png';
import cardCollectStore, { checkWholeNewTag } from '@/pages/appreciate/store/cardCollectStore';
import { TabType } from '@/pages/market/store/store';
import { jump2Appreciate, jump2Market } from '@/router';
import Preview from '../gacha-animator-container/preview';
import GachaDialogView from './dialog';
import { GACAG_ANIMATOR, pageShowDelayDur, pageShowDur } from '../const';
import GachaTitleView from './title';
import GachaFuncView from './func';
import './index.scss';

export function QuickEntrance({ classNames }: { classNames: string }) {
    const [animationData, setAnimationData] = useState({});
    const [previewVisible, setPreviewVisible] = useState(false);
    useEffect(() => {
        const timer = setTimeout(() => {
            const animation = createAnimation({
                timingFunction: 'linear',
            });
            animation.translateX(0).step({ duration: pageShowDur });
            setAnimationData(animation.export());
        }, pageShowDelayDur);
        return () => {
            clearTimeout(timer);
        };
    }, []);

    const redDot = cardCollectStore((state) => state.redDot);

    const gotoCard = useCallback(() => {
        // 图鉴入口~
        jump2Appreciate({});
    }, []);

    function handleRedemptionClick() {
        jump2Market({ type: TabType.card });
    }

    return (
        <View className={classnames(classNames, 'flex flex-row justify-end')}>
            <View
                className="flex flex-col justify-end pr-[19.67px] mb-[7px] translate-x-[55px]"
                animation={animationData}>
                <View className="relative w-[34px] h-[40px]">
                    <Image
                        className="w-[34px] h-[40px] relative"
                        src={gacha_func_icon_appreciate}
                        onClick={gotoCard}
                    />
                    {checkWholeNewTag(redDot) && (
                        <View className="absolute right-[5px] top-[2px] w-[6px] h-[6px] bg-[#FF561E] rounded-full" />
                    )}
                </View>
                <Image
                    className="w-[34px] h-[40px] relative mt-[20px]"
                    src={gacha_func_icon_enchange}
                    onClick={handleRedemptionClick}
                />
                <Image
                    className="w-[34px] h-[40px] relative mt-[20px]"
                    src={gacha_func_icon_preview}
                    onClick={() => setPreviewVisible(true)}
                />
                {previewVisible ? (
                    <Preview visible={previewVisible} onClose={() => setPreviewVisible(false)} />
                ) : null}
            </View>
        </View>
    );
}

const GachaFuncContainer = () => {
    useDidShow(() => {
        fetch(GACAG_ANIMATOR.ANIMATOR_BG_LOADING);
        fetch(GACAG_ANIMATOR.ANIMATOR_BG_RESULT_GUIDE_R);
        fetch(GACAG_ANIMATOR.ANIMATOR_BG_RESULT_GUIDE_SR);
        fetch(GACAG_ANIMATOR.ANIMATOR_BG_RESULT_GUIDE_SSR);
    });

    return (
        <View className="gacha-func-container">
            <View
                style={{
                    position: 'absolute',
                    width: '100%',
                    left: 0,
                    top: 0,
                }}>
                <GachaTitleView />
            </View>
            <View
                style={{
                    position: 'absolute',
                    width: '100%',
                    height: '260px',
                    left: 0,
                    bottom: 0,
                }}>
                <GachaFuncView />
            </View>
            <View
                style={{
                    position: 'absolute',
                    width: '100%',
                    height: '100',
                    left: 0,
                    bottom: 0,
                }}>
                <GachaDialogView />
            </View>
        </View>
    );
};
export default React.memo(GachaFuncContainer);
