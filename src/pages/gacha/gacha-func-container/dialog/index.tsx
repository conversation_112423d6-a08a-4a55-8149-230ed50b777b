import React, { useCallback, useEffect, useRef, useState } from 'react';
import { View } from '@tarojs/components';
import Taro, { useDidShow, showToast, onAppShow, offAppShow } from '@tarojs/taro';
import { gotoRechargePage } from '@/pages/market/pay-panel/payPanelStore';
import { coronaWarnMessage } from '@music/mat-base-h5';
import cardCollectStore from '@/pages/appreciate/store/cardCollectStore';
import GachaConfirmModal from './confirm';
import GachaGlodLackModal from './gold-lack';
import GachaTicketsLackModal from './tickets-lack';
import useGachaStore from '../../useGachaStore';
import { buyTicketApi, gachaQueueApi } from '../../gachaApi';

const GachaDialogView = () => {
    const [gachaConfirm, setGachaConfirm] = useState(false);
    const [gachaGlodLack, setGachaGlodLack] = useState(false);
    const [gachaTicketsLack, setGachaTicketsLack] = useState(false);
    const exchangeInfoResp = useGachaStore((state) => state.exchangeInfoResp);
    const gachaCount = useGachaStore((state) => state.gachaCount);
    const setGachaResp = useGachaStore((state) => state.setGachaResp);
    const exchangeInfoFatch = useGachaStore((state) => state.exchangeInfoFatch);
    const gachaClicked = useGachaStore((state) => state.gachaClicked);
    const gachaFreeClicked = useGachaStore((state) => state.gachaFreeClicked);
    const resetGachaClick = useGachaStore((state) => state.resetGachaClick);
    const resetGachaFreeClick = useGachaStore((state) => state.resetGachaFreeClick);
    const glodAccount = useGachaStore((state) => state.glodAccount);
    const pageValid = useRef(false);

    function gachaConfirmReq() {
        let needTickets = 1;
        exchangeInfoResp?.exchangeInfos?.forEach((item) => {
            if (item.rounds === gachaCount) {
                needTickets = item.amount;
            }
        });

        if (needTickets > exchangeInfoResp?.ticketAmount || 0) {
            const exchangeGoldRate = exchangeInfoResp?.exchangeGoldRate || 1;
            const goldLack = exchangeGoldRate * (needTickets - exchangeInfoResp?.ticketAmount);
            if (goldLack > glodAccount || 0) {
                setGachaGlodLack(true);
                return;
            }
            setGachaTicketsLack(true);
            return;
        }
        setGachaConfirm(true);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }

    const buyTicketsReq = async () => {
        try {
            let needTickets = 1;
            exchangeInfoResp?.exchangeInfos?.forEach((item) => {
                if (item.rounds === gachaCount) {
                    needTickets = item.amount;
                }
            });
            const ticketAmount = exchangeInfoResp?.ticketAmount || 0;
            const lackTicketCounts = needTickets - ticketAmount;
            await buyTicketApi(lackTicketCounts);
            if (!pageValid.current) {
                exchangeInfoFatch();
                return;
            }
            try {
                // eslint-disable-next-line @typescript-eslint/no-use-before-define
                gachaQueueFatch(true);
            } catch (error) {
                //
            }
        } catch (error) {
            coronaWarnMessage(
                'Gacha流程',
                `buyTicketApi 接口：gachaCount：${gachaCount}， error:${JSON.stringify(error)}`
            );
            exchangeInfoFatch();
            if (error.code === 20030) {
                setGachaGlodLack(true);
            } else {
                showToast({
                    title: error.message,
                    icon: 'none',
                });
            }
        }
    };
    const gachaQueueFatch = useCallback(
        async (ignoreTicktes: boolean) => {
            let needTickets = 1;
            exchangeInfoResp?.exchangeInfos?.forEach((item) => {
                if (item.rounds === gachaCount) {
                    needTickets = item.amount;
                }
            });
            if (!ignoreTicktes && needTickets > (exchangeInfoResp?.ticketAmount || 0)) {
                setGachaTicketsLack(true);
                return;
            }

            try {
                useGachaStore.setState({ isFetchGachaLoading: true });
                const res = await gachaQueueApi('chapter_card', gachaCount);
                if (!pageValid.current) {
                    exchangeInfoFatch();
                    return;
                }
                const rewards = res?.rewards || [];
                if (rewards) {
                    setGachaResp(rewards || []);
                    // 更新new标识~
                    cardCollectStore.getState().addCards(rewards || []);
                } else {
                    showToast({
                        title: '结局展示/拉取异常',
                    });
                }
            } catch (error) {
                coronaWarnMessage(
                    'Gacha流程',
                    `gachaQueueApi 接口 gachaCount:${gachaCount},error：${JSON.stringify(error)}`
                );
                useGachaStore.setState({ isFetchGachaLoading: false });
                exchangeInfoFatch();
                if (error.code === 2200) {
                    setGachaTicketsLack(true);
                } else if (error.code === 20030) {
                    setGachaTicketsLack(true);
                } else if (error.code === 20038) {
                    /// 强制实名认证
                } else {
                    showToast({
                        title: error.message,
                        icon: 'none',
                    });
                }
            }
        },
        [exchangeInfoFatch, exchangeInfoResp, gachaCount, setGachaResp]
    );

    useDidShow(() => {
        exchangeInfoFatch();
    });

    useEffect(() => {
        if (gachaClicked) {
            resetGachaClick();
            gachaConfirmReq();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [gachaClicked]);

    useEffect(() => {
        if (gachaFreeClicked) {
            gachaQueueFatch(true);
            resetGachaFreeClick();
        }
    }, [gachaFreeClicked, gachaQueueFatch, resetGachaFreeClick]);

    useEffect(() => {
        pageValid.current = true;
        const handleAppShow = () => {
            exchangeInfoFatch();
        };
        onAppShow(handleAppShow);
        return () => {
            pageValid.current = false;
            offAppShow(handleAppShow);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <View className="relative w-[100%] h-[100%]">
            {gachaConfirm ? (
                <GachaConfirmModal
                    count={gachaCount}
                    onConfirm={() => {
                        gachaQueueFatch(false);
                        setGachaConfirm(false);
                    }}
                    onCancel={() => {
                        setGachaConfirm(false);
                    }}
                    visible={gachaConfirm}
                />
            ) : null}
            {gachaGlodLack ? (
                <GachaGlodLackModal
                    onConfirm={() => {
                        setGachaGlodLack(false);
                        gotoRechargePage();
                    }}
                    onCancel={() => {
                        setGachaGlodLack(false);
                    }}
                    visible={gachaGlodLack}
                />
            ) : null}
            {gachaTicketsLack ? (
                <GachaTicketsLackModal
                    count={gachaCount}
                    onConfirm={() => {
                        setGachaTicketsLack(false);
                        buyTicketsReq();
                    }}
                    onCancel={() => {
                        setGachaTicketsLack(false);
                    }}
                    visible={gachaTicketsLack}
                />
            ) : null}
        </View>
    );
};

export default React.memo(GachaDialogView);
