import React from 'react';
import { View, Text, Image, ITouchEvent } from '@tarojs/components';
import gacha_dialog_notify_icon from '@/assets/game/gacha/gacha_dialog_notify_icon.png';
import dialog_icon_cancel from '@/assets/game/gacha/dialog_icon_cancel.png';
import dialog_icon_confirm from '@/assets/game/gacha/dialog_icon_confirm.png';
import FullscreenModal from '@/components/FullScreenModal';
import { getCoinName } from '@/utils/appSourceAdapter';
import './index.scss';
import useGachaStore from '@/pages/gacha/useGachaStore';

export interface TicketsLackProps {
    count: number;
    onCancel?: () => void;
    onConfirm: () => void;
    visible: boolean;
}

const GachaTicketsLackModal = ({ count, onCancel, onConfirm, visible }: TicketsLackProps) => {
    const exchangeGoldRate = useGachaStore(
        (state) => state.exchangeInfoResp?.exchangeGoldRate || 100
    );
    const ticketsCount = useGachaStore((state) => state.exchangeInfoResp?.ticketAmount || 0);
    const exchangeInfos = useGachaStore((state) => state.exchangeInfoResp?.exchangeInfos || []);

    let needTickets = 1;
    exchangeInfos.forEach((item) => {
        if (item.rounds === count) {
            needTickets = item.amount;
        }
    });

    const lackTickets = needTickets - ticketsCount;
    const targetCostGold = lackTickets * exchangeGoldRate;
    return (
        <FullscreenModal onClose={() => onCancel()} visible={visible}>
            <View
                className="gacha-tickets-modal"
                onClick={(e: ITouchEvent) => {
                    e.stopPropagation();
                }}>
                <View className="modal-content">
                    <Text className="text-content">
                        缺少{lackTickets}个高纬透镜，是否消耗{targetCostGold}
                        {getCoinName()}购买
                    </Text>
                    <Image src={gacha_dialog_notify_icon} className="w-[90px] h-[90px] mt-[19px]" />
                    <View className="btn-group">
                        <Image
                            src={dialog_icon_cancel}
                            className="w-[140px] h-[38px]"
                            onClick={onCancel}
                        />
                        <Image
                            src={dialog_icon_confirm}
                            className="w-[140px] h-[38px]"
                            onClick={onConfirm}
                        />
                    </View>
                </View>
            </View>
        </FullscreenModal>
    );
};

export default GachaTicketsLackModal;
