import React, { useEffect } from 'react';
import { View, Text, Image, ITouchEvent } from '@tarojs/components';
import { GACAG_ANIMATOR } from '@/pages/gacha/const';
import gacha_dialog_notify_icon from '@/assets/game/gacha/gacha_dialog_notify_icon.png';
import dialog_icon_cancel from '@/assets/game/gacha/dialog_icon_cancel.png';
import dialog_icon_confirm from '@/assets/game/gacha/dialog_icon_confirm.png';
import FullscreenModal from '@/components/FullScreenModal';
import useGachaStore from '../../../useGachaStore';
import './index.scss';

interface GachaTenConfirmProps {
    count: number;
    onCancel?: () => void;
    onConfirm: () => void;
    visible: boolean;
}

const GachaConfirmModal = ({ count, onCancel, onConfirm, visible }: GachaTenConfirmProps) => {
    const exchangeInfoResp = useGachaStore((state) => state.exchangeInfoResp);
    let needTickets = 1;
    exchangeInfoResp?.exchangeInfos?.forEach((item) => {
        if (item.rounds === count) {
            needTickets = item.amount;
        }
    });
    const ticketAcount = useGachaStore((state) => state.exchangeInfoResp?.ticketAmount || 0);

    useEffect(() => {
        fetch(GACAG_ANIMATOR.ANIMATOR_RESULT_BG_START, {
            method: 'GET',
        });
        fetch(GACAG_ANIMATOR.ANIMATOR_RESULT_BG_LOOP, {
            method: 'GET',
        });
    }, []);

    return (
        <FullscreenModal onClose={() => onCancel()} visible={visible}>
            <View
                className="gacha-confirm-modal"
                onClick={(e: ITouchEvent) => {
                    e.stopPropagation();
                }}>
                <View className="modal-content">
                    <Text className="text-content">
                        是否消耗{needTickets}个高维透镜进行{count}次观测之旅
                    </Text>
                    <Image src={gacha_dialog_notify_icon} className="w-[90px] h-[90px] mt-[9px]" />
                    <View className="btn-group">
                        <View className="content">已拥有:{ticketAcount}</View>
                        <View className="content">本次消耗:{needTickets}</View>
                    </View>
                    <View className="btn-group1">
                        <Image
                            src={dialog_icon_cancel}
                            className="w-[140px] h-[38px]"
                            onClick={onCancel}
                        />
                        <Image
                            src={dialog_icon_confirm}
                            className="w-[140px] h-[38px]"
                            onClick={onConfirm}
                        />
                    </View>
                </View>
            </View>
        </FullscreenModal>
    );
};

export default GachaConfirmModal;
