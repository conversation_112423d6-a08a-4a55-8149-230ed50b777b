import React from 'react';
import { View, Text, Image, ITouchEvent } from '@tarojs/components';
import gacha_dialog_notify_icon from '@/assets/game/gacha/gacha_dialog_notify_icon.png';
import dialog_icon_cancel from '@/assets/game/gacha/dialog_icon_cancel.png';
import dialog_icon_confirm from '@/assets/game/gacha/dialog_icon_confirm.png';
import FullscreenModal from '@/components/FullScreenModal';
import { getCoinName } from '@/utils/appSourceAdapter';
import './index.scss';

export interface GoldLackProps {
    onCancel?: () => void;
    onConfirm: () => void;
    visible: boolean;
}

const GachaGlodModal = ({ onCancel, onConfirm, visible }: GoldLackProps) => {
    return (
        <FullscreenModal onClose={() => onCancel()} visible={visible}>
            <View
                className="gacha-gold-modal"
                onClick={(e: ITouchEvent) => {
                    e.stopPropagation();
                }}>
                <View className="modal-content">
                    <Text className="text-content">您的{getCoinName()}不足，请充值后购买。</Text>
                    <Image src={gacha_dialog_notify_icon} className="w-[90px] h-[90px] mt-[19px]" />
                    <View className="btn-group">
                        <Image
                            src={dialog_icon_cancel}
                            className="w-[140px] h-[38px]"
                            onClick={onCancel}
                        />
                        <Image
                            src={dialog_icon_confirm}
                            className="w-[140px] h-[38px]"
                            onClick={onConfirm}
                        />
                    </View>
                </View>
            </View>
        </FullscreenModal>
    );
};

export default GachaGlodModal;
