// eslint-disable-next-line max-classes-per-file
import * as Gacha from '@/const/gacha';

export const GACAG_ANIMATOR = Gacha.GACAG_ANIMATOR;

/**
 * 抽卡~
 */
export const GACHA_TYPE = {
    /**
     * 高维透镜~
     */
    TYPE_CARD_LENS: 'TYPE_CARD_LENS',
    /**
     * R卡
     */
    TYPE_CARD_R: 'TYPE_CARD_R',
    /**
     * SR卡
     */
    TYPE_CARD_SR: 'TYPE_CARD_SR',
    /**
     * SSR卡
     */
    TYPE_CARD_SSR: 'TYPE_CARD_SSR',
};

/**
 * 抽卡~
 */
export const GACHA_STEP = {
    /**
     * /过渡视频~
     */
    STEP_LOADING: 10,
    STEP_TRANSITION: 1,
    STEP_CARDS: 2,
    STEP_RESULT: 3,
};

/**
 * 卡面动画的配置~
 * 人物在左边， 还是人物在右边~
 */
export const CARD_CONFIG = {
    CONFIG_LEFT: 'left',
    CONFIG_RIGHT: 'right',
};

/**
 * 背景显示间隔~
 */
export const cardBgShowDur = 100;
/**
 * 卡面出现的耗时~~
 */
export const cardInfoShowDur = 200;
/**
 * 卡面消失时间~
 */
export const cardHideDur = 200;
export const cardResetDur = 100;
export const cardResetWaittingDur = cardResetDur + 100;

export const cardInfoNewShowDur = 150;
export const pageShowDur = 200;
export const pageShowDelayDur = 200;

export interface LotteryCardInfoVo {
    /**
     * 用户账户奖券个数
     */
    ticketAmount: number;
    /**
     * 兑换信息
     */
    exchangeInfos: LotteryExchangeInfo[];
    /**
     * 金币兑换抽奖券汇率
     */
    exchangeGoldRate: number;
    /**
     * 抽卡红点
     */
    lotteryCardPoint?: boolean;

    /**
     * ssr保底剩余次数
     */
    ssrRemainCount: number;
}

export interface LotteryExchangeInfo {
    rounds: number;
    amount: number;
}

export interface LotteryRewardResponse {
    rewards: LotteryRewardDetail[];
}

export const EXCLUSIVE_CARD = 'EXCLUSIVE_CARD'; // 卡面~
export const UNIVERSAL_SHARD = 'UNIVERSAL_SHARD'; // 通用碎片~
/**
 * 抽奖奖品结果
 */
export interface LotteryRewardDetail {
    rewardType: number;
    resourceId: string;
    rewardName: string;
    rewardImg: string;
    chapterInfoItem: ChapterRewardItem;
    firstReceive: boolean;
    decomposedReward: DecomposedRewardDetail[];
    number: number;
    id: string;
    rewardNum?: number;
}

export function checkDecomposedReward(item: LotteryRewardDetail) {
    return (
        (item.decomposedReward?.length || 0) > 0 ||
        (!item.firstReceive && item?.chapterInfoItem?.chapterLevel === 'R')
    );
}

/**
 * 剧情奖品信息
 */
export interface ChapterRewardItem {
    chapterDesc: string;
    chapterId: string;
    roleId: string;
    chapterLevel: string;
    chapterImg: string;
    chapterName: string;
    endingInfo: ChapterEndingItem[];
    roleName?: string;
    roleUserId?: string;
    roleUserImg?: string;
}

export interface ChapterEndingItem {
    endingId: string;
    endingName: string;
    endingBigImgUrl: string;
    happiness: boolean;
}

/**
 * 奖品转换
 */
export interface DecomposedRewardDetail {
    lotteryRewardDetail: LotteryRewardDetail;
}

export class LotteryRewardDetailImpl implements LotteryRewardDetail {
    firstReceive: boolean;

    rewardNum?: number;

    id: string;

    rewardType: number;

    resourceId: string;

    rewardName: string;

    rewardImg: string;

    chapterInfoItem: ChapterRewardItem;

    decomposedReward: DecomposedRewardDetail[];

    number: number;
}

export class GachaBgCardItem {
    url: string;

    isVideo: boolean;
}
