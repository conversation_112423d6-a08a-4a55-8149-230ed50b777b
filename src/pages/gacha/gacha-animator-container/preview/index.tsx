import React, { useState } from 'react';
import RootPortal from '@/components/RootPortal';
import classNames from 'classnames';
import { AlphaVideo } from '@music/ct-animation';
import { Swiper, SwiperItem, View, Image } from '@tarojs/components';
import NAV_BACK_ICON from '@/assets/common/comm_icon_nav_back.png';
import LOTTERY_ARROW_DOWN from '@/assets/game/gacha/lottery_arrow_down.png';
import ErrorBoundary from '@/components/ErrorBoundary';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { LOTTERY_PREVIEW_BG_EFFECT } from '@/const/gacha';
import PreviewContainer from './previewContainer';
import RecordContainer from './recordContainer';

import './index.scss';

const tabs = [
    {
        title: '掉落预览',
        desc: 'PREVIEW',
    },
    {
        title: '观测记录',
        desc: 'RECORD',
    },
];

const Preview = ({ visible, onClose }: { visible?: boolean; onClose: () => void }) => {
    const [current, setCurrent] = useState(0);

    const handleTabClick = (index: number) => {
        try {
            if (current !== index) {
                setCurrent(index);
            }
        } catch (err) {
            coronaWarnMessage('handleTabClick', {
                error: (err || {}).stack || err,
            });
        }
    };

    const handleClose = () => {
        try {
            onClose();
        } catch (err) {
            coronaWarnMessage('handleClose', {
                error: (err || {}).stack || err,
            });
        }
    };

    const handleSwiperChange = (e: { detail: { current: React.SetStateAction<number> } }) => {
        setCurrent(e.detail.current);
    };

    if (!visible) return null;

    const items = [PreviewContainer, RecordContainer];
    const tabContents = items.map((Component, index) => (
        <SwiperItem key={`tab-${tabs[index]}`} className="swiper-item">
            <View className="tab-content">
                <View className="tab-content-inner">
                    <View className="content-wrapper">
                        <Component />
                    </View>
                </View>
            </View>
        </SwiperItem>
    ));

    const Modal = (
        <RootPortal>
            <ErrorBoundary>
                <View className="m-preview">
                    <Image className="close-btn" onClick={handleClose} src={NAV_BACK_ICON} />
                    <AlphaVideo
                        className="preview-bg-effect"
                        src={LOTTERY_PREVIEW_BG_EFFECT}
                        style={{
                            width: '100%',
                            height: '100%',
                        }}
                        loop
                        prefetchMp4={false}
                        autoplay
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        // @ts-ignore
                        sizeMode="cover"
                    />
                    <View className="tab-container">
                        {tabs.map((tab, index) => (
                            // eslint-disable-next-line react/no-array-index-key
                            <React.Fragment key={`tab-${index}`}>
                                <View
                                    className={classNames('tab-item', {
                                        active: current === index,
                                    })}
                                    onClick={() => handleTabClick(index)}>
                                    <View className="title">{tab.title}</View>
                                    <View className="desc">{tab.desc}</View>
                                </View>
                                {index === 0 && <View className="divider" />}
                            </React.Fragment>
                        ))}
                    </View>
                    <Swiper
                        disableProgrammaticAnimation="true"
                        current={current}
                        onChange={handleSwiperChange}
                        className="tab-panes"
                        skipHiddenItemLayout
                        circular={false}
                        style={{ overflow: 'visible' }}>
                        {tabContents}
                    </Swiper>
                    <Image className="arrow-down" src={LOTTERY_ARROW_DOWN} />
                </View>
            </ErrorBoundary>
        </RootPortal>
    );

    return Modal;
};

export default React.memo(Preview);
