.record-list {
    width: 100%;
    padding: 16px 0;
    display: flex;
    flex-direction: column;

    .record-list-scroll {
        order: 2;
        overflow: auto;
        height: calc(100vh - 240px);
        -webkit-mask-image: linear-gradient(
            to bottom,
            rgba(0, 0, 0) 0%,
            rgba(0, 0, 0) 90%,
            rgba(0, 0, 0, 0) 100%
        );
    }

    .record-header {
        display: flex;
        align-items: center;
        height: 30px;
        color: rgba(255, 255, 255, 0.4);
        background: rgb(140, 140, 140, 0.1);
        font-size: 12px;
        font-weight: 600;
        border-bottom: 1px solid rgba(255, 255, 255, 0.12);
        border-top: 1px solid rgba(255, 255, 255, 0.12);
        order: 1;

        .header-quality {
            width: 90px;
            text-align: center;
        }

        .header-name {
            flex: 1;
            flex-basis: 90px;
            margin-left: 33px;
        }

        .header-time {
            text-align: center;
            margin-right: 70px;
        }
    }

    .record-empty {
        text-align: center;
        color: rgba(255, 255, 255, 0.3);
        font-size: 14px;
        margin-top: 200px;
        order: 2;
    }

    .record-list-scroll-content {
        order: 2;

        .record-item {
            display: flex;
            align-items: center;
            border-bottom: 0.5px solid rgba(255, 255, 255, 0.12);
            color: #fff;
            font-size: 12px;
            padding: 12px 26px;

            .item-image-container {
                width: 60px;
                height: 23px;
                margin-right: 16px;
                position: relative;
                border-radius: 8px;
                overflow: hidden;

                .item-image {
                    height: 100%;
                    position: relative;
                    z-index: 1;
                }
            }

            &.ssr-bg {
                color: #ffe4c7;

                .item-image {
                    width: 41px;
                }
            }

            &.sr-bg {
                color: #e3c7ff;

                .item-image {
                    width: 30px;
                }
            }

            &.r-bg {
                color: #c7dcff;

                .item-image {
                    width: 20px;
                }
            }

            .item-name {
                flex: 1;
            }

            .item-time {
                margin-left: 16px;
            }
        }
    }

    .record-list-scroll-loading {
        order: 3;

        .loading-item {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 4.5rem;
            width: 100%;
            color: rgba(255, 255, 255, 0.4);
            font-size: 12px;
        }
    }
}
