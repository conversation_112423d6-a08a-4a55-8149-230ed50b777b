/* eslint-disable react/no-array-index-key */
import React, { useCallback, useEffect, useRef } from 'react';
import { View, Image, ScrollView } from '@tarojs/components';
import classNames from 'classnames';
import { DateUtil } from '@music/helper';
import TYPE_R from '@/assets/game/gacha/type_r.png';
import TYPE_SR from '@/assets/game/gacha/type_sr.png';
import TYPE_SSR from '@/assets/game/gacha/type_ssr.png';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { jump2ChapterInfo } from '@/router';
import useLotteryRewardStore, {
    type LotteryRewardAward,
} from '../../../store/useLotteryRewardStore';

import './index.scss';

const CARD_IMAGES: Record<'SSR' | 'SR' | 'R', string> = {
    SSR: TYPE_SSR,
    SR: TYPE_SR,
    R: TYPE_R,
};

const LoadingItem = () => {
    const isLoading = useLotteryRewardStore((state) => state.loading);
    const getLotteryRecord = useLotteryRewardStore((state) => state.getLotteryRecord);
    const hasMore = useLotteryRewardStore((state) => state.more);
    const loaderRef = useRef(null);

    const handleInfiniteScroll = useCallback(
        async ([entry]: { isIntersecting: boolean }[]) => {
            if (entry.isIntersecting && !isLoading && hasMore) {
                await getLotteryRecord();
            }
        },
        [getLotteryRecord, hasMore, isLoading]
    );

    useEffect(() => {
        const option = {
            threshold: 0.5,
        };
        let observer: any;

        if (loaderRef.current) {
            observer = new IntersectionObserver(handleInfiniteScroll, option);
            observer.observe(loaderRef.current);
        }
        return () => observer && observer.disconnect();
    }, [handleInfiniteScroll]);

    return (
        <div className="loading-item" ref={loaderRef}>
            {isLoading && <View className="loading-item-text">加载中...</View>}
        </div>
    );
};

const EmptyItem = () => <View className="record-empty">当前没有观测记录</View>;

const RecordContainer = () => {
    const lotteryRecord = useLotteryRewardStore((state) => state.lotteryRecord);
    const getLotteryRecord = useLotteryRewardStore((state) => state.getLotteryRecord);

    const renderName = (item: LotteryRewardAward) => {
        if (item?.roleName) {
            return `${item?.roleName}·${item?.rewardName}`;
        }
        return `${item?.rewardName}`;
    };

    const handleItemClick = (item: LotteryRewardAward) => {
        try {
            const chapterStr = encodeURIComponent(JSON.stringify(item));
            jump2ChapterInfo({
                chapterId: item?.chapterId,
                robotUserId: item?.roleUserId,
                chapter: chapterStr,
                from: null,
            });
        } catch (err) {
            coronaWarnMessage('handleItemClick', {
                error: (err || {}).stack || err,
            });
        }
    };

    useEffect(() => {
        getLotteryRecord();
    }, [getLotteryRecord]);

    const renderTitle = () => {
        return (
            // 因为taro 3.x swiper的bug：滑动后dom顺序会改变，在taro4.0.9版本才修复此bug
            // 所以这里故意把record-header跟record-list-scroll调换了位置
            // 如果后面发现这里有其他问题，再调换回来，寻求别的解决方案
            <View className="record-header">
                <View className="header-quality">品质</View>
                <View className="header-name">名称</View>
                <View className="header-time">时间</View>
            </View>
        );
    };

    const renderContent = () => {
        if (!lotteryRecord?.length) {
            return (
                <View className="record-list-scroll">
                    <EmptyItem />
                </View>
            );
        }

        return (
            <ScrollView className="record-list-scroll" key={2}>
                <View className="record-list-scroll-content">
                    {lotteryRecord?.map((record: LotteryRewardAward, index: number) => (
                        <View
                            key={`${record?.rewardId}-${index}`}
                            className={classNames('record-item', {
                                [`${record?.rewardLevel?.toLowerCase()}-bg`]: true,
                            })}
                            onClick={() => handleItemClick(record)}>
                            <View className="item-image-container">
                                <Image
                                    className="item-image"
                                    src={
                                        CARD_IMAGES[record?.rewardLevel as keyof typeof CARD_IMAGES]
                                    }
                                />
                            </View>
                            <View className="item-name">{renderName(record)}</View>
                            <View className="item-time">
                                {DateUtil.format(record?.recordTime, 'YYYY/MM/DD HH:mm:ss')}
                            </View>
                        </View>
                    ))}
                </View>
                <View className="record-list-scroll-loading">
                    <LoadingItem />
                </View>
            </ScrollView>
        );
    };

    return (
        <View className="record-list">
            {renderTitle()}
            {renderContent()}
        </View>
    );
};

export default React.memo(RecordContainer);
