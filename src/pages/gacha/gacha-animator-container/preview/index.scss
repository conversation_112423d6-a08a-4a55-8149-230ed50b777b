.m-preview {
    width: 100%;
    height: 100%;
    z-index: 1000;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: center / 100% 100% repeat url('../../../../assets/game/gacha/preview_bg.jpg');

    ::-webkit-scrollbar {
        display: none;
    }

    /* stylelint-disable-next-line selector-type-no-unknown */
    taro-swiper-core {
        height: auto !important;
    }

    .tab-container {
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: center;
        padding-top: 100px;
        z-index: 10;

        .tab-item {
            text-align: center;
            position: relative;
            font-weight: 400;
            transition: opacity 0.3s ease-in-out;

            .title {
                font-size: 18px;
                margin-bottom: 2px;
                color: rgb(255, 255, 255, 0.8);
                font-family: 'SourceHanSerifCN-Bold', sans-serif;
                transition: color 0.3s ease-in-out;
            }

            .desc {
                font-size: 8px;
                color: rgb(255, 255, 255, 0.2);
                transition: color 0.3s ease-in-out;
            }

            &.active {
                .title {
                    color: #fff;
                    text-shadow: 0 0 6px rgba(255, 138, 43, 0.24), 0 0 6px #ff8a2b;
                }
            }

            &.active::after {
                content: '';
                position: absolute;
                bottom: -2px;
                left: -20px;
                width: 59px;
                height: 57px;
                background: center / 100% 100% repeat
                    url('../../../../assets/game/gacha/preview_tab_active_bg.png');
            }
        }

        .divider {
            width: 1px;
            height: 12px;
            background-color: rgba(255, 255, 255, 0.1);
            margin: 0 18px;
        }
    }

    .tab-content {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;

        .tab-content-inner {
            width: 100%;
            height: 100%;
            transition: transform 0.3s ease-in-out;

            .content-wrapper {
                display: flex;
                width: 100%;
                height: 100%;

                > div {
                    flex: 1;
                    width: 100%;
                    height: 100%;
                }

                .preview-container {
                    flex-shrink: 0;
                    width: 100%;
                }

                .record-container {
                    flex-shrink: 0;
                    width: 100%;
                }
            }

            &.slide-left {
                transform: translateX(-100%);
            }

            &.slide-right {
                transform: translateX(0);
            }
        }
    }

    .close-btn {
        position: absolute;
        width: 26px;
        height: 26px;
        top: 53px;
        left: 20px;
        z-index: 100;
        cursor: pointer;
    }

    .preview-bg-effect {
        position: absolute;
        z-index: 0;
    }

    .arrow-down {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        width: 14px;
        height: 14px;
    }
}
