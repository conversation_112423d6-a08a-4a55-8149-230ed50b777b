.preview-container {
    display: flex;
    position: relative;
    flex-direction: column;
    padding: 0 18px;
    overflow: auto;
    height: calc(100vh - 190px);
    margin-top: -11px;
    -webkit-mask-image: linear-gradient(
        to bottom,
        rgba(0, 0, 0) 0%,
        rgba(0, 0, 0) 90%,
        rgba(0, 0, 0, 0) 100%
    );

    .row {
        display: flex;
        flex-wrap: wrap;
        margin: -12px 0 0 -5px;
    }

    .gap {
        margin-top: 16px;
    }

    .title-ssr {
        width: 59px;
        height: 25px;
    }

    .title-sr {
        width: 48px;
        height: 25px;
    }

    .title-lr {
        width: 37px;
        height: 25px;
    }
}

.preview-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-shrink: 0;
    margin: 12px 0 0 10px;
    position: relative;
    width: 74px;

    taro-image-core {
        overflow: visible;
    }

    .item-images {
        width: 74px;
        height: 72px;
        background-size: 100% 100%;
        background-repeat: repeat;
        background-position: center;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .item-image {
        width: 93%;
        height: 93%;
        z-index: 1;

        img {
            border-radius: 15px;
            object-fit: cover;
            object-position: 0% 42%;
        }
    }

    .item-name {
        text-align: center;
        font-size: 12px;
        color: #fff;
        font-weight: 600;
        margin-top: 7px;
        width: 50px;
        word-wrap: break-word;
    }
}
