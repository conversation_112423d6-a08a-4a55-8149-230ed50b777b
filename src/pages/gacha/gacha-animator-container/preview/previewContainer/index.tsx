/* eslint-disable react/no-array-index-key */
import React, { useEffect, useState } from 'react';
import { View, Image } from '@tarojs/components';
import { coronaWarnMessage } from '@music/mat-base-h5';
import BG_R from '@/assets/game/gacha/gacha_result_bg_r.png';
import BG_SR from '@/assets/game/gacha/gacha_result_bg_sr.png';
import BG_SSR from '@/assets/game/gacha/gacha_result_bg_ssr.png';
import TITLE_LR from '@/assets/game/gacha/title_lr.png';
import TLTLE_LSR from '@/assets/game/gacha/title_lsr.png';
import TITLE_LSSR from '@/assets/game/gacha/title_lssr.png';
import { optimizeImage } from '@/utils/image';
import CardPreview from '@/components/CardPreview';
import useLotteryRewardStore, {
    type LotteryRewardAward,
} from '../../../store/useLotteryRewardStore';

import './index.scss';

export const CARD_BGS: Record<'SSR' | 'SR' | 'R', string> = {
    SSR: BG_SSR,
    SR: BG_SR,
    R: BG_R,
};

const PreviewItemComponent: React.FC<{
    item: LotteryRewardAward;
}> = ({ item }: { item: LotteryRewardAward }) => {
    const [cardPreviewVisible, setCardPreviewVisible] = useState<boolean>(false);
    const optimizedSrc = optimizeImage({
        src: item?.rewardIcon,
        width: 74,
        height: 72,
    });

    const handleShowCardPreview = () => {
        try {
            setCardPreviewVisible(true);
        } catch (err) {
            coronaWarnMessage('handleShowCardPreview', {
                error: (err || {}).stack || err,
            });
        }
    };

    const renderName = () => {
        if (item?.roleName) {
            return (
                <React.Fragment>
                    <View>{item?.roleName}·</View>
                    <View>{item?.rewardName}</View>
                </React.Fragment>
            );
        }
        return `${item?.rewardName}`;
    };

    return (
        <View className="preview-item" onClick={handleShowCardPreview}>
            <View
                className="item-images"
                style={{ backgroundImage: `url(${CARD_BGS[item.rewardLevel]})` }}>
                <Image src={optimizedSrc} className="item-image" />
            </View>
            <View className="item-name">{renderName()}</View>
            {cardPreviewVisible && (
                <CardPreview
                    data={{ ...item, isLock: true }}
                    onClose={() => setCardPreviewVisible(false)}
                />
            )}
        </View>
    );
};

const PreviewContainer: React.FC = () => {
    const lotteryRewards = useLotteryRewardStore((state) => state.lotteryRewards);
    const getLotteryRewardPool = useLotteryRewardStore((state) => state.getLotteryRewardPool);
    const resetStore = useLotteryRewardStore((state) => state.resetStore);
    const lotteryRewardsR = lotteryRewards?.R;
    const lotteryRewardsSr = lotteryRewards?.SR;
    const lotteryRewardsSsr = lotteryRewards?.SSR;

    useEffect(() => {
        getLotteryRewardPool();

        return () => {
            resetStore();
        };
    }, [getLotteryRewardPool, resetStore]);

    return (
        <View className="preview-container">
            <View className="ssr-container gap">
                <Image src={TITLE_LSSR} className="title-ssr" />
                <View className="row">
                    {lotteryRewardsSsr?.map((item: LotteryRewardAward, index: number) => (
                        <PreviewItemComponent key={`${item?.rewardId}-${index}`} item={item} />
                    ))}
                </View>
            </View>
            <View className="sr-container gap">
                <Image src={TLTLE_LSR} className="title-sr" />
                <View className="row">
                    {lotteryRewardsSr?.map((item: LotteryRewardAward) => (
                        <PreviewItemComponent key={item?.rewardId} item={item} />
                    ))}
                </View>
            </View>
            <View className="r-containe gap">
                <Image src={TITLE_LR} className="title-lr" />
                <View className="row">
                    {lotteryRewardsR?.map((item: LotteryRewardAward) => (
                        <PreviewItemComponent key={item?.rewardId} item={item} />
                    ))}
                </View>
            </View>
        </View>
    );
};

export default React.memo(PreviewContainer);
