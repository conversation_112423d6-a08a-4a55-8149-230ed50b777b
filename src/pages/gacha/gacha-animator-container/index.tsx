import React, { useEffect, useState, useRef, useCallback } from 'react';
import { View } from '@tarojs/components';
import { createAnimation } from '@tarojs/taro';
import { optimizeImage } from '@/utils/image';
import SwipeBack from '@/utils/adapter/swipeBack';
import { coronaWarnMessage } from '@music/mat-base-h5';
import TransitionView from './transition';
import GachaLoadingAnimationView from './loading';
import { GACHA_STEP } from '../const';
import {
    GachaAnimatorSegment,
    GachaCardAnimatorSegment,
    GachaResultAnimatorSegment,
    GachaTransitionAnimatorSegment,
} from './const';
import GachaResultView from './result';
import CardsView from './cards';
import useGachaStore from '../useGachaStore';
import { infoJsonDefault, infoWholeJsonStr } from './MockData';
import { jump2GachaCard } from '@/router';

const GachaAnimatorContainer = () => {
    const gachaResp = useGachaStore((state) => state.gachaResp);
    const gachaCardsList = useGachaStore((state) => state.gachaCardsList);
    const isFetchGachaLoading = useGachaStore((state) => state.isFetchGachaLoading);
    const resetGachaQueue = useGachaStore((state) => state.resetGachaQueue);
    const setGachaResp = useGachaStore((state) => state.setGachaResp);

    const animatorQueue = useRef<GachaAnimatorSegment[]>([]);

    const [showAnimatorContainer, setShowAnimatorContainer] = useState(0);
    // 网络请求过程中的loading动画
    const showLoadingView = useRef(false);
    // 展示抽卡转场动画
    const showTransitionView = useRef(false);
    // 展示卡面
    const showCards = useRef(false);
    // 展示结果
    const showResult = useRef(false);

    const handleAnimatorQueue = useCallback(() => {
        // 开始执行动画
        if (animatorQueue.current.length === 0) {
            coronaWarnMessage('Gacha流程', `handleAnimatorQueue 动效播放完毕`);
            resetGachaQueue();
            setShowAnimatorContainer(0);
            useGachaStore.setState({ isFetchGachaLoading: false });
            return;
        }

        const item = animatorQueue.current[0];
        showLoadingView.current = false;
        showTransitionView.current = false;
        showCards.current = false;
        showResult.current = false;
        switch (item.step) {
            case GACHA_STEP.STEP_LOADING:
                showLoadingView.current = true;
                break;
            case GACHA_STEP.STEP_TRANSITION:
                coronaWarnMessage('Gacha流程', `handleAnimatorQueue 播放动效- 引导动画`);
                showTransitionView.current = true;
                break;
            case GACHA_STEP.STEP_CARDS:
                coronaWarnMessage('Gacha流程', `handleAnimatorQueue 播放动效- 卡面动画`);
                showCards.current = true;
                break;
            case GACHA_STEP.STEP_RESULT:
                coronaWarnMessage('Gacha流程', `handleAnimatorQueue 播放动效- 结果动画`);
                showResult.current = true;
                break;
            default:
                break;
        }
        setShowAnimatorContainer(new Date().getTime());
        if (item.step === GACHA_STEP.STEP_TRANSITION) {
            gachaCardsList.forEach((cardItem) => {
                const chapterImg = optimizeImage({
                    src: cardItem?.chapterInfoItem?.chapterImg || '',
                    width: 249,
                    height: 648,
                });
                fetch(chapterImg);
                const endingBigImgUrl1 =
                    cardItem?.chapterInfoItem?.endingInfo[0]?.endingBigImgUrl || '';
                if (endingBigImgUrl1 && !endingBigImgUrl1.endsWith('.mp4')) {
                    const endingInfoImage1 = optimizeImage({
                        src: endingBigImgUrl1,
                        width: 203.13,
                        height: 77.17,
                    });
                    fetch(endingInfoImage1);
                }
                const endingBigImgUrl2 =
                    cardItem?.chapterInfoItem?.endingInfo[1]?.endingBigImgUrl || '';
                if (endingBigImgUrl2 && !endingBigImgUrl2.endsWith('.mp4')) {
                    const endingInfoImage2 = optimizeImage({
                        src: endingBigImgUrl2,
                        width: 203.13,
                        height: 77.17,
                    });
                    fetch(endingInfoImage2);
                }
            });
        }
    }, [gachaCardsList, resetGachaQueue]);

    /**
     * 单个抽卡动效播放完成~
     */
    const handleAnimatorItemFinished = useCallback(() => {
        showLoadingView.current = false;
        showTransitionView.current = false;
        showCards.current = false;
        showResult.current = false;
        animatorQueue.current = animatorQueue.current.slice(1) || [];
        handleAnimatorQueue();
    }, [handleAnimatorQueue]);

    const handleTransitionAnimatorItemFinished = useCallback(() => {
        const item = animatorQueue.current[0];
        if (item.step === GACHA_STEP.STEP_TRANSITION) {
            handleAnimatorItemFinished();
        }
    }, [handleAnimatorItemFinished]);

    const handleCardsAnimatorItemFinished = useCallback(() => {
        const item = animatorQueue.current[0];
        if (item.step === GACHA_STEP.STEP_CARDS) {
            handleAnimatorItemFinished();
        }
    }, [handleAnimatorItemFinished]);

    function showWholeAnimator() {
        animatorQueue.current = [
            new GachaTransitionAnimatorSegment(),
            new GachaCardAnimatorSegment(),
            new GachaResultAnimatorSegment(),
        ];
        handleAnimatorQueue();
    }

    function showCardsTestClick() {
        jump2GachaCard({ infoJsonStr: infoJsonDefault });
    }

    function showWholeTest() {
        const queue = JSON.parse(infoWholeJsonStr);
        useGachaStore.setState({ isFetchGachaLoading: true });
        setGachaResp(queue.rewards);
        showWholeAnimator();
    }

    function showResultTest() {
        animatorQueue.current = [new GachaResultAnimatorSegment()];
        handleAnimatorQueue();
    }

    function EmptyContainer() {
        return (
            <View>
                <View
                    style={{
                        position: 'absolute',
                        zIndex: '110',
                        top: '30%',
                        backgroundColor: 'red',
                        pointerEvents: 'auto',
                        left: '0',
                        display: 'none',
                    }}>
                    <View
                        style={{ width: '500px' }}
                        onClick={(e) => {
                            e.stopPropagation();
                            showCardsTestClick();
                        }}>
                        展示卡面（测试数据）
                    </View>
                    <View
                        style={{ width: '500px' }}
                        onClick={(e) => {
                            e.stopPropagation();
                            showResultTest();
                        }}>
                        展示结果（测试数据）
                    </View>
                    <View
                        style={{ width: '500px' }}
                        onClick={(e) => {
                            e.stopPropagation();
                            showWholeTest();
                        }}>
                        全链路（测试数据）
                    </View>
                </View>
            </View>
        );
    }

    function AnimatorContainer() {
        return (
            <View style={{ position: 'relative', width: '100%', height: '100%' }}>
                {showTransitionView.current && (
                    <View className="absolute w-[100%] h-[100%] top-[0] left-[0]">
                        <TransitionView finish={handleTransitionAnimatorItemFinished} />
                    </View>
                )}
                {showCards.current && (
                    <View className="absolute w-[100%] h-[100%] top-[0] left-[0]">
                        <CardsView finish={handleCardsAnimatorItemFinished} />
                    </View>
                )}
                {showResult.current && (
                    <View className="absolute w-[100%] h-[100%] top-[0] left-[0]">
                        <GachaResultView finish={handleAnimatorItemFinished} />
                    </View>
                )}
            </View>
        );
    }

    const [animationData, setAnimationData] = useState({});

    useEffect(() => {
        SwipeBack.enabled = !isFetchGachaLoading;
        // 创建动画实例
        const animation = createAnimation({
            timingFunction: 'ease-in-out',
        });
        const opacity = isFetchGachaLoading ? 1 : 0;

        animation.opacity(opacity).step({ duration: 800 });
        setAnimationData(animation.export());
    }, [isFetchGachaLoading]);

    useEffect(() => {
        // 监听 store 中的数据~
        if (gachaResp?.length === 0) {
            // 如果不是在请求中或者数据为空，不播放动画
            setShowAnimatorContainer(0);
            return;
        }
        showWholeAnimator();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [gachaResp]);

    useEffect(() => {
        return () => {
            resetGachaQueue();
        };
    }, [resetGachaQueue]);

    return (
        <View className="relative w-[100%] h-[100%]">
            <View
                className="absolute w-[100%] h-[100%] top-[0] left-[0]"
                style={{ opacity: 0 }}
                animation={animationData}>
                <GachaLoadingAnimationView />
            </View>
            {showAnimatorContainer !== 0 ? <AnimatorContainer /> : <EmptyContainer />}
        </View>
    );
};

export default React.memo(GachaAnimatorContainer);
