// eslint-disable-next-line max-classes-per-file
import { GACHA_STEP, GACHA_TYPE } from '../const';

export abstract class GachaAnimatorSegment {
    step: number = GACHA_STEP.STEP_TRANSITION;
}

export class GachaLoadingAnimatorSegment extends GachaAnimatorSegment {
    step: number = GACHA_STEP.STEP_LOADING;
}
export class GachaTransitionAnimatorSegment extends GachaAnimatorSegment {
    step: number = GACHA_STEP.STEP_TRANSITION;
}
export class GachaCardAnimatorSegment extends GachaAnimatorSegment {
    step: number = GACHA_STEP.STEP_CARDS;
}
export class GachaResultAnimatorSegment extends GachaAnimatorSegment {
    step: number = GACHA_STEP.STEP_RESULT;
}

export abstract class GachaAnimatorItem {
    /**
     * GACHA_TYPE
     */
    type: string;

    /**
     * 抽卡动画 地址~
     */
    gacahaVideoUrl: string;

    /**
     * 最终产物地址（通用碎片、卡面）~
     */
    finalImgUrl: string;

    /**
     * 卡面地址~
     */
    cardImgUrl: string;

    /**
     * 是否已拥有该卡片、碎片~
     */
    hasSameCard = false;

    /**
     * 是否是碎片~
     */
    isDebris = false;

    /**
     * 是否是转换~
     */
    isConversion = false;

    /**
     * 抽卡过渡动效~
     */
    isGachaResultGuide = false;

    ignoreCardAnimator(): boolean {
        return this.isDebris || this.type === GACHA_TYPE.TYPE_CARD_LENS;
    }
}

export class GachaAnimatorItemImpl extends GachaAnimatorItem {}
