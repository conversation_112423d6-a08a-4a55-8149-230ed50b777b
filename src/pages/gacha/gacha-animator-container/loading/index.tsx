import React from 'react';
import { View, Image } from '@tarojs/components';
import { NormalVideo, SizeMode } from '@music/ct-animation';
import gacha_bg_default from '@/assets/game/gacha/gacha_bg_default.png';
import { GACAG_ANIMATOR } from '../../const';

const GachaLoadingAnimationView = () => {
    return (
        <View className="relative w-[100%] h-[100%]">
            <View className="absolute w-[100%] h-[100%] top-[0] left-[0]">
                <Image className="w-full h-full object-cover" src={gacha_bg_default} />
            </View>
            <View className="absolute w-[100%] h-[100%] top-[0] left-[0]">
                <NormalVideo
                    src={GACAG_ANIMATOR.ANIMATOR_BG_LOADING}
                    autoplay
                    loop
                    sizeMode={SizeMode.Cover}
                    style={{ width: '100%', height: '100%' }}
                />
            </View>
        </View>
    );
};
export default React.memo(GachaLoadingAnimationView);
