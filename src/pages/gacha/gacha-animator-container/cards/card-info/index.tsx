import React, { useEffect, useRef, useState, useMemo } from 'react';
import { View, Image } from '@tarojs/components';
import { createAnimation, getCurrentInstance } from '@tarojs/taro';
import gacha_card_item_mask_sr from '@/assets/game/gacha/gacha_card_item_mask_sr.png';
import gacha_card_item_mask_ssr from '@/assets/game/gacha/gacha_card_item_mask_ssr.png';
import gacha_card_item_tag_sr from '@/assets/game/gacha/gacha_card_item_info_tag_sr.png';
import gacha_card_item_tag_r from '@/assets/game/gacha/gacha_card_item_info_tag_r.png';
import gacha_card_info_avatar_bg from '@/assets/game/gacha/gacha_card_info_avatar_bg.png';
import { optimizeImage } from '@/utils/image';
import { endingDetailApi } from '@/service/profileApi';
import { jump2EndingInfo, jump2Profile } from '@/router';
import useGachaStore from '../../../useGachaStore';
import {
    cardHideDur,
    LotteryRewardDetail,
    cardInfoShowDur,
    cardResetDur,
    cardResetWaittingDur,
} from '../../../const';

const CardInfoDescView = ({ detail }: { detail?: LotteryRewardDetail }) => {
    const { fromSource = '' } = useMemo(() => {
        return getCurrentInstance().router?.params || {};
    }, []);
    const [animationData, setAnimationData] = useState({});
    const gachaCardsList = useGachaStore((state) => state.gachaCardsList);
    const cardsIndex = useGachaStore((state) => state.cardsIndex);

    const timer = useRef<NodeJS.Timeout>();
    const resetTimer = useRef<NodeJS.Timeout>();
    const type = useRef('');
    const mask1 = useRef('');
    const mask2 = useRef('');
    const endingInfoImageTag = useRef('');
    const endingInfoDesc = useRef('');
    const endingInfoImage1 = useRef('');
    const endingInfoTitle1 = useRef('');

    const endingInfoImage2 = useRef('');
    const endingInfoTitle2 = useRef('');

    const itemInfo = useRef<LotteryRewardDetail>(null);

    function showInfo(item: LotteryRewardDetail) {
        itemInfo.current = item;
        const chapterRewardItem = item?.chapterInfoItem;
        endingInfoTitle1.current = chapterRewardItem?.endingInfo[0]?.endingName || '';
        endingInfoImage1.current = optimizeImage({
            src: chapterRewardItem?.endingInfo[0]?.endingBigImgUrl || '',
            width: 203.13,
            height: 77.17,
        });

        endingInfoTitle2.current = chapterRewardItem?.endingInfo[1]?.endingName || '';
        endingInfoImage2.current = optimizeImage({
            src: chapterRewardItem?.endingInfo[1]?.endingBigImgUrl || '',
            width: 203.13,
            height: 77.17,
        });

        mask1.current = chapterRewardItem?.endingInfo[0]?.happiness
            ? gacha_card_item_mask_ssr
            : gacha_card_item_mask_sr;
        mask2.current = chapterRewardItem?.endingInfo[1]?.happiness
            ? gacha_card_item_mask_ssr
            : gacha_card_item_mask_sr;

        if (type.current === 'SR' || type.current === 'SSR') {
            endingInfoImageTag.current = gacha_card_item_tag_sr;
            endingInfoDesc.current = '';
        } else {
            endingInfoImageTag.current = gacha_card_item_tag_r;
            endingInfoDesc.current = chapterRewardItem?.chapterDesc;
            endingInfoTitle1.current = '';
            endingInfoImage1.current = '';
            endingInfoTitle2.current = '';
            endingInfoImage2.current = '';
            mask1.current = '';
            mask2.current = '';
        }

        if (!endingInfoTitle1.current) {
            mask1.current = '';
        }
        if (!endingInfoTitle2.current) {
            mask2.current = '';
        }
    }

    function showAnimator(item: LotteryRewardDetail) {
        showInfo(item);
        const animation = createAnimation({
            timingFunction: 'ease-in-out',
        });
        animation.opacity(1).translateX(0).scaleX(1).scaleY(1).step({ duration: cardInfoShowDur });
        setAnimationData(animation.export());
    }

    function hideAnimator() {
        const animation = createAnimation({});
        animation
            .opacity(0)
            .translateX(0)
            .scaleX(0.9)
            .scaleY(0.9)
            .step({ duration: cardHideDur, timingFunction: 'ease-in-out' });
        animation
            .opacity(0)
            .translateX(240)
            .scaleX(0.9)
            .scaleY(0.9)
            .step({ duration: cardResetDur, timingFunction: 'ease-in-out' });
        setAnimationData(animation.export());
        resetTimer.current = setTimeout(() => {
            endingInfoImageTag.current = '';
            endingInfoDesc.current = '';
            endingInfoTitle1.current = '';
            endingInfoImage1.current = '';
            endingInfoTitle2.current = '';
            endingInfoImage2.current = '';
            mask1.current = '';
            mask2.current = '';
        }, cardHideDur);
    }

    useEffect(() => {
        if (detail) {
            type.current = detail?.chapterInfoItem?.chapterLevel || '';
            showAnimator(detail);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [detail]);

    useEffect(() => {
        if (detail) {
            return;
        }

        if (cardsIndex < 0 || cardsIndex === gachaCardsList.length) {
            return;
        }

        const item = gachaCardsList[cardsIndex];
        type.current = item?.chapterInfoItem?.chapterLevel || '';

        if (!item) {
            hideAnimator();
            return;
        }

        if (cardsIndex === 0) {
            showAnimator(item);
        } else if (cardsIndex >= 0) {
            hideAnimator();
            timer.current = setTimeout(() => {
                showAnimator(item);
            }, cardHideDur + cardResetWaittingDur);
        }
        // eslint-disable-next-line consistent-return
        return () => {
            clearTimeout(timer.current);
            clearTimeout(resetTimer.current);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [cardsIndex]);

    const gotoEndingPage = async (index: number) => {
        try {
            const res = await endingDetailApi({
                chapterId: itemInfo.current.chapterInfoItem.chapterId,
            });
            if (!res || res.length <= 0) {
                return;
            }
            const userId = itemInfo.current.chapterInfoItem.roleUserId;
            const endingStr = encodeURIComponent(JSON.stringify(res[0]));
            const ending2Str = encodeURIComponent(res[1] ? JSON.stringify(res[1]) : '');
            const endingId = itemInfo.current.chapterInfoItem.endingInfo[index].endingId;

            let uiState = 2;
            if (res[index].endingCardId === endingId) {
                if (index === 1) {
                    uiState = 3;
                }
            } else if (index === 0) {
                uiState = 3;
            }
            jump2EndingInfo({
                uiState,
                userId,
                ending: endingStr,
                ending2: ending2Str,
                from: null,
            });
        } catch (e) {
            // 错误
        }
    };

    // 抽离公共卡片组件
    const CardItem = ({ image, mask, title }: { image: string; mask: string; title: string }) => (
        <View className="relative w-[203.13px] h-[77.17px] right-[0px] overflow-hidden">
            <View
                style={{
                    backgroundImage: image ? `url(${image})` : 'none',
                }}
                className="absolute w-[194px] h-[64.99px] bottom-[5.9px] right-[3.09px] rounded-tr-[28.5px] rounded-bl-[28.5px] bg-center bg-cover bg-no-repeat"
            />
            {mask && <Image src={mask} className="absolute w-[100%] h-[100%] top-[0] right-[0]" />}
            <View className="absolute w-full h-[23.32px] bottom-[9.1px] right-[12px] flex justify-end items-center">
                <View className="text-right text-[white] text-[15.2px] font-[SourceHanSerifCN-Bold]">
                    {title}
                </View>
            </View>
        </View>
    );

    return (
        <View className="relative w-[100%] h-[100%]">
            <View className="absolute w-[222.95px] h-[225.36px] right-[5px] bottom-[93.9px]">
                <View className="w-full h-full flex justify-center items-center">
                    <View
                        className="relative flex flex-col justify-center items-center opacity-[0] scale-[0.9] translate-x-[240px]"
                        animation={animationData}>
                        {endingInfoImageTag.current && (
                            <View className="relative w-[200px] h-[88px] flex flex-row items-center order-1">
                                <View
                                    className="flex items-center justify-center mt-25 w-[62px] h-[62px] z-[2] pointer-events-auto"
                                    onClick={() => {
                                        jump2Profile({
                                            robotUserId: itemInfo.current?.chapterInfoItem?.roleUserId,
                                            hasAiChapter: null,
                                            fromSource,
                                        });
                                    }}>
                                    <Image
                                        src={gacha_card_info_avatar_bg}
                                        className="absolute w-[62px] h-[62px]"
                                    />
                                    <Image
                                        mode="aspectFill"
                                        src={itemInfo.current?.chapterInfoItem?.roleUserImg || ''}
                                        className=" w-[48px] h-[48px] rounded-full"
                                    />
                                </View>
                                <Image
                                    src={endingInfoImageTag.current}
                                    className="absolute w-[238px] h-[88px] right-[-18px] top-[-3px] z-[1]"
                                />
                            </View>
                        )}

                        {endingInfoDesc.current && (
                            <View className="flex flex-row order-2 mt-[3px]">
                                <View className="text-[28px] text-[white] pr-[6px]">“</View>
                                <View className="text-[12px] text-[white] min-w-[184px] max-w-[223px] mr-[25px] mt-[11px] line-clamp-4 overflow-hidden">
                                    {endingInfoDesc.current}
                                </View>
                            </View>
                        )}

                        {endingInfoImage1.current && (
                            <View
                                className="mt-[-7px] order-2 pointer-events-auto"
                                onClick={() => {
                                    gotoEndingPage(0);
                                }}>
                                <CardItem
                                    image={endingInfoImage1.current}
                                    mask={mask1.current}
                                    title={endingInfoTitle1.current}
                                />
                            </View>
                        )}
                        {endingInfoImage2.current && (
                            <View
                                className="mt-[-7px] order-3 pointer-events-auto"
                                onClick={() => {
                                    gotoEndingPage(1);
                                }}>
                                <CardItem
                                    image={endingInfoImage2.current}
                                    mask={mask2.current}
                                    title={endingInfoTitle2.current}
                                />
                            </View>
                        )}
                    </View>
                </View>
            </View>
        </View>
    );
};

CardInfoDescView.defaultProps = {
    detail: undefined,
};

export default React.memo(CardInfoDescView);
