import React, { useEffect, useRef, useState } from 'react';
import { View, Image } from '@tarojs/components';
import gacha_bottom_img from '@/assets/game/gacha/gacha_bottom_img.png';
import { createAnimation } from '@tarojs/taro';
import useGachaStore from '../../../useGachaStore';
import {
    cardHideDur,
    cardInfoShowDur,
    cardResetDur,
    cardResetWaittingDur,
    LotteryRewardDetail,
} from '../../../const';

const CardBottomExtraInfoView = ({ detail }: { detail?: LotteryRewardDetail }) => {
    const [animationData, setAnimationData] = useState({});
    const gachaCardsList = useGachaStore((state) => state.gachaCardsList);
    const cardsIndex = useGachaStore((state) => state.cardsIndex);
    const timer = useRef<NodeJS.Timeout>();

    function showAnimator() {
        const animation = createAnimation({
            timingFunction: 'ease-in-out',
        });
        animation
            .opacity(0.3)
            .translateX(0)
            .scaleX(1)
            .scaleY(1)
            .step({ duration: cardInfoShowDur });
        setAnimationData(animation.export());
    }

    function hideAnimator() {
        const animation = createAnimation({});
        animation
            .opacity(0)
            .translateX(0)
            .scaleX(0.9)
            .scaleY(0.9)
            .step({ duration: cardHideDur, timingFunction: 'ease-in-out' });
        animation
            .opacity(0)
            .translateX(312)
            .scaleX(0.9)
            .scaleY(0.9)
            .step({ duration: cardResetDur, timingFunction: 'ease-in-out' });
        setAnimationData(animation.export());
    }

    useEffect(() => {
        if (detail) {
            showAnimator();
        }
    }, [detail]);

    useEffect(() => {
        if (detail) {
            return;
        }
        if (cardsIndex < 0 || cardsIndex === gachaCardsList.length) {
            return;
        }

        if (cardsIndex === 0) {
            showAnimator();
        } else if (cardsIndex > 0) {
            hideAnimator();
            timer.current = setTimeout(() => {
                showAnimator();
            }, cardHideDur + cardResetWaittingDur);
        }
        // eslint-disable-next-line consistent-return
        return () => {
            clearTimeout(timer.current);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [cardsIndex]);

    return (
        <View className="relative w-[100%] h-[100%]">
            <View
                className="absolute w-[304.47px] h-[52px] right-[7px] bottom-[21px] opacity-[0] scale-[0.9] translate-x-[312px]"
                animation={animationData}>
                <Image src={gacha_bottom_img} className="w-[304px] h-[52px]" />
            </View>
        </View>
    );
};

CardBottomExtraInfoView.defaultProps = {
    detail: undefined,
};

export default React.memo(CardBottomExtraInfoView);
