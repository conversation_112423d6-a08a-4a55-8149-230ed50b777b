import React, { useEffect, useRef, useState } from 'react';
import { View, Image } from '@tarojs/components';
import gacha_card_new from '@/assets/game/gacha/gacha_card_new.png';
import { createAnimation } from '@tarojs/taro';
import useGachaStore from '../../../useGachaStore';
import {
    cardHideDur,
    LotteryRewardDetail,
    cardResetDur,
    cardInfoNewShowDur,
    cardResetWaittingDur,
    checkDecomposedReward,
} from '../../../const';

const CardNewView = ({ detail }: { detail?: LotteryRewardDetail }) => {
    const [animationData, setAnimationData] = useState({});
    const gachaCardsList = useGachaStore((state) => state.gachaCardsList);
    const cardsIndex = useGachaStore((state) => state.cardsIndex);
    const timer = useRef<NodeJS.Timeout>();

    function showAnimator(item: LotteryRewardDetail) {
        if (!checkDecomposedReward(item)) {
            // 执行进入动效~
            const animation = createAnimation({
                timingFunction: 'ease-in-out',
            });
            animation.opacity(1).scaleX(1).scaleY(1).step({ duration: cardInfoNewShowDur });
            setAnimationData(animation.export());
        }
    }

    function hideAnimator() {
        const animation = createAnimation({
            timingFunction: 'ease-in-out',
        });
        animation.opacity(0).scaleX(0.9).scaleY(0.9).step({ duration: cardResetDur });
        setAnimationData(animation.export());
    }

    useEffect(() => {
        if (detail) {
            showAnimator(detail);
        }
    }, [detail]);

    useEffect(() => {
        if (detail) {
            return;
        }

        if (cardsIndex < 0 || cardsIndex === gachaCardsList.length) {
            return;
        }

        const item = gachaCardsList[cardsIndex];
        if (cardsIndex === 0) {
            showAnimator(item);
        } else if (cardsIndex > 0) {
            hideAnimator();
            timer.current = setTimeout(() => {
                showAnimator(item);
            }, cardHideDur + cardResetWaittingDur);
        }
        // eslint-disable-next-line consistent-return
        return () => {
            clearTimeout(timer.current);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [cardsIndex]);

    return (
        <View className="relative w-[100%] h-[100%]">
            <Image
                src={gacha_card_new}
                className="absolute w-[82.79px] h-[64px] right-[44.21px] top-[50.19px] opacity-[0]  scale-[0.9]"
                animation={animationData}
            />
        </View>
    );
};

CardNewView.defaultProps = {
    detail: undefined,
};

export default React.memo(CardNewView);
