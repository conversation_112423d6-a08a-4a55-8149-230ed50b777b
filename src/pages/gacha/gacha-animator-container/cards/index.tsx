import React, { useCallback, useEffect, useRef } from 'react';
import { View, Image } from '@tarojs/components';
import gacha_result_icon_guide_skip from '@/assets/game/gacha/gacha_result_icon_guide_skip.png';
import { coronaWarnMessage } from '@music/mat-base-h5';
import ErrorBoundary from '@/components/ErrorBoundary';
import CardInfoImgView from './card-img';
import CardBgView from './bg';
import CardBottomExtraInfoView from './bottom-extra-info';
import useGachaStore from '../../useGachaStore';
import {
    cardBgShowDur,
    cardHideDur,
    cardInfoShowDur,
    cardResetWaittingDur,
    LotteryRewardDetail,
} from '../../const';
import CardInfoDescView from './card-info';
import CardNewView from './new';

const CardsView = ({ detail, finish }: { detail?: LotteryRewardDetail; finish: () => void }) => {
    const gachaCardsList = useGachaStore((state) => state.gachaCardsList);
    const cardsIndex = useGachaStore((state) => state.cardsIndex);
    const timer = useRef<NodeJS.Timeout>();
    const nextCardsIndex = useGachaStore((state) => state.nextCardsIndex);

    useEffect(() => {
        if (detail) {
            return;
        }
        timer.current = setTimeout(() => {
            nextCardsIndex();
        }, cardBgShowDur);
        // eslint-disable-next-line consistent-return
        return () => {
            clearTimeout(timer.current);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const canNext = useRef(true);
    useEffect(() => {
        if (detail) {
            return;
        }
        if (cardsIndex === -1) {
            return;
        }
        if (gachaCardsList.length === cardsIndex) {
            coronaWarnMessage('Gacha流程', `CardsView 播放结束,卡面数量:${gachaCardsList.length} `);
            finish();
            return;
        }
        canNext.current = false;
        const timeoutTimer = setTimeout(() => {
            canNext.current = true;
        }, cardInfoShowDur + cardHideDur + cardResetWaittingDur);
        // eslint-disable-next-line consistent-return
        return () => {
            clearTimeout(timeoutTimer);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [cardsIndex]);

    const handleShowNext = useCallback(() => {
        if (!detail && canNext.current) {
            coronaWarnMessage(
                'Gacha流程',
                `CardsView 手动切换下一个卡 当前cardsIndex:${cardsIndex}`
            );
            nextCardsIndex();
        }
    }, [cardsIndex, detail, nextCardsIndex]);

    function handleAnimatorFinish() {
        coronaWarnMessage('Gacha流程', `CardsView 跳过`);
        finish();
    }

    return (
        <View className="relative w-[100%] h-[100%]">
            <View className="absolute w-[100%] h-[100%] top-[0] left-[0]">
                <CardBgView detail={detail} showNext={handleShowNext} />
            </View>
            <View className="flex flex-col absolute w-[100%] h-[100%] top-[0] left-[0]">
                {detail && <View className="relative w-full h-[44px]" />}
                <View className="relative w-full h-full flex items-center">
                    <View className="relative w-[100%] h-[81.82vh]">
                        <View className="absolute w-[100%] h-[100%]">
                            <ErrorBoundary>
                                <CardInfoImgView detail={detail} />
                            </ErrorBoundary>
                        </View>
                        <View className="absolute w-[100%] h-[100%]">
                            <ErrorBoundary>
                                <CardInfoDescView detail={detail} />
                            </ErrorBoundary>
                        </View>
                        <View className="absolute w-[100%] h-[100%]">
                            <ErrorBoundary>
                                <CardNewView detail={detail} />
                            </ErrorBoundary>
                        </View>
                        <View className="absolute w-[100%] h-[100%]">
                            <ErrorBoundary>
                                <CardBottomExtraInfoView detail={detail} />
                            </ErrorBoundary>
                        </View>
                    </View>
                </View>
            </View>
            {!detail && gachaCardsList.length > 1 && (
                <Image
                    className="absolute w-[56px] h-[27px] bottom-[96px] right-[14px] pointer-events-auto"
                    src={gacha_result_icon_guide_skip}
                    onClick={handleAnimatorFinish}
                />
            )}
        </View>
    );
};
CardsView.defaultProps = {
    detail: undefined,
};
export default React.memo(CardsView);
