import React, { useEffect, useRef, useState } from 'react';
import { View, Image, Text } from '@tarojs/components';
import { createAnimation } from '@tarojs/taro';
import gacha_card_info_bg_image from '@/assets/game/gacha/gacha_card_info_bg_image.png';
import gacha_card_info_tag_r from '@/assets/game/gacha/gacha_card_info_tag_r.png';
import gacha_card_info_tag_sr from '@/assets/game/gacha/gacha_card_info_tag_sr.png';
import gacha_card_info_tag_ssr from '@/assets/game/gacha/gacha_card_info_tag_ssr.png';
import gacha_card_info_tag_bg_r from '@/assets/game/gacha/gacha_card_info_tag_bg_r.png';
import gacha_card_info_tag_bg_sr from '@/assets/game/gacha/gacha_card_info_tag_bg_sr.png';
import gacha_card_info_tag_bg_ssr from '@/assets/game/gacha/gacha_card_info_tag_bg_ssr.png';
import gacha_card_info_ssr_tag from '@/assets/game/gacha/gacha_card_info_ssr_tag.png';
import gacha_card_info_sr_tag from '@/assets/game/gacha/gacha_card_info_sr_tag.png';
import gacha_card_info_r_tag from '@/assets/game/gacha/gacha_card_info_r_tag.png';
import gacha_card_info_s_tag from '@/assets/game/gacha/gacha_card_info_s_tag.png';
import { optimizeImage } from '@/utils/image';
import useGachaStore from '../../../useGachaStore';
import {
    cardHideDur,
    LotteryRewardDetail,
    cardInfoShowDur,
    cardResetDur,
    cardResetWaittingDur,
} from '../../../const';
import './index.scss';

const TEXT_SHADOW_MAP = {
    SSR: '2px 2px 4px rgba(255, 132, 0, 1)',
    SR: '3px 3px 6px rgba(31, 27, 255, 1)',
    DEFAULT: '3px 3px 6px rgba(27, 141, 255, 1)',
};
const CardInfoImgView = ({ detail }: { detail?: LotteryRewardDetail }) => {
    const [animationData, setAnimationData] = useState({});
    const gachaCardsList = useGachaStore((state) => state.gachaCardsList);
    const cardsIndex = useGachaStore((state) => state.cardsIndex);

    const viewRef = useRef(null);
    const timer = useRef<NodeJS.Timeout>();
    const chapterImg = useRef(null);
    const chapterTagBg = useRef('');
    const chapterTagImg = useRef('');
    const chapterName = useRef('');
    const rewardType = useRef('');

    function showAnimator(item: LotteryRewardDetail) {
        chapterImg.current = optimizeImage({
            src: item?.chapterInfoItem?.chapterImg || '',
            width: 249,
            height: 648,
        });
        chapterName.current = item?.chapterInfoItem?.chapterName || '';
        const roleName = item?.chapterInfoItem?.roleName || '';
        if (roleName) {
            chapterName.current = `${roleName}·${chapterName.current}`;
        }
        if (chapterName.current?.length > 11) {
            chapterName.current = `${chapterName.current?.slice(0, 12)}`;
        }
        rewardType.current = item?.chapterInfoItem?.chapterLevel || '';
        switch (rewardType.current) {
            case 'SR':
                chapterTagBg.current = gacha_card_info_tag_bg_sr;
                chapterTagImg.current = gacha_card_info_tag_sr;
                break;
            case 'SSR':
                chapterTagBg.current = gacha_card_info_tag_bg_ssr;
                chapterTagImg.current = gacha_card_info_tag_ssr;
                break;
            default:
                chapterTagBg.current = gacha_card_info_tag_bg_r;
                chapterTagImg.current = gacha_card_info_tag_r;
                break;
        }

        const animation = createAnimation({
            timingFunction: 'ease-in-out',
        });
        animation
            .opacity(1)
            .translateX(0)
            .scaleX(0.98)
            .scaleY(0.98)
            .step({ duration: cardInfoShowDur });
        setAnimationData(animation.export());
    }

    function hideAnimator() {
        const animation = createAnimation({});
        animation
            .opacity(0)
            .translateX(146)
            .scaleX(0.7)
            .scaleY(0.7)
            .step({ duration: cardHideDur, timingFunction: 'ease-in-out' });
        animation
            .opacity(0)
            .translateX(-132)
            .scaleX(0.7)
            .scaleY(0.7)
            .step({ duration: cardResetDur, timingFunction: 'ease-in-out' });
        setAnimationData(animation.export());
        setTimeout(() => {
            rewardType.current = '';
        }, cardHideDur);
    }

    useEffect(() => {
        if (detail) {
            showAnimator(detail);
        }
    }, [detail]);

    useEffect(() => {
        if (detail) {
            return;
        }

        if (cardsIndex < 0 || cardsIndex === gachaCardsList.length) {
            return;
        }

        const item = gachaCardsList[cardsIndex];

        if (cardsIndex === 0) {
            showAnimator(item);
        } else if (cardsIndex > 0) {
            hideAnimator();
            timer.current = setTimeout(() => {
                showAnimator(item);
            }, cardHideDur + cardResetWaittingDur);
        }
        // eslint-disable-next-line consistent-return
        return () => {
            clearTimeout(timer.current);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [cardsIndex]);

    return (
        <View className="relative w-[100%] h-[100%]">
            <View
                ref={viewRef}
                className="relative w-[294px] h-[100%] left-[0px] opacity-[0] scale-[0.7] translate-x-[-132px]"
                animation={animationData}>
                <Image
                    src={gacha_card_info_bg_image}
                    className="absolute w-[272px] h-[100%] left-[22px]"
                />
                <View
                    style={{
                        backgroundImage: chapterImg.current ? `url(${chapterImg.current})` : 'none',
                    }}
                    className="vertical-trapezoid-img"
                />
                <View className="absolute w-[222.24px] h-[96px] top-[67.19px] left-[0]">
                    <View className="relative w-[100%] h-[100%]">
                        <View
                            style={{
                                backgroundImage: `url(${chapterTagBg.current})`,
                            }}
                            className="w-[233px] h-[83px] mb-13 bg-center bg-contain bg-no-repeat"
                        />
                        {rewardType.current === 'SSR' && (
                            <View
                                style={{
                                    backgroundImage: `url(${chapterTagImg.current})`,
                                }}
                                className="absolute w-[69px] h-[32px] top-[12px] left-[35px] bg-center bg-cover bg-no-repeat"
                            />
                        )}
                        {rewardType.current === 'SR' && (
                            <View
                                style={{
                                    backgroundImage: `url(${chapterTagImg.current})`,
                                }}
                                className="absolute w-[47px] h-[32px] top-[12px] left-[35px] bg-center bg-cover bg-no-repeat"
                            />
                        )}

                        {rewardType.current === 'R' && (
                            <View
                                style={{
                                    backgroundImage: `url(${chapterTagImg.current})`,
                                }}
                                className="absolute w-[27px] h-[32px] top-[12px] left-[35px] bg-center bg-cover bg-no-repeat"
                            />
                        )}

                        <View
                            style={{
                                textShadow:
                                    TEXT_SHADOW_MAP[rewardType.current] || TEXT_SHADOW_MAP.DEFAULT,
                            }}
                            className="vertical-trapezoid-text">
                            {chapterName.current}
                        </View>
                        {rewardType.current === 'SSR' && (
                            <View>
                                <Image
                                    src={gacha_card_info_s_tag}
                                    className="w-[41px] h-[26px] left-[35px] top-[-10px]"
                                />
                                <Image
                                    src={gacha_card_info_ssr_tag}
                                    className="w-[50px] h-[26px] left-[35px] top-[-10px]"
                                />
                            </View>
                        )}
                        {rewardType.current === 'SR' && (
                            <View>
                                <Image
                                    src={gacha_card_info_s_tag}
                                    className="w-[41px] h-[26px] left-[35px] top-[-10px]"
                                />
                                <Image
                                    src={gacha_card_info_sr_tag}
                                    className="w-[50px] h-[26px] left-[35px] top-[-10px]"
                                />
                            </View>
                        )}
                        {rewardType.current === 'R' && (
                            <Image
                                src={gacha_card_info_r_tag}
                                className="w-[41px] h-[26px] left-[35px] top-[-10px]"
                            />
                        )}
                    </View>
                </View>
            </View>
        </View>
    );
};

CardInfoImgView.defaultProps = {
    detail: undefined,
};

export default React.memo(CardInfoImgView);
