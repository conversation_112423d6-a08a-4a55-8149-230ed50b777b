import React, { useEffect, useState } from 'react';
import { createAnimation } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import bg from '@/assets/game/gacha/gacha_cards_bg.png';
import { debounce } from '@/utils';
import { cardBgShowDur, LotteryRewardDetail } from '../../../const';

const CardBgView = ({
    detail,
    showNext,
}: {
    detail?: LotteryRewardDetail;
    showNext: () => void;
}) => {
    const [animationData, setAnimationData] = useState({});

    function showBgAnimator() {
        const animation = createAnimation({
            timingFunction: 'ease-in-out',
        });
        animation.opacity(1).step({ duration: cardBgShowDur });
        setAnimationData(animation.export());
    }

    useEffect(() => {
        if (detail) {
            showBgAnimator();
        }
    }, [detail]);

    return (
        <View
            className="relative w-[100%] h-[100%]  pointer-events-auto"
            onClick={debounce(() => {
                showNext();
            })}>
            <Image
                src={bg}
                className="absolute left-[0] top-[0] object-cover w-[100%] h-[100%] opacity-[0]"
                animation={animationData}
            />
            {!detail && (
                <View className="absolute left-1/2 transform -translate-x-1/2 inline-block bottom-[60px] text-[12px] text-[white] opacity-[0.5]">
                    点击继续
                </View>
            )}
        </View>
    );
};
CardBgView.defaultProps = {
    detail: undefined,
};
export default React.memo(CardBgView);
