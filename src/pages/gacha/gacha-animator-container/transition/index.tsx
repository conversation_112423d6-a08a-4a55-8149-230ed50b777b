import React, { useCallback } from 'react';
import { View } from '@tarojs/components';
import TransitionVideoView from './video';
import TransitionBackView from './back';

/**
 * 粒子动效层~
 * @returns
 */
const TransitionView = ({ finish }: { finish: () => void }) => {
    const handleFinished = useCallback(() => {
        finish();
    }, [finish]);

    return (
        <View className="relative w-[100%] h-[100%]">
            <View className="absolute w-[100%] h-[100%] top-[0] left-[0]">
                <TransitionVideoView finish={handleFinished} />
            </View>
            <View className="absolute w-[100%] h-[100%] top-[0] left-[0]">
                <TransitionBackView finish={handleFinished} />
            </View>
        </View>
    );
};
export default React.memo(TransitionView);
