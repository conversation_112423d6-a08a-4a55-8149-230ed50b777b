import React from 'react';
import { View, Image } from '@tarojs/components';
import gacha_result_icon_guide_skip from '@/assets/game/gacha/gacha_result_icon_guide_skip.png';
import { coronaWarnMessage } from '@music/mat-base-h5';
import useGachaStore from '../../../useGachaStore';
/**
 * 返回页~
 * @returns
 */
const TransitionBackView = ({ finish }: { finish: () => void }) => {
    const needShowTransitionBackFunc = useGachaStore((state) => state.needShowTransitionBackFunc);

    function handleFinished() {
        coronaWarnMessage('Gacha流程', ` 引导动画(TransitionBackView) 跳过`);
        finish();
    }

    // 点击后，才展示 跳过按钮~  再点击 跳过，才真的跳过~
    return (
        <View className="relative w-[100%] h-[100%]">
            {needShowTransitionBackFunc && (
                <Image
                    className="absolute w-[56px] h-[27px] bottom-[96px] right-[14px] pointer-events-auto"
                    src={gacha_result_icon_guide_skip}
                    onClick={handleFinished}
                />
            )}
        </View>
    );
};
export default React.memo(TransitionBackView);
