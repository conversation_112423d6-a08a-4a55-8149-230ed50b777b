import React, { useEffect, useState } from 'react';
import { View } from '@tarojs/components';
import { NormalVideo } from '@music/ct-animation';
import { createAnimation } from '@tarojs/taro';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { GACAG_ANIMATOR } from '../../../const';
import useGachaStore from '../../../useGachaStore';
/**
 * 粒子动效层~
 * @returns
 */
const TransitionVideoView = ({ finish }: { finish: () => void }) => {
    const [animationData, setAnimationData] = useState({});
    const gachaResp = useGachaStore((state) => state.gachaResp);
    const setNeedShowTransitionBackFunc = useGachaStore(
        (state) => state.setNeedShowTransitionBackFunc
    );
    function handleFinished() {
        coronaWarnMessage('Gacha流程', `TransitionVideoView 视频播放完成`);
        finish();
    }
    function handleClick() {
        setNeedShowTransitionBackFunc(true);
    }

    const videoResourceByLevel = (): string => {
        if (gachaResp.find((item) => item.chapterInfoItem.chapterLevel === 'SSR')) {
            return GACAG_ANIMATOR.ANIMATOR_BG_RESULT_GUIDE_SSR;
        }
        if (gachaResp.find((item) => item.chapterInfoItem.chapterLevel === 'SR')) {
            return GACAG_ANIMATOR.ANIMATOR_BG_RESULT_GUIDE_SR;
        }
        return GACAG_ANIMATOR.ANIMATOR_BG_RESULT_GUIDE_R;
    };

    function startAnimator() {
        const animation = createAnimation({});
        animation.opacity(1).step({ duration: 500, timingFunction: 'ease-in-out' });
        setAnimationData(animation.export());
    }

    useEffect(() => {
        startAnimator();
    }, []);

    return (
        <View className="relative w-[100%] h-[100%] pointer-events-auto" onClick={handleClick}>
            <View className="w-[100%] h-[100%] opacity-[0]" animation={animationData}>
                <NormalVideo
                    src={videoResourceByLevel()}
                    autoplay
                    sizeMode="cover"
                    onComplete={handleFinished}
                    style={{ width: '100%', height: '100%' }}
                />
            </View>
        </View>
    );
};
export default React.memo(TransitionVideoView);
