import React, { useEffect, useRef, useState } from 'react';
import classnames from 'classnames';
import { View, Image } from '@tarojs/components';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { optimizeImage } from '@/utils/image';
import gacha_result_bg_r from '@/assets/game/gacha/gacha_result_bg_r.png';
import gacha_result_bg_sr from '@/assets/game/gacha/gacha_result_bg_sr.png';
import gacha_result_bg_ssr from '@/assets/game/gacha/gacha_result_bg_ssr.png';
import gacha_result_tag_r from '@/assets/game/gacha/gacha_result_tag_r.png';
import gacha_result_tag_sr from '@/assets/game/gacha/gacha_result_tag_sr.png';
import gacha_result_tag_ssr from '@/assets/game/gacha/gacha_result_tag_ssr.png';
import gacha_result_line_sr from '@/assets/game/gacha/gacha_result_line_sr.png';
import gacha_result_line_r from '@/assets/game/gacha/gacha_result_line_r.png';
import gacha_result_line_ssr from '@/assets/game/gacha/gacha_result_line_ssr.png';
import gacha_result_clip from '@/assets/game/gacha/gacha_result_clip.png';
import gacha_card_coversion_tag_img from '@/assets/game/gacha/gacha_card_coversion_tag_img.png';
import useLotteryResultStore from '@/pages/gacha/store/useLotteryResultStore';
import { jump2ChapterInfo } from '@/router';
import { checkDecomposedReward, LotteryRewardDetail } from '../../../../const';

import './index.scss';

/**
 * 抽卡结果页~
 */

const AnimationDuration = {
    first: 800,
    second: 1000,
    last: 1250,
};
const GachaResultItemView = ({
    item,
    handleClose,
}: {
    item: LotteryRewardDetail;
    handleClose: () => void;
}) => {
    const [isTransitionStart, setIsTransitionStart] = useState(false);
    const gainFirstEffectEnd = useLotteryResultStore((state) => state.gainFirstEffectEnd);
    const [isFirstAnimationEnd, setIsFirstAnimationEnd] = useState(false);
    const cardTopRef = useRef<HTMLDivElement>(null);
    const cardClipRef = useRef<HTMLDivElement>(null);
    const [clipShow, setClipShow] = useState(false);
    const firstEffectRef = useRef<NodeJS.Timeout | null>(null);
    const secondEffectRef = useRef<NodeJS.Timeout | null>(null);
    const lastEffectRef = useRef<NodeJS.Timeout | null>(null);
    const isTransition = checkDecomposedReward(item) && item.chapterInfoItem.chapterLevel !== 'R';

    function handleItemClick(e: TouchEvent) {
        try {
            e.stopPropagation();
            const chapterStr = encodeURIComponent(JSON.stringify(item?.chapterInfoItem));
            jump2ChapterInfo({
                chapterId: item?.chapterInfoItem?.chapterId,
                robotUserId: item?.chapterInfoItem?.roleUserId,
                chapter: chapterStr,
                from: null,
            });
        } catch (err) {
            coronaWarnMessage('handleItemClick', {
                error: (err || {}).stack || err,
            });
        }
    }

    let itemBg = gacha_result_bg_r;
    const itemImg = optimizeImage({
        src: item?.rewardImg,
        width: 66,
        height: 63,
    });

    const itemCardImg = item?.decomposedReward?.[0]?.lotteryRewardDetail?.rewardImg;
    let itemNum = 0;
    const decomposedReward = item.decomposedReward;
    if (decomposedReward) {
        itemNum =
            Array.isArray(decomposedReward) && decomposedReward.length > 0
                ? decomposedReward[0].lotteryRewardDetail?.number || 0
                : 0;
    }
    let itemTagImg = null;
    let itemLineImg = null;
    const rewardLevel = item?.chapterInfoItem?.chapterLevel;
    const title = (
        isTransition
            ? item?.decomposedReward?.[0]?.lotteryRewardDetail?.rewardName
            : item?.rewardName
    )?.slice(0, 7);
    const itemClipImg = gacha_result_clip;

    const roleName = (item?.chapterInfoItem?.roleName ?? '')?.slice(0, 5);
    // 通用碎片
    switch (rewardLevel) {
        case '':
            itemBg = gacha_result_bg_r;
            itemTagImg = null;
            itemLineImg = null;
            break;
        case 'R':
            itemBg = gacha_result_bg_r;
            itemLineImg = gacha_result_line_r;
            itemTagImg = gacha_result_tag_r;
            break;
        case 'SR':
            itemBg = gacha_result_bg_sr;
            itemLineImg = gacha_result_line_sr;
            itemTagImg = gacha_result_tag_sr;
            break;
        case 'SSR':
            itemBg = gacha_result_bg_ssr;
            itemLineImg = gacha_result_line_ssr;
            itemTagImg = gacha_result_tag_ssr;
            break;
        default:
            break;
    }

    useEffect(() => {
        clearTimeout(firstEffectRef.current);
        if (isTransition) {
            firstEffectRef.current = setTimeout(() => {
                clearTimeout(secondEffectRef.current);
                secondEffectRef.current = setTimeout(() => {
                    setIsFirstAnimationEnd(true);
                }, AnimationDuration.second);
                setIsTransitionStart(true);
                clearTimeout(lastEffectRef.current);
                lastEffectRef.current = setTimeout(() => {
                    setClipShow(true);
                }, AnimationDuration.last);
            }, AnimationDuration.first);
        } else {
            firstEffectRef.current = setTimeout(() => {
                setIsTransitionStart(true);
            }, AnimationDuration.first);
        }

        return () => {
            clearTimeout(firstEffectRef.current);
            clearTimeout(secondEffectRef.current);
            clearTimeout(lastEffectRef.current);
        };
    }, [gainFirstEffectEnd, isTransition]);

    return (
        <React.Fragment>
            <View className="w-[74px] h-[107px] relative mb-[4px]">
                {isTransitionStart && (
                    <View
                        className="relative flex flex-col w-[74px] h-[107px]"
                        onClick={(e) => handleItemClick(e as any)}>
                        <View
                            className={classnames('absolute w-[74px] h-[72px] top-[0] left-[0]', {
                                'fade-scale-reverse-animation': !isTransition,
                                'fade-scale-first-animation-i':
                                    isTransition && !isFirstAnimationEnd,
                                'fade-scale-first-animation-ii':
                                    isTransition && isFirstAnimationEnd,
                            })}
                            ref={cardTopRef}>
                            <View className="relative w-[74px] h-[72px]">
                                {/* 卡片背景 */}
                                {itemBg && !clipShow && (
                                    <Image
                                        src={itemBg}
                                        className="absolute w-[74px] h-[72px] -translate-y-[1px]"
                                    />
                                )}
                                {/* 转圈动画 */}
                                {itemLineImg && (
                                    <Image
                                        src={itemLineImg}
                                        className="absolute w-[90px] h-[90px] top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] z-[2]"
                                    />
                                )}
                                {/* 卡片 */}
                                {itemImg && (
                                    <View
                                        style={{
                                            backgroundImage: itemImg ? `url(${itemImg})` : 'none',
                                        }}
                                        className="absolute rounded-[15px] w-[66px] h-[63px] top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] z-[1] bg-center bg-cover bg-no-repeat"
                                    />
                                )}
                                {/* 卡片标签 */}
                                {itemTagImg && (
                                    <Image
                                        src={itemTagImg}
                                        className="absolute w-[28px] h-[16.28px] top-[-2px] right-[-10px] z-[2]"
                                    />
                                )}
                                {/* 数量~ */}
                                {item?.rewardNum > 1 && (
                                    <View className="absolute rounded-[20px] bg-[#2C334F] text-[11px] w-[32px] h-[20px] bottom-[0px] right-[0px] text-[white] flex items-center justify-center">
                                        {item?.rewardNum}
                                    </View>
                                )}
                            </View>
                        </View>
                        <View className="absolute w-[74px] h-[28px] flex flex-col items-center bottom-[0] left-[0]">
                            <View className="text-[12px] h-[14px] text-white text-center">
                                {isTransition ? '剧情' : roleName}
                            </View>
                            {title && (
                                <View className="text-[12px] h-[14px] text-white text-center ">
                                    {title}
                                </View>
                            )}
                        </View>
                    </View>
                )}
                {clipShow && isTransition && (
                    <View className="absolute w-[66px] h-[90px] top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] z-[1]">
                        {/* 碎片 */}
                        {itemClipImg && (
                            <Image
                                src={`${itemClipImg}?${item.id}`}
                                key={`clip-${item?.id}`}
                                className="absolute w-[66px] h-[66px] scale-[2.2] transform-gpu top-[-3px] left-[1px] z-[2]"
                            />
                        )}
                        {itemCardImg && (
                            <View
                                ref={cardClipRef}
                                className="fade-scale-reverse-animation absolute top-[25px] left-[50%] translate-x-[-50%] z-[1]">
                                {/* 卡片 */}
                                <Image
                                    src={itemCardImg}
                                    className="absolute w-[66px] h-[66px] top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] z-[1]"
                                />
                            </View>
                        )}
                        {/* 转换~ */}
                        {isTransition && (
                            <Image
                                src={gacha_card_coversion_tag_img}
                                className="absolute w-[32px] h-[17px] bottom-[31px] z-[2]"
                            />
                        )}
                        {/* 数量~ */}
                        {itemNum >= 1 && (
                            <View className="absolute rounded-[20px] z-[2] bg-[#2C334F75] w-[32px] h-[17px] text-[11px] leading-[17px]  bottom-[31px] right-[0px] text-[white] flex items-center justify-center">
                                {itemNum}
                            </View>
                        )}
                    </View>
                )}
            </View>
        </React.Fragment>
    );
};
export default React.memo(GachaResultItemView);
