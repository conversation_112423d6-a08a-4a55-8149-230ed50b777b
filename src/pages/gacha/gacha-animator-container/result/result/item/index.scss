.fade-scale-positive-animation {
    animation: fadeAndScalePositive 500ms ease-out forwards;
    animation-delay: 40ms;
}

.fade-scale-reverse-animation {
    animation: fadeAndScaleReverse 500ms ease-out forwards;
}

.fade-scale-first-animation {
    animation: fadeAndScaleFirst 500ms ease-out forwards;
}

.fade-scale-first-animation-i {
    animation: fadeAndScaleFirstI 500ms ease-out forwards;
}

.fade-scale-first-animation-ii {
    animation: fadeAndScaleFirstII 500ms ease-out forwards;
}

// 转换后的卡片：100%-0%的缩放，透明度100%-0%
@keyframes fadeAndScalePositive {
    0% {
        opacity: 1;
        transform: scale(1);
    }

    100% {
        opacity: 0;
        transform: scale(0.7);
    }
}
// 非转换剧情卡出现：70%-100%的缩放，透明度0%-100%
@keyframes fadeAndScaleReverse {
    0% {
        opacity: 0;
        transform: scale(0.7);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeAndScaleFirstI {
    0% {
        opacity: 0;
        transform: scale(0.7);
    }

    50% {
        transform: scale(1.2);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeAndScaleFirstII {
    0% {
        opacity: 1;
        transform: scale(1);
    }

    100% {
        opacity: 0;
        transform: scale(0.7);
    }
}
