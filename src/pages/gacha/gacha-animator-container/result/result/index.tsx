import React, { useEffect, useState } from 'react';
import { getSystemInfoSync } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { ResponsiveGrid } from '@/components';
import ErrorBoundary from '@/components/ErrorBoundary';
import GachaResultItemView from './item';
import useGachaStore from '../../../useGachaStore';
import { LotteryRewardDetail } from '../../../const';

/**
 * 抽卡结果页~
 * @param param0
 * @returns
 */
const GachaResultView = ({ finish }: { finish: () => void }) => {
    const [resultList, setResultList] = useState<LotteryRewardDetail[]>([]);

    const gachaResp = useGachaStore((state) => state.gachaResp);

    const rootFontSize = getSystemInfoSync().screenWidth / 18.75;

    const isSingleLottery = gachaResp.length === 1;

    function handleClose() {
        finish();
    }

    useEffect(() => {
        // 监听 store 中的数据~
        if (gachaResp.length === 0) {
            handleClose();
            return;
        }
        setResultList(gachaResp);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [gachaResp]);

    function Result() {
        return (
            <View className="relative w-[100%] h-[100%]" onClick={handleClose}>
                <View className="absolute w-[100%] h-[100%] top-[0] left-[0]" />
                <View
                    className="absolute w-[100%] h-[100%] top-[0] left-[0] flex flex-col pointer-events-auto"
                    style={{
                        paddingBottom: 'var(--safearea-height)',
                        marginTop: isSingleLottery ? `${80 / rootFontSize}rem` : '0',
                    }}>
                    <View className="flex-none mt-[240px]">
                        <View className="w-[100%] h-[100%]">
                            <ResponsiveGrid
                                items={resultList}
                                renderItem={(item: LotteryRewardDetail) => (
                                    <ErrorBoundary>
                                        <GachaResultItemView
                                            item={item}
                                            handleClose={handleClose}
                                        />
                                    </ErrorBoundary>
                                )}
                            />
                        </View>
                    </View>
                    <View className="relative flex-[2] min-h-0 felx items-center">
                        <View className="absolute w-[100%] flex flex-col items-center bottom-[7px]">
                            <View className="text-[12px] h-[14px] text-white text-center opacity-[0.4]">
                                点击空白处返回
                            </View>
                        </View>
                    </View>
                </View>
            </View>
        );
    }

    return (
        <View className="relative w-[100%] h-[100%]">
            <Result />
        </View>
    );
};
export default React.memo(GachaResultView);
