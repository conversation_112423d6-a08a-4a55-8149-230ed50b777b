import React, { useCallback } from 'react';
import { View } from '@tarojs/components';
import { AlphaVideo } from '@music/ct-animation';
import { coronaWarnMessage } from '@music/mat-base-h5';

/**
 * 抽卡结果页 背景动效~
 * @param param0
 * @returns
 */
const GachaResultBgAnimatorView = (
    {
        src,
        onComplete,
        loop,
    }: {
        src: string;
        onComplete?: () => void;
        loop?: boolean;
    } = {
        src: '',
        onComplete: () => {},
        loop: false,
    }
) => {
    const handleErr = useCallback(
        (error: string) => {
            coronaWarnMessage(
                'Gacha-Alpha-Err',
                `结果页(GachaResultBgAnimatorView) loop：${loop} error:${error}`
            );
        },
        [loop]
    );

    return (
        <View className="relative w-[100%] h-[100%] opacity-[1]">
            <AlphaVideo
                src={src}
                onComplete={onComplete}
                loop={loop}
                sizeMode="cover"
                style={{ width: '100%', height: '100%' }}
                onError={handleErr}
                onDataError={handleErr}
            />
        </View>
    );
};
export default React.memo(GachaResultBgAnimatorView);
