import React, { useCallback, useEffect, useRef } from 'react';
import { View } from '@tarojs/components';
import GachaResultBgAnimatorView from './pre-video';
import Result from './result';
import { GACAG_ANIMATOR } from '../../const';
import useLotteryResultStore from '../../store/useLotteryResultStore';
import useGachaStore from '../../useGachaStore';

/**
 * 抽卡结果页~
 * @param param0
 * @returns
 */

const LoopBGView = React.memo(
    ({
        loopVideoRef,
        isSingleLottery,
    }: {
        loopVideoRef: React.RefObject<HTMLVideoElement>;
        isSingleLottery: boolean;
    }) => {
        return (
            <View
                className="absolute opacity-0  w-[100%] h-[100%] pointer-events-none z-[1]"
                ref={loopVideoRef}>
                <GachaResultBgAnimatorView
                    src={
                        GACAG_ANIMATOR[
                            isSingleLottery
                                ? 'ANIMATOR_RESULT_BG_LOOP_SINGLE'
                                : 'ANIMATOR_RESULT_BG_LOOP'
                        ]
                    }
                    loop
                />
            </View>
        );
    }
);
const GachaResultView = ({ finish }: { finish: () => void }) => {
    const startVideoRef = useRef<HTMLVideoElement>(null);
    const loopVideoRef = useRef<HTMLVideoElement>(null);
    const gachaResp = useGachaStore((state) => state.gachaResp);
    const resetGainFirstEffect = useLotteryResultStore((state) => state.resetGainFirstEffect);

    const onComplete = useCallback(() => {
        startVideoRef.current.style.display = 'none';
        loopVideoRef.current.style.opacity = '1';
    }, []);

    const isSingleLottery = gachaResp.length === 1;

    useEffect(() => {
        return () => {
            resetGainFirstEffect();
        };
    }, [resetGainFirstEffect]);

    return (
        <View className="relative w-[100%] h-[100%]">
            <View
                className="absolute  w-[100%] h-[100%] pointer-events-none z-[2] opacity-[1]"
                ref={startVideoRef}>
                <GachaResultBgAnimatorView
                    src={
                        GACAG_ANIMATOR[
                            isSingleLottery
                                ? 'ANIMATOR_RESULT_BG_START_SINGLE'
                                : 'ANIMATOR_RESULT_BG_START'
                        ]
                    }
                    onComplete={onComplete}
                    loop={false}
                />
            </View>
            <LoopBGView loopVideoRef={loopVideoRef} isSingleLottery={isSingleLottery} />
            <View className="absolute  w-[100%] h-[100%] pointer-events-none z-[3]">
                <Result finish={finish} />
            </View>
        </View>
    );
};
export default React.memo(GachaResultView);
