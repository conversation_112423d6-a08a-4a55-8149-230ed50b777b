export const infoWholeJsonStr = `{
    "rewards": [
        {
            "rewardType": 2,
            "resourceId": "202503041818358524854700323000",
            "rewardName": "HKH测试剧情1",
            "number": 1,
            "rewardImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809790308/c2ee/91d4/63ee/ab67c454a7912d382816f9c656a79cff.png",
            "chapterInfoItem": {
                "chapterId": "202503041818358524854700323000",
                "roleId": "202501091736446244854700114000",
                "chapterImg": "https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/58712090272/509f/fa0e/9935/b21b32b31ea145216d3313b7dee5ba96.png",
                "chapterName": "HKH测试剧情1",
                "chapterLevel": "SSR",
                "endingInfo": [
                    {
                        "endingName": "郑智测试剧情01-结局01",
                        "endingBigImgUrl": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809303483/c82c/1620/cfbf/2c2799b6bc7f8eaa85c7275ba511d57a.png",
                        "happiness": true
                    },
                    {
                        "endingName": "郑智测试剧情01-结局02",
                        "endingBigImgUrl": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809303483/c82c/1620/cfbf/2c2799b6bc7f8eaa85c7275ba511d57a.png",
                        "happiness": true
                    }
                ]
            },
            "decomposedReward": [

            ]
        },
        {
            "rewardType": 2,
            "resourceId": "202503041818358524854700323000",
            "rewardName": "HKH测试剧情1",
            "number": 1,
            "rewardImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809790308/c2ee/91d4/63ee/ab67c454a7912d382816f9c656a79cff.png",
            "chapterInfoItem": {
                "chapterId": "202503041818358524854700323000",
                "roleId": "202501091736446244854700114000",
                "chapterImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809793720/ec86/7fce/5d44/e93701ea9a114400c938541e40c19f92.png",
                "chapterName": "HKH测试剧情1",
                "chapterLevel": "SR",
                "endingInfo": [
                    {
                        "endingName": "郑智测试剧情01-结局01",
                        "endingBigImgUrl": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809303483/c82c/1620/cfbf/2c2799b6bc7f8eaa85c7275ba511d57a.png",
                        "happiness": true
                    }
                ]
            },
            "firstReceive":false,
            "decomposedReward": [

            ]
        },
        {
            "rewardType": 2,
            "resourceId": "202503041818358524854700323000",
            "rewardName": "HKH测试剧情1",
            "number": 1,
            "rewardImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809790308/c2ee/91d4/63ee/ab67c454a7912d382816f9c656a79cff.png",
            "chapterInfoItem": {
                "chapterId": "202503041818358524854700323000",
                "roleId": "202501091736446244854700114000",
                "chapterImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809793720/ec86/7fce/5d44/e93701ea9a114400c938541e40c19f92.png",
                "chapterName": "HKH测试剧情1",
                "chapterLevel": "R",
                "endingInfo": [

                ]
            },
            "firstReceive":true,
            "decomposedReward": [
            ]
        },
        {
            "rewardType": 2,
            "resourceId": "202503041818358524854700323000",
            "rewardName": "HKH测试剧情1",
            "number": 1,
            "rewardImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809790308/c2ee/91d4/63ee/ab67c454a7912d382816f9c656a79cff.png",
            "chapterInfoItem": {
                "chapterId": "202503041818358524854700323000",
                "roleId": "202501091736446244854700114000",
                "chapterImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809793720/ec86/7fce/5d44/e93701ea9a114400c938541e40c19f92.png",
                "chapterName": "HKH测试剧情1",
                "chapterLevel": "SSR",
                "endingInfo": [
                    {
                        "endingName": "郑智测试剧情01-结局01",
                        "endingBigImgUrl": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809303483/c82c/1620/cfbf/2c2799b6bc7f8eaa85c7275ba511d57a.png",
                        "happiness": true
                    },
                    {
                        "endingName": "郑智测试剧情01-结局02",
                        "endingBigImgUrl": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809303483/c82c/1620/cfbf/2c2799b6bc7f8eaa85c7275ba511d57a.png",
                        "happiness": true
                    }
                ]
            },
            "firstReceive":false,
            "decomposedReward": [

            ]
        },
        {
            "rewardType": 2,
            "resourceId": "202503041818358524854700323000",
            "rewardName": "HKH测试剧情1",
            "number": 1,
            "rewardImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809790308/c2ee/91d4/63ee/ab67c454a7912d382816f9c656a79cff.png",
            "chapterInfoItem": {
                "chapterId": "202503041818358524854700323000",
                "roleId": "202501091736446244854700114000",
                "chapterImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809793720/ec86/7fce/5d44/e93701ea9a114400c938541e40c19f92.png",
                "chapterName": "HKH测试剧情1",
                "chapterLevel": "SR",
                "endingInfo": [
                    {
                        "endingName": "郑智测试剧情01-结局01",
                        "endingBigImgUrl": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809303483/c82c/1620/cfbf/2c2799b6bc7f8eaa85c7275ba511d57a.png",
                        "happiness": true
                    }
                ]
            },
            "firstReceive":false,
            "decomposedReward": [

            ]
        },
        {
            "rewardType": 2,
            "resourceId": "202501142004146284854700315004",
            "rewardName": "郑智测试剧情01",
            "number": 1,
            "rewardImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809270610/f14e/5f44/96bb/4124beb1266c71d213393726e526aa0b.png",
            "chapterInfoItem": {
                "chapterId": "202501142004146284854700315004",
                "roleId": "202501091736446244854700114000",
                "chapterImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809270611/f5bb/93d3/fe26/de7c3c63be6d3efc5360a96f31b6736d.png",
                "chapterName": "郑智测试剧情01",
                "chapterLevel": "R",
                "endingInfo": [

                ]
            },
            "firstReceive":false,
            "decomposedReward": [
                {
                    "lotteryRewardDetail": {
                        "rewardType": 3,
                        "resourceId": "1014230",
                        "rewardName": "通用碎片",
                        "number": 10,
                        "rewardImg": "http://p5.music.126.net/obj/KS_DoQ5ewpvDphA/414809217626/b49d/2043/7217/3e6890ad196805a21c37fdbe27d96740.png",
                        "chapterInfoItem": null,
                        "decomposedReward": null
                    }
                }
            ]
        },
        {
            "rewardType": 2,
            "resourceId": "202503041818358524854700323000",
            "rewardName": "HKH测试剧情1",
            "number": 1,
            "rewardImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809790308/c2ee/91d4/63ee/ab67c454a7912d382816f9c656a79cff.png",
            "chapterInfoItem": {
                "chapterId": "202503041818358524854700323000",
                "roleId": "202501091736446244854700114000",
                "chapterImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809793720/ec86/7fce/5d44/e93701ea9a114400c938541e40c19f92.png",
                "chapterName": "HKH测试剧情1",
                "chapterLevel": "SSR",
                "endingInfo": [
                    {
                        "endingName": "郑智测试剧情01-结局01",
                        "endingBigImgUrl": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809303483/c82c/1620/cfbf/2c2799b6bc7f8eaa85c7275ba511d57a.png",
                        "happiness": true
                    },
                    {
                        "endingName": "郑智测试剧情01-结局02",
                        "endingBigImgUrl": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809303483/c82c/1620/cfbf/2c2799b6bc7f8eaa85c7275ba511d57a.png",
                        "happiness": true
                    }
                ]
            },
            "firstReceive":false,
            "decomposedReward": [

            ]
        },
        {
            "rewardType": 2,
            "resourceId": "202503041818358524854700323000",
            "rewardName": "HKH测试剧情1",
            "number": 1,
            "rewardImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809790308/c2ee/91d4/63ee/ab67c454a7912d382816f9c656a79cff.png",
            "chapterInfoItem": {
                "chapterId": "202503041818358524854700323000",
                "roleId": "202501091736446244854700114000",
                "chapterImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809793720/ec86/7fce/5d44/e93701ea9a114400c938541e40c19f92.png",
                "chapterName": "HKH测试剧情1",
                "chapterLevel": "SR",
                "endingInfo": [
                    {
                        "endingName": "郑智测试剧情01-结局011",
                        "endingBigImgUrl": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809303483/c82c/1620/cfbf/2c2799b6bc7f8eaa85c7275ba511d57a.png",
                        "happiness": true
                    }
                ]
            },
            "firstReceive":false,
            "decomposedReward": [

            ]
        },
        {
            "rewardType": 2,
            "resourceId": "202503041818358524854700323000",
            "rewardName": "HKH测试剧情1",
            "number": 1,
            "rewardImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809790308/c2ee/91d4/63ee/ab67c454a7912d382816f9c656a79cff.png",
            "chapterInfoItem": {
                "chapterId": "202503041818358524854700323000",
                "roleId": "202501091736446244854700114000",
                "chapterImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809793720/ec86/7fce/5d44/e93701ea9a114400c938541e40c19f92.png",
                "chapterName": "HKH测试剧情1",
                "chapterLevel": "R",
                "endingInfo": [

                ]
            },
            "firstReceive":false,
            "decomposedReward": [
                {
                    "lotteryRewardDetail": {
                        "rewardType": 3,
                        "resourceId": "1014230",
                        "rewardName": "通用碎片",
                        "number": 3,
                        "rewardImg": "http://p5.music.126.net/obj/KS_DoQ5ewpvDphA/414809217626/b49d/2043/7217/3e6890ad196805a21c37fdbe27d96740.png",
                        "chapterInfoItem": null,
                        "decomposedReward": null
                    }
                }
            ]
        },
        {
            "rewardType": 2,
            "resourceId": "202503041818358524854700323000",
            "rewardName": "HKH测试剧情1",
            "number": 1,
            "rewardImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809790308/c2ee/91d4/63ee/ab67c454a7912d382816f9c656a79cff.png",
            "chapterInfoItem": {
                "chapterId": "202503041818358524854700323000",
                "roleId": "202501091736446244854700114000",
                "chapterImg": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809793720/ec86/7fce/5d44/e93701ea9a114400c938541e40c19f92.png",
                "chapterName": "HKH测试剧情1",
                "chapterLevel": "SSR",
                "endingInfo": [
                    {
                        "endingName": "郑智测试剧情01-结局011",
                        "endingBigImgUrl": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809303483/c82c/1620/cfbf/2c2799b6bc7f8eaa85c7275ba511d57a.png",
                        "happiness": true
                    },
                    {
                        "endingName": "郑智测试剧情01-结局021",
                        "endingBigImgUrl": "http://p6.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809303483/c82c/1620/cfbf/2c2799b6bc7f8eaa85c7275ba511d57a.png",
                        "happiness": true
                    }
                ]
            },
            "firstReceive":false,
            "decomposedReward": [

            ]
        }
    ]
}`;

export const infoJsonDefault = `{"chapterId":"202504172026281834854700336000","roleId":"202501151538138854854700118004","chapterImg":"http://p5.music.126.net/obj/LSnCvQ4FwpzDoBQ/414809328054/e19f/d1d3/d661/e6df5a480842d89811c1f87ae2e2fccb.png","chapterName":"QA2号剧情777892","chapterLevel":"SR",
        "endingInfo":[{
        "endingId": "202504222027347694854700731003",
        "endingName": "黎川结局06",
        "endingBigImgUrl": "http://p5.music.126.net/obj/LSnCvQ4FwpzDoBQ/414811600024/f578/b92f/f352/b3667c2b8522dd5b6e5d8922fcbb2979.png",
        "happiness": true
      }]}`;
