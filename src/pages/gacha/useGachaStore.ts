import { create } from 'zustand';
import { showToast } from '@tarojs/taro';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { exchangeInfoApi } from './gachaApi';
import { LotteryCardInfoVo, LotteryRewardDetail } from './const';

interface GachaState {
    needShowTransitionBackFunc: boolean; // 抽卡结果 前置视频中 跳过按钮~
    setNeedShowTransitionBackFunc: (value: boolean) => void;
    currentShowLoopIndex: number;
    setCurrentShowLoopIndex: (value: number) => void;
    glodAccount: number;
    setGlodeAccount: (value: number) => void;
    exchangeInfoResp: LotteryCardInfoVo;
    exchangeInfoFatch: () => Promise<void>;
    cardsIndex: number;
    nextCardsIndex: () => void;
    gachaCardsList: LotteryRewardDetail[];
    gachaResp: LotteryRewardDetail[];
    setGachaResp: (value: LotteryRewardDetail[]) => void;
    resetGachaQueue: () => void;
    isFetchGachaLoading: boolean;
    gachaCount: number;
    gachaClicked: boolean;
    gachaFreeClicked: boolean;
    setGachaClick: (value: number) => void;
    setGachaFreeClick: (value: number) => void;
    resetGachaClick: () => void;
    resetGachaFreeClick: () => void;
}

const useGachaStore = create<GachaState>((set, get) => {
    return {
        needShowTransitionBackFunc: false,
        setNeedShowTransitionBackFunc: (value) => set({ needShowTransitionBackFunc: value }),
        currentShowLoopIndex: 0,
        setCurrentShowLoopIndex: (value) => {
            set({ currentShowLoopIndex: value });
        },
        glodAccount: 0,
        setGlodeAccount: (value) => {
            set({ glodAccount: value });
        },
        exchangeInfoResp: null,
        exchangeInfoFatch: async () => {
            try {
                const res = await exchangeInfoApi('chapter_card');
                set({
                    exchangeInfoResp: res,
                });
            } catch (err) {
                coronaWarnMessage('Gacha流程', `exchangeInfoApi 接口：${JSON.stringify(err)}`);
                showToast({
                    title: err.message,
                    icon: 'none',
                });
            }
        },
        cardsIndex: -1,
        nextCardsIndex: () => {
            set((state) => ({
                cardsIndex: state.cardsIndex + 1,
            }));
        },
        gachaCardsList: [],
        gachaResp: [],
        setGachaResp: (value) => {
            const deepUpdate = value.map((item) => ({
                id: item.resourceId,
                ...item,
            }));
            const formatValue = deepUpdate.filter((item) => {
                return item.rewardType === 2;
            });
            set({ gachaCardsList: formatValue, gachaResp: deepUpdate });
            get().exchangeInfoFatch();
        },
        resetGachaQueue: () => {
            set({
                needShowTransitionBackFunc: false,
                isFetchGachaLoading: false,
                cardsIndex: -1,
                gachaCardsList: [],
                gachaResp: [],
            });
        },
        isFetchGachaLoading: false,
        gachaCount: 0,
        gachaClicked: false,
        setGachaClick: (value) => {
            set((state) => ({
                gachaCount: value,
                gachaClicked: !state.isFetchGachaLoading,
            }));
        },
        setGachaFreeClick(value) {
            set((state) => ({
                gachaCount: value,
                gachaFreeClicked: !state.isFetchGachaLoading,
            }));
        },
        resetGachaClick: () => {
            set({
                gachaClicked: false,
            });
        },
        resetGachaFreeClick: () => {
            set({
                gachaFreeClicked: false,
            });
        },
    };
});

export default useGachaStore;
