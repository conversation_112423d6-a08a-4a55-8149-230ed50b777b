import fetch from '@/utils/fetch';
import type { PageType } from '@/types/common';

/**
 * https://music-ox.hz.netease.com/ox/music/api/detail/4578555/basic
 * @param type 抽卡~
 * @returns
 */
export const gachaQueueApi = (type: string, count: number): any =>
    fetch('/api/sociallive/ai/game/lottery/action', {
        method: 'POST',
        data: {
            request: {
                lotteryType: type,
                rounds: count,
            },
        },
    });

export const buyTicketApi = (ticketCountValue: number): any =>
    fetch('/api/sociallive/ai/game/buy/ticket', {
        method: 'POST',
        data: {
            ticketCount: ticketCountValue,
        },
    });

/**
 * //https://music-ox.hz.netease.com/ox/music/api/detail/4548055/basic
 * @param sceneTypeValue
 * @returns
 */
export const exchangeInfoApi = (sceneTypeValue: string): any =>
    fetch('/api/sociallive/ai/game/lottery/exchange/info', {
        method: 'POST',
        data: {
            sceneType: sceneTypeValue,
        },
    });

// 抽奖奖池信息
export const lotteryRewardPoolApi = (): any =>
    fetch('/api/sociallive/ai/game/lottery/award', {
        data: {},
    });

// 抽奖记录
export const lotteryRecordApi = (data: PageType): any =>
    fetch('/api/sociallive/ai/game/lottery/record', {
        data: {
            page: data,
        },
    });
