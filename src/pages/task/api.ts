import fetch from '@/utils/fetch';
import { IResource } from '@/pages/task/type';

interface AwardInfoRes {
    resource?: IResource[];
    rewardBoxId?: number;
}

export const getAwardInfo = (data: { rewardId: number }): Promise<AwardInfoRes> =>
    fetch('/api/mirth/home/<USER>/sign/reward/info/get', { data });

export const getAwardInfos = (data: { rewardIds: number[] }): Promise<AwardInfoRes[]> =>
    fetch('/api/mirth/home/<USER>/sign/reward/info/batch', { data });

export default {};
