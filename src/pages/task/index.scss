html,
body,
.taro_page {
    overflow-y: hidden;
}

.page-task-wrapper {
    width: 100%;
    min-height: 100vh;
    background: url(../../assets/task/bg.jpg) no-repeat top center / 100% 298px, #fef2f4;
    background-position: 0 0;
    padding-top: constant(safe-area-inset-top, 24px);
    padding-top: env(safe-area-inset-top, 24px);
    // stylelint-disable-next-line
    @supports (padding: max(0px)) {
        padding-top: max(24px, env(safe-area-inset-top));
    }
    position: relative;

    .scroll-header {
        height: 44px;
        margin: 0 20px 9px 20px;
        box-sizing: border-box;
        position: relative;

        .header-icon {
            width: 26px;
            height: 26px;
            position: absolute;
            left: 0;
            top: 9px;
        }
    }

    .scroll-tabs {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;

        .tab-header {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 44px;
            margin: 0 auto;

            .tab-item {
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 18px;
                position: relative;
                color: rgba(38, 38, 42, 0.4);
                font-weight: 500;
                font-family: 'SourceHanSerifCN-Bold', sans-serif;
                width: 66px;
                line-height: 18px;
                padding-bottom: 5px;

                &.active {
                    color: #26262a;

                    &::before {
                        content: '';
                        position: absolute;
                        left: 50%;
                        bottom: -6px;
                        transform: translateX(-50%);
                        width: 54px;
                        height: 6px;
                        background: url('../../assets/task/tab-active.png') no-repeat;
                        background-size: 100% 100%;
                    }
                }

                // 红点样式
                &.has-dot {
                    &::after {
                        content: '';
                        position: absolute;
                        top: -3px;
                        right: 10px;
                        width: 5px;
                        height: 5px;
                        background: #ff561e;
                        border-radius: 50%;
                    }
                }
            }
        }

        .tab-content {
            flex: 1;
            width: 100%;
            white-space: nowrap;

            .tab-content-inner {
                display: flex;
                width: 200%;
                height: 100%;
            }

            .tab-pane {
                display: inline-block;
                width: 50%;
                height: 100%;
            }
        }
    }
}
