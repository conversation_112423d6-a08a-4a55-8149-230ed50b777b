// 签到状态
export enum SignStatus {
    SignedIn = 1, // 已签到
    NotSignedIn = 2, // 未签到
}

// 奖励状态
export enum RewardStatus {
    NotReward = 0, // 已签到，未领取
    NotReceived = 1, // 未签到，不可领取
    Rewarded = 2, // 已签到，已领取
    RewardExpired = 3, // 奖励已过期
}

// 签到奖励信息
export interface RewardInfo {
    rewardId: number;
    rewardName: string;
    rewardIcon: string;
    rewardNum: number;
    rewardStatus: RewardStatus;
}

// 签到信息
export interface SignInfoList {
    signInPeriodId: number;
    signInDate: string;
    signStatus: SignStatus;
    signInRewardInfo: RewardInfo;
}

export interface SignInProgressInfo {
    signInDays: number; // 签到持续天数
    signInRewardInfoDTO: RewardInfo;
}

// 签到接口定义
export interface SignInfo {
    totalSignInDays: number;
    signInfoList: SignInfoList[];
    cumulativeSignInRewardInfos?: SignInProgressInfo[];
}

export interface ProgressNode {
    id: number;
    value: number;
    label: string;
    claimed: boolean;
    rewardStatus: number;
}

export interface SignlistResStoreType {
    totalSignInDays: number;
    signInInfoList: SignInfoList[];
    cumulativeSignInRewardInfos: SignInProgressInfo[];
    serverDate: string;
    totalDays: number;
}

// 签到store类型定义
export interface SignStoreType extends SignInfo {
    signInPeriodId: number;
    isLoading: boolean;
    progressNodes: ProgressNode[];
    totalDays: number;
    serverDate: string;
    signinSuccess: boolean;
    changeSigninStatus: (status: boolean) => void;
    // 初始化数据
    init: () => Promise<void>;
    // 用户点击签到
    signIn: () => Promise<void>;
    // 预览奖励
    receiveAward: (id?: number, value?: number) => void;
}

export interface IResource {
    id: number;
    name: string;
    desc: string;
    price: number;
    resNum: number;
    imgUrl: string;
}

export interface IReward {
    rewardBoxId?: number;
    resource: IResource[];
}

// 任务store类型定义
export interface TaskItem {
    missionId: number;
    missionName: string;
    missionDesc: string;
    targetValue: number;
    missionProgress: number;
    userMissionStatus: number;
    rewardDTO: RewardInfo;
    taskJumpUrl: string;
}

export enum TaskType {
    Daily = 1, // 日任务
    Weekly = 2, // 周任务
}

// 任务完成状态
export enum TaskStatus {
    canReceive = 1, // 待领取
    goDone = 0, // 去完成
    finished = 3, // 已完成
}

// 任务奖励状态
export enum TaskRewardStatus {
    NotReward = 0, // 完成任务，未领取
    NotComplete = 1, // 未完成，不可领取
    Rewarded = 2, // 已完成，已领取
}

export interface TaskActiveRewardItem {
    activeValue: number;
    rewardDTO: RewardInfo;
    rewardStatus: number;
}

export interface TaskListResStoreType {
    dailyMissions: {
        missionBoxId: number;
        activeValueUpperLimit: number;
        missionInfoDTOList: TaskItem[];
        activeValue: number;
        countdown: number;
        missionActiveRewardDTOList: TaskActiveRewardItem[];
    };
    weeklyMissions: {
        missionBoxId: number;
        activeValueUpperLimit: number;
        missionInfoDTOList: TaskItem[];
        activeValue: number;
        countdown: number;
        missionActiveRewardDTOList: TaskActiveRewardItem[];
    };
}

export interface TaskStoreType {
    dailyMissionBoxId: number;
    dailyLimitValue: number;
    dailyActiveValue: number;
    dailyCountdown: number;
    dailyTaskList: TaskItem[];
    dailyRewardList: ProgressNode[];
    weeklyMissionBoxId: number;
    weeklyLimitValue: number;
    weeklyActiveValue: number;
    weeklyCountdown: number;
    weeklyTaskList: TaskItem[];
    weeklyRewardList: ProgressNode[];
    init: () => Promise<void>;
    receiveTaskAward: (missionId: number, type: number) => void;
    receiveActiveAward: (rewardId: number, missionType: number) => void;
    receiveAll: (missionType: number) => Promise<number[]>;
}

export interface SignListRef {
    triggerSigninClick: () => void;
}
