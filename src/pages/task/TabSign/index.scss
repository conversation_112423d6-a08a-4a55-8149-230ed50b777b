.sign-wrapper {
    color: #000;
    font-family: 'PingFang SC', sans-serif;
    font-size: 12px;

    .current-signed {
        display: flex;
        align-items: flex-end;
        margin-bottom: 14px;

        .text {
            opacity: 0.6;
            line-height: 1;
        }

        .heavier {
            display: flex;
            align-items: flex-end;
            position: relative;

            .num {
                font-size: 30px;
                font-weight: 600;
                margin-left: 10px;
                margin-right: 3px;
                line-height: 1.2;
                height: 30px;
                position: relative;
                z-index: 1;
            }

            .unit {
                line-height: 1;
                position: relative;
                z-index: 1;
            }

            &::before {
                content: '';
                width: 100%;
                height: 8px;
                flex-shrink: 0;
                border-radius: 25px;
                position: absolute;
                z-index: 0;
                bottom: -5px;
                right: -5px;
                background: linear-gradient(90deg, rgba(255, 195, 216, 0) 0%, #ff9fc1 100%);
            }
        }
    }

    .current-signed,
    .progress,
    .tips {
        padding: 0 20px;
        box-sizing: border-box;
    }

    .progress {
        padding: 0 39px 0 20px !important;
    }

    .tips {
        font-size: 11px;
        font-weight: 400;
        opacity: 0.3;
        margin-bottom: 8px;
        margin-top: 23px;
    }

    .list {
        width: 100vw;
        height: calc(100vh - 207px);
        background-color: #fff;
        border-radius: 20px;
        padding: 24px 24px 0;
        box-sizing: border-box;
    }
}
