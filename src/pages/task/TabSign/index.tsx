import React, { useCallback, useEffect, useState } from 'react';
import { View, Text } from '@tarojs/components';
import RewardProgress from '../components/RewardProgress';
import SignList from '../components/SignList';

import SussessModal from '../components/SuccessModal';
import PreviewModal from '../components/PreviewModal';
import useStore from '../store/sign';
import { SignStoreType } from '../type';

import './index.scss';

const Sign = () => {
    const totalSignInDays = useStore((state: SignStoreType) => state.totalSignInDays);
    const progressNodes = useStore((state: SignStoreType) => state.progressNodes);
    const totalDays = useStore((state: SignStoreType) => state.totalDays);
    const init = useStore((state: SignStoreType) => state.init);
    const receiveAward = useStore((state: SignStoreType) => state.receiveAward);
    const signinSuccess = useStore((state: SignStoreType) => state.signinSuccess);
    const changeSigninStatus = useStore((state: SignStoreType) => state.changeSigninStatus);

    const [rewardId, setRewardId] = useState(0);
    const [previewVisible, setPreviewVisible] = useState(false);

    useEffect(() => {
        // 不依赖任何参数，只请求1次
        init();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const handleChooseReward = (id?: number, value?: number) => {
        setRewardId(id);
        receiveAward(id, value);
    };

    const onChooseReward = useCallback((id: number) => {
        setRewardId(id);
    }, []);

    return (
        <View className="sign-wrapper">
            <View className="current-signed">
                <Text className="text">已签到</Text>
                <View className="heavier">
                    <Text className="num">{totalSignInDays}</Text>
                    <Text className="unit">天</Text>
                </View>
            </View>

            <View className="progress">
                <RewardProgress
                    type="end-edge"
                    total={totalDays}
                    current={totalSignInDays}
                    nodes={progressNodes}
                    onChoose={handleChooseReward}
                />
            </View>

            <View className="tips">每日凌晨5：00更新</View>

            <View className="list">
                <SignList onChooseReward={onChooseReward} setPreviewVisible={setPreviewVisible} />
            </View>

            {signinSuccess && rewardId ? (
                <SussessModal
                    rewardId={rewardId}
                    show={signinSuccess}
                    setSuccessVisible={changeSigninStatus}
                />
            ) : null}
            {previewVisible ? (
                <PreviewModal
                    rewardId={rewardId}
                    show={previewVisible}
                    setPreviewVisible={setPreviewVisible}
                />
            ) : null}
        </View>
    );
};

export default Sign;
