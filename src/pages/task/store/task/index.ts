import { create } from 'zustand';
import { showToast } from '@tarojs/taro';
import fetch from '@/utils/fetch';
import { filterTask } from '@/utils/appSourceAdapter';
import {
    TaskActiveRewardItem,
    TaskListResStoreType,
    TaskRewardStatus,
    TaskStoreType,
    TaskType,
} from '../../type';
import useRootStore from '../index';

const taskListUrl = '/api/mirth/home/<USER>/mission/info/get';
const receiveTaskUrl = '/api/mirth/home/<USER>/mission/reward/receive';
const receiveActiveUrl = '/api/mirth/home/<USER>/mission/active/reward/batch';
const receiveAllUrl = '/api/mirth/home/<USER>/reward/receive/batch';

// 返回结果映射为progress需要的结构
const resToProgress = (list: TaskActiveRewardItem[]) => {
    const progressList = list.map((item: TaskActiveRewardItem) => ({
        id: item.rewardDTO.rewardId, // 奖励id，用来预览奖励
        value: item.activeValue,
        label: `${item.activeValue}`,
        rewardStatus: item.rewardStatus,
        claimed: item.rewardStatus === TaskRewardStatus.Rewarded, // 是否已领取
    }));

    return progressList;
};

const useStore = create<TaskStoreType>((set, get) => {
    return {
        // 任务总的id
        dailyMissionBoxId: 0,
        // 日任务倒计时，精确到毫秒
        dailyCountdown: 0,
        // 日任务相关
        dailyActiveValue: 0,
        dailyTaskList: [],
        dailyRewardList: [],
        dailyLimitValue: 0,

        // 周任务相关
        weeklyMissionBoxId: 0,
        weeklyActiveValue: 0,
        // 周任务倒计时，精确到毫秒
        weeklyCountdown: 0,
        weeklyTaskList: [],
        weeklyRewardList: [],
        weeklyLimitValue: 0,

        // 初始化日任务和周任务数据
        init: async () => {
            useRootStore.getState().fetchPoint();
            try {
                const res = await fetch<TaskListResStoreType>(taskListUrl, {});
                const { dailyMissions, weeklyMissions } = res || ({} as TaskListResStoreType);

                set({
                    dailyMissionBoxId: dailyMissions.missionBoxId,
                    dailyLimitValue: dailyMissions.activeValueUpperLimit,
                    dailyActiveValue: dailyMissions.activeValue,
                    dailyCountdown: dailyMissions.countdown,
                    dailyTaskList: filterTask(dailyMissions.missionInfoDTOList),
                    dailyRewardList: resToProgress(dailyMissions.missionActiveRewardDTOList),
                    weeklyMissionBoxId: weeklyMissions.missionBoxId,
                    weeklyLimitValue: weeklyMissions.activeValueUpperLimit,
                    weeklyActiveValue: weeklyMissions.activeValue,
                    weeklyCountdown: weeklyMissions.countdown,
                    weeklyTaskList: filterTask(weeklyMissions.missionInfoDTOList),
                    weeklyRewardList: resToProgress(weeklyMissions.missionActiveRewardDTOList),
                });
            } catch (err: any) {
                showToast({
                    title: err.message || '获取任务信息失败',
                    icon: 'none',
                    duration: 2000,
                });
            }
        },

        // 领取单个任务奖励
        receiveTaskAward: async (missionId: number, missionType: number) => {
            try {
                await fetch(receiveTaskUrl, {
                    data: {
                        missionId,
                        missionBoxId:
                            missionType === TaskType.Daily
                                ? get().dailyMissionBoxId
                                : get().weeklyMissionBoxId,
                    },
                });
                showToast({
                    title: '领取成功',
                    icon: 'none',
                    duration: 2000,
                });
                get().init();
            } catch (err: any) {
                showToast({
                    title: err.message || '领取失败',
                    icon: 'none',
                    duration: 2000,
                });
            }
        },

        // 领取活跃进度值奖励, missionType 1 日任务 2 周任务
        receiveActiveAward: async (_rewardId: number, missionType: number) => {
            try {
                await fetch(receiveActiveUrl, {
                    data: {
                        type: missionType,
                    },
                });
                get().init();
            } catch (err: any) {
                showToast({
                    title: err.message || '领取失败',
                    icon: 'none',
                    duration: 2000,
                });
            }
        },

        receiveAll: async (missionType: number): Promise<number[]> => {
            try {
                await fetch(receiveAllUrl, {
                    data: {
                        type: missionType,
                    },
                });
                const oldRewardList =
                    missionType === TaskType.Daily ? get().dailyRewardList : get().weeklyRewardList;
                await get().init();
                const newRewardList =
                    missionType === TaskType.Daily ? get().dailyRewardList : get().weeklyRewardList;
                // 比对新旧奖励列表，找出已领取的奖励
                const newlyClaimedRewards = newRewardList.filter((newItem) => {
                    const oldItem = oldRewardList.find((item) => item.id === newItem.id);
                    return (
                        newItem.rewardStatus === TaskRewardStatus.Rewarded &&
                        oldItem &&
                        oldItem.rewardStatus !== TaskRewardStatus.Rewarded
                    );
                });
                return newlyClaimedRewards.map((item) => item.id) || [];
            } catch (err: any) {
                showToast({
                    title: err.message || '领取失败',
                    icon: 'none',
                    duration: 2000,
                });
                return [];
            }
        },
    };
});

export default useStore;
