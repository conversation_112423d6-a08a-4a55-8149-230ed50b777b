import { create } from 'zustand';
import { showToast } from '@tarojs/taro';
import fetch from '@/utils/fetch';

const pointgetUrl = '/api/mirth/home/<USER>/aigc/red/point/get';

interface PointStoreType {
    signInRedPoint: boolean;
    missionRedPoint: boolean;
    fetchPoint: () => void;
}

interface PointResStoreType {
    signInRedPoint: boolean;
    missionRedPoint: boolean;
}

const useStore = create<PointStoreType>((set) => {
    return {
        // 初始化日任务和周任务数据
        signInRedPoint: false,
        missionRedPoint: false,

        fetchPoint: async () => {
            try {
                const res = await fetch<PointResStoreType>(pointgetUrl, {});

                set({
                    signInRedPoint: res?.signInRedPoint,
                    missionRedPoint: res?.missionRedPoint,
                });
            } catch (err: any) {
                showToast({
                    title: err.message || '获取任务信息失败',
                    icon: 'none',
                    duration: 2000,
                });
            }
        },
    };
});

export default useStore;
