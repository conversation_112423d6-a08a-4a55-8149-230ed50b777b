import { showToast } from '@tarojs/taro';
import fetch from '@/utils/fetch';
import {
    SignlistResStoreType,
    SignInProgressInfo,
    SignInfoList,
    RewardStatus,
    ProgressNode,
} from '../../type';
import useRootStore from '../index';

const signinfoUrl = '/api/mirth/home/<USER>/sign/info/get';
const signinUrl = '/api/mirth/home/<USER>/sign/in';
const receiveAwardUrl = '/api/mirth/home/<USER>/sign/cumulative/reward/receiver';

// 返回结果映射为progress需要的结构
const resToProgress = (list: SignInProgressInfo[]) => {
    const progressList = list.map((item: SignInProgressInfo) => ({
        id: item.signInRewardInfoDTO.rewardId, // 奖励id，用来预览奖励
        value: item.signInDays,
        label: `${item.signInDays}天`,
        rewardStatus: item.signInRewardInfoDTO.rewardStatus,
        claimed: item.signInRewardInfoDTO.rewardStatus === RewardStatus.Rewarded, // 是否已领取
    }));

    return progressList;
};

const signinfo = (set: any, get: any) => ({
    // 基础数据初始化
    totalDays: 30, // 策划 @向安琪 定的总天数
    signInPeriodId: 0,
    totalSignInDays: 0,
    signInfoList: [] as SignInfoList[],
    isLoading: false,
    serverDate: '',

    // 进度条数据初始化
    progressNodes: [] as ProgressNode[],

    // 签到状态
    signinSuccess: false,
    changeSigninStatus: (status: boolean) => {
        set({ signinSuccess: status });
    },

    // 获取签到信息
    init: async () => {
        set({ isLoading: true });
        useRootStore.getState().fetchPoint();
        try {
            const res = await fetch<SignlistResStoreType>(signinfoUrl, {});
            const { serverDate, signInInfoList, totalSignInDays, cumulativeSignInRewardInfos } =
                res || {};

            set({
                // 服务端为了方便，在每个结构里都塞了一个id，前端可以随便取一个出来用
                signInPeriodId: signInInfoList?.[0]?.signInPeriodId || 0,
                // 用户总的签到天数
                totalSignInDays,
                serverDate,
                // 签到周期列表，一个周期固定30天，返回30个items信息
                signInfoList: signInInfoList,
                progressNodes: resToProgress(cumulativeSignInRewardInfos),
            });
        } catch (err: any) {
            showToast({
                title: err.message || '获取签到信息失败',
                icon: 'none',
                duration: 2000,
            });
        } finally {
            set({ isLoading: false });
        }
    },

    // 用户点击签到
    signIn: async () => {
        const { signInPeriodId, serverDate } = get();

        try {
            await fetch(signinUrl, {
                data: { signInPeriodId, signInDate: serverDate },
            });
            get().init();
            set({ signinSuccess: true });
        } catch (err: any) {
            showToast({
                title: err.message || '签到失败',
                icon: 'none',
                duration: 2000,
            });
        }
    },

    // 用户领取累计签到奖励
    receiveAward: async (id?: number, value?: number) => {
        const { signInPeriodId } = get();
        try {
            await fetch(receiveAwardUrl, {
                data: { signInPeriodId, signDays: value },
            });
            get().init();
            set({ signinSuccess: true });
        } catch (err: any) {
            showToast({
                title: err.message || '领取奖励失败',
                icon: 'none',
                duration: 2000,
            });
        }
    },
});

export default signinfo;
