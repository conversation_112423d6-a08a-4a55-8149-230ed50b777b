import React, { useCallback, memo } from 'react';
import { useDidShow, showToast } from '@tarojs/taro';
import { View, Text, Image } from '@tarojs/components';
import { optimizeImage } from '@/utils/image';
import { addClickLog } from '@/utils/logTool';
import { jumpLink } from '@/router';
import Progress from '../Progress';

import useStore from '../../store/task';
import { TaskItem, TaskStatus, TaskStoreType } from '../../type';

import './index.scss';

interface TaskListProps {
    list: TaskItem[];
    type: number;
}

const TaskList = ({ list, type }: TaskListProps) => {
    const init = useStore((state: TaskStoreType) => state.init);
    const receiveTaskAward = useStore((state: TaskStoreType) => state.receiveTaskAward);

    const [isRequesting, setIsRequesting] = React.useState(false);

    // 跳转任务，每周登陆任务比较特殊，需要用id判断给toast，其他情况直接跳转
    const onJump = useCallback((missionId: number, jumpUrl: string) => {
        // 周登陆任务，线上id：107206，测试id：1017992
        if (missionId === 1017992 || missionId === 107206) {
            showToast({
                title: '今日已登录',
                icon: 'none',
                duration: 2000,
            });
        } else {
            jumpLink(jumpUrl);
        }
    }, []);

    // 获取按钮的类名
    const getBtnClassName = useCallback((status: TaskStatus) => {
        const statusClassMap = {
            [TaskStatus.canReceive]: 'can-receive',
            [TaskStatus.goDone]: 'go-done',
            [TaskStatus.finished]: 'finished',
        };
        return statusClassMap[status] || 'finished';
    }, []);

    // 处理按钮点击事件
    const handleBtnClick = useCallback(
        async (
            status: TaskStatus,
            missionId: number,
            jumpUrl: string,
            etype: number,
            missionName: string
        ) => {
            if (isRequesting) {
                return;
            }
            setIsRequesting(true);

            try {
                if (status === TaskStatus.canReceive) {
                    await receiveTaskAward(missionId, etype);
                } else if (status === TaskStatus.goDone) {
                    onJump(missionId, jumpUrl);
                    addClickLog(
                        'btn_ai_active_system_task|page_ai_active_system_task|page_ai_active_system|page_h5_biz',
                        {
                            context: missionName,
                        }
                    );
                }
            } catch (e) {
                showToast({
                    title: e?.message || '操作失败',
                    icon: 'none',
                    duration: 2000,
                });
            } finally {
                setIsRequesting(false);
            }
        },
        [isRequesting, onJump, receiveTaskAward]
    );

    // 每次完成任务回来，都要重新刷新任务列表
    useDidShow(() => {
        init();
    });

    return (
        <View className="task-list">
            {list.map((item: TaskItem) => {
                const {
                    rewardDTO,
                    missionId,
                    missionName,
                    targetValue,
                    missionProgress,
                    userMissionStatus,
                    taskJumpUrl,
                } = item || {};

                return (
                    <View className="task-item" key={`task-${missionId}`}>
                        <View className="task-top">
                            <Text className="task-name">{missionName}</Text>
                            <View className="task-item-progress">
                                <Text>{`${missionProgress}/${targetValue}`}</Text>
                                <Progress current={missionProgress} total={targetValue} />
                            </View>
                        </View>
                        <View className="task-body">
                            <View className="task-info">
                                <Image
                                    className="icon"
                                    src={optimizeImage({ src: rewardDTO.rewardIcon, type: 'png' })}
                                />
                                <View className="info">
                                    <Text>{rewardDTO.rewardName}</Text>
                                    <Text className="symbol">x</Text>
                                    <Text className="num">{rewardDTO.rewardNum || 1}</Text>
                                </View>
                            </View>

                            <View
                                className={`btn ${getBtnClassName(userMissionStatus)}`}
                                onClick={() =>
                                    handleBtnClick(
                                        userMissionStatus,
                                        missionId,
                                        taskJumpUrl,
                                        type,
                                        missionName
                                    )
                                }
                            />
                        </View>
                    </View>
                );
            })}
        </View>
    );
};

export default memo(TaskList);
