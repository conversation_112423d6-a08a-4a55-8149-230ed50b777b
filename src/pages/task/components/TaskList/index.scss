.task-list {
    width: calc(100vw - 40px);
    height: auto;
    border-radius: 20px;
    background-color: #fff;
    padding: 4px 20px;
    box-sizing: border-box;
    color: #000;

    .task-item {
        width: calc(100vw - 80px);
        height: 82px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
        margin-top: 16px;

        &:last-child {
            border-bottom: none;
        }

        .task-top {
            width: 100%;
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 10px;

            .task-name {
                font-size: 14px;
                font-weight: 600;
                opacity: 0.8;
                white-space: nowrap;
            }

            .task-item-progress {
                font-size: 11px;
                opacity: 0.8;
                display: flex;
                flex-direction: column;
                align-items: flex-end;
            }
        }

        .task-body {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .task-info {
                display: flex;
                align-items: center;
                justify-content: flex-start;

                .icon {
                    display: inline-block;
                    width: 36px;
                    height: 36px;
                    margin-right: 6px;
                    border-radius: 4px;
                }

                .info {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    opacity: 0.6;
                    font-size: 11px;

                    .symbol {
                        color: #ff689e;
                        font-size: 12px;
                        line-height: 12px;
                        opacity: 0.6;
                        margin-left: 3px;
                        margin-right: 2px;
                    }

                    .num {
                        font-size: 16px;
                        line-height: 16px;
                        color: #ff8ab4;
                        opacity: 0.6;
                    }
                }
            }

            .btn {
                width: 96px;
                height: 26px;
                background: url(../../../../assets/task/task/btn-receive.png) no-repeat;
                background-size: 100% 100%;

                &.go-done {
                    background: url(../../../../assets/task/task/btn-godone.png) no-repeat;
                    background-size: 100% 100%;
                }

                &.finished {
                    background: url(../../../../assets/task/task/btn-done.png) no-repeat;
                    background-size: 100% 100%;
                }
            }
        }

        &:has(.btn.finished) {
            opacity: 0.3;
        }
    }
}
