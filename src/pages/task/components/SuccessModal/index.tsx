import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { showToast } from '@tarojs/taro';
import { View, ITouchEvent } from '@tarojs/components';

import FullscreenModal from '@/components/FullScreenModal';
import usePopupStore from '@/store/Popup/usePopupStore';
import { PopupBusinessKey } from '@/store/Popup/type';

import RewardItem from '../RewardItem';
import { getAwardInfos } from '../../api';
import { IResource } from '../../type';

import './index.scss';

interface ISuccessModal {
    show: boolean;
    rewardId?: number;
    rewardIds?: number[];
    setSuccessVisible: (state: boolean) => void;
}

export const SuccessModalContent = ({
    previewList,
    onClick,
}: {
    previewList: IResource[];
    onClick: (event: ITouchEvent) => void;
}) => {
    return (
        <View
            className="success-modal"
            onClick={(e: ITouchEvent) => {
                e.stopPropagation();
            }}>
            {previewList.length <= 3 ? (
                <View className="success-content">
                    {previewList.map((item: IResource) => (
                        <RewardItem key={item.id} item={item} />
                    ))}
                </View>
            ) : (
                <View className="task-grid-container">
                    {previewList.map((item: IResource) => (
                        <View key={item.id} className="task-grid-item">
                            <RewardItem item={item} />
                        </View>
                    ))}
                </View>
            )}
            <View className="btn" onClick={onClick} />
        </View>
    );
};

const SuccessModal = ({ rewardId, rewardIds, show, setSuccessVisible }: ISuccessModal) => {
    const finalRewardIds = useMemo(() => {
        return rewardId ? [rewardId] : rewardIds;
    }, [rewardId, rewardIds]);

    const [previewList, setPreviewList] = useState([]);

    const fetchPreviewList = useCallback(async () => {
        try {
            const res = await getAwardInfos({ rewardIds: finalRewardIds });
            const allResources = res.map((item) => item.resource || []).flat();

            setPreviewList(allResources);
        } catch (err) {
            showToast({
                title: err.message || '获取预览信息失败',
                icon: 'none',
                duration: 2000,
            });
        }
    }, [finalRewardIds]);

    useEffect(() => {
        if (show && finalRewardIds) {
            fetchPreviewList();
        }
    }, [show, fetchPreviewList, finalRewardIds]);

    // 把全局签到弹框销毁
    const closeAllFullScreenModal = useCallback(() => {
        usePopupStore.getState().completePopup(PopupBusinessKey.SIGN);
        setSuccessVisible(false);
    }, [setSuccessVisible]);

    return (
        <FullscreenModal onClose={closeAllFullScreenModal} visible={show}>
            <SuccessModalContent previewList={previewList} onClick={closeAllFullScreenModal} />
        </FullscreenModal>
    );
};

export default SuccessModal;
