.success-modal {
    width: 313px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    min-height: 250px;
    background: url(../../../../assets/task/sign/modal-success-bg.png) no-repeat top center,
        linear-gradient(to bottom, transparent 0px, transparent 40px, #fef4f6 40px, #fef4f6 100%);
    background-size: 100% 151px, 100% 100%;
    border-radius: 15px;

    .success-content {
        height: 91px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        position: relative;
        margin-top: 81px;
    }

    .task-grid-container {
        width: 266px;
        display: flex;
        flex-wrap: wrap;
        margin-top: 61px;

        .task-grid-item {
            display: flex;
            flex-direction: column;
            flex: 0 0 calc((100% - 44px) / 3);

            &:nth-child(3n-1) {
                margin-left: 22px;
                margin-right: 22px;
            }
        }
    }

    .btn {
        width: 168px;
        height: 46px;
        border-radius: 23px;
        background: url(../../../../assets/task/sign/btn-clamied.png) 50% / cover no-repeat;
        margin-top: 32px;
        margin-bottom: 30px;
    }
}
