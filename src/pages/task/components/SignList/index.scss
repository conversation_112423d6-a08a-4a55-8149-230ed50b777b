.signlist-wrapper {
    border-radius: 8px;
    background: url(../../../../assets/task/sign/signlist-page-bg.png) no-repeat;
    background-size: 100% 100%;
    // margin: 0 auto;

    .signlist-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        grid-auto-rows: calc((100vw - 52px) / 5);
        grid-auto-flow: row; // 确保按行填充

        .signlist-item-wrapper {
            position: relative;
            width: calc((100vw - 52px) / 5);
            height: calc((100vw - 52px) / 5);
            display: flex;
            justify-content: center;
            align-items: center;

            .sign-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 3;

                .claimed-icon {
                    width: 16px;
                    height: 16px;
                }

                .expired-text {
                    font-size: 10px;
                    color: #8f7d86;
                    opacity: 0.8;
                }
            }

            .signlist-item {
                position: relative;
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;

                .item-content {
                    position: relative;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 100%;
                    height: 100%;

                    .drop-icon {
                        width: 50px;
                        height: 50px;
                        border-radius: 4px;
                        z-index: 1;
                    }

                    .overlay {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        z-index: 2;
                    }
                }

                .claimable-tag {
                    position: absolute;
                    top: -10px;
                    right: -10px;
                    z-index: 3;

                    .claimable-icon {
                        width: 40px;
                        height: 24px;
                    }
                }

                // 状态样式
                &.expired,
                &.claimed {
                    opacity: 0.15;
                }

                &.claimable {
                    z-index: 1;
                    border-radius: 4px;
                    border: 1.4px solid #ff9ec0;
                    background: rgba(255, 255, 255, 0);
                    box-shadow: 0 0 5.6px 0 #ff8cab;
                }
            }
        }
    }
}
