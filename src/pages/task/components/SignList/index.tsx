import React, { forwardRef, useImperativeHandle, useCallback } from 'react';
import { View, Image, Text } from '@tarojs/components';
import clsx from 'classnames';

import iconCanreceive from '@/assets/task/sign/can-receive.png';
import iconHasreceived from '@/assets/task/sign/has-received.png';

import { optimizeImage } from '@/utils/image';
import useStore from '../../store/sign';
import { SignStoreType, RewardInfo, SignListRef } from '../../type';

import './index.scss';

enum SignStatus {
    default,
    signed,
}

enum RewardStatus {
    claimable,
    default,
    claimed,
    expired,
}

interface IProps {
    className?: string;
    closeModal?: () => void;
    onChooseReward?: (id: number) => void;
    setPreviewVisible?: any;
}

const SignList = forwardRef<SignListRef, IProps>(
    ({ className, closeModal, onChooseReward, setPreviewVisible }: IProps, ref) => {
        const items = useStore((state: SignStoreType) => state.signInfoList);
        const serverDate = useStore((state: SignStoreType) => state.serverDate);
        const signIn = useStore((state: SignStoreType) => state.signIn);

        const [isRequesting, setIsRequesting] = React.useState(false);

        const handleSignIn = useCallback(
            async (claimable: boolean, id: number) => {
                if (isRequesting) {
                    return;
                }

                setIsRequesting(true);

                onChooseReward(id);

                // 如果可领取，直接签到；否则弹出预览
                if (claimable) {
                    await signIn();
                    if (closeModal) {
                        setTimeout(() => {
                            closeModal();
                            setIsRequesting(false);
                        }, 200);
                    } else {
                        setIsRequesting(false);
                    }
                } else {
                    setPreviewVisible(true);
                    setIsRequesting(false);
                }
            },
            [closeModal, isRequesting, onChooseReward, setPreviewVisible, signIn]
        );

        // 通过 useImperativeHandle 向父组件暴露一个方法，让父组件点击button可以直接签到；保持签到逻辑一致
        useImperativeHandle(ref, () => ({
            triggerSigninClick: () => {
                const item = items.find(
                    (it) => SignStatus[it.signStatus] === 'default' && serverDate === it.signInDate
                );
                const { signInRewardInfo: el = {} as RewardInfo } = item || {};
                // if (el.rewardId) {
                handleSignIn(true, el.rewardId);
                // }
            },
        }));

        return (
            <View className={clsx('signlist-wrapper', className)}>
                <View className="signlist-grid">
                    {items.map((item) => {
                        const {
                            signInDate,
                            signStatus = SignStatus.default,
                            signInRewardInfo: el = {} as RewardInfo,
                        } = item || {};
                        let statusStyle = RewardStatus[el.rewardStatus];

                        // 可领取状态
                        const claimable =
                            SignStatus[signStatus] === 'default' && serverDate === signInDate;
                        if (claimable) {
                            statusStyle = 'claimable';
                        }

                        return (
                            <View
                                key={el.rewardId}
                                className="signlist-item-wrapper"
                                onClick={() => handleSignIn(claimable, el.rewardId)}>
                                <View className={clsx('signlist-item', statusStyle)}>
                                    {/* 命中当天日期：serverDate，并且未签到，提醒用户签到 */}
                                    {statusStyle === 'claimable' && (
                                        <View className="claimable-tag">
                                            <Image
                                                className="claimable-icon"
                                                src={iconCanreceive}
                                            />
                                        </View>
                                    )}

                                    <View className="item-content">
                                        {/* 每个item固有的图片 */}
                                        <Image
                                            className="drop-icon"
                                            src={optimizeImage({ src: el.rewardIcon, type: 'png' })}
                                        />
                                    </View>
                                </View>
                                {/* 已过期状态叠加 */}
                                {statusStyle === 'expired' && (
                                    <View className="sign-overlay">
                                        <Text className="expired-text">-已过期-</Text>
                                    </View>
                                )}
                                {/* 已领取状态叠加 */}
                                {statusStyle === 'claimed' && (
                                    <View className="sign-overlay">
                                        <Image className="claimed-icon" src={iconHasreceived} />
                                    </View>
                                )}
                            </View>
                        );
                    })}
                </View>
            </View>
        );
    }
);

export default SignList;
