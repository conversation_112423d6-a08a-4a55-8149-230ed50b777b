.tabs {
    .tab-list {
        width: 90px;
        height: 24px;
        border-radius: 20px;
        background: rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: center;
        margin: 2px auto;

        .tab-item {
            width: 45px;
            height: 24px;
            line-height: 25px;
            text-align: center;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.4);
            position: relative;

            &.active {
                color: #ff689e;
                border-radius: 20px;
                background: #fff;
                box-shadow: 0 0 4px 0 #e6d4d9;
            }

            // 红点样式
            &.has-dot {
                &::after {
                    content: '';
                    position: absolute;
                    top: 4px;
                    right: 7px;
                    width: 5px;
                    height: 5px;
                    background: #ff561e;
                    border-radius: 50%;
                }
            }
        }
    }

    .tab-panes {
        // width: 100%;
        flex: 1;
        height: calc(100vh - 62px);
        box-sizing: border-box;

        .swiper-item {
            box-sizing: border-box;
            padding-left: 10px;
        }

        .tab-pane {
            height: 100%;
            width: 100% !important;
            overflow-x: hidden;

            &::-webkit-scrollbar {
                display: none; /* Chrome, Safari, Opera */
                width: 0;
                background: transparent;
            }
        }
    }
}
