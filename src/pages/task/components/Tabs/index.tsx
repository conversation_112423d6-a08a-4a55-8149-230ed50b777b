import React, { useState, useRef } from 'react';
import { Swiper, SwiperItem, Text, View } from '@tarojs/components';

import './index.scss';

interface TabProps {
    tabs: string[];
    items: React.FC[];
    onReachEdge?: (direction: 'left' | 'right') => void;
    redPointMap?: {
        [key: number]: boolean;
    };
}

const Tabs = ({ tabs, items, onReachEdge, redPointMap }: TabProps) => {
    const [current, setCurrent] = useState(0);
    const startXRef = useRef(0);
    const isAtEdgeRef = useRef(false);

    const handleSwiperChange = (e: { detail: { current: React.SetStateAction<number> } }) => {
        setCurrent(e.detail.current);
    };

    function handleTabClick(index: number): void {
        setCurrent(index);
    }

    // 处理滑动开始事件
    const handleTouchStart = (e: { touches: { clientX: number }[] }) => {
        startXRef.current = e.touches[0].clientX;
        isAtEdgeRef.current = false;
    };

    // 处理滑动过程中的事件
    const handleTouchMove = (e: { touches: { clientX: any }[] }) => {
        const currentX = e.touches[0].clientX;
        const diffX = currentX - startXRef.current;

        // 只在第一个或最后一个tab时检测边缘滑动
        if (current === 0 && diffX > 200) {
            // 向右滑动且在第一个tab，触发向左切换
            if (onReachEdge && !isAtEdgeRef.current) {
                isAtEdgeRef.current = true;
                onReachEdge('left');
            }
        } else if (current === items.length - 1 && diffX < -200) {
            // 向左滑动且在最后一个tab，触发向右切换
            if (onReachEdge && !isAtEdgeRef.current) {
                isAtEdgeRef.current = true;
                onReachEdge('right');
            }
        }
    };

    // 处理滑动结束事件
    const handleTouchEnd = () => {
        // 重置边缘状态标志
        setTimeout(() => {
            isAtEdgeRef.current = false;
        }, 100);
    };

    return (
        <View className="tabs">
            <View className="tab-list">
                {tabs.map((tab, index) => (
                    <Text
                        key={tab}
                        className={`tab-item ${current === index ? 'active' : ''} ${
                            redPointMap[index] ? 'has-dot' : ''
                        }`}
                        onClick={() => handleTabClick(index)}>
                        {tab}
                    </Text>
                ))}
            </View>
            <Swiper
                disableProgrammaticAnimation="true"
                current={current}
                onChange={handleSwiperChange}
                className="tab-panes"
                onTouchStart={handleTouchStart}
                onTouchEnd={handleTouchEnd}
                onTouchMove={handleTouchMove}
                skipHiddenItemLayout
                circular={false}
                style={{ overflow: 'visible' }}>
                {items.map((Component, index) => (
                    // eslint-disable-next-line react/no-array-index-key
                    <SwiperItem key={`tab-${tabs[index]}`} className="swiper-item">
                        <View className="tab-pane">
                            <Component />
                        </View>
                    </SwiperItem>
                ))}
            </Swiper>
        </View>
    );
};

export default Tabs;
