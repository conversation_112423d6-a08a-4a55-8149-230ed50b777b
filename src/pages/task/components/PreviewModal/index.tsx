import React, { useCallback, useEffect, useState } from 'react';
import { showToast } from '@tarojs/taro';
import { ITouchEvent, Text, View } from '@tarojs/components';
import FullscreenModal from '@/components/FullScreenModal';
import RewardItem from '../RewardItem';
import { getAwardInfo } from '../../api';
import { IResource } from '../../type';

import './index.scss';

interface IPreviewModal {
    show: boolean;
    rewardId: number;
    setPreviewVisible: (state: boolean) => void;
}

export const PreviewModalContent = ({ previewList }: { previewList: IResource[] }) => {
    return (
        <View
            className="task-preview-modal"
            onClick={(e: ITouchEvent) => {
                e.stopPropagation();
            }}>
            {previewList.length <= 3 ? (
                <View className="preview-content">
                    {previewList.map((item: IResource) => (
                        <RewardItem key={item.id} item={item} />
                    ))}
                </View>
            ) : (
                <View className="task-grid-container">
                    {previewList.map((item: IResource) => (
                        <View key={item.id} className="task-grid-item">
                            <RewardItem item={item} />
                        </View>
                    ))}
                </View>
            )}
            <Text className="tips">点击空白区域关闭</Text>
        </View>
    );
};

const PreviewModal = ({ rewardId, show, setPreviewVisible }: IPreviewModal) => {
    const [previewList, setPreviewList] = useState([]);

    const fetchPreviewList = useCallback(async () => {
        try {
            const res = await getAwardInfo({ rewardId });

            setPreviewList(res.resource || []);
        } catch (err) {
            showToast({
                title: err.message || '获取预览信息失败',
                icon: 'none',
                duration: 2000,
            });
        }
    }, [rewardId]);

    useEffect(() => {
        fetchPreviewList();
    }, [show, fetchPreviewList]);

    return (
        <FullscreenModal onClose={() => setPreviewVisible(false)} visible={show}>
            <PreviewModalContent previewList={previewList} />
        </FullscreenModal>
    );
};

export default PreviewModal;
