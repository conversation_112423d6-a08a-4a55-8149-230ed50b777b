.task-preview-modal {
    width: 313px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    flex-direction: column;
    min-height: 269px;
    background: url(../../../../assets/task/sign/modal-preview-bg.png) no-repeat top center,
        linear-gradient(to bottom, transparent 0px, transparent 40px, #fef4f6 40px, #fef4f6 100%);
    background-size: 100% 135px, 100% 100%;
    border-radius: 15px;

    .preview-content {
        height: 91px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        position: relative;
        margin-top: 81px;
    }

    .task-grid-container {
        width: 266px;
        display: flex;
        flex-wrap: wrap;
        margin-top: 61px;

        .task-grid-item {
            display: flex;
            flex-direction: column;
            flex: 0 0 calc((100% - 44px) / 3);

            &:nth-child(3n-1) {
                margin-left: 22px;
                margin-right: 22px;
            }
        }
    }

    .tips {
        color: rgba(115, 115, 115, 0.3);
        text-align: center;
        font-size: 12px;
        font-weight: 400;
        opacity: 0.8;
        margin-top: 26px;
        margin-bottom: 33px;
    }
}
