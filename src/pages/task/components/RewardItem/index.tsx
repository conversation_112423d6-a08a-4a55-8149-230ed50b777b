import React from 'react';
import { View, Text, Image } from '@tarojs/components';
import { optimizeImage } from '@/utils/image';
import { IResource } from '../../type';

import './index.scss';

const RewardItem = ({ item }: { item: IResource }) => {
    const src = optimizeImage({
        src: item.imgUrl,
        type: 'png',
    });

    return (
        <View className="reward-item">
            <View className="imgUrl">
                <Image className="reward-img" src={src} mode="scaleToFill" />
                <Text className="num">{item.resNum}</Text>
            </View>
            <View className="name">
                <Text>{item.name}</Text>
            </View>
        </View>
    );
};

export default RewardItem;
