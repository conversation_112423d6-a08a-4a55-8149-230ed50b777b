.progress-wrapper {
    position: relative;
    width: 100%;
    height: 50px;
    padding: 20px 0;

    .progress-bar {
        position: relative;
        height: 6px;
        background: rgba(205, 174, 190, 0.1);
        border-radius: 3px;
        overflow: hidden;

        .progress-fill {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            background: #ff6ba0;
            transition: width 0.3s ease;
        }
    }

    .progress-nodes {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        .node-item {
            position: absolute;
            top: 4px;
            transform: translateX(-50%);
            display: flex;
            flex-direction: column;
            align-items: center;

            .node-icon {
                /* stylelint-disable-next-line prettier/prettier */
                width: 38PX;
                /* stylelint-disable-next-line prettier/prettier */
                height: 38PX;
            }

            .node-value {
                font-size: 12px;
                line-height: 12px;
                color: #999;
            }
        }
    }
}
