import React, { useState } from 'react';
import { Image, View } from '@tarojs/components';

import iconUnreach from '@/assets/task/unreach.png';
import iconClaimed from '@/assets/task/claimed.png';
import iconUnclaimed from '@/assets/task/unclaimed.png';

import PreviewModal from '../PreviewModal';
import { TaskRewardStatus } from '../../type';

import './index.scss';

interface ProgressProps {
    total: number;
    current: number;
    nodes: Array<{
        id: number;
        value: number;
        claimed?: boolean;
        label: string;
        rewardStatus?: number;
    }>;
    className?: string;
    onChoose?: (id?: number, value?: number) => void;
    type: 'full-edge' | 'full-center' | 'end-edge';
}

const ICON_MAP = {
    unreach: iconUnreach,
    claimed: iconClaimed,
    unclaimed: iconUnclaimed,
};

/**
 * 进度条组件
 * @param total 总进度
 * @param current 当前进度
 * @param nodes 节点数组
 * @param type 进度条类型 full-edge: 首尾有节点均分, full-center: 首尾无节点均分, end-edge: 尾部有节点均分
 */
const Progress: React.FC<ProgressProps> = ({
    total,
    current,
    nodes,
    className,
    type = 'full-edge',
    onChoose,
}: ProgressProps) => {
    const [previewVisible, setPreviewVisible] = useState(false);
    const [selectedRewardId, setSelectedRewardId] = useState(0);

    const getNodeStatus = (status: number) => {
        if (status === TaskRewardStatus.NotReward) {
            return 'unclaimed';
        }

        if (status === TaskRewardStatus.Rewarded) {
            return 'claimed';
        }

        return 'unreach';
    };

    const getNodePosition = (index: number, length: number) => {
        if (type === 'full-edge') {
            return `${(index / (length - 1)) * 100}%`;
        }
        if (type === 'end-edge') {
            return `${((index + 1) / length) * 100}%`;
        }
        // full-center 模式下，节点在1/(length+1)到length/(length+1)之间均分
        return `${((index + 1) / (length + 1)) * 100}%`;
    };

    const getProgressWidth = () => {
        // 找到当前进度所在的区间
        const nextNode = nodes.find((node) => node.value > current);
        const prevNode = [...nodes].reverse().find((node) => node.value <= current);

        if (!prevNode) {
            // 计算从0到第一个节点之间的进度
            const firstNode = nodes[0];
            if (!firstNode || current <= 0) return '0%';

            // 计算相对进度
            const progressRatio = current / firstNode.value;
            let firstNodePos: number;

            switch (type) {
                case 'full-edge':
                    firstNodePos = 0;
                    break;
                case 'end-edge':
                    firstNodePos = (1 / nodes.length) * 100;
                    break;
                default: // full-center
                    firstNodePos = (1 / (nodes.length + 1)) * 100;
                    break;
            }
            return `${progressRatio * firstNodePos}%`;
        }

        if (!nextNode) {
            const lastNode = nodes[nodes.length - 1];
            const remainingProgress = (current - lastNode.value) / (total - lastNode.value);
            const lastNodePos =
                type === 'full-edge' || type === 'end-edge'
                    ? 100
                    : (nodes.length / (nodes.length + 1)) * 100;
            const maxPos = type === 'full-edge' || type === 'end-edge' ? 100 : 100;
            return `${lastNodePos + (maxPos - lastNodePos) * remainingProgress}%`;
        }

        // 计算在当前区间内的相对进度
        const intervalProgress = (current - prevNode.value) / (nextNode.value - prevNode.value);
        const prevIndex = nodes.findIndex((n) => n.value === prevNode.value);
        const nextIndex = nodes.findIndex((n) => n.value === nextNode.value);

        // 计算两个节点的位置百分比
        let prevPos, nextPos;
        if (type === 'full-edge') {
            prevPos = (prevIndex / (nodes.length - 1)) * 100;
            nextPos = (nextIndex / (nodes.length - 1)) * 100;
        } else if (type === 'end-edge') {
            prevPos = ((prevIndex + 1) / nodes.length) * 100;
            nextPos = ((nextIndex + 1) / nodes.length) * 100;
        } else {
            prevPos = ((prevIndex + 1) / (nodes.length + 1)) * 100;
            nextPos = ((nextIndex + 1) / (nodes.length + 1)) * 100;
        }

        // 计算实际进度条位置
        return `${prevPos + (nextPos - prevPos) * intervalProgress}%`;
    };

    const handleClick = (item: {
        id: number;
        value: number;
        claimed?: boolean;
        rewardStatus?: number;
    }) => {
        // 只要不是待领取的状态，都给他展示预览奖励
        const isPreview = getNodeStatus(item.rewardStatus) !== 'unclaimed';

        if (isPreview) {
            setSelectedRewardId(item.id);
            setPreviewVisible(true);
        } else {
            // 让业务自己去处理领取奖励逻辑
            onChoose?.(item.id, item.value);
        }
    };

    return (
        <View className={`progress-wrapper ${className}`}>
            <View className="progress-bar">
                <View
                    className="progress-fill"
                    style={{
                        width: getProgressWidth(),
                    }}
                />
            </View>
            <View className="progress-nodes">
                {nodes.map((node, index) => (
                    <View
                        key={node.value}
                        className="node-item"
                        onClick={() => handleClick(node)}
                        style={{
                            left: getNodePosition(index, nodes.length),
                        }}>
                        <Image
                            className="node-icon"
                            src={ICON_MAP[getNodeStatus(node.rewardStatus)]}
                        />
                        <View className="node-value">{node.label}</View>
                    </View>
                ))}
            </View>

            {previewVisible ? (
                <PreviewModal
                    rewardId={selectedRewardId}
                    show={previewVisible}
                    setPreviewVisible={setPreviewVisible}
                />
            ) : null}
        </View>
    );
};

export default Progress;
