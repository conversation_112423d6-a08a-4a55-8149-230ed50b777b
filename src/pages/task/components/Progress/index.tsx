import React from 'react';
import { View } from '@tarojs/components';
import './index.scss';

interface ProgressBarProps {
    current: number;
    total: number;
    className?: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
    current,
    total,
    className,
}: ProgressBarProps) => {
    // 计算进度百分比，确保不超过100%
    const percentage = Math.min(Math.max((current / total) * 100, 0), 100);

    return (
        <View className={`progress-bar-container ${className || ''}`}>
            <View className="progress-bar-fill" style={{ width: `${percentage}%` }} />
        </View>
    );
};

export default ProgressBar;
