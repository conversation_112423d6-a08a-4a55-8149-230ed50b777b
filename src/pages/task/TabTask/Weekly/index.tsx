import React, { memo, useCallback, useMemo, useState } from 'react';
import { Text, View } from '@tarojs/components';
import Timer from '@/components/Timer';
import useReceiveBottom from '@/pages/task/TabTask/useReceiveBottom';
import RewardProgress from '../../components/RewardProgress';
import TaskList from '../../components/TaskList';
import SussessModal from '../../components/SuccessModal';
import useStore from '../../store/task';
import { TaskRewardStatus, TaskStatus, TaskStoreType, TaskType } from '../../type';

const Weekly = () => {
    const countdown = useStore((state: TaskStoreType) => state.weeklyCountdown);
    const weeklyLimitValue = useStore((state: TaskStoreType) => state.weeklyLimitValue);
    const weeklyActiveValue = useStore((state: TaskStoreType) => state.weeklyActiveValue);
    const weeklyTaskList = useStore((state: TaskStoreType) => state.weeklyTaskList);
    const weeklyRewardList = useStore((state: TaskStoreType) => state.weeklyRewardList);
    const receiveActiveAward = useStore((state: TaskStoreType) => state.receiveActiveAward);
    const init = useStore((state: TaskStoreType) => state.init);

    const [rewardIds, setRewardIds] = useState([]);
    const [receiveSuccess, setReceiveSuccess] = useState(false);

    const handleChooseReward = async (id: number) => {
        // 2代表周任务，写死就行
        const allRewardIds =
            weeklyRewardList
                ?.filter((item) => item.rewardStatus === TaskRewardStatus.NotReward)
                ?.map((item) => {
                    return item.id;
                }) || [];
        setRewardIds(allRewardIds);
        try {
            await receiveActiveAward(id, TaskType.Weekly);
            setReceiveSuccess(true);
        } catch (e) {
            console.log(e);
        }
    };

    const onEnd = useCallback(() => {
        init();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const showReceiveAll = useMemo(() => {
        return (
            weeklyTaskList.some((item) => item.userMissionStatus === TaskStatus.canReceive) ||
            weeklyRewardList.some((item) => item.rewardStatus === TaskRewardStatus.NotReward)
        );
    }, [weeklyTaskList, weeklyRewardList]);

    const onReceiverAll = useCallback(() => {
        useStore
            .getState()
            .receiveAll(TaskType.Weekly)
            .then((allRewardIds) => {
                if (allRewardIds && allRewardIds.length > 0) {
                    setRewardIds(allRewardIds);
                    setReceiveSuccess(true);
                }
            });
    }, []);

    const buttonBottom = useReceiveBottom(showReceiveAll);

    return (
        <View className="task-daily-wrapper">
            <View className="daily-progress">
                <View className="tips">
                    <View className="favorite">
                        <Text className="desc">当前活跃度 </Text>
                        <Text className="num">{weeklyActiveValue}</Text>
                    </View>
                    <Text className="countdown">
                        <View className="num">
                            <Timer
                                initTime="--天--小时--分"
                                format="{dd}天{hh}小时{mm}分"
                                onEnd={onEnd}
                                downDuration={60 * 1000}
                                time={countdown}
                            />
                        </View>
                    </Text>
                </View>
                <View className="task-progress">
                    <RewardProgress
                        type="end-edge"
                        total={weeklyLimitValue}
                        current={weeklyActiveValue}
                        nodes={weeklyRewardList}
                        onChoose={handleChooseReward}
                    />
                </View>
            </View>
            <TaskList list={weeklyTaskList} type={TaskType.Weekly} />

            {showReceiveAll && (
                <View className="receive-all-container" style={{ bottom: `${buttonBottom}px` }}>
                    <View className="receive-all-btn" onClick={onReceiverAll} />
                </View>
            )}

            {receiveSuccess && rewardIds && rewardIds.length > 0 ? (
                <SussessModal
                    rewardIds={rewardIds}
                    show={receiveSuccess}
                    setSuccessVisible={setReceiveSuccess}
                />
            ) : null}
        </View>
    );
};

export default memo(Weekly);
