/* eslint-disable no-console */
import Timer from '@/components/Timer';
import { Text, View } from '@tarojs/components';
import React, { memo, useCallback, useMemo, useState } from 'react';
import useReceiveBottom from '@/pages/task/TabTask/useReceiveBottom';
import RewardProgress from '../../components/RewardProgress';
import SussessModal from '../../components/SuccessModal';
import TaskList from '../../components/TaskList';
import useStore from '../../store/task';
import { TaskRewardStatus, TaskStatus, TaskStoreType, TaskType } from '../../type';

const Daily = () => {
    const countdown = useStore((state: TaskStoreType) => state.dailyCountdown);
    const dailyLimitValue = useStore((state: TaskStoreType) => state.dailyLimitValue);
    const dailyActiveValue = useStore((state: TaskStoreType) => state.dailyActiveValue);
    const dailyTaskList = useStore((state: TaskStoreType) => state.dailyTaskList);
    const dailyRewardList = useStore((state: TaskStoreType) => state.dailyRewardList);
    const receiveActiveAward = useStore((state: TaskStoreType) => state.receiveActiveAward);
    const init = useStore((state: TaskStoreType) => state.init);

    const [rewardIds, setRewardIds] = useState([]);
    const [receiveSuccess, setReceiveSuccess] = useState(false);

    const handleChooseReward = async (id: number) => {
        // 1代表日任务，写死就行
        // 遍历所有能获取奖励的任务，获取到id
        const allRewardIds =
            dailyRewardList
                ?.filter((item) => item.rewardStatus === TaskRewardStatus.NotReward)
                ?.map((item) => {
                    return item.id;
                }) || [];
        setRewardIds(allRewardIds);
        try {
            await receiveActiveAward(id, TaskType.Daily);
            setReceiveSuccess(true);
        } catch (e) {
            console.log(e);
        }
    };

    const onEnd = useCallback(() => {
        init();

        // 为了消除前端分钟倒计时时间和服务端的误差，这里多请求1次，误差在1分钟以内
        const timer = setTimeout(init, 1000 * 60);

        return () => {
            clearTimeout(timer);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const showReceiveAll = useMemo(() => {
        return (
            dailyTaskList.some((item) => item.userMissionStatus === TaskStatus.canReceive) ||
            dailyRewardList.some((item) => item.rewardStatus === TaskRewardStatus.NotReward)
        );
    }, [dailyRewardList, dailyTaskList]);

    const onReceiverAll = useCallback(() => {
        useStore
            .getState()
            .receiveAll(TaskType.Daily)
            .then((allRewardIds) => {
                if (allRewardIds && allRewardIds.length > 0) {
                    setRewardIds(allRewardIds);
                    setReceiveSuccess(true);
                }
            });
    }, []);

    const buttonBottom = useReceiveBottom(showReceiveAll);

    return (
        <View className="task-daily-wrapper">
            <View className="daily-progress">
                <View className="tips">
                    <View className="favorite">
                        <Text className="desc">当前活跃度 </Text>
                        <Text className="num">{dailyActiveValue}</Text>
                    </View>
                    <Text className="countdown">
                        <View className="num">
                            <Timer
                                initTime="--小时--分"
                                format="{hh}小时{mm}分"
                                onEnd={onEnd}
                                downDuration={60 * 1000}
                                time={countdown}
                            />
                        </View>
                    </Text>
                </View>
                <View className="task-progress">
                    <RewardProgress
                        type="end-edge"
                        total={dailyLimitValue}
                        current={dailyActiveValue}
                        nodes={dailyRewardList}
                        onChoose={handleChooseReward}
                    />
                </View>
            </View>
            <TaskList list={dailyTaskList} type={TaskType.Daily} />

            {showReceiveAll && (
                <View className="receive-all-container" style={{ bottom: `${buttonBottom}px` }}>
                    <View className="receive-all-btn" onClick={onReceiverAll} />
                </View>
            )}

            {receiveSuccess && rewardIds && rewardIds.length > 0 ? (
                <SussessModal
                    rewardIds={rewardIds}
                    show={receiveSuccess}
                    setSuccessVisible={setReceiveSuccess}
                />
            ) : null}
        </View>
    );
};

export default memo(Daily);
