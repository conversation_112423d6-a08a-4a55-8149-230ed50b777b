import React, { useState, useEffect } from 'react';
import { View } from '@tarojs/components';
import Tabs from '../components/Tabs';
import Daily from './Daily';
import Weekly from './Weekly';

import useStore from '../store/task';
import { TaskStoreType, TaskStatus, TaskRewardStatus } from '../type';

import './index.scss';

interface TaskProps {
    onReachEdge?: (direction: 'left' | 'right') => void;
}

const tabs = ['每日', '每周'];
const Task = ({ onReachEdge }: TaskProps) => {
    const init = useStore((state: TaskStoreType) => state.init);

    // 为了计算红点
    const dailyTaskList = useStore((state: TaskStoreType) => state.dailyTaskList);
    const dailyRewardList = useStore((state: TaskStoreType) => state.dailyRewardList);
    const weeklyTaskList = useStore((state: TaskStoreType) => state.weeklyTaskList);
    const weeklyRewardList = useStore((state: TaskStoreType) => state.weeklyRewardList);

    const [redPointMap, setRedPointMap] = useState({
        0: false,
        1: false,
    });

    useEffect(() => {
        // 计算每日任务红点
        const hasDailyTaskToReceive = dailyTaskList.some(
            (task) => task.userMissionStatus === TaskStatus.canReceive
        );
        // 计算每日活跃度奖励红点
        const hasDailyRewardToReceive = dailyRewardList.some(
            (reward) => reward.rewardStatus === TaskRewardStatus.NotReward
        );

        // 计算每周任务红点
        const hasWeeklyTaskToReceive = weeklyTaskList.some(
            (task) => task.userMissionStatus === TaskStatus.canReceive
        );
        // 计算每周活跃度奖励红点
        const hasWeeklyRewardToReceive = weeklyRewardList.some(
            (reward) => reward.rewardStatus === TaskRewardStatus.NotReward
        );

        // 更新红点状态
        setRedPointMap({
            0: hasDailyTaskToReceive || hasDailyRewardToReceive,
            1: hasWeeklyTaskToReceive || hasWeeklyRewardToReceive,
        });
    }, [dailyTaskList, dailyRewardList, weeklyTaskList, weeklyRewardList]);

    useEffect(() => {
        // 不依赖任何参数，只请求1次
        init();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <View className="task-wrapper">
            <Tabs
                tabs={tabs}
                items={[Daily, Weekly]}
                onReachEdge={onReachEdge}
                redPointMap={redPointMap}
            />
        </View>
    );
};

export default Task;
