/* eslint-disable no-console */
import { useCallback, useEffect, useState } from 'react';
import Taro, { getSystemInfo, createSelectorQuery } from '@tarojs/taro';

function useReceiveBottom(showReceiveAll: boolean) {
    const [buttonBottom, setButtonBottom] = useState(0);
    const calculateBottomOffset = useCallback(() => {
        const query = createSelectorQuery();
        query
            .select('.task-daily-wrapper')
            .boundingClientRect()
            .exec((receiveRect) => {
                console.log('Daily->>task-daily-wrapper', receiveRect);
                if (receiveRect && receiveRect[0]) {
                    // 获取屏幕高度
                    getSystemInfo({
                        success: (res) => {
                            console.log('Daily->>getSystemInfo', res);
                            const screenHeight = res.windowHeight ?? 0;
                            const { bottom } = receiveRect[0];

                            const distanceToBottom = bottom - screenHeight;
                            setButtonBottom(Math.abs(distanceToBottom));
                            console.log('Daily->>调整底部偏移为:', distanceToBottom);
                        },
                    });
                }
            });
    }, []);

    useEffect(() => {
        if (showReceiveAll) {
            Taro.nextTick(() => {
                calculateBottomOffset();
            });
        }
    }, [calculateBottomOffset, showReceiveAll]);

    return showReceiveAll ? buttonBottom : 0;
}

export default useReceiveBottom;
