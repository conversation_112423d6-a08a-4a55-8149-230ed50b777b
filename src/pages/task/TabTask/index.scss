.task-wrapper {
    color: #000;
    font-family: 'PingFang SC', sans-serif;
    font-size: 12px;
    box-sizing: border-box;

    /* stylelint-disable-next-line selector-type-no-unknown */
    taro-swiper-core .swiper-container {
        overflow-y: hidden !important;
        overflow-x: hidden !important;
        height: calc(100% - 20px);
        @supports (env(safe-area-inset-bottom, 0)) {
            height: calc(100% - 20px - env(safe-area-inset-bottom, 0));
        }

        border-radius: 20px;
        margin-top: 17px;

        &::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
            width: 0;
            background: transparent;
        }
    }

    .task-progress {
        width: 100%;
        padding-left: 20px;
        padding-right: 34px;
    }

    .task-daily-wrapper {
        color: #000;
        box-sizing: border-box;
        margin-bottom: 120px;
        padding: 0 10px;

        .daily-progress {
            width: calc(100vw - 40px);
            height: 117px;
            border-radius: 20px;
            background-color: #fff;
            margin-bottom: 17px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .tips {
            padding: 0 20px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;

            .desc {
                opacity: 0.6;
                font-size: 12px;
            }

            .num {
                font-weight: 600;
            }

            .countdown {
                display: flex;
                align-items: center;
                justify-content: center;

                .num {
                    opacity: 0.4;
                    font-weight: 400;
                }

                &::before {
                    content: '';
                    display: inline-block;
                    margin-right: 3px;
                    width: 12px;
                    height: 12px;
                    background: url(../../../assets/task/task/countdown.png) no-repeat;
                    background-size: 100% 100%;
                }
            }
        }
    }

    .receive-all-container {
        position: absolute;
        bottom: 20px;
        left: 0;
        right: 0;
        height: 89px;
        background: rgba(255, 255, 255, 0.6);
        backdrop-filter: blur(2.5px);
        transform: translateZ(0);
        will-change: transform;
        isolation: isolate;
        contain: paint;
        z-index: 10;

        .receive-all-btn {
            position: absolute;
            top: 6px;
            right: 34px;
            width: 108px;
            height: 34px;
            background: url('../../../assets/task/task/btn-receive-all.png') no-repeat center / 100%
                100%;
        }
    }
}
