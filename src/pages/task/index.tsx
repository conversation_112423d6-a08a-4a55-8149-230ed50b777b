import React, { useState, useRef, useEffect } from 'react';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import { View, ScrollView, Image } from '@tarojs/components';
import EventTrackView from '@/components/EventTrack';
import backIcon from '@/assets/header/icon-back.png';
import Sign from './TabSign';
import Task from './TabTask';
import useStore from './store';
import './index.scss';

const tabs = ['签到', '任务'];
const Main = () => {
    const { tab = 0 } = getCurrentInstance().router?.params || {};

    const signInRedPoint = useStore((state) => state?.signInRedPoint);
    const missionRedPoint = useStore((state) => state?.missionRedPoint);

    const [currentTab, setCurrentTab] = useState<number>(+tab);
    const scrollRef = useRef(null);

    const redPointMap: any = {
        0: signInRedPoint,
        1: missionRedPoint,
    };

    // 添加一个标志位，用于控制是否响应滚动事件
    const [isScrolling, setIsScrolling] = useState(false);

    // 处理滑动切换
    const handleScroll = (e: { detail: { scrollLeft: any; scrollWidth: any } }) => {
        // 如果正在进行点击切换的滚动，则不响应滚动事件
        if (isScrolling) return;

        const { scrollLeft = 0, scrollWidth = 0 } = e?.detail || {};
        const tabWidth = scrollWidth / tabs.length;

        // 根据滚动位置判断当前应该显示哪个tab
        const targetIndex = scrollLeft >= tabWidth / 2 ? 1 : 0;

        if (targetIndex !== currentTab) {
            setCurrentTab(targetIndex);
        }
    };

    // 处理点击切换
    const handleTabClick = (index: number) => {
        if (index === currentTab) return; // 如果点击当前tab，不做任何操作

        setCurrentTab(index);
        // 设置标志位，禁用滚动事件响应
        setIsScrolling(true);

        if (scrollRef.current && scrollRef.current.scrollTo) {
            scrollRef.current.scrollTo({
                left: index * (scrollRef.current.scrollWidth / tabs.length),
                behavior: 'smooth',
            });

            // 滚动动画完成后，重新启用滚动事件响应
            setTimeout(() => {
                setIsScrolling(false);
            }, 300); // 300ms 应该足够滚动动画完成
        }
    };

    // 处理滑动结束事件
    const handleScrollEnd = () => {
        // 滑动结束后，确保滚动到正确的位置
        if (!scrollRef.current) return;

        const scrollWidth = scrollRef.current.scrollWidth;
        const scrollLeft = scrollRef.current.scrollLeft;
        const tabWidth = scrollWidth / tabs.length;

        // 特殊处理iOS的回弹效果
        if (scrollLeft <= 10) {
            // 第一个tab的处理
            setIsScrolling(true);
            scrollRef.current.scrollLeft = 0;
            setCurrentTab(0);
        } else if (scrollLeft >= tabWidth - 50) {
            // 第二个tab的处理
            setIsScrolling(true);
            scrollRef.current.scrollLeft = tabWidth;
            setCurrentTab(1);
        } else {
            // 一般情况下的处理
            const currentIndex = Math.round(scrollLeft / tabWidth);

            if (scrollRef.current.scrollTo) {
                scrollRef.current.scrollTo({
                    left: currentIndex * tabWidth,
                    behavior: 'smooth',
                });
            }
            setCurrentTab(currentIndex);
        }
    };

    // 处理滑动边界
    const handleReachEdge = (direction: 'left' | 'right') => {
        if (direction === 'left' && currentTab > 0) {
            // 向左滑动到边缘，切换到前一个tab
            handleTabClick(currentTab - 1);
        } else if (direction === 'right' && currentTab < tabs.length - 1) {
            // 向右滑动到边缘，切换到后一个tab
            handleTabClick(currentTab + 1);
        }
    };

    // 初始化时，根据 URL 参数设置滚动位置
    useEffect(() => {
        // 确保 DOM 已经渲染完成
        setTimeout(() => {
            if (scrollRef.current && +tab > 0 && scrollRef.current.scrollTo) {
                scrollRef.current.scrollTo({
                    left: +tab * (scrollRef.current.scrollWidth / tabs.length),
                    behavior: 'smooth',
                });
            }
        }, 50);
    }, [tab]);

    return (
        <View className="page-task-wrapper">
            <EventTrackView
                params={{
                    _spm: 'page_ai_active_system|page_h5_biz',
                }}
            />
            <View className="scroll-tabs">
                <View className="scroll-header">
                    <View className="header-icon" onClick={() => Taro.navigateBack()}>
                        <Image src={backIcon} />
                    </View>
                    <View className="tab-header">
                        {tabs.map((item, index) => (
                            <View
                                key={item}
                                className={`tab-item ${currentTab === index ? 'active' : ''} ${
                                    redPointMap[index] ? 'has-dot' : ''
                                }`}
                                onClick={() => handleTabClick(index)}>
                                {item}
                            </View>
                        ))}
                    </View>
                </View>
                <ScrollView
                    className="tab-content"
                    scrollX
                    scrollWithAnimation
                    ref={scrollRef}
                    onScroll={handleScroll}
                    onTouchEnd={handleScrollEnd}
                    onTouchCancel={handleScrollEnd}>
                    <View className="tab-content-inner">
                        <View className="tab-pane">
                            <Sign />
                        </View>
                        <View className="tab-pane">
                            <Task onReachEdge={handleReachEdge} />
                        </View>
                    </View>
                </ScrollView>
            </View>
        </View>
    );
};

export default Main;
