import React, { useCallback, useEffect, useRef, useState } from 'react';
import { ITouchEvent, View } from '@tarojs/components';
import FullscreenModal from '@/components/FullScreenModal';
import SignList from '../components/SignList';

import SussessModal from '../components/SuccessModal';
import PreviewModal from '../components/PreviewModal';
import useStore from '../store/sign';
import { SignListRef, SignStoreType } from '../type';

import './index.scss';

interface IModalSign {
    show: boolean;
    onClose: () => void;
}

const ModalSign = ({ show, onClose }: IModalSign) => {
    const childRef = useRef<SignListRef>(null);
    const [signed, setSigned] = useState(false);
    const [rewardId, setRewardId] = useState(0);
    const [previewVisible, setPreviewVisible] = useState(false);
    const [modalVisible, setModalVisible] = useState(true);

    const init = useStore((state: SignStoreType) => state.init);
    const isLoading = useStore((state: SignStoreType) => state.isLoading);
    const signinSuccess = useStore((state: SignStoreType) => state.signinSuccess);
    const changeSigninStatus = useStore((state: SignStoreType) => state.changeSigninStatus);

    const closeModal = useCallback(() => {
        // 不能直接关闭，需要等成功弹框关闭以后才能真正销毁
        setModalVisible(false);
    }, []);

    const handleClick = useCallback(() => {
        if (childRef.current) {
            childRef.current.triggerSigninClick();
        }

        setSigned(true);
    }, [setSigned]);

    const onChooseReward = useCallback((id: number) => {
        setRewardId(id);
    }, []);

    useEffect(() => {
        // 不依赖任何参数，只请求1次
        init();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <FullscreenModal onClose={onClose} visible={show}>
            <View
                id="signModal"
                className="sign-modal"
                style={{
                    display: modalVisible ? 'flex' : 'none',
                }}
                onClick={(e: ITouchEvent) => {
                    e.stopPropagation();
                }}>
                <View className="sign-content">
                    <SignList
                        className="sign-modal-list"
                        closeModal={closeModal}
                        ref={childRef}
                        onChooseReward={onChooseReward}
                        setPreviewVisible={setPreviewVisible}
                    />
                </View>
                <View
                    className={`btn ${signed ? 'signed' : ''} ${isLoading ? 'loading' : ''}`}
                    {...(!signed &&
                        !isLoading && {
                            onClick: handleClick,
                        })}
                />

                {signinSuccess && rewardId ? (
                    <SussessModal
                        rewardId={rewardId}
                        show={signinSuccess}
                        setSuccessVisible={changeSigninStatus}
                    />
                ) : null}
                {previewVisible ? (
                    <PreviewModal
                        rewardId={rewardId}
                        show={previewVisible}
                        setPreviewVisible={setPreviewVisible}
                    />
                ) : null}
            </View>
        </FullscreenModal>
    );
};

export default ModalSign;
