// eslint-disable-next-line max-classes-per-file
import useUserInfoStore from '@/store/useUserInfoStore';
import { RedDotType } from './utils';

export const KEY_APPRECIATE_TYPE = {
    KEY_CHAPTER: 'chapter',
    KEY_ENDING: 'ending',
};

/**
 * tab 数据~
 */
export class GameRobotTabDto {
    robotName: string;

    robotUserId: number;
}

export interface GameCardItemDto {
    chapterName: string;
    chapterId: string;
    roleId: string;
    endingName: string;
    endingId: string;
    cardImg: string;
    robotName: string;
    robotUserId: number;
    /**
     * 用户是否拥有
     */
    ownership?: boolean;
}

export interface GameCardTypeDto {
    cardLevel: string;
    cardLevelDesc: string;
    cardItem: GameCardItemDto[];
}

export interface GameCardTabDto {
    roleTableInfo: GameRobotTabDto[];
    showDetail: GameCardTypeDto[];
}

export class GameCardFormateDto {
    r: GameCardItemDto[];

    sr: GameCardItemDto[];

    ssr: GameCardItemDto[];
}

export const TAG_COLLECT_CARDSUPDATED = 'collect-cardsUpdated';
export const TAG_COLLECT_REDUPDATED = 'collect-redUpdated';

export function getTypeKey(type: string) {
    const self = useUserInfoStore.getState().userBaseInfo;
    return `${self?.userBase?.userId}-${type}`;
}

export function getTypeData(type: string, redDot: RedDotType) {
    return redDot?.get(getTypeKey(type));
}

export function checkTypeNewTag(type: string, redDot: RedDotType) {
    return (redDot?.get(getTypeKey(type))?.size || -1) > 0;
}
