.collect-card-preview-container {
    display: flex;
    position: relative;
    flex-direction: column;
    overflow: auto;
    height: 100%;
    width: 100%;
    padding-bottom: 60px;
    padding-top: 4px;
    -webkit-mask-image: linear-gradient(
        to bottom,
        rgba(0, 0, 0) 0%,
        rgba(0, 0, 0) 90%,
        rgba(0, 0, 0, 0) 100%
    );

    .gap {
        margin-top: 12px;
    }

    .gap-bottom {
        margin-bottom: 50px;
    }

    .title-ssr {
        width: 59px;
        height: 25px;
        margin-left: 19px;
    }

    .title-sr {
        width: 48px;
        height: 25px;
        margin-left: 19px;
    }

    .title-lr {
        width: 37px;
        height: 25px;
        margin-left: 19px;
    }

    .emptyContent {
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        color: white;
        top: 211px;
        pointer-events: none;
        font-size: 14px;
        opacity: 0.4;
        padding: 10px;
    }
}

.collect-card-preview-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-shrink: 0;
    position: relative;
    width: 105px;

    /* stylelint-disable-next-line selector-type-no-unknown */
    taro-image-core {
        overflow: visible;
    }

    .item-images {
        width: 105px;
        height: 186.67px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .item-images-tag-new {
        width: 43.98px;
        height: 34px;
        background-size: 100% 100%;
        background-repeat: repeat;
        background-position: center;
        position: absolute;
        top: -8px;
        right: -0.98px;
        z-index: 2;
    }

    .item-image {
        overflow: hidden;
        border-radius: 8px;
        width: 100%;
        height: 100%;
        position: relative;

        .img {
            width: 100%;
            height: 100%;
            border-radius: 8px;
            object-fit: cover;
            object-position: center;

            &.unPossessed {
                filter: blur(3px);
                overflow: hidden;
            }
        }

        .mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: black;
            opacity: 0.2;
        }
    }

    .item-name {
        text-align: center;
        font-size: 12px;
        color: #fff;
        margin-top: 7px;
        width: 100%;
        word-wrap: break-word;
    }
}
