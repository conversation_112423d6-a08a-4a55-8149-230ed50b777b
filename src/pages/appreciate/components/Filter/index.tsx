import { View, Image } from '@tarojs/components';
import React, { memo, useCallback } from 'react';
import appreciate_icon_filter_selected from '@/assets/appreciate/appreciate_icon_filter_selected.png';
import appreciate_icon_filter_line from '@/assets/appreciate/appreciate_icon_filter_line.png';
import { usePageStore } from '@/components/storeContext/StoreContext';
import cardCollectStore, { FilterType } from '../../store/cardCollectStore';

import usePopupStore from '../../store/usePopupStore';
import { MainPlotToastStore } from '../../../chat/components/ChatPlot/MainPlotList/Toast/MainPlotToastStore';

import './index.scss';

const FilterComponents = () => {
    const filterValue = cardCollectStore((state) => state.filterValue);
    const mainPlotToastStore = usePageStore(MainPlotToastStore);

    const handleClickForAll = useCallback(
        (e: { stopPropagation: () => void }) => {
            mainPlotToastStore
                .getState()
                .showToast({ scene: 'appreciateList', content: `展示全部` });
            e.stopPropagation();
            usePopupStore.getState().setShowFilter(false);
            cardCollectStore.getState().filter(FilterType.ALL);
        },
        [mainPlotToastStore]
    );
    const handleClickForPossessed = useCallback(
        (e: { stopPropagation: () => void }) => {
            mainPlotToastStore
                .getState()
                .showToast({ scene: 'appreciateList', content: `展示已拥有` });
            e.stopPropagation();
            usePopupStore.getState().setShowFilter(false);
            cardCollectStore.getState().filter(FilterType.POSSESSED);
        },
        [mainPlotToastStore]
    );

    return (
        <View className="appreciate-filter-root">
            <View className="func-container" onClick={handleClickForAll}>
                <View className="func">全部</View>
                {filterValue === FilterType.ALL && (
                    <Image src={appreciate_icon_filter_selected} className="select" />
                )}
            </View>
            <Image src={appreciate_icon_filter_line} />
            <View className="func-container" onClick={handleClickForPossessed}>
                <View className="func">已拥有</View>
                {filterValue === FilterType.POSSESSED && (
                    <Image src={appreciate_icon_filter_selected} className="select" />
                )}
            </View>
        </View>
    );
};

export default memo(FilterComponents);
