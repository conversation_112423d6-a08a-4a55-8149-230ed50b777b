.appreciate-filter-root {
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 60.91%, rgba(0, 0, 0, 0) 100%);
    border: 0.5px solid;
    border-image-source: linear-gradient(
        113.74deg,
        rgba(255, 255, 255, 0.3) 5.85%,
        rgba(255, 255, 255, 0.1) 92.68%
    );
    box-shadow: 0 0 5px 0 rgba(255, 255, 255, 0.5) inset;
    backdrop-filter: blur(40px);
    width: 84px;
    border-radius: 8px;
    display: flex;
    height: auto;
    flex-direction: column;

    .func-container {
        padding-left: 10.5px;
        padding-right: 10.5px;
        padding-top: 6px;
        padding-bottom: 6px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        .func {
            font-size: 11px;
            line-height: 16px;
            color: white;
        }

        .select {
            width: 12px;
            height: 12px;
        }
    }
}
