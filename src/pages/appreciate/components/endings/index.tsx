import { View, Image, Text } from '@tarojs/components';
import React, { memo } from 'react';
import TITLE_LR from '@/assets/game/gacha/title_lr.png';
import TLTLE_LSR from '@/assets/game/gacha/title_lsr.png';
import TITLE_LSSR from '@/assets/game/gacha/title_lssr.png';
import { optimizeImage } from '@/utils/image';
import { endingDetailApi } from '@/service/profileApi';
import appreciate_item_new from '@/assets/appreciate/appreciate_item_new.png';
import appreciate_item_star_icon from '@/assets/appreciate/appreciate_item_star_icon.png';

import { jump2EndingInfo } from '@/router';

import classNames from 'classnames';
import appreciate_icon_lock from '@/assets/appreciate/appreciate_icon_lock.png';
import cardCollectStore, { FilterType } from '../../store/cardCollectStore';

import { GameCardItemDto } from '../../const';

import './index.scss';

const CardtemComponent: React.FC<{
    item: GameCardItemDto;
}> = ({ item }: { item: GameCardItemDto }) => {
    // 用来触发刷新~
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const redDot = cardCollectStore((state) => state.redDot);
    const optimizedSrc = optimizeImage({
        src: item?.cardImg,
        width: 105,
        height: 186,
    });

    const handleShowCardPreview = async () => {
        try {
            const res = await endingDetailApi({
                chapterId: item.chapterId,
            });
            if (!res || res.length <= 0) {
                return;
            }
            const userId = item.robotUserId;
            const endingStr = encodeURIComponent(JSON.stringify(res[0]));
            const ending2Str = encodeURIComponent(res[1] ? JSON.stringify(res[1]) : '');
            const endingId = item.endingId;

            let uiState = 2;
            if (res[0].endingCardId !== endingId) {
                uiState = 3;
            }
            jump2EndingInfo({
                uiState,
                userId: `${userId}`,
                ending: endingStr,
                ending2: ending2Str,
                from: null,
            });
        } catch (e) {
            // 错误
        }
    };

    const renderName = () => {
        return (
            <React.Fragment>
                <View className="text-[white] text-[12px] font-[600]">
                    {(item?.robotName || '').slice(0, 8)}
                </View>
                <View className="text-[white] text-[12px]">
                    {(item?.endingName || '').slice(0, 8)}
                </View>
                <View className="inline-flex self-start flex-row items-center bg-[#FFFFFF1A] pl-3 pr-3 rounded-[4px]">
                    <Image src={appreciate_item_star_icon} className="w-[12px] h-[12px] mr-1" />
                    <Text className="text-white opacity-60 text-[12px]">
                        {(item?.chapterName || '').slice(0, 7)}
                    </Text>
                </View>
            </React.Fragment>
        );
    };

    const ownership = item.ownership ?? true;
    const newTag = cardCollectStore
        .getState()
        .checkNewTag('ending', `${item.robotUserId}`, undefined, item.endingId);
    return (
        <View className="collect-ending-preview-item" onClick={handleShowCardPreview}>
            <View className="item-images">
                <View className="item-image">
                    <Image
                        src={optimizedSrc}
                        className={classNames('img', {
                            unPossessed: !ownership,
                        })}
                    />
                    {!ownership && (
                        <React.Fragment>
                            <View className="mask" />

                            <Image
                                src={appreciate_icon_lock}
                                className="absolute w-[12px] h-[14.5px] left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"
                            />
                        </React.Fragment>
                    )}
                </View>
                {newTag && <Image src={appreciate_item_new} className="item-images-tag-new" />}
            </View>
            <View className="item-name">{renderName()}</View>
        </View>
    );
};

const Card = ({ index }: { index: number }) => {
    const cardsFormate = cardCollectStore((state) => state.endingCardsFormate);
    const robotUserId = cardCollectStore((state) => state.endingCardsInfo)[index]?.robotUserId;
    const filterValue = cardCollectStore((state) => state.filterValue);
    const collectR = cardsFormate?.r?.filter(
        (item) =>
            (robotUserId === -9999 || item.robotUserId === robotUserId) &&
            (filterValue === FilterType.ALL || item.ownership)
    );
    const collectSr = cardsFormate?.sr?.filter(
        (item) =>
            (robotUserId === -9999 || item.robotUserId === robotUserId) &&
            (filterValue === FilterType.ALL || item.ownership)
    );
    const collectSsr = cardsFormate?.ssr?.filter(
        (item) =>
            (robotUserId === -9999 || item.robotUserId === robotUserId) &&
            (filterValue === FilterType.ALL || item.ownership)
    );
    return (
        <View className="collect-ending-preview-container">
            {collectSsr?.length > 0 && (
                <View className="gap">
                    <Image src={TITLE_LSSR} className="title-ssr" />
                    <div className="flex-1 grid grid-cols-3 gap-y-[12px] ml-[13.5px]">
                        {collectSsr.map((item: GameCardItemDto) => (
                            <CardtemComponent
                                key={`${item?.robotUserId}-${item?.endingId}`}
                                item={item}
                            />
                        ))}
                    </div>
                </View>
            )}

            {collectSr?.length > 0 && (
                <View className="gap">
                    <Image src={TLTLE_LSR} className="title-sr" />
                    <div className="flex-1 grid grid-cols-3 gap-y-[12px] ml-[13.5px]">
                        {collectSr.map((item: GameCardItemDto) => (
                            <CardtemComponent
                                key={`${item?.robotUserId}-${item?.endingId}`}
                                item={item}
                            />
                        ))}
                    </div>
                </View>
            )}
            {collectR?.length > 0 && (
                <View className="gap">
                    <Image src={TITLE_LR} className="title-lr" />
                    <div className="flex-1 grid grid-cols-3 gap-y-[12px] ml-[13.5px]">
                        {collectR.map((item: GameCardItemDto) => (
                            <CardtemComponent
                                key={`${item?.robotUserId}-${item?.endingId}`}
                                item={item}
                            />
                        ))}
                    </div>
                </View>
            )}
            <View className="gap-bottom" />

            {(collectSsr?.length || 0) === 0 &&
                (collectSr?.length || 0) === 0 &&
                (collectR?.length || 0) === 0 && (
                    <View className="emptyContent">当前未获得任何结局</View>
                )}
        </View>
    );
};

export default memo(Card);
