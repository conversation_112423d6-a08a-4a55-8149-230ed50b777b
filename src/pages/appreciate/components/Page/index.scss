.page_dex {
    .tabs .tabs__labels .tabs__labels__item {
        font-weight: normal;
        font-size: 14px !important;
        color: rgba(255, 251, 229, 0.6);
    }

    .tabs .tabs__labels .tabs__labels__item--active {
        color: rgba(255, 251, 229, 1);
        text-shadow: 0 0 4px 2px rgba(255, 158, 68, 1) inset !important;
    }

    .collect-tab-head-selected {
        position: absolute;
        top: 0;
        transition: none;
        background: rgba(0, 0, 0, 0.2) !important;
        box-shadow: 0 0 8px 0 rgba(255, 255, 255, 1) inset !important;
        border-radius: 15px !important;
        height: 27px !important;
    }

    .collect-tab-head {
        padding-left: 10px;
        padding-right: 10px;
        color: rgba(255, 255, 255, 0.6);
        height: 27px !important;
        line-height: 27px !important;
        font-family: 'SourceHanSerifCN-Bold', sans-serif;

        &.has-dot {
            &::after {
                content: '';
                position: absolute;
                top: 2px;
                right: 2px;
                width: 5px;
                height: 5px;
                background: #ff561e;
                border-radius: 50%;
            }
        }
    }
}
