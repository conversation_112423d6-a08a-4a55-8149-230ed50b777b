import React, { useEffect, useState } from 'react';
import { View } from '@tarojs/components';
import {
    Tabs,
    <PERSON>bsCont,
    TabsContItem,
    TabsHead,
    TabsHeadButtonBar,
    TabsHeadLabel,
} from '@music/ct-mobile-ms-tabs';
import cardCollectStore from '../../store/cardCollectStore';
import CardPage from '../Cards';
import '@music/ct-mobile-ms-tabs/es/style/index.css';
import { getTypeData, KEY_APPRECIATE_TYPE } from '../../const';
import './index.scss';

const { KEY_CHAPTER } = KEY_APPRECIATE_TYPE;
const Page = ({ robotName }: { robotName: string }) => {
    const cardsInfo = cardCollectStore((state) => state.cardsInfo);
    const redDot = cardCollectStore((state) => state.redDot);

    const [activeTab, setActiveTab] = useState<string>('全部');

    useEffect(() => {
        const find = cardsInfo?.find((item) => item.robotName === decodeURIComponent(robotName));
        if (find) {
            setActiveTab(decodeURIComponent(robotName));
        }
    }, [cardsInfo, robotName]);

    return (
        <View className="flex flex-col w-[100%] h-[100%] page_dex overflow-hidden">
            {Number(cardsInfo?.length) > 0 ? (
                <Tabs
                    active={activeTab}
                    tapSlideDuration={0}
                    onChange={(tab: string) => setActiveTab(tab)}
                    className="flex flex-col w-[100%]">
                    <TabsHead className="pl-[25px] relative h-[27px] shrink-0">
                        {cardsInfo?.map((tab) => (
                            <TabsHeadLabel
                                key={tab.robotUserId}
                                name={tab.robotName}
                                title={tab.robotName}
                                className={`collect-tab-head  ${
                                    getTypeData(KEY_CHAPTER, redDot)?.has(`${tab?.robotUserId}`)
                                        ? 'has-dot'
                                        : ''
                                }`}
                            />
                        ))}
                        <TabsHeadButtonBar className="collect-tab-head-selected" />
                    </TabsHead>
                    <TabsCont className="h-[100%] w-[100%]">
                        {cardsInfo?.map((tab, index) => (
                            <TabsContItem name={tab.robotName} key={tab.robotUserId}>
                                <CardPage index={index} />
                            </TabsContItem>
                        ))}
                    </TabsCont>
                </Tabs>
            ) : null}
        </View>
    );
};

export default Page;
