import React from 'react';
import { View, Image } from '@tarojs/components';

import icon_title from '@/assets/appreciate/hint/ic_gacha_hint_title.png';
import icon_btn from '@/assets/appreciate/hint/ic_gacha_hint_btn.png';
import bg_dialog from '@/assets/appreciate/hint/bg_gacha_hint.png';

import './index.scss';

const GachaHintDialogContent = ({ dismiss }: { dismiss: () => void }) => {
    return (
        <View className="gacha-hint-wrapper">
            <Image src={bg_dialog} className="gacha-hint-bg" />
            <View className="gacha-hint-modal">
                <Image src={icon_title} className="gacha-hint-title" />
                <Image src={icon_btn} onClick={dismiss} className="gacha-hint-btn" />
            </View>
        </View>
    );
};

export default GachaHintDialogContent;
