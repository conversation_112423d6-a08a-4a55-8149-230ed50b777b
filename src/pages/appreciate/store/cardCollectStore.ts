import { create } from 'zustand';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { getStorageSync, setStorageSync } from '@tarojs/taro';
import { checkDecomposedReward, LotteryRewardDetail } from '../../gacha/const';
import {
    GameCardFormateDto,
    GameCardItemDto,
    GameCardTabDto,
    GameRobotTabDto,
    getTypeData,
    getTypeKey,
    KEY_APPRECIATE_TYPE,
    TAG_COLLECT_CARDSUPDATED,
    TAG_COLLECT_REDUPDATED,
} from '../const';
import { cardInfoReq } from '../collectApi';
import {
    deserializeNewCards,
    deserializeRedDot,
    NewCardsType,
    RedDotType,
    serializeNewCards,
    serializeRedDot,
} from '../utils';

export enum FilterType {
    ALL = 'all',
    POSSESSED = 'possessed',
    UNPOSSESSED = 'unPossessed',
}

interface ICardCollectStore {
    filterValue: FilterType;
    filter: (value: FilterType) => void;
    newCards: NewCardsType; // key:type、 key 为  robotUserId, value: chaption-itemId
    redDot: RedDotType; // 卡面红点 key:type、 value 为  Set(robotUserId)
    addEnding: (robotUserId: string, endingId: string) => void;
    addCards: (cards: LotteryRewardDetail[]) => void; // 通过抽卡新获得的卡片~
    checkNewTag: (
        type: 'chapter' | 'ending',
        robotUserId: string,
        chapterId: string,
        endingId: string
    ) => boolean; // 校验单张卡片是否有new~
    removeNewTag: (
        type: 'chapter' | 'ending',
        robotUserId: string,
        chapterId: string,
        endingId: string
    ) => void;
    removeWholeNewTag: (type: 'chapter' | 'ending') => void;
    reset: () => void;
    getCards: () => void;
    getEndingCards: () => void;
    // 卡面图鉴~
    cardResp: GameCardTabDto; // 接口返回的原始数据~
    cardsInfo: GameRobotTabDto[]; // tabs~
    cardsFormate: GameCardFormateDto;

    endingCardResp: GameCardTabDto; // 接口返回的原始数据~
    endingCardsInfo: GameRobotTabDto[]; // tabs~
    endingCardsFormate: GameCardFormateDto;
}
const localCard = getStorageSync(TAG_COLLECT_CARDSUPDATED) || '';
const locaRedDot = getStorageSync(TAG_COLLECT_REDUPDATED) || '';
const { KEY_CHAPTER, KEY_ENDING } = KEY_APPRECIATE_TYPE;

export function checkWholeNewTag(redDot: RedDotType) {
    return (
        (getTypeData(KEY_CHAPTER, redDot)?.size || -1) > 0 ||
        (getTypeData(KEY_ENDING, redDot)?.size || -1) > 0
    );
}

const cardCollectStore = create<ICardCollectStore>((set, get) => ({
    filterValue: FilterType.ALL,
    filter: (value) => {
        set({
            filterValue: value,
        });
    },
    newCards: deserializeNewCards(localCard),
    redDot: deserializeRedDot(locaRedDot),
    addEnding: (robotUserId, endingId) => {
        if (robotUserId && endingId) {
            // 结局卡片处理
            const cardsUpdated = get().newCards;
            const redDotUpdated = get().redDot;
            const typeKey = getTypeKey(KEY_ENDING);
            const endingRobot = cardsUpdated.get(typeKey) || new Map();
            const endingSet = endingRobot.get(robotUserId) || new Set();

            const endingKey = `chapterId-${endingId}`;
            endingSet.add(endingKey);

            endingRobot.set(robotUserId, endingSet);
            cardsUpdated.set(typeKey, endingRobot);

            const updateRedDot = (type: string) => {
                const robotSet = new Set<string>();
                cardsUpdated?.get(type)?.forEach((value: { size: number }, key: string) => {
                    if (value?.size > 0) {
                        robotSet.add(key);
                    }
                });
                if (robotSet?.size > 0) {
                    robotSet.add('-9999');
                }
                redDotUpdated.set(type, robotSet);
            };

            updateRedDot(typeKey);
            setStorageSync(TAG_COLLECT_CARDSUPDATED, serializeNewCards(cardsUpdated));
            setStorageSync(TAG_COLLECT_REDUPDATED, serializeRedDot(redDotUpdated));
            set({
                newCards: cardsUpdated,
                redDot: new Map(redDotUpdated),
            });
        }
    },
    addCards: (cards) => {
        const cardsUpdated = get().newCards;
        const redDotUpdated = get().redDot;
        const typeKey = getTypeKey(KEY_CHAPTER);
        cards
            ?.filter((item) => !checkDecomposedReward(item))
            ?.forEach((item) => {
                const robotUserId = item.chapterInfoItem.roleUserId;
                const chapterId = item.chapterInfoItem.chapterId;

                // 章节卡片处理
                const chapterRobot = cardsUpdated.get(typeKey) || new Map();
                const chapterSet = chapterRobot.get(robotUserId) || new Set();

                // 添加基础章节标识
                const chapterKey = `${chapterId}`;
                chapterSet.add(chapterKey);
                chapterRobot.set(robotUserId, chapterSet);
                cardsUpdated.set(typeKey, chapterRobot);

                // 红点处理
                const updateRedDot = (type: string) => {
                    const robotSet = new Set<string>();
                    cardsUpdated?.get(type)?.forEach((value: { size: number }, key: string) => {
                        if (value?.size > 0) {
                            robotSet.add(key);
                        }
                    });
                    if (robotSet?.size > 0) {
                        robotSet.add('-9999');
                    }
                    redDotUpdated.set(type, robotSet);
                };

                updateRedDot(typeKey);
            });
        setStorageSync(TAG_COLLECT_CARDSUPDATED, serializeNewCards(cardsUpdated));
        setStorageSync(TAG_COLLECT_REDUPDATED, serializeRedDot(redDotUpdated));
        set({
            newCards: cardsUpdated,
            redDot: new Map(redDotUpdated),
        });
    },

    checkNewTag: (type, robotUserId, chapterId, endingId) => {
        const typeKey = getTypeKey(type);
        const key = endingId ? `chapterId-${endingId}` : `${chapterId}`;
        const keySet = get().newCards?.get(typeKey)?.get(robotUserId);
        const result = keySet?.has(key) || false;
        return result;
    },

    removeNewTag: (type, robotUserId, chapterId, endingId) => {
        const typeKey = getTypeKey(type);
        const key = endingId ? `chapterId-${endingId}` : `${chapterId}`;
        const cardsUpdated = get().newCards;
        const robotSet = cardsUpdated?.get(typeKey)?.get(robotUserId);
        robotSet?.delete(key);
        const redDotUpdated = get().redDot;
        if (robotSet?.size === 0) {
            const typeMap = redDotUpdated?.get(typeKey);
            typeMap?.delete(robotUserId);
            if (typeMap?.size === 1 && typeMap?.has('-9999')) {
                typeMap.clear();
            }
        }
        setStorageSync(TAG_COLLECT_CARDSUPDATED, serializeNewCards(cardsUpdated));
        setStorageSync(TAG_COLLECT_REDUPDATED, serializeRedDot(redDotUpdated));
        set({
            newCards: cardsUpdated,
            redDot: new Map(redDotUpdated),
        });
    },

    removeWholeNewTag: (type) => {
        const typeKey = getTypeKey(type);
        const cardsUpdated = get().newCards;
        cardsUpdated?.get(typeKey)?.clear();
        const redDotUpdated = get().redDot;
        redDotUpdated?.get(typeKey)?.clear();
        setStorageSync(TAG_COLLECT_CARDSUPDATED, serializeNewCards(cardsUpdated));
        setStorageSync(TAG_COLLECT_REDUPDATED, serializeRedDot(redDotUpdated));
        set({
            newCards: cardsUpdated,
            redDot: new Map(redDotUpdated),
        });
    },
    cardResp: null,
    cardsFormate: null,
    cardsInfo: [],
    endingCardResp: null,
    endingCardsFormate: null,
    endingCardsInfo: [],
    reset: () => {
        set(() => ({
            filterValue: FilterType.ALL,
            cardResp: null,
            cardsFormate: null,
            cardsInfo: null,
            endingCardResp: null,
            endingCardsFormate: null,
            endingCardsInfo: null,
        }));
    },
    getCards: async () => {
        const initialCardsInfo = [{ robotName: '全部', robotUserId: -9999 } as GameRobotTabDto];
        try {
            const res: GameCardTabDto = await cardInfoReq('chapter');
            const ssrList =
                res.showDetail.filter((item) => item.cardLevel === 'SSR')[0]?.cardItem || [];
            const srList =
                res.showDetail.filter((item) => item.cardLevel === 'SR')[0]?.cardItem || [];
            const rList =
                res.showDetail.filter((item) => item.cardLevel === 'R')[0]?.cardItem || [];
            const getCheckStatus = (item: GameCardItemDto) =>
                get().checkNewTag('chapter', `${item.robotUserId}`, item.chapterId, undefined);
            const format = {
                r: [...rList].sort((a, b) => Number(getCheckStatus(b)) - Number(getCheckStatus(a))),
                sr: [...srList].sort(
                    (a, b) => Number(getCheckStatus(b)) - Number(getCheckStatus(a))
                ),
                ssr: [...ssrList].sort(
                    (a, b) => Number(getCheckStatus(b)) - Number(getCheckStatus(a))
                ),
            } as GameCardFormateDto;
            set(() => ({
                cardResp: res,
                cardsFormate: format,
                cardsInfo: initialCardsInfo.concat(res.roleTableInfo),
            }));
        } catch (err) {
            set(() => ({
                cardResp: null,
                cardsFormate: null,
                cardsInfo: initialCardsInfo,
            }));
            coronaWarnMessage('Gacha流程', `cardInfoReq 接口：${JSON.stringify(err)}`);
        }
    },

    getEndingCards: async () => {
        const initialCardsInfo = [{ robotName: '全部', robotUserId: -9999 } as GameRobotTabDto];
        try {
            const endingRes: GameCardTabDto = await cardInfoReq('chapter_ending');
            const endingSsrList =
                endingRes.showDetail.filter((item) => item.cardLevel === 'SSR')[0]?.cardItem || [];
            const endingSrList =
                endingRes.showDetail.filter((item) => item.cardLevel === 'SR')[0]?.cardItem || [];
            const endingRList =
                endingRes.showDetail.filter((item) => item.cardLevel === 'R')[0]?.cardItem || [];
            const getCheckStatus = (item: GameCardItemDto) =>
                get().checkNewTag('ending', `${item.robotUserId}`, undefined, item.endingId);
            const endingFormat = {
                r: [...endingRList].sort(
                    (a, b) => Number(getCheckStatus(b)) - Number(getCheckStatus(a))
                ),
                sr: [...endingSrList].sort(
                    (a, b) => Number(getCheckStatus(b)) - Number(getCheckStatus(a))
                ),
                ssr: [...endingSsrList].sort(
                    (a, b) => Number(getCheckStatus(b)) - Number(getCheckStatus(a))
                ),
            } as GameCardFormateDto;
            set(() => ({
                endingCardResp: endingRes,
                endingCardsFormate: endingFormat,
                endingCardsInfo: initialCardsInfo.concat(endingRes.roleTableInfo),
            }));
        } catch (err) {
            set(() => ({
                endingCardResp: null,
                endingCardsFormate: null,
                endingCardsInfo: initialCardsInfo,
            }));
            // eslint-disable-next-line no-console
            coronaWarnMessage('Gacha流程', `cardInfoReq 接口：${JSON.stringify(err)}`);
        }
    },
}));

export default cardCollectStore;
