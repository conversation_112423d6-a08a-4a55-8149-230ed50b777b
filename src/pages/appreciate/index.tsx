import React, { useState, useEffect, useCallback, useMemo } from 'react';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import EventTrackView from '@/components/EventTrack';
import backIcon from '@/assets/header/icon-white.png';
import appreciate_icon_filter from '@/assets/appreciate/appreciate_icon_filter.png';
import appreciate_icon_redDot_clear from '@/assets/appreciate/appreciate_icon_redDot_clear.png';
import appreciate_mask_list_bottom from '@/assets/appreciate/appreciate_mask_list_bottom.png';
import StoreProvider, { usePageStore } from '@/components/storeContext/StoreContext';
import { DialogRootLayer, useDialog } from '@/components/dialog';
import { createSimpleModalProvider } from '@/components/SimpleModal';
import { preloadVideo } from '@/const/gacha';
import cardCollectStore from './store/cardCollectStore';

import Chapter from './components/Page/chapter';
import Ending from './components/Page/ending';
import FilterComponents from './components/Filter';
import { checkTypeNewTag, KEY_APPRECIATE_TYPE } from './const';
import usePopupStore from './store/usePopupStore';
import MainPlotToast from '../chat/components/ChatPlot/MainPlotList/Toast';
import { MainPlotToastStore } from '../chat/components/ChatPlot/MainPlotList/Toast/MainPlotToastStore';

import './index.scss';
import GachaHintDialogContent from './components/hint/gachaHintDialog';

const tabs = ['卡面图鉴', '结局图鉴'];
const { KEY_CHAPTER, KEY_ENDING } = KEY_APPRECIATE_TYPE;
const Main = () => {
    const mainPlotToastStore = usePageStore(MainPlotToastStore);
    const { tab = 0 } = getCurrentInstance().router?.params || {};
    const moveRef = React.useRef(null);
    const getCards = cardCollectStore((state) => state.getCards);
    const reset = cardCollectStore((state) => state.reset);
    const getEndingCards = cardCollectStore((state) => state.getEndingCards);
    const showFilter = usePopupStore((state) => state.showFilter);
    const redDot = cardCollectStore((state) => state?.redDot);
    const [currentTab, setCurrentTab] = useState<number>(+tab);
    const redPointMap = {
        0: checkTypeNewTag(KEY_CHAPTER, redDot),
        1: checkTypeNewTag(KEY_ENDING, redDot),
    } as Record<number, boolean>;

    // 处理点击切换
    const handleTabClick = (index: number) => {
        if (index === currentTab) return; // 如果点击当前tab，不做任何操作

        setCurrentTab(index);
        if (moveRef.current) {
            const moveDistance = moveRef.current.offsetWidth / 2;
            if (index === 0) {
                moveRef.current.style.transform = `translateX(0px)`;
            }
            if (index === 1) {
                moveRef.current.style.transform = `translateX(${-moveDistance}px)`;
            }
        }
    };

    const handleClear = useCallback(() => {
        cardCollectStore.getState().removeWholeNewTag(currentTab === 0 ? 'chapter' : 'ending');
        mainPlotToastStore
            .getState()
            .showToast({ scene: 'appreciateList', content: `已清除所有红点` });
    }, [currentTab, mainPlotToastStore]);

    const handleFilter = useCallback(() => {
        usePopupStore.getState().setShowFilter(true);
    }, []);

    useEffect(() => {
        getCards();
        getEndingCards();
        return () => {
            reset();
        };
    }, [getCards, getEndingCards, reset]);

    const hintDialogProvider = useMemo(() => createSimpleModalProvider({ isCenter: true }), []);
    const dialog = useDialog(hintDialogProvider);
    const { showGachaDialog = false } = getCurrentInstance().router?.params || {};
    const { robotName = '' } = getCurrentInstance().router?.params || {};

    const GachaDiaglog = () => {
        return (
            <GachaHintDialogContent
                dismiss={() => {
                    dialog?.hide();
                }}
            />
        );
    };

    useEffect(() => {
        if (showGachaDialog && showGachaDialog === 'true') {
            Taro.nextTick(() => {
                setTimeout(() => {
                    dialog?.show({
                        children: <GachaDiaglog />,
                    });
                }, 500);
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [showGachaDialog]);

    const handleClickBack = () => {
        Taro.redirectTo({
            url: `/pages/gacha/index?fromSource=chatRelay&fromPage=appreciateList`,
            success: () => {
                preloadVideo();
            },
        });
    };

    useEffect(() => {
        document.body.classList.add('appreciate-page-show');

        return () => {
            document.body.classList.remove('appreciate-page-show');
        };
    }, []);

    return (
        <View className="collect-page-task-wrapper">
            <EventTrackView />
            <View
                className="scroll-tabs"
                onTouchStart={() => {
                    usePopupStore.getState().setShowFilter(false);
                }}>
                <View className="scroll-header">
                    <View className="header-back" onClick={() => handleClickBack()}>
                        <Image src={backIcon} />
                    </View>
                    <View className="tab-header">
                        {tabs.map((item, index) => (
                            <View
                                className={`tab-item ${currentTab === index ? 'active' : ''} ${
                                    redPointMap[index] ? 'has-dot' : ''
                                }`}
                                onClick={() => handleTabClick(index)}>
                                <View
                                    key={item}
                                    className={`content ${currentTab === index ? 'active' : ''} `}>
                                    {item}
                                </View>
                                {index === 0 && (
                                    <View
                                        className={`contentDesc ${
                                            currentTab === index ? 'active' : ''
                                        } `}>
                                        STORY CARD
                                    </View>
                                )}
                                {index === 1 && (
                                    <View
                                        className={`contentDesc ${
                                            currentTab === index ? 'active' : ''
                                        } `}>
                                        ENDDING
                                    </View>
                                )}
                            </View>
                        ))}
                    </View>
                    <View className="header-filter" onClick={handleFilter}>
                        <Image src={appreciate_icon_filter} />
                    </View>
                </View>
                <View
                    className="flex flex-row w-[200%] h-[100%]"
                    ref={moveRef}
                    style={{ transition: 'transform 0.3s ease' }}>
                    <View className="flex w-[50%] h-[100%] ">
                        <Chapter robotName={robotName} />
                    </View>
                    <View className="flex w-[50%] h-[100%]">
                        <Ending />
                    </View>
                </View>
            </View>
            <Image src={appreciate_mask_list_bottom} className="mask" />
            <Image src={appreciate_icon_redDot_clear} className="clear" onClick={handleClear} />
            {showFilter && (
                <View className="header-filter-component">
                    <FilterComponents />
                </View>
            )}
            <View className="appreciateToastStyle">
                <MainPlotToast scene="appreciateList" />
            </View>
        </View>
    );
};

const MainPage = () => {
    return (
        <StoreProvider type="page">
            <Main />
            <DialogRootLayer />
        </StoreProvider>
    );
};

export default MainPage;
