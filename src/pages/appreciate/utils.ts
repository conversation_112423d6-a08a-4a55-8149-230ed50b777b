export type NewCardsType = Map<string, Map<string, Set<string>>>;
export type RedDotType = Map<string, Set<string>>;

export function serializeNewCards(newCards: NewCardsType): string {
    const serialized: Record<string, Record<string, string[]>> = {};

    // 第一层 Map 转换
    newCards.forEach((innerMap, outerKey) => {
        const innerObj: Record<string, string[]> = {};

        // 第二层 Map 转换
        innerMap.forEach((valueSet, innerKey) => {
            // 过滤空集合（可选）
            if (valueSet.size > 0) {
                innerObj[innerKey] = Array.from(valueSet);
            }
        });

        // 过滤空的内层对象（可选）
        if (Object.keys(innerObj).length > 0) {
            serialized[outerKey] = innerObj;
        }
    });

    return JSON.stringify(serialized);
}

export function deserializeNewCards(jsonStr: string): NewCardsType {
    if (!jsonStr) {
        return new Map();
    }
    try {
        const parsed = JSON.parse(jsonStr);
        const resultMap = new Map<string, Map<string, Set<string>>>();

        // 第一层重建
        Object.entries(parsed).forEach(([outerKey, innerObj]) => {
            const innerMap = new Map<string, Set<string>>();

            // 第二层重建
            Object.entries(innerObj as Record<string, string[]>).forEach(
                ([innerKey, valueArray]) => {
                    // 类型安全校验
                    if (Array.isArray(valueArray)) {
                        innerMap.set(innerKey, new Set(valueArray));
                    }
                }
            );

            resultMap.set(outerKey, innerMap);
        });

        return resultMap;
    } catch (e) {
        // eslint-disable-next-line no-console
        console.error('反序列化失败，返回空结构', e);
        return new Map(); // 安全返回值
    }
}

export function serializeRedDot(redDot: RedDotType): string {
    // 将 Map 转换为可序列化对象
    const serializableObject: Record<string, string[]> = {};

    redDot.forEach((robotSet, type) => {
        // 过滤空集合（根据需求决定是否保留空类型）
        if (robotSet.size > 0) {
            serializableObject[type] = Array.from(robotSet);
        }
    });

    return JSON.stringify(serializableObject);
}

// ==================== 反序列化 ====================
export function deserializeRedDot(jsonStr: string): RedDotType {
    if (!jsonStr) {
        return new Map();
    }
    try {
        const parsedData: Record<string, string[]> = JSON.parse(jsonStr);
        const redDotMap = new Map<string, Set<string>>();

        Object.entries(parsedData).forEach(([type, robotArray]) => {
            // 过滤无效数据
            if (Array.isArray(robotArray)) {
                redDotMap.set(type, new Set(robotArray));
            }
        });

        return redDotMap;
    } catch (e) {
        // eslint-disable-next-line no-console
        console.error('反序列化 redDot 失败，返回默认值', e);
        return new Map(); // 返回空 Map 防止程序崩溃
    }
}
