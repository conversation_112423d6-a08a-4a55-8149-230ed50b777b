html,
body,
.taro_page {
    overflow-y: hidden;
}

.collect-page-task-wrapper {
    width: 100%;
    height: 100vh;
    position: relative;
    padding-top: var(--status-bar-height);
    background: center / 100% 100% repeat url('../../assets/game/gacha/preview_bg.jpg');

    .scroll-tabs {
        width: 100%;
        height: 100%;
        display: flex;
        padding-bottom: 58.6px;
        flex-direction: column;

        .scroll-header {
            height: 44px;
            margin: 0 22px 9px 20px;
            box-sizing: border-box;
            position: relative;

            .header-back {
                width: 26px;
                height: 26px;
                position: absolute;
                left: 0;
                top: 9px;
            }

            .header-filter {
                width: 20px;
                height: 20px;
                position: absolute;
                right: 0;
                top: 12px;
            }
        }

        .tab-header {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 44px;
            overflow: visible;
            margin: 0 auto;

            .tab-item {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                position: relative;
                height: 100%;
                min-width: 105px;
                padding-left: 15px;
                padding-right: 15px;
                padding-bottom: 2px;

                &.active::before {
                    content: '';
                    position: absolute;
                    bottom: -4px;
                    left: -5px;
                    width: 59px;
                    height: 57px;
                    background: center / 100% 100% repeat
                        url('../../assets/game/gacha/preview_tab_active_bg.png');
                }

                .content {
                    font-size: 18px;
                    font-weight: 500;
                    color: rgba(255, 255, 255, 0.8);
                    font-family: 'SourceHanSerifCN-Bold', sans-serif;

                    &.active {
                        color: white;
                        text-shadow: 0 0 6px rgba(255, 138, 43, 0.24), 0 0 6px #ff8a2b;
                    }
                }

                .contentDesc {
                    font-size: 8px;
                    font-weight: 400;
                    color: rgba(255, 255, 255, 0.2);

                    &.active {
                        color: rgba(241, 192, 140, 0.4);
                    }
                }

                // 红点样式
                &.has-dot {
                    &::after {
                        content: '';
                        position: absolute;
                        top: 3px;
                        right: 8px;
                        width: 5px;
                        height: 5px;
                        background: #ff561e;
                        border-radius: 50%;
                    }
                }
            }
        }

        .tab-content {
            flex: 1;
            width: 100%;
            height: 100%;
            white-space: nowrap;

            .tab-content-inner {
                display: flex;
                width: 200%;
                height: 100%;
            }

            .tab-pane {
                display: inline-block;
                width: 50%;
                height: 100%;
            }
        }
    }

    .clear {
        width: 32px;
        height: 35px;
        position: fixed;
        right: 12px;
        bottom: 67px;
        z-index: 2;
    }

    .mask {
        width: 100%;
        height: 117px;
        position: fixed;
        left: 0;
        bottom: 0;
        z-index: 1;
        pointer-events: none;
    }

    .header-filter-component {
        position: fixed;
        z-index: 1;
        right: 20px;
        top: calc(38px + var(--status-bar-height));
    }
}

.appreciateToastStyle {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 120;
    pointer-events: none;
    top: calc(54px + var(--status-bar-height));
}
