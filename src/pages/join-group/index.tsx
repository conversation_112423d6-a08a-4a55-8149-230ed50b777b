import React, { useState, useEffect } from 'react';
import { View, Text, Image } from '@tarojs/components';
import CommonHeader from '@/components/Header';
import EventTrackView from '@/components/EventTrack';
import { apiGetWechatImage } from './api';

import './index.scss';

interface WechatImageResponse {
    qrCodeUrl: string; // 微信二维码图片
}

const JoinGroup = () => {
    const [imageUrl, setImageUrl] = useState('');

    useEffect(() => {
        apiGetWechatImage().then((res: WechatImageResponse) => {
            setImageUrl((res && res.qrCodeUrl) || '');
        });
    }, []);

    return (
        <View className="join-group-container">
            <EventTrackView
                params={{
                    _spm: 'page_ai_group_code|page_h5_biz',
                }}
            />

            {/* 背景图片 */}
            <View className="background-image" />
            <View className="background-gradient" />

            {/* 头部导航 */}
            <CommonHeader />

            {/* 主要内容 */}
            <View className="content-wrapper">
                <Text className="main-title">有更多想法？</Text>
                <Text className="sub-title">加入微信群，与同好分享</Text>

                {/* 二维码容器 */}
                <View className="qr-code-container">
                    <Image className="qr-code-image" src={imageUrl} mode="aspectFit" />
                </View>

                <Text className="tip-text">截图保存二维码，微信扫码进入群聊</Text>
            </View>
        </View>
    );
};

export default JoinGroup;
