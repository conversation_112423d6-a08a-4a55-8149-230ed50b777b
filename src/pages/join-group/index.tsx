import React, { useState, useEffect } from 'react';
import { View, Text, Image } from '@tarojs/components';
import CommonHeader from '@/components/Header';
import EventTrackView from '@/components/EventTrack';
import { apiGetWechatImage } from './api';

import './index.scss';

interface WechatImageResponse {
    qrCodeUrl: string; // 微信二维码图片
}

const JoinGroup = () => {
    const [imageUrl, setImageUrl] = useState('');
    useEffect(() => {
        apiGetWechatImage().then((res: WechatImageResponse) => {
            setImageUrl((res && res.qrCodeUrl) || '');
        });
    }, []);

    return (
        <View className="join-group-container">
            <EventTrackView
                params={{
                    _spm: 'page_ai_group_code|page_h5_biz',
                }}
            />
            <View className="top-bg" />
            <CommonHeader />
            <Text className="title">有更多想法？</Text>
            <Text className="desc">加入微信群，与同好分享</Text>
            <Image className="wechat-image" src={imageUrl} />
            <Text className="tip-content">截图保存二维码，微信扫码进入群聊</Text>
        </View>
    );
};

export default JoinGroup;
