import CLOSE_ICON from '@/assets/common/comm_icon_nav_back.png';
import { Image, Text, View, ITouchEvent } from '@tarojs/components';
import Taro, {
    getCurrentInstance,
    createSelectorQuery,
    showToast,
    getSystemInfoSync,
    onWindowResize,
    offWindowResize,
    getImageInfo,
} from '@tarojs/taro';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { saveToPhotosAlbum } from '@/utils/rpc';
import './index.scss';

interface ImageSize {
    width: number;
    height: number;
}

function calculateImageDisplaySize(
    screenWidth: number,
    screenHeight: number,
    imageWidth: number,
    imageHeight: number
): ImageSize {
    // 如果图片尺寸小于屏幕尺寸，直接返回原图尺寸
    if (imageWidth <= screenWidth && imageHeight <= screenHeight) {
        return {
            width: imageWidth,
            height: imageHeight,
        };
    }

    // 计算宽高比
    const imageRatio = imageWidth / imageHeight;

    // 先尝试以屏幕宽度为基准进行缩放
    let finalWidth = screenWidth;
    let finalHeight = screenWidth / imageRatio;

    // 如果按宽度缩放后高度超过屏幕高度
    if (finalHeight > screenHeight) {
        // 则改为以屏幕高度为基准进行缩放
        finalHeight = screenHeight;
        finalWidth = screenHeight * imageRatio;
    }

    return {
        width: Math.floor(finalWidth),
        height: Math.floor(finalHeight),
    };
}

const ImagePreview = () => {
    const { url = '', width = 0, height = 0 } = getCurrentInstance().router?.params || {};

    const [imageSize, setImageSize] = useState<ImageSize>({
        width: Number(width),
        height: Number(height),
    });
    const [screenSize, setScreenSize] = useState<ImageSize | null>(null);
    const [displaySize, setDisplaySize] = useState<ImageSize | null>(null);
    const [imagePath, setImagePath] = useState<string>('');

    const [scale, setScale] = useState(1);
    const [position, setPosition] = useState({ x: 0, y: 0 });
    const touchStartRef = useRef(false);
    const lastTouchStateRef = useRef({ xData: 0, yData: 0 });

    const containerSizeRef = useRef<{ width: number; height: number } | null>(null);
    const getContainerSize = useCallback(() => {
        const query = createSelectorQuery();
        query
            .select('.image-container')
            .boundingClientRect()
            .exec((res) => {
                if (res[0]) {
                    containerSizeRef.current = { width: res[0].width, height: res[0].height };
                    console.log('containerSizeRef', containerSizeRef);
                }
            });
    }, []);

    useEffect(() => {
        getContainerSize();

        const handleResize = () => {
            getContainerSize();
        };

        onWindowResize(handleResize);

        return () => {
            offWindowResize(handleResize);
        };
    }, [getContainerSize]);

    // 获取图片实际尺寸
    useEffect(() => {
        if (url) {
            try {
                getImageInfo({
                    src: decodeURIComponent(url),
                    success: (res) => {
                        setImageSize({
                            width: res.width,
                            height: res.height,
                        });
                        setImagePath(res.path);
                    },
                });
            } catch (e) {
                console.error('Failed to get image info:', e);
            }
        }
    }, [url]);

    useEffect(() => {
        const systemInfo = getSystemInfoSync();
        setScreenSize({
            width: systemInfo.windowWidth,
            height: systemInfo.windowHeight,
        });
    }, []);

    const clickBackAction = useCallback(() => {
        Taro.navigateBack();
    }, []);

    // 计算容器样式
    const imageStyle = displaySize
        ? {
              width: `${displaySize.width}px`,
              height: `${displaySize.height}px`,
              display: 'block',
              transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
              willChange: 'transform',
          }
        : {
              display: 'none',
          };

    useEffect(() => {
        if (imageSize && screenSize) {
            console.log('imageSize', imageSize);
            console.log('screenSize', screenSize);
            setDisplaySize(
                calculateImageDisplaySize(
                    screenSize.width,
                    screenSize.height,
                    imageSize.width,
                    imageSize.height
                )
            );
        }
    }, [imageSize, screenSize]);

    const saveImageAction = (e) => {
        e.stopPropagation();
        const image = imagePath.split(',')[1];
        saveToPhotosAlbum(
            image,
            (event) => {
                showToast({
                    title: '保存成功',
                });
            },
            (event) => {
                showToast({
                    title: '保存失败',
                    icon: 'none',
                });
            }
        );
    };

    const handleTouchStart = useCallback((e: ITouchEvent) => {
        console.log('handleTouchStart', e.touches.length, e.touches);
        e.preventDefault();
        touchStartRef.current = true;
        if (e.touches.length === 2) {
            const dist = Math.hypot(
                e.touches[0].pageX - e.touches[1].pageX,
                e.touches[0].pageY - e.touches[1].pageY
            );
            lastTouchStateRef.current = { xData: dist, yData: dist };
        } else if (e.touches.length === 1) {
            lastTouchStateRef.current = { xData: e.touches[0].pageX, yData: e.touches[0].pageY };
        }
    }, []);

    const handleTouchMove = useCallback(
        (e: ITouchEvent) => {
            if (!touchStartRef.current || !displaySize || !containerSizeRef.current) {
                return;
            }
            console.log('handleTouchMove', e.touches.length, e.touches);
            e.preventDefault();

            const containerWidth = containerSizeRef.current.width;
            const containerHeight = containerSizeRef.current.height;

            console.log('containerWidth', containerWidth, 'containerHeight', containerHeight);

            if (e.touches.length === 2) {
                const dist = Math.hypot(
                    e.touches[0].pageX - e.touches[1].pageX,
                    e.touches[0].pageY - e.touches[1].pageY
                );
                const newScale = scale * (dist / lastTouchStateRef.current.xData);
                const finalScale = Math.max(1, Math.min(newScale, 3));

                setScale(finalScale);
                lastTouchStateRef.current = { xData: dist, yData: dist };

                // 防止缩小时图片超出屏幕
                const scaledWidth = displaySize.width * finalScale;
                const scaledHeight = displaySize.height * finalScale;

                const maxX = scaledWidth > containerWidth ? (scaledWidth - containerWidth) / 2 : 0;
                const maxY =
                    scaledHeight > containerHeight ? (scaledHeight - containerHeight) / 2 : 0;

                const boundedX = Math.max(-maxX, Math.min(maxX, position.x));
                const boundedY = Math.max(-maxY, Math.min(maxY, position.y));

                if (boundedX !== position.x || boundedY !== position.y) {
                    setPosition({ x: boundedX, y: boundedY });
                }
            } else if (e.touches.length === 1) {
                const deltaX = e.touches[0].pageX - lastTouchStateRef.current.xData;
                const deltaY = e.touches[0].pageY - lastTouchStateRef.current.yData;
                const newX = position.x + deltaX;
                const newY = position.y + deltaY;

                const scaledWidth = displaySize.width * scale;
                const scaledHeight = displaySize.height * scale;

                const maxX = scaledWidth > containerWidth ? (scaledWidth - containerWidth) / 2 : 0;
                const maxY =
                    scaledHeight > containerHeight ? (scaledHeight - containerHeight) / 2 : 0;

                const boundedX = Math.max(-maxX, Math.min(maxX, newX));
                const boundedY = Math.max(-maxY, Math.min(maxY, newY));

                setPosition({ x: boundedX, y: boundedY });
                lastTouchStateRef.current = {
                    xData: e.touches[0].pageX,
                    yData: e.touches[0].pageY,
                };
            }
        },
        [scale, setPosition, position, displaySize]
    );

    const handleTouchEnd = useCallback((e: ITouchEvent) => {
        console.log('handleTouchEnd', e.touches.length, e.touches);
        e.preventDefault();
        touchStartRef.current = false;
    }, []);

    return (
        <View className="safe-view-container" onClick={clickBackAction}>
            <View className="image-container">
                <Image
                    id="target-image"
                    style={imageStyle}
                    src={decodeURIComponent(url)}
                    mode="aspectFit"
                    onTouchStart={handleTouchStart}
                    onTouchMove={handleTouchMove}
                    onTouchEnd={handleTouchEnd}
                />
            </View>
            <View className="image-navigation">
                <Image className="close-button" src={CLOSE_ICON} mode="scaleToFill" />
            </View>
            <View className="save-container" onClick={(e) => saveImageAction(e)}>
                <Text className="save-text">保存</Text>
            </View>
        </View>
    );
};

export default React.memo(ImagePreview);
