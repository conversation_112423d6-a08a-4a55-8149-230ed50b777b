.safe-view-container {
    background: black;
    width: 100%;
    height: 100%;

    .image-navigation {
        width: 100%;
        height: 57px;
        padding-top: var(--status-bar-height);

        .close-button {
            left: 18;
            width: 30px;
            height: 30px;
            top: 10px;
        }
    }

    .image-container {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .save-container {
        position: fixed;
        right: 20px;
        bottom: 20px;
        background: rgba(126, 126, 126, 0.2);
        border-radius: 20px;
        width: 92px;
        height: 38px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: constant(safe-area-inset-bottom);
        margin-bottom: env(safe-area-inset-bottom);
        z-index: 100;

        .save-text {
            font-size: 16px;
            font-weight: 500;
            line-height: 16px;
            letter-spacing: 0;
            color: #fff;
        }
    }
}
