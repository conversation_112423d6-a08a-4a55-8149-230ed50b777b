import React from 'react';
import { View } from '@tarojs/components';
import GachaExplainPage from './explain';

// 定义明确类型
// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface GachaExplainProps {}

const GachaExplain: React.FC<GachaExplainProps> = () => {
    return (
        <View
            style={{
                position: 'relative',
                width: '100%',
                height: '100%',
                backgroundColor: '#222c6b',
            }}>
            <GachaExplainPage />
        </View>
    );
};

export default GachaExplain;
