import React from 'react';
import { Image, View } from '@tarojs/components';
import ICON_BACK from '@/assets/game/gacha/icon_back_white.png';
import gacha_explain_bg from '@/assets/game/gacha/gacha_explain_bg.png';
import gacha_explain_content from '@/assets/game/gacha/gacha_explain_content.png';
import Header from '@/components/Header';

const GachaExplainPage = () => {
    return (
        <View style={{ position: 'relative', width: '100%', height: '100%' }}>
            <View className="absolute w-[100%] h-[100%] top-[0] left-[0] overflow-hidden">
                <View
                    style={{
                        backgroundImage: `url(${gacha_explain_bg})`,
                    }}
                    className="absolute w-[100%] h-[100%] top-[0] left-[0] bg-center bg-cover bg-no-repeat"
                />

                <div className="absolute w-[100%] h-[100%] pt-[152px] pb-[52px] left-[0] top-[0]">
                    <div className="w-[100%] h-[100%]  overflow-y-auto">
                        <Image className="w-[90vw] block mx-auto" src={gacha_explain_content} />
                    </div>
                </div>

                <Header
                    backConfig={{
                        src: ICON_BACK,
                    }}
                />
            </View>
        </View>
    );
};

export default React.memo(GachaExplainPage);
