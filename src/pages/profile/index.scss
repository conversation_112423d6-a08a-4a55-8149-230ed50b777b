.profile-page-wrapper {
    background-color: #faf7f9;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .content-wrapper {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
        appearance: none;
        -webkit-appearance: none;

        &::-webkit-scrollbar {
            display: none;
        }

        .bg-wrapper {
            flex-shrink: 0;
            height: 168px;
            width: 100%;
        }

        .card-content {
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            background-color: #fff;
            display: flex;
            flex-direction: column;
            margin-top: -15px;
            position: relative;
            flex: 1;
            transform: translateZ(0); // 强制开启GPU加速层
            will-change: transform; // 提示浏览器优化

            .avatar-wrapper {
                height: 71px;
                display: flex;
                flex-direction: row;
                margin-bottom: 15px;
                flex-shrink: 0;
                padding: 0 20px;

                .avatar {
                    width: 92px;
                    height: 92px;
                    box-sizing: border-box;
                    border-radius: 50%;
                    border: 2px solid #fff;
                    box-shadow: 0 0 10px 0 rgba(147, 147, 147, 0.3);
                    object-fit: cover;
                    margin-top: 0;
                    transform: translateY(-19px);
                    will-change: transform;
                }

                .right-wrapper {
                    min-height: 72px;
                    padding-top: 12px;
                    display: flex;
                    flex-direction: column;
                    flex: 1;
                    margin-left: 12px;
                    overflow: hidden;

                    .name-wrapper {
                        display: flex;
                        align-items: center;

                        .name {
                            font-size: 20px;
                            font-weight: 600;
                            line-height: 100%;
                            text-align: right;
                            letter-spacing: 0;
                            color: #26262a;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        .gender-age-wrapper {
                            box-sizing: border-box;
                            border-radius: 87px;
                            opacity: 1;
                            background: #ffebf2;
                            padding: 2px 5px 2px 3px;
                            display: flex;
                            flex-direction: row;
                            align-items: center;
                            justify-content: center;
                            margin-left: 5px;
                            flex-shrink: 0;

                            &.male {
                                background: #ebfaff;
                            }

                            .gender {
                                width: 16px;
                                height: 16px;
                                margin-left: 4px;
                            }

                            .age {
                                font-size: 12px;
                                font-weight: normal;
                                line-height: 100%;
                                letter-spacing: 0;
                                color: #ff689e;

                                &.male {
                                    color: #55b8ff;
                                }
                            }
                        }
                    }

                    .sign-txt {
                        margin-top: 6px;
                        font-size: 14px;
                        font-weight: normal;
                        line-height: 100%;
                        letter-spacing: 0;
                        color: #303030;
                        opacity: 0.6;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        width: 100%;
                    }
                }
            }

            .label-wrapper {
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                overflow: hidden;
                margin-bottom: 14px;
                flex-shrink: 0;
                padding: 0 20px;

                .label {
                    font-size: 11px;
                    font-weight: normal;
                    line-height: 100%;
                    letter-spacing: 0;
                    color: rgba(0, 0, 0, 0.4);
                    padding: 5px 7px;
                    margin-right: 6px;
                    border-radius: 3px;
                    background: rgba(159, 159, 159, 0.1);
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    margin-bottom: 6px;
                }
            }

            .desc-wrapper {
                overflow: hidden;
                display: flex;
                flex-direction: column;
                flex-shrink: 0;
                padding: 0 20px;
                background-color: #fff;

                .desc-title {
                    display: block;
                    font-size: 16px;
                    font-weight: 600;
                    line-height: 100%;
                    letter-spacing: 0;
                    color: #000;
                    opacity: 0.8;
                }

                .desc-content {
                    font-size: 14px;
                    font-weight: normal;
                    line-height: 180%;
                    letter-spacing: 0;
                    color: #000;
                    opacity: 0.6;
                    margin-top: 10px;

                    &.two-line {
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        line-clamp: 2;
                        -webkit-line-clamp: 2;
                        overflow: hidden;
                        word-break: break-all;
                        text-overflow: ellipsis;
                    }
                }

                .desc-content-toggle-expand {
                    font-size: 13px;
                    font-weight: 600;
                    line-height: normal;
                    letter-spacing: 0;
                    color: rgba(0, 0, 0, 0.2);

                    &::after {
                        content: '';
                        display: inline-block;
                        width: 10px;
                        height: 10px;
                        background-image: url('../../assets/profile/icon-expand.png');
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                        vertical-align: baseline;
                        margin-left: 2px;
                    }
                }

                .desc-content-toggle-fold {
                    font-size: 13px;
                    font-weight: 600;
                    line-height: normal;
                    letter-spacing: 0;
                    color: rgba(0, 0, 0, 0.2);

                    &::after {
                        content: '';
                        display: inline-block;
                        width: 10px;
                        height: 10px;
                        background-image: url('../../assets/profile/icon-expand.png');
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                        vertical-align: baseline;
                        margin-left: 2px;
                        transform: rotate(180deg);
                    }
                }
            }

            .at-tabs {
                flex-shrink: 0;
                background-color: #fff;
                padding: 15px 20px 0 20px;

                .at-tabs__header {
                    .at-tabs__item {
                        color: #000;
                        opacity: 0.3;
                        font-size: 16px;
                        font-weight: 600;
                        line-height: normal;
                        letter-spacing: 0;
                        padding: 0 0 4px 0;
                        flex: none;
                        margin-right: 20px;
                    }

                    .at-tabs__item--active {
                        color: #000;
                        opacity: 0.8;
                        font-size: 16px;
                        font-weight: 600;
                        line-height: normal;
                        letter-spacing: 0;
                    }

                    .at-tabs__item-underline {
                        border-radius: 28px;
                        background: #000;
                        width: 10px;
                        height: 2px;
                        position: absolute;
                        left: 50%;
                        bottom: 0;
                        transform: translateX(-50%) scaleX(0);
                        transition: none;
                    }

                    .at-tabs__item--active .at-tabs__item-underline {
                        transform: translateX(-50%) scaleX(1);
                    }
                }

                .at-tabs__body {
                    .at-tabs__underline {
                        background-color: transparent;
                    }
                }
            }
        }
    }

    .footer-wrapper {
        position: fixed;
        bottom: 0;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .chat-text {
            width: calc(100% - 40px);
            height: 50px;
            border-radius: 140px;
            background: linear-gradient(0deg, #ff689e, #ff689e), #ffe9b6;
            backdrop-filter: blur(10px);
            opacity: 1;
            font-size: 18px;
            font-weight: 600;
            line-height: 50px;
            letter-spacing: 0;
            color: #fff;
            text-align: center;
        }

        .footer-padding {
            height: 30px;
            width: 100%;
        }
    }

    //修复Image中mode="aspectFill"变形问题
    .taro-img__mode-aspectfill {
        object-fit: cover;
    }
}
