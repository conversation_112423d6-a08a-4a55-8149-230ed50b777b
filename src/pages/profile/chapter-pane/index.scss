.chapter-tab-content {
    margin-top: 10px;
    margin-bottom: 140px;

    .grid-container {
        display: flex;
        flex-wrap: wrap;
        margin: -8px; // 抵消子元素的margin

        .grid-item {
            display: flex;
            flex-direction: column;
            flex: 0 0 calc(50% - 16px); // 两列布局，减去margin
            margin: 8px;
            overflow: hidden;
            position: relative;

            .chapter-image-container {
                position: relative;
                width: 100%;
                height: 216px;

                .chapter-image {
                    object-fit: cover;
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                    box-sizing: border-box;
                    border-radius: 10px;
                }

                .chapter-image-lock {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                    background: rgba(187, 187, 187, 0.2);
                    backdrop-filter: blur(10px);
                    box-sizing: border-box;
                    border-radius: 10px;
                }
            }

            .chapter-corner-image {
                position: absolute;
                right: 0;
                bottom: 0;
                width: 81px;
                height: 30px;
                border-radius: 0 0 10px 0;
            }

            .chapter-title-wrapper {
                position: relative;
                display: flex;
                flex-direction: row;
                align-items: center;
                margin-top: 10px;

                .chapter-title-lock {
                    width: 16px;
                    height: 16px;
                    margin-right: 2px;
                }

                .chapter-title {
                    font-size: 14px;
                    font-weight: 600;
                    line-height: 16px;
                    letter-spacing: 0;
                    display: block;
                    color: #000;
                    opacity: 0.7;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            .chapter-description {
                font-size: 12px;
                font-weight: normal;
                line-height: 150%;
                letter-spacing: 0;
                color: #000;
                opacity: 0.4;
                display: block;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                line-clamp: 2;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                margin-top: 6px;
            }
        }
    }
}
