import lockIcon from '@/assets/profile/icon-lock.png';
import rIcon from '@/assets/profile/icon-r.png';
import srIcon from '@/assets/profile/icon-sr.png';
import ssrIcon from '@/assets/profile/icon-ssr.png';
import { debounce } from '@/utils';
import { optimizeImage } from '@/utils/image';
import { Image, Text, View } from '@tarojs/components';
import React, { useCallback } from 'react';
import { jump2ChapterInfo } from '@/router';
import {
    AigcChapterCardInfo,
    AigcRobotProfile,
    CardStatus,
    ChapterLevel,
} from '../../../../types/AigcRobotProfile';
import './index.scss';

function getLevelIcon(level: string): string {
    if (level === ChapterLevel.SR) {
        return srIcon;
    }
    if (level === ChapterLevel.SSR) {
        return ssrIcon;
    }
    return rIcon;
}

export interface ChapterPaneProps {
    profile: AigcRobotProfile;
}

const ChapterPane = ({ profile }: ChapterPaneProps) => {
    const onGotoChapter = useCallback(
        (chapter: AigcChapterCardInfo) => {
            const chapterStr = encodeURIComponent(JSON.stringify(chapter));
            jump2ChapterInfo({
                chapterId: chapter.chapterId,
                robotUserId: `${profile.userId}`,
                chapter: chapterStr,
                from: null,
            });
        },
        [profile]
    );

    return (
        <View className="chapter-tab-content">
            <View className="grid-container">
                {profile.chapterCardInfoDtos?.map((chapter) => (
                    <View
                        key={chapter.chapterId}
                        className="grid-item"
                        onClick={debounce(() => onGotoChapter(chapter))}>
                        <View className="chapter-image-container">
                            <Image
                                className="chapter-image"
                                src={optimizeImage({
                                    src: chapter.chapterImg,
                                    width: 162,
                                    height: 216,
                                })}
                                mode="aspectFill"
                            />
                            {chapter.cardStatus !== CardStatus.UNLOCK && (
                                <View className="chapter-image-lock" />
                            )}
                            <Image
                                className="chapter-corner-image"
                                src={getLevelIcon(chapter.chapterLevel)}
                                mode="scaleToFill"
                            />
                        </View>
                        <View className="chapter-title-wrapper">
                            {chapter.cardStatus !== CardStatus.UNLOCK && (
                                <Image className="chapter-title-lock" src={lockIcon} />
                            )}
                            <Text className="chapter-title">{chapter.chapterName}</Text>
                        </View>
                        <Text className="chapter-description">
                            {chapter.cardStatus !== CardStatus.UNLOCK
                                ? '待解锁'
                                : chapter.chapterDesc}
                        </Text>
                    </View>
                ))}
            </View>
        </View>
    );
};

export default React.memo(ChapterPane);
