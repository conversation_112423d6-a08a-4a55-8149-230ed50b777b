.ending-tab-content {
    margin-top: 10px;
    margin-bottom: 140px;

    .grid-container {
        display: flex;
        flex-wrap: wrap;
        margin: -8px; // 抵消子元素的margin

        .grid-item {
            display: flex;
            flex-direction: column;
            flex: 0 0 calc(33.333% - 16px); // 三列布局，减去margin
            margin: 8px;
            overflow: hidden;
            position: relative;

            .chapter-image-container {
                position: relative;
                width: 100%;
                height: 186px;

                .chapter-image {
                    object-fit: cover;
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                    box-sizing: border-box;
                    border-radius: 10px;
                }

                .chapter-image-lock {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                    background: rgba(109, 109, 109, 0.5);
                    backdrop-filter: blur(10px);
                    box-sizing: border-box;
                    border-radius: 10px;

                    &::after {
                        content: '';
                        display: block;
                        width: 26px;
                        height: 26px;
                        background-image: url('../../../assets/profile/icon-end-lock.png');
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                    }
                }

                .chapter-image-mainplot {
                    width: 59px;
                    height: 57px;
                    position: absolute;
                    top: 0;
                    left: 0;

                    &::before {
                        content: '';
                        display: inline-block;
                        width: 59px;
                        height: 57px;
                        margin-right: 2px;
                        flex-shrink: 0;
                        background-image: url('../../../assets/profile/icon-chapter-main-plot.png');
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                        vertical-align: middle;
                        // background-color: #f83e01;
                    }
                }
            }

            .chapter-title-unlock {
                font-size: 14px;
                font-weight: 600;
                line-height: 1.5;
                letter-spacing: 0;
                display: block;
                color: #000;
                opacity: 0.7;
                margin-top: 4px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                padding: 0 8px;
                width: 100%;
                text-align: center;
            }

            .chapter-title-lock {
                font-size: 14px;
                font-weight: 600;
                line-height: 1.5;
                letter-spacing: 0;
                display: block;
                color: #000;
                margin-top: 4px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                padding: 0 8px;
                width: 100%;
                text-align: center;
            }

            .chapter-bind-name-container {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                margin-top: 2px;

                .chapter-bind-name {
                    color: rgba(55, 4, 99, 0.6);
                    font-size: 11px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 15px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    text-align: center;
                    background: #f0ebf2;
                    border-radius: 4px;
                    padding: 1px 3px;

                    &::before {
                        content: '';
                        display: inline-block;
                        width: 12px;
                        height: 12px;
                        margin-right: 2px;
                        flex-shrink: 0;
                        background-image: url('../../../assets/profile/icon-chapter-name.png');
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                        vertical-align: middle;
                        // background-color: #f83e01;
                    }
                }

                .chapter-bind-name-mainplot {
                    color: rgba(255, 104, 158, 1);
                    font-size: 11px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 15px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    text-align: center;
                    background: #ffeef4;
                    border-radius: 4px;
                    padding: 1px 3px;

                    &::before {
                        content: '';
                        display: inline-block;
                        width: 12px;
                        height: 12px;
                        margin-right: 2px;
                        flex-shrink: 0;
                        background-image: url('../../../assets/profile/icon-chapter-name-mainplot.png');
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                        vertical-align: middle;
                        // background-color: #f83e01;
                    }
                }
            }
        }
    }
}
