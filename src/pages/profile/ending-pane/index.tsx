import { debounce } from '@/utils';
import { optimizeImage } from '@/utils/image';
import { Image, Text, View } from '@tarojs/components';
import React, { Fragment, useCallback } from 'react';
import { jump2EndingInfo } from '@/router';
import {
    AigcEndingCardInfo,
    AigcRobotProfile,
    bindChapterRelationName,
    CardStatus,
} from '../../../../types/AigcRobotProfile';
import './index.scss';

export interface ChapterPaneProps {
    profile: AigcRobotProfile;
}

export function findPairEndInfo(
    list: AigcEndingCardInfo[],
    ending: AigcEndingCardInfo
): AigcEndingCardInfo | undefined {
    return list.find(
        (item) =>
            item.bindChapterId === ending.bindChapterId && item.endingCardId !== ending.endingCardId
    );
}

const BottomNameComp = (ending: AigcEndingCardInfo) => {
    return ending.bindChapterType === 1 ? (
        <Fragment>
            <Text className="chapter-title-lock">{ending.endingName}</Text>
            <View className="chapter-bind-name-container">
                <Text className="chapter-bind-name-mainplot">
                    {bindChapterRelationName(ending.bindChapterRelationType)}
                </Text>
            </View>
        </Fragment>
    ) : (
        <Fragment>
            <Text className="chapter-title-lock">{ending.endingName}</Text>
            <View className="chapter-bind-name-container">
                <Text className="chapter-bind-name">{ending.bindChapterName}</Text>
            </View>
        </Fragment>
    );
};

const EndingPane = ({ profile }: ChapterPaneProps) => {
    const onGotoEnding = useCallback(
        (ending: AigcEndingCardInfo, aigcRobotProfile: AigcRobotProfile) => {
            const userId = aigcRobotProfile?.userId;
            // 找到配对的结局卡片
            const pairEnding = findPairEndInfo(profile.endingCardInfoDtos, ending);

            // 如果找到配对结局，确定哪个在列表中排在前面
            if (pairEnding) {
                const endingIndex = profile.endingCardInfoDtos.findIndex(
                    (item) => item.endingCardId === ending.endingCardId
                );
                const pairEndingIndex = profile.endingCardInfoDtos.findIndex(
                    (item) => item.endingCardId === pairEnding.endingCardId
                );

                // 按照列表顺序排序
                const [firstEnding, secondEnding] =
                    endingIndex < pairEndingIndex ? [ending, pairEnding] : [pairEnding, ending];

                const firstEndingStr = encodeURIComponent(JSON.stringify(firstEnding));
                const secondEndingStr = encodeURIComponent(JSON.stringify(secondEnding));

                jump2EndingInfo({
                    uiState: null,
                    userId: `${userId}`,
                    ending: firstEndingStr,
                    ending2: secondEndingStr,
                    from: null,
                });
            } else {
                // 没有配对结局，只传递当前结局
                const endingStr = encodeURIComponent(JSON.stringify(ending));
                jump2EndingInfo({
                    uiState: null,
                    userId: `${userId}`,
                    ending: endingStr,
                    ending2: null,
                    from: null,
                });
            }
        },
        [profile.endingCardInfoDtos]
    );
    return (
        <View className="ending-tab-content">
            <View className="grid-container">
                {profile?.endingCardInfoDtos?.map((ending) => (
                    <View
                        key={ending.endingCardId}
                        className="grid-item"
                        onClick={debounce(() => {
                            onGotoEnding(ending, profile);
                        })}>
                        <View className="chapter-image-container">
                            <Image
                                className="chapter-image"
                                src={optimizeImage({
                                    src: ending.endingCardImg,
                                    width: 105,
                                    height: 186,
                                })}
                                mode="aspectFill"
                            />
                            {ending.cardStatus !== CardStatus.UNLOCK && (
                                <View className="chapter-image-lock" />
                            )}
                            {ending.cardStatus === CardStatus.UNLOCK &&
                                ending.bindChapterType === 1 && (
                                    <View className="chapter-image-mainplot" />
                                )}
                        </View>
                        {ending.cardStatus !== CardStatus.UNLOCK ? (
                            <Text className="chapter-title-unlock">未解锁</Text>
                        ) : (
                            BottomNameComp(ending)
                        )}
                    </View>
                ))}
            </View>
        </View>
    );
};

export default React.memo(EndingPane);
