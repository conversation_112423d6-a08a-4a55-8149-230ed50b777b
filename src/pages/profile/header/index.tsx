import addFriendIcon from '@/assets/profile/icon-add-friend.png';
import backIcon from '@/assets/profile/icon-back.png';
import moreIcon from '@/assets/profile/icon-more.png';
import { debounce } from '@/utils';
import { optimizeImage } from '@/utils/image';
import { Image, Text, View } from '@tarojs/components';
import React, { useCallback } from 'react';
import Header from '@/components/Header';
import { jump2ImgPreview } from '@/router';
import { AigcRobotProfile } from '../../../../types/AigcRobotProfile';
import './index.scss';

export interface ChapterPaneProps {
    profile: AigcRobotProfile;
    onClickAddFriend: () => void;
    onClickMore: () => void;
}

const ProfileHeader = ({ profile, onClickAddFriend, onClickMore }: ChapterPaneProps) => {
    const onBgPreview = useCallback(() => {
        const url = profile?.backgroundImg || '';
        jump2ImgPreview({ url });
    }, [profile]);

    return (
        <View className="header-wrapper">
            <Image
                className="header-pic"
                mode="aspectFill"
                src={optimizeImage({
                    src: profile.backgroundImg,
                    width: 375,
                    height: 183,
                })}
                onClick={debounce(onBgPreview)}
            />

            <View className="absolute top-[0px] left-[0px] w-[100%]">
                <Header
                    backConfig={{ src: backIcon }}
                    funcConfig={{
                        node: (
                            <View className="header-right">
                                {!profile.friend && (
                                    <View
                                        className="header-add-friend-container"
                                        onClick={debounce(onClickAddFriend)}>
                                        <Image className="header-add-friend" src={addFriendIcon} />
                                        <Text className="header-add-friend-text">加好友</Text>
                                    </View>
                                )}
                                <Image
                                    className="header-more"
                                    src={moreIcon}
                                    onClick={debounce(onClickMore)}
                                />
                            </View>
                        ),
                    }}
                />
            </View>
        </View>
    );
};

export default React.memo(ProfileHeader);
