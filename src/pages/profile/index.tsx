/* eslint-disable no-console */
import femaleIcon from '@/assets/profile/icon-female.png';
import maleIcon from '@/assets/profile/icon-male.png';
import Avatar from '@/components/avatar';
import BottomActionModal, { ActionProps } from '@/components/bottom-action-modal';
import CircleLoading from '@/components/CircleLoading';
import { ConfirmModalProvider } from '@/components/confirm-modal';
import { useDialog } from '@/components/dialog';
import { addFriendApi, deleteFriendApi } from '@/service/friendApi';
import { profileDetailApi } from '@/service/profileApi';
import useChapterGuideStore from '@/store/useChapterGuideStore';
import { debounce } from '@/utils';
import { pageDidAppear } from '@/utils/rpc';
import { Image, Text, View } from '@tarojs/components';
import { optimizeImage } from '@/utils/image';
import Taro, { getCurrentInstance, useDidShow, showToast, getCurrentPages } from '@tarojs/taro';
import classNames from 'classnames';
import React, { Fragment, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AtTabs, AtTabsPane } from 'taro-ui';
import { jump2Chat, jump2ImgPreview } from '@/router';
import useDetailStore from '../chat/store/useDetailStore';
import { AigcRobotProfile, GENDER, getAge } from '../../../types/AigcRobotProfile';
import ChapterPane from './chapter-pane';
import EndingPane from './ending-pane';
import Header from './header';

import './index.scss';
import useScenarioStore from '../chat/store/useScenarioStore';

const Profile = () => {
    const [profile, setProfile] = useState<AigcRobotProfile | null>(null);
    const tabList = useMemo(() => [{ title: '剧情卡' }, { title: '结局图鉴' }], []);
    const [currentTab, setCurrentTab] = useState<number>(0);

    const { robotUserId, fromSource, hasAiChapter } = useMemo(() => {
        return getCurrentInstance().router?.params || {};
    }, []);

    const profileRequest = useCallback(() => {
        profileDetailApi({ robotUserId })
            .then((res) => {
                setProfile(res);
            })
            .catch((err) => {
                showToast({
                    title: err.message,
                    icon: 'none',
                });
                if (profile === null) {
                    Taro.navigateBack();
                }
            });
    }, [profile, robotUserId]);

    useEffect(() => {
        Taro.eventCenter.on('profile_update', profileRequest);
        return () => {
            Taro.eventCenter.off('profile_update', profileRequest);
        };
    }, [profileRequest]);

    useEffect(() => {
        pageDidAppear(() => {
            console.log('pageDidAppear');
            profileRequest();
        });
    }, [profileRequest]);

    useDidShow(() => {
        profileRequest();
    });

    const onAvatarClick = useCallback(() => {
        const url = profile?.avatarImgUrl || '';
        jump2ImgPreview({ url });
    }, [profile]);

    const handlerClickTab = useCallback((index: number) => {
        setCurrentTab(index);
    }, []);

    const onGotoChat = useCallback(() => {
        if (!profile) {
            return;
        }
        if (fromSource === 'chat') {
            Taro.navigateBack();
            return;
        }

        const gotoChat = () => {
            const botBgUrl = profile?.aigcChatResourceInfo?.botBackgroundUrl;
            const optimizedUrl = optimizeImage({
                src: botBgUrl,
                width: 375,
                height: 812,
            });
            const optimizedMp4 = profile?.aigcChatResourceInfo?.botStandByUrls?.[0];
            const botContactUrl = profile?.aigcChatResourceInfo?.botContactUrl ?? '';
            jump2Chat({
                robotUserId: `${profile.userId}`,
                hasAiChapter: `${hasAiChapter}`,
                robotAccid: `${profile.accId}`,
                fromSource: 'profile',
                botBgUrl: `${optimizedUrl}`,
                botStandByUrl: `${optimizedMp4}`,
                botContactUrl: `${botContactUrl}`,
            });
        };

        if (fromSource === 'chatRelay') {
            Taro.navigateBack({
                delta: getCurrentPages().length - 1,
                success: () => {
                    Taro.nextTick(() => {
                        gotoChat();
                    });
                },
            });
        } else {
            gotoChat();
        }
    }, [profile, fromSource, hasAiChapter]);

    const onClickAddFriend = useCallback(() => {
        if (!profile) {
            return;
        }
        setProfile({
            ...profile,
            friend: true,
        });
        addFriendApi(profile.userId)
            .then(() => {})
            .catch((err) => {
                showToast({
                    title: err.message,
                    icon: 'none',
                });
                setProfile({
                    ...profile,
                    friend: false,
                });
            });
    }, [profile]);

    const deleteFriend = useCallback(() => {
        deleteFriendApi(profile?.userId)
            .then(() => {
                showToast({
                    title: '删除成功',
                    icon: 'success',
                });
                setProfile({
                    ...profile,
                    friend: false,
                });
                if (fromSource && fromSource.startsWith('chat')) {
                    // 从私聊跳转时刷新会话
                    useDetailStore.getState()?.updateSession?.();
                }
                /// 删除剧情引导标记
                useChapterGuideStore
                    .getState()
                    .updateGuideStatus((profile?.userId || 0).toString(), false);

                // 设置场景逻辑
                const userId = profile?.userId;
                if (userId) {
                    useScenarioStore.getState().cleanCurrentScenarioRecommendContent();
                    useScenarioStore.getState().fetchScenarioState(userId.toString(), '0');
                    useScenarioStore
                        .getState()
                        .fetchAllScenarioAigcIfNeed(userId.toString(), '0', false);
                }
            })
            .catch((err) => {
                showToast({
                    title: err.message,
                    icon: 'none',
                });
            });
    }, [fromSource, profile]);

    const [showExpandtDesc, setShowExpandtDesc] = useState<{
        showAction: boolean;
        isExpand: boolean;
    }>({ showAction: false, isExpand: false });
    const onShowMoreDesc = useCallback(() => {
        setShowExpandtDesc({
            showAction: showExpandtDesc.showAction,
            isExpand: !showExpandtDesc.isExpand,
        });
    }, [showExpandtDesc]);

    const contentRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        if (contentRef.current) {
            const lineHeight = parseFloat(getComputedStyle(contentRef.current).lineHeight);
            const containerHeight = contentRef.current?.scrollHeight;
            const twoLineHeight = 2 * lineHeight;
            setShowExpandtDesc({
                showAction: containerHeight > twoLineHeight,
                isExpand: false,
            });
        }
    }, [profile?.backgroundDesc]);

    const [showActionModal, setShowActionModal] = useState(false);

    const deleteConfirmDialog = useDialog(ConfirmModalProvider);

    const onActionModalClose = useCallback(() => {
        setShowActionModal(false);
    }, []);

    const actionList: ActionProps[] = useMemo(() => {
        const reportItem: ActionProps = {
            title: '举报',
            onClick: () => {
                setShowActionModal(false);
                showToast({
                    title: '举报成功',
                });
            },
        };
        const deleteItem = {
            title: '删除好友',
            onClick: () => {
                setShowActionModal(false);
                deleteConfirmDialog?.show({
                    title: '确定删除好友吗？',
                    info: '删除后，聊天记录将被永久删除，好感度将清空',
                    onConfirm: () => {
                        deleteFriend();
                    },
                });
            },
        };
        if (profile?.friend === true) {
            return [reportItem, deleteItem];
        }
        return [reportItem];
    }, [deleteConfirmDialog, deleteFriend, profile?.friend]);

    const onClickMore = useCallback(() => {
        setShowActionModal(true);
    }, [setShowActionModal]);

    return (
        <View className="profile-page-wrapper">
            {profile ? (
                <Fragment>
                    <View className="content-wrapper">
                        <View className="bg-wrapper">
                            <Header
                                profile={profile}
                                onClickAddFriend={onClickAddFriend}
                                onClickMore={onClickMore}
                            />
                        </View>
                        <View className="card-content">
                            <View className="avatar-wrapper">
                                <Avatar
                                    width={92}
                                    height={92}
                                    className="avatar"
                                    onClick={debounce(onAvatarClick)}
                                    src={profile.avatarImgUrl}
                                    lazyload
                                />
                                <View className="right-wrapper">
                                    <View className="name-wrapper">
                                        <Text className="name">{profile.nickname}</Text>
                                        <View
                                            className={classNames('gender-age-wrapper', {
                                                male: profile.gender === GENDER.MALE,
                                            })}>
                                            <Image
                                                className="gender"
                                                src={
                                                    profile.gender === GENDER.MALE
                                                        ? maleIcon
                                                        : femaleIcon
                                                }
                                            />
                                            <Text
                                                className={classNames('age', {
                                                    male: profile.gender === GENDER.MALE,
                                                })}>
                                                {getAge(profile)}
                                            </Text>
                                        </View>
                                    </View>
                                    <Text className="sign-txt">{profile.intro}</Text>
                                </View>
                            </View>
                            <View className="label-wrapper">
                                {profile?.labels?.map((label) => (
                                    <Text key={label} className="label">
                                        #{label}
                                    </Text>
                                ))}
                            </View>

                            <View className="desc-wrapper">
                                <Text className="desc-title">介绍</Text>
                                <Text
                                    ref={contentRef}
                                    className={classNames('desc-content', {
                                        'two-line':
                                            showExpandtDesc.showAction && !showExpandtDesc.isExpand,
                                    })}>
                                    {profile.backgroundDesc}
                                </Text>
                                {showExpandtDesc.showAction && (
                                    <Text
                                        className={classNames({
                                            'desc-content-toggle-fold': showExpandtDesc.isExpand,
                                            'desc-content-toggle-expand': !showExpandtDesc.isExpand,
                                        })}
                                        onClick={debounce(onShowMoreDesc)}>
                                        {!showExpandtDesc.isExpand ? '展开' : '收起'}
                                    </Text>
                                )}
                            </View>

                            <AtTabs
                                current={currentTab}
                                tabList={tabList}
                                swipeable={false}
                                animated={false}
                                onClick={debounce(handlerClickTab)}
                                customStyle={{
                                    height: 'auto',
                                }}>
                                <AtTabsPane current={currentTab} index={0}>
                                    <ChapterPane profile={profile} />
                                </AtTabsPane>
                                <AtTabsPane current={currentTab} index={1}>
                                    <EndingPane profile={profile} />
                                </AtTabsPane>
                            </AtTabs>
                        </View>
                    </View>

                    <View className="footer-wrapper">
                        <Text className="chat-text" onClick={debounce(onGotoChat)}>
                            去聊天
                        </Text>
                        <View className="footer-padding" />
                    </View>
                    <BottomActionModal
                        isOpened={showActionModal}
                        actions={actionList}
                        onClose={onActionModalClose}
                    />
                </Fragment>
            ) : (
                <CircleLoading />
            )}
        </View>
    );
};

export default React.memo(Profile);
