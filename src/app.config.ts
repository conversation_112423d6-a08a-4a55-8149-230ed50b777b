/* eslint-disable @typescript-eslint/no-var-requires */
const BaseConfig = require('./base.config');
const MirthConfig = require('./mirth.config');
const MoyiConfig = require('./moyi.config');

/**
 * @typedef {Object} ConfigType
 * @property {Record<string, any>} appConfig
 * @property {Record<string, any>} tabBarConfig
 */

/** @type {ConfigType} */
let config = MirthConfig;

if (process && process.env.APP_SOURCE !== 'mirth') {
    config = MoyiConfig;
} else {
    config = MirthConfig;
}

export default {
    ...BaseConfig,
    ...config,
};
