import fetch from '@/utils/fetch';
import { AigcRobotPotentialContact } from 'src/pages/friends/AigcRobotPotentialContact';
import { AigcRobotRelation } from 'src/pages/friends/AigcRobotRelation';

export const listPotentialdApi = (): Promise<AigcRobotPotentialContact[]> =>
    fetch('/api/mirth/user/aigc/potential/contact/list', {
        method: 'post',
        data: {},
    }) as Promise<AigcRobotPotentialContact[]>;

export const removePotentialdApi = (robotUserId: number): Promise<void> =>
    fetch('/api/mirth/user/aigc/potential/contact/remove', {
        method: 'post',
        data: {
            robotUserId,
        },
    }) as Promise<void>;

export const listFriendApi = (): Promise<AigcRobotRelation[]> =>
    fetch('/api/mirth/user/aigc/friend/list', {
        method: 'post',
        data: {},
    }) as Promise<AigcRobotRelation[]>;

export const addFriendApi = (robotUserId: number): Promise<void> =>
    fetch('/api/mirth/user/aigc/relation/operation', {
        method: 'post',
        data: {
            robotUserId,
            operationType: 1,
        },
    }) as Promise<void>;

export const deleteFriendApi = (robotUserId: number): Promise<void> =>
    fetch('/api/mirth/user/aigc/relation/operation', {
        method: 'post',
        data: {
            robotUserId,
            operationType: 2,
        },
    }) as Promise<void>;

export const isFriendApi = (robotUserId: number): Promise<boolean> =>
    fetch('/api/mirth/user/aigc/relation/friend', {
        method: 'post',
        data: {
            robotUserId,
        },
    }) as Promise<boolean>;
