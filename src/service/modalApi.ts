import fetch from '@/utils/fetch';

export interface TeenState {
    state?: number; // 是否开启青少年模式 0关闭 1开启
    finish?: boolean; // 用户是否选择不再显示弹窗
}

export const queryTeenState = (): Promise<TeenState> =>
    fetch('/api/mirth/user/teen/pattern/state/get', {
        method: 'post',
        data: { source: 'h5' },
    });

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const uploadClientEvent = (data: { type: number; body: { userId: number } }): any =>
    fetch('/api/mirth/user/client/upload/event', {
        method: 'post',
        data,
    });
