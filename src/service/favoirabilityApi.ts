import fetch from '@/utils/fetch';

export const intimacyDetailApi = (data: { robotUserId: string }): any =>
    fetch('/api/mirth/user/aigc/intimacy/detail', {
        method: 'post',
        data,
    });

export const intimacyLevelApi = (data): any =>
    fetch('/api/mirth/user/aigc/intimacy/level/get', {
        method: 'post',
        data,
    });

export const testIntimacyAddApi = (data): any =>
    fetch('/api/mirth/user/test/aigc/intimacy/add', {
        method: 'post',
        data,
    });

export const testIntimacyCleanApi = (data): any =>
    fetch('/api/mirth/user/test/aigc/intimacy/clear', {
        method: 'post',
        data,
    });
