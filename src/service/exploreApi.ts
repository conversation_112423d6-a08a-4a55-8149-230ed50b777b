import fetch from '@/utils/fetch';
import { AigcRobotRecommend } from '@/pages/explore/meta/AigcRobotRecommend';
import { AigcRedPoint } from 'src/pages/explore/meta/AigcRedPoint';

export const recommendListApi = (): Promise<AigcRobotRecommend[]> =>
    fetch('/api/mirth/user/aigc/recommend/list', {
        method: 'post',
        data: {},
    }) as Promise<AigcRobotRecommend[]>;

export const recommendRedApi = (): Promise<AigcRedPoint> =>
    fetch('/api/mirth/home/<USER>/aigc/red/point/get', {
        method: 'post',
        data: {},
    }) as Promise<AigcRedPoint>;

export default null;
