import fetch from '@/utils/fetch';

export const scenarioStatusQuery = (robotUserId: string, storyId: string): any =>
    fetch('/api/mirth/chatbot/scene/setting/info', {
        method: 'post',
        data: {
            robotUserId,
            storyId,
        },
    });

export const scenarioRecommendQuery = (
    robotUserId: string,
    storyId: string,
    position: number
): any =>
    fetch('/api/mirth/chat/scene/setting/recommend', {
        method: 'post',
        data: {
            robotUserId,
            storyId,
            positionStr: position,
        },
    });

export const scenarioStartRequest = (
    robotUserId: string,
    storyId: string,
    settingContent: string,
    aiGenerate: boolean
): any =>
    fetch('/api/mirth/chat/scene/setting/start', {
        method: 'post',
        data: {
            robotUserId,
            storyId,
            settingContent,
            aiGenerate,
        },
    });

export const scenarioEndRequest = (robotUserId: string, storyId: string): any =>
    fetch('/api/mirth/chat/scene/setting/end', {
        method: 'post',
        data: {
            robotUserId,
            storyId,
        },
    });
