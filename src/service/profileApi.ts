import fetch from '@/utils/fetch';
import { AigcEndingCardInfo, AigcRobotProfile } from '@/types/AigcRobotProfile';
import {
    GameChapterPrecheckResult,
    GameChapterStartResult,
} from 'src/pages/profile/GameChapterStartResult';
import { ChapterDetail } from './res/ChapterDetail';

export const profileDetailApi = (data: { robotUserId: string }): Promise<AigcRobotProfile> =>
    fetch('/api/mirth/user/aigc/profile/detail', {
        method: 'post',
        data,
    }) as Promise<AigcRobotProfile>;

export const chapterDetailApi = (chapterId: string): Promise<ChapterDetail> =>
    fetch('/api/sociallive/ai/game/chapter/card/aggregate/info', {
        method: 'post',
        data: { chapterId },
    }) as Promise<ChapterDetail>;

export const startChapterApi = (data: Record<string, unknown>): Promise<GameChapterStartResult> =>
    fetch('/api/sociallive/ai/game/chapter/start', {
        method: 'post',
        data,
    }) as Promise<GameChapterStartResult>;

export const precheckChapterApi = (
    data: Record<string, unknown>
): Promise<GameChapterPrecheckResult> =>
    fetch('/api/sociallive/ai/game/chapter/start/precheck', {
        method: 'post',
        data,
    }) as Promise<GameChapterPrecheckResult>;

export const endingDetailApi = (data: { chapterId: string }): Promise<AigcEndingCardInfo[]> =>
    fetch('/api/sociallive/ai/game/ending/detail', {
        method: 'post',
        data,
    }) as Promise<AigcEndingCardInfo[]>;
