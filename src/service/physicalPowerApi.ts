import fetch from '@/utils/fetch';

export const balanceQuery = (): any =>
    fetch('/api/mirth/finance/stamina/balance/query', {
        method: 'post',
        data: {},
    });

export const productPage = (req): any =>
    fetch('/api/social/middle/pay/payout/product/page', {
        method: 'post',
        data: {
            req,
        },
    });

export const assetBalance = (currency): any =>
    fetch('/api/social/middle/asset/balance', {
        method: 'post',
        data: {
            currency,
        },
    });

export const payoutApply = (req): any =>
    fetch('/api/social/middle/pay/payment/payout/exchange', {
        method: 'post',
        data: {
            req,
        },
    });

export const intimacyClear = (): any =>
    fetch('/api/mirth/user/test/aigc/intimacy/clear', {
        method: 'post',
        data: {
            userId: '871311149',
            robotUserId: '435633321',
        },
    });

export const intimacyAdd = (): any =>
    fetch('/api/mirth/user/test/aigc/intimacy/add', {
        method: 'post',
        data: {
            userId: '871311149',
            robotUserId: '435633321',
            incrIntimacy: 45,
        },
    });

export const gameBalanceQuery = (): any =>
    fetch('/api/sociallive/ai/game/query/balance', {
        method: 'POST',
        data: {
            sceneType: 'chapter_card',
        },
    });
