import { BuyInspirationData, InspirationData } from '@/pages/chat/type/inspiration';
import fetch from '@/utils/fetch';
import { ContactListResult } from '@/components/Message/conversationModel';

// 获取新手引导配置
export const apiUserGuide = (): any =>
    fetch('/api/mirth/user/aigc/guide/resource', {
        method: 'post',
        data: {},
    });

// 获取im token  accid
export const apiGetImToken = (): any =>
    fetch('/api/mirth/im/token/get', {
        method: 'post',
        data: {},
    });

// 会话初始化
export const apiInitSession = (data): any =>
    fetch('/api/sociallive/chatbot/session/init', {
        method: 'post',
        data,
    });

// 获取会话列表用户信息
export const apiContactList = (contactList): any =>
    fetch('/api/mirth/chat/contact/list', {
        method: 'post',
        data: { contactList: JSON.stringify(contactList), source: 'bot' },
    });

// 获取用户信息
export const apiGetUserCenterInfo = (data?: any): any =>
    fetch('/api/mirth/user/center/detail', {
        method: 'post',
        data,
    });

// 图片转文字
export const apiImageGenerateContent = (data): any =>
    fetch('/api/sociallive/chatbot/content/to/text', {
        method: 'post',
        data,
    });

// 图片消息发送
export const apiSendImage = (data): any =>
    fetch('/api/mirth/chatbot/message/send', {
        method: 'post',
        data,
    });

// 灵感回复
export const getInspiration = (data: { toUserId: string }): any =>
    fetch('/api/mirth/chat/inspiration/list', {
        method: 'post',
        data,
    });

// 灵感回复
export const fetchInspiration = (data: {
    toUserId: string;
    messageId: string;
    unlockId: number;
    location: number;
    sync: boolean;
}): Promise<InspirationData> =>
    fetch('/api/mirth/chat/inspiration/info', {
        method: 'post',
        data,
    }) as Promise<InspirationData>;

// 灵感回复付费
export const buyInspiration = (data: {
    toUserId: string;
    messageId: string;
}): Promise<BuyInspirationData> =>
    fetch('/api/mirth/chat/inspiration/buy', {
        method: 'post',
        data,
    }) as Promise<BuyInspirationData>;

// 内心独白
export const getMonologue = (data: { monologueId: string; userId: string }): any =>
    fetch('/api/mirth/home/<USER>/unlock', {
        method: 'post',
        data,
    });

// 内心独白权限
export const getMonologuePermission = (data: { monologueId: string; robotUserId: string }): any =>
    fetch('/api/mirth/home/<USER>/status/get', {
        method: 'post',
        data,
    });

// 获取剧情引导信息
export const apiGetEpicGuide = (data: { robotUserId: string }): any => {
    return fetch('/api/mirth/user/aigc/chat/robot/chapter/guide', {
        method: 'post',
        data,
    });
};

// 重置对话关系
export const apiResetSessionRelation = (data: { robotUserId: number }): any => {
    return fetch('/api/mirth/user/aigc/relation/session/reset', {
        method: 'post',
        data,
    });
};
