import fetch from '@/utils/fetch';

export const giftResourcesQuery = (timestamp: number): any =>
    fetch('/api/social/middle/gift/resource/list', {
        method: 'post',
        data: {
            startTime: timestamp,
            scene: 'mirth_gift_ai',
        },
    });

export const giftPanelsQuery = (): any =>
    fetch('/api/social/middle/panel/list', {
        method: 'post',
        data: {
            scene: 'mirth_gift_ai',
        },
    });

export const giftsByPanelQuery = (panelCode: string): any =>
    fetch('/api/social/middle/panel/config/list', {
        method: 'post',
        data: {
            panelCode,
        },
    });

export const giftSendRequest = (
    panelCode: string,
    targetUserIds: string[],
    giftId: number,
    number: number,
    batchLevel: number,
    sessionPeriodId: string
): any =>
    fetch('/api/social/middle/gift/present', {
        method: 'post',
        data: {
            param: JSON.stringify({
                panelCode,
                targetUserIds,
                giftId,
                number,
                batchLevel,
                ext: {
                    sessionPeriodId,
                },
            }),
        },
    });

export const packSendRequest = (
    targetIds: string[],
    resourceId: number,
    number: number,
    resourceType: number,
    batchLevel: number,
    sessionPeriodId: string
): any =>
    fetch('/api/social/middle/gift/mirth/backpack/present', {
        method: 'post',
        data: {
            param: JSON.stringify({
                targetIds,
                resourceId,
                number,
                resourceType,
                batchLevel,
                ext: {
                    sessionPeriodId,
                },
            }),
        },
    });

export const backpackQuery = (robotUserId: number): any =>
    fetch('/api/social/middle/gift/mirth/backpack/resource/list', {
        method: 'post',
        data: {
            robotUserId,
        },
    });
