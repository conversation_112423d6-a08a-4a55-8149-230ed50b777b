export interface StaminaBalanceRes {
    balance?: number;
    autoRecoveryLimit?: number;
    singleRecoveryNum?: number;
    singleRecoveryMills?: number;
    lastRecoveryTimeMills?: number;
    nextRecoveryRemainMills?: number;
    allRecoveryRemainMills?: number;
}

export interface BalanceRes {
    balance?: number;
}

export interface ExcahngeBalanceRes {
    ticketBalance?: string;
    shardBalance?: string;
}

export function getGameBalance(data?: string): string | undefined {
    if (!data) {
        return undefined;
    }
    if (data === undefined || data === '' || data === null || data === 'null') {
        return undefined;
    }
    return data;
}
