/**
 * 字段定义
 * https://music-ox.hz.netease.com/ox/music/api/detail/4530825/basic
 */

export interface ProductExtend {
    productImg?: string;
    bannerImg?: string;
    iosBannerImg?: string;
    cornerMark?: string;
    notShowPromoProduct?: boolean;
    level?: number;
    supportChannels?: string[];
}

export interface Product {
    id?: number;
    title?: string;
    subTitle?: string;
    originalPrice?: string;
    paymentPrice?: string;
    paymentCurrency?: string;
    originalAmount?: string;
    receiveAmount?: string;
    receiveCurrency?: string;
    extend?: ProductExtend;
}

export interface ProductChannel {
    name?: string;
    icon?: string;
    code?: string;
    channel?: string;
    subChannel?: string;
}

export interface PromoCode {
    promoType?: string;
    promoCode?: string;
    deltaPrice?: string;
    deltaAmount?: string;
}

export interface ProductLevel {
    product: Product;
    channels: ProductChannel[];
    promoCode: PromoCode;
}

export interface PayProductList {
    levels?: ProductLevel[];
    lastChannelCode?: string;
    lastProductId?: number;
}
