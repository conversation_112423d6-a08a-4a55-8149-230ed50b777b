export interface ChapterDetail {
    chapterId?: string;
    chapterName?: string;
    chapterDesc?: string;
    chapterLevel?: string;
    chapterImg?: string;
    cardStatus?: number;
    roleId?: string;
    enableExploration?: boolean;
    chapterBigImg?: string; // 视频url(策划眼中的动图)
    chapterLevelImg?: string;
    chapterCardSources?: string[];
}

export const ChapterCardSource = {
    Lottery: 'lottery', // 抽奖
    ShardExchange: 'shardExchange', // 兑换
};
