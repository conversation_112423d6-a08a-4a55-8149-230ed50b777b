import { create } from 'zustand';

/**
 * EpicPlayer 组件的属性接口
 * 用于配置播放器的初始化参数
 */
interface EpicPlayerProps {
    roleId: string; // 角色ID，标识当前播放的角色
    chapterId: string; // 章节ID，标识当前播放的章节
    recordId: string; // 记录ID，用于标识特定的播放记录
    backToChat: boolean; // 是否在播放结束后返回聊天界面
    showSkipButton?: boolean; // 是否显示跳过按钮（可选）
    onPlayEnd?: (result: boolean) => void; // 播放结束的回调函数（可选）
}

/**
 * EpicPlayer 状态管理接口
 */
interface EpicPlayerState {
    // 状态属性
    isVisible: boolean; // 播放器是否可见
    backToChat: boolean; // 播放结束后是否返回聊天界面
    roleId: string; // 当前播放的角色ID
    chapterId: string; // 当前播放的章节ID
    recordId: string; // 当前播放记录ID
    showSkipButton: boolean; // 是否显示跳过按钮
    onPlayEnd: (result: boolean) => void; // 播放结束回调函数

    // 操作方法
    showEpicPlayer: (epicPlayerProps: EpicPlayerProps) => void; // 显示播放器
    hideEpicPlayer: (isError: boolean, result: boolean) => void; // 隐藏播放器
}

/**
 * EpicPlayer 全局状态管理 Store
 */
const useEpicPlayerStore = create<EpicPlayerState>((set, get) => ({
    // 初始状态值
    isVisible: false,
    backToChat: false,
    roleId: '',
    chapterId: '',
    recordId: '',
    showSkipButton: true,
    onPlayEnd: () => {},

    /**
     * 显示 EpicPlayer 播放器
     * @param epicPlayerProps - 播放器配置参数
     */
    showEpicPlayer: (epicPlayerProps: EpicPlayerProps) => {
        const { roleId, chapterId, recordId, backToChat, showSkipButton, onPlayEnd } =
            epicPlayerProps;
        set({
            isVisible: true,
            roleId,
            chapterId,
            backToChat,
            onPlayEnd: onPlayEnd ?? (() => {}), // 如果未提供回调，使用空函数
            recordId: recordId || '', // 如果未提供recordId，使用空字符串
            showSkipButton: showSkipButton ?? true, // 如果未指定，默认显示跳过按钮
        });
    },

    /**
     * 隐藏 EpicPlayer 播放器
     * @param isError - 是否因错误而关闭
     * @param result - 播放结果（成功/失败）
     */
    hideEpicPlayer: (isError, result) => {
        // 重置播放器状态
        set({ isVisible: false, roleId: '', chapterId: '' });
        // 如果不是因为错误关闭，执行播放结束回调
        if (!isError) {
            get().onPlayEnd(result);
        }
    },
}));

export default useEpicPlayerStore;
