import { StoreCreator } from '@/components/storeContext/StoreContext';
import { assetBalance, balanceQuery, gameBalanceQuery } from '@/service/physicalPowerApi';
import { BalanceRes, ExcahngeBalanceRes, StaminaBalanceRes } from '@/service/res/balanceRes';
import { showToast } from '@tarojs/taro';
import { getCoinType } from '@/utils/appSourceAdapter';
import { create } from 'zustand';

export interface BalanceState {
    balance?: BalanceRes;
    requestBalance: () => void;
    staminaBalance?: StaminaBalanceRes;
    requestStaminaBalance: () => void;
    exchangeBalance?: ExcahngeBalanceRes;
    requestExchangeBalance: () => void;
}

export const useBalanceStore = create<BalanceState>((set) => ({
    requestBalance: () => {
        assetBalance(getCoinType())
            .then((res) => {
                console.log('req', 'assetBalance', 'res:', res);
                set({ balance: res });
            })
            .catch((err) => {
                showToast({
                    title: err.message,
                    icon: 'none',
                });
            });
    },
    requestStaminaBalance: () => {
        balanceQuery()
            .then((res) => {
                console.log('req', 'balanceQuery', 'res:', res);
                set({ staminaBalance: res });
            })
            .catch((err) => {
                showToast({
                    title: err.message,
                    icon: 'none',
                });
            });
    },
    requestExchangeBalance: () => {
        gameBalanceQuery()
            .then((res) => {
                console.log('req', 'exchangeBalance', 'res:', res);
                set({ exchangeBalance: res });
            })
            .catch((err) => {
                showToast({
                    title: err.message,
                    icon: 'none',
                });
            });
    },
}));

export const balanceStoreProvider: StoreCreator<BalanceState> = () => {
    return useBalanceStore;
};
