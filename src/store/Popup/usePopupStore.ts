import { create } from 'zustand';
import useAppPathStore from '@/store/useAppPathStore';

import { PopupStatus, PopupPageScope, PopupBusinessKey } from './type';
import { kMainPaths, PopupData, PopupRegistrationOptions, defaultPopupSettings } from './popupdata';

interface PopupState {
    currentPopup: PopupData | null; // 当前显示的弹窗
    registerPopup: (options: PopupRegistrationOptions) => void; // 注册弹窗
    startPopup: (key: PopupBusinessKey) => void; // 开始显示指定弹窗
    completePopup: (key: PopupBusinessKey) => void; // 完成指定弹窗
    showPopupIfNeeded: (path: string) => void; // 检查并显示弹窗
}

const usePopupStore = create<PopupState>((set) => {
    let popups: PopupData[] = [];

    // 是否是一级页面
    const isMainPath = (path: string) => {
        if (!path) {
            return false;
        }
        return kMainPaths.some((scope) => path.includes(scope));
    };

    // 清理失效的弹窗
    const cleanupExpiredPopups = () => {
        popups = popups.filter((popup) => {
            return popup.status !== PopupStatus.CLOSED && popup.status !== PopupStatus.REJECTED;
        });
    };

    // 是否有阻塞的弹窗处理
    const hasBlockingPopups = () => {
        return popups.some((popup) => popup.isBlocking && popup.status === PopupStatus.PENDING);
    };

    // 当前是否有弹窗正在显示
    const isPopupShowing = () => {
        return popups.some((popup) => popup.status === PopupStatus.SHOWING);
    };

    // 获取当前页面需要显示的弹窗
    const getCurrentPopup = (path: string) => {
        if (popups.length === 0) {
            return null;
        }
        // 遍历数组
        for (let i = 0; i < popups.length; i++) {
            const popup = popups[i];
            if (popup.status !== PopupStatus.READY) {
                // 未准备就绪就跳过
                continue;
            }
            if (!popup.pageScope) {
                if (
                    popup.whitePageList &&
                    !popup.whitePageList.find((item) => path.includes(item))
                ) {
                    // 配置白名单，但不包含时跳过
                    continue;
                }
                if (
                    popup.blackPageList &&
                    popup.blackPageList.find((item) => path.includes(item))
                ) {
                    // 配置黑名单，包含时跳过
                    continue;
                }
                return popup;
            }
            if (popup.pageScope === PopupPageScope.MAIN && isMainPath(path)) {
                return popup;
            }
        }
        return null;
    };

    // 检查当前页面是否需要显示弹窗
    const showPopupIfNeeded = (path: string) => {
        cleanupExpiredPopups();
        if (hasBlockingPopups()) {
            return;
        }
        if (isPopupShowing()) {
            return;
        }
        const popup = getCurrentPopup(path);
        if (!popup) {
            return;
        }
        // 弹窗准备就绪，显示弹窗
        popup.status = PopupStatus.SHOWING;
        set({ currentPopup: popup });
    };

    // 检查弹窗条件
    const checkPopupCondition = async (popupKey: string) => {
        const popup = popups.find((p) => p.key === popupKey);
        if (!popup) {
            return;
        }
        try {
            // 执行条件检查
            let shouldShow = true;
            if (popup.condition) {
                shouldShow = await popup.condition();
            }
            popup.status = shouldShow ? PopupStatus.READY : PopupStatus.REJECTED;
        } catch {
            popup.status = PopupStatus.REJECTED;
        } finally {
            showPopupIfNeeded(useAppPathStore.getState().currentPath);
        }
    };

    const insertPopup = (popup: PopupData) => {
        for (let i = 0; i < popups.length; i++) {
            if (popups[i].priority >= popup.priority) {
                popups.splice(i, 0, popup);
                return;
            }
        }
        popups.push(popup);
    };

    return {
        currentPopup: null,
        registerPopup: (options) => {
            const id = `popup-${Date.now()}`;
            const newPopup: PopupData = {
                ...defaultPopupSettings,
                id,
                ...options,
            };
            insertPopup(newPopup);
            if (newPopup.key !== PopupBusinessKey.APP_GUIDE) {
                // 非新手引导情况下，注册弹窗后立即检查条件
                checkPopupCondition(newPopup.key);
            }
        },
        startPopup: (key) => {
            const popup = popups.find((p) => p.key === key);
            if (popup) {
                popup.status = PopupStatus.SHOWING;
            }
        },
        completePopup: (key) => {
            const popup = popups.find((p) => p.key === key);
            if (popup) {
                popup.status = PopupStatus.CLOSED;
                set({ currentPopup: null });
                /// 这里添加延迟 1s 是因为弹窗点击可能会跳转到其他页面，所以需要延迟检查
                setTimeout(() => {
                    showPopupIfNeeded(useAppPathStore.getState().currentPath);
                }, 1000);
            }
        },
        showPopupIfNeeded,
    };
});

export default usePopupStore;
