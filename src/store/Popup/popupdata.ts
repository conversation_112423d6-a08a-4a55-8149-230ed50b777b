import { PopupType, PopupStatus, PopupPageScope, PopupBusinessKey } from './type';

// 默认一级页面地址集合
export const kMainPaths: string[] = [
    'aichat/index.html',
    '/message/',
    '/friends/',
    '/explore/',
    '/self/',
];

// 弹窗数据结构
export interface PopupData {
    id: string; // 唯一 ID
    key: PopupBusinessKey; // 弹窗业务ID
    type: PopupType; // 弹窗类型
    priority: number; // 优先级，数字越小优先级越高
    status: PopupStatus; // 弹窗状态
    isBlocking: boolean; // 是否阻塞其他弹窗显示
    pageScope?: PopupPageScope; // 如果定义了，弹窗只会在指定页面显示
    whitePageList?: string[]; // 指定页面白名单
    blackPageList?: string[]; // 指定页面黑名单
    condition?: () => Promise<boolean>; // 弹窗显示条件
    dependencies?: PopupBusinessKey[]; // 依赖的其他弹窗key，这些弹窗关闭后才会显示
    mutex?: PopupBusinessKey[]; // 互斥的弹窗key，显示此弹窗时会关闭这些弹窗
}

// 弹窗注册选项（用于定义弹窗）
export interface PopupRegistrationOptions {
    key: PopupBusinessKey; // 业务Key
    pageScope?: PopupPageScope;
    whitePageList?: string[];
    blackPageList?: string[];
    type?: PopupType;
    priority?: number;
    isBlocking?: boolean;
    condition?: () => Promise<boolean>;
}

// 默认弹窗设置
export const defaultPopupSettings: Partial<PopupData> = {
    type: PopupType.MODAL,
    priority: 100,
    status: PopupStatus.PENDING,
    isBlocking: false,
};
