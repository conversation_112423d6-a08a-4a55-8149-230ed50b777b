// 定义弹窗类型
export enum PopupType {
    MODAL = 'modal', // 弹窗
    CUSTOM = 'custom', // 自定义(适配新手引导的场景)
}

// 弹窗状态
export enum PopupStatus {
    PENDING = 'pending', // 检查条件中
    READY = 'ready', // 已准备好，等待显示
    SHOWING = 'showing', // 正在显示
    CLOSED = 'closed', // 已关闭
    REJECTED = 'rejected', // 条件检查未通过
}

// 弹窗显示页面
export enum PopupPageScope {
    MAIN = 'main', // 一级页面
    MESSAGE = 'message', // 消息页面
    FRIEND = 'friends', // 好友页面
    EXPLORE = 'explore', // 发现页面
    SELF = 'self', // 我的页面
    CHAT = 'chat', // 私聊页面
}

// 弹窗的业务 Key（按照优先级排序）
export enum PopupBusinessKey {
    FORCE_UPGRADE = 'force_upgrade', // 强制升级弹窗
    NEW_MIRTH = 'new_mirth', // 老用户告知
    APP_GUIDE = 'app_guide', // 新手引导
    SIGN = 'sign', // 签到
    USER_REWARDS = 'user_rewards', // 新用户十连抽
    YOUTH_MODE = 'youth_mode', // 青少年模式
    COMMON = 'common', // 通用弹窗
}
