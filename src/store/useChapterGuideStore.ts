import { create } from 'zustand';
import NIMService from '@/hooks/useNewNIM';
import { useSessionStore } from '@/hooks/sessionStore';
import Taro, { getCurrentInstance, getStorageSync, setStorageSync } from '@tarojs/taro';
import useEpicPlayerStore from '@/store/useEpicPlayerStore';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { apiGetEpicGuide } from '@/service/imApi';
import { Conversation } from '@/components/Message/conversationModel';
import useUserInfoStore from '@/store/useUserInfoStore';
import { startChapterApi } from '@/service/profileApi';
import { GameChapterStartResult, StartCode } from '@/pages/profile/GameChapterStartResult';

const EPIC_STORAGE_KEY = 'USER_EPIC_DATA';

interface EpicGuideData {
    uid: string; // 当前用户的 UID
    epicGuide: string[]; // 已经显示过的剧情引导对应 AI 的 uid
}

interface ChapterGuide {
    robotUserId: string; // AI 机器人用户ID
    robotAccid: string; // AI 机器人 Accid
    roleId: string; // AI 机器人角色ID
    chapterId: string; // AI 机器人剧情ID
    hasGuide: boolean | undefined; // 是否有剧情引导 (undefined 表示未查询过)
    showGuideTip: boolean; // 是否显示剧情引导提示
    updateGuideStatus: (robotUserId: string, status: boolean) => void; // 更新剧情引导状态
    startGuide: () => void; // 开始剧情引导
    endGuide: (result: boolean) => void; // 结束剧情引导
    endGuideForError: () => void; // 发送剧情引导完成通知
    endGuideTip: () => void; // 结束剧情引导提示
}

const useChapterGuideStore = create<ChapterGuide>((set, get) => {
    // 检查用户信息是否正确
    const checkUserInfo = (robotUserId: string) => {
        if (!robotUserId || robotUserId.length < 2) {
            coronaWarnMessage('EpicGuide', `检查剧情引导信息失败 - ai的 uid 错误 - ${robotUserId}`);
            return { valid: false, myUserId: '' };
        }
        const userInfo = useUserInfoStore.getState().userBaseInfo;
        const myUserId = userInfo?.userBase?.userId?.toString() || '';
        if (!myUserId) {
            coronaWarnMessage('EpicGuide', '检查本地剧情引导信息失败 - 获取自己的 uid 为空');
            return { valid: false, myUserId: '' };
        }
        return { valid: true, myUserId };
    };
    // 查询是否需要剧情引导
    const queryGuideStatus = (robotUserId: string, robotAccid: string) => {
        const conversationId = NIMService.convertSessionIdToConversationId(robotAccid);
        const conversation = useSessionStore
            .getState()
            .finalSessionList.find((item: Conversation) => item.conversationId === conversationId);
        if (conversation) {
            // 有会话时不显示初遇剧情
            return true;
        }
        const { valid, myUserId } = checkUserInfo(robotUserId);
        if (!valid) {
            return true;
        }
        const storageStr = getStorageSync(EPIC_STORAGE_KEY) || '[]';
        const storageData = JSON.parse(storageStr);
        const userIndex = storageData.findIndex((item: EpicGuideData) => item.uid === myUserId);
        if (userIndex === -1) {
            // 不存在本地记录，则默认未完成
            return false;
        }
        const userData = storageData[userIndex];
        const hasGuide = userData.epicGuide.includes(robotUserId);
        if (userIndex !== 0) {
            // 当前用户不在第一位，则移动到第一位并删除超过 2 位的记录
            storageData.splice(userIndex, 1);
            storageData.unshift(userData);
            const trimmedData = storageData.slice(0, 2);
            setStorageSync(EPIC_STORAGE_KEY, JSON.stringify(trimmedData));
        }
        return hasGuide;
    };
    // 获取引导信息
    const fetchGuideInfo = async (guideStatus: boolean) => {
        try {
            const robotUserId = get().robotUserId;
            const res = await apiGetEpicGuide({ robotUserId });
            if (!res) {
                set({ hasGuide: false });
                coronaWarnMessage('EpicGuide', '获取剧情引导信息失败 - 返回数据为空');
                return;
            }
            const { roleId, chapterId } = res;
            if (!roleId || !chapterId) {
                // 没有剧情引导信息
                set({ hasGuide: false });
                return;
            }
            if (!guideStatus) {
                // 需要剧情引导时,先请求开始剧情
                startChapterApi({ chapterId, roleId }).then((response) => {
                    const result = response as GameChapterStartResult;
                    const success = result.code === StartCode.SUCCESS;
                    if (success) {
                        useEpicPlayerStore.getState().showEpicPlayer({
                            roleId,
                            chapterId,
                            recordId: result.recordId,
                            backToChat: true,
                            onPlayEnd: get().endGuide,
                        });
                    }
                    set({ hasGuide: success, roleId, chapterId });
                });
            } else {
                set({ hasGuide: !guideStatus, roleId, chapterId });
            }
        } catch (e) {
            set({ hasGuide: false });
            coronaWarnMessage('EpicGuide', `获取剧情引导信息失败 ${e}`);
        }
    };

    return {
        robotUserId: '',
        robotAccid: '',
        roleId: '',
        chapterId: '',
        hasGuide: undefined,
        showGuideTip: false,
        updateGuideStatus(robotUserId, status) {
            try {
                const { valid, myUserId } = checkUserInfo(robotUserId);
                if (!valid) {
                    return;
                }
                const storageStr = getStorageSync(EPIC_STORAGE_KEY) || '[]';
                let storageData = JSON.parse(storageStr);
                let userIndex = storageData.findIndex(
                    (item: EpicGuideData) => item.uid === myUserId
                );
                let userData;
                if (userIndex === -1) {
                    // 用户不存在，创建新用户数据
                    userData = { uid: myUserId, epicGuide: [] };
                    storageData.unshift(userData);
                    userIndex = 0;
                } else {
                    // 获取用户数据
                    userData = storageData[userIndex];
                    if (userIndex !== 0) {
                        // 将该用户移动到数组最前面
                        storageData.splice(userIndex, 1);
                        storageData.unshift(userData);
                        userIndex = 0;
                    }
                }
                const epicIndex = userData.epicGuide.indexOf(robotUserId);
                if (status && epicIndex === -1) {
                    userData.epicGuide.push(robotUserId);
                } else if (!status && epicIndex !== -1) {
                    userData.epicGuide.splice(epicIndex, 1);
                }
                // 删除超过俩个的记录
                storageData = storageData.slice(0, 2);
                setStorageSync(EPIC_STORAGE_KEY, JSON.stringify(storageData));
            } catch (e) {
                coronaWarnMessage('EpicGuide', `更新本地剧情引导信息失败 ${e}`);
            }
        },
        startGuide: () => {
            // 重置上次引导状态
            set({
                hasGuide: undefined,
                showGuideTip: false,
                robotUserId: '',
                robotAccid: '',
                roleId: '',
                chapterId: '',
            });
            // 获取AI信息
            const { robotAccid = '', robotUserId = '' } = getCurrentInstance().router?.params || {};
            if (!robotAccid || !robotUserId) {
                // 没有获取到 AI 信息不显示(新手引导情况下)
                set({ hasGuide: false });
                return;
            }
            set({ robotUserId, robotAccid });
            // 检查是否需要剧情引导
            const guideStatus = queryGuideStatus(robotUserId, robotAccid);
            fetchGuideInfo(guideStatus);
        },
        endGuide: (result) => {
            // 清理完成标记
            set({ hasGuide: false, showGuideTip: result });
            // 保存关闭状态
            get().updateGuideStatus(get().robotUserId, true);
            // 通知继续动画
            Taro.nextTick(() => {
                Taro.eventCenter.trigger('chapter_guide_finish');
            });
            if (result) {
                //  引导完成后，延迟 5s 后关闭提示
                setTimeout(() => {
                    set({ showGuideTip: false });
                }, 5200);
            }
        },
        endGuideForError: () => {
            set({ hasGuide: false, showGuideTip: false });
            Taro.eventCenter.trigger('chapter_guide_finish');
        },
        endGuideTip: () => {
            set({ showGuideTip: false });
        },
    };
});

export default useChapterGuideStore;
