import { create } from 'zustand';
import { coronaWarnMessage } from '@music/mat-base-h5';
import Toast from '@/components/Toast';
import Taro from '@tarojs/taro';
import { apiGetUserCenterInfo } from '@/service/imApi';
import { useBalanceStore } from '@/store/balanceStore';
import { type UserDetailVo, UpdateUser } from '@/types/userInfo';
import fetch from '@/utils/fetch';

const apiGetFirstEnterPopupInfo = (data: any) =>
    fetch('/api/mirth/user/aigc/entrance/popup/info', {
        method: 'post',
        data,
    });
const apiUpdateUserBaseInfo = (data: any) =>
    fetch('/api/mirth/user/center/update', {
        method: 'post',
        data,
    });

interface SelfStore {
    /** 用户基础信息 */
    userBaseInfo: UserDetailVo;
    /** 名称 */
    nickname: string;
    /** 性别 */
    selectedGender: 1 | 2;
    /** 有效状态 */
    effectiveStatus: 0 | 1 | 2;
    /** 升级弹窗信息 */
    firstEnterPopupInfo: {
        /** 是否有弹窗 */
        complete: boolean;
        /** 跳转key */
        jumpKey: string;
        /** 上线时间 */
        onlineTime: number;
        /** 注册时间 */
        registerTime: number;
    };
    setSelectedGender: (gender: 1 | 2) => void;
    getUserBaseInfo: () => Promise<void>;
    updateUserBaseInfo: (data: UpdateUser) => Promise<void>;
    setNickname: (nickname: string) => void;
    setEffectiveStatus: (status: 0 | 1 | 2) => void;
    validateInput: (value: string) => void;
    getFirstEnterPopupInfo: () => Promise<boolean>;
    reuestApi: () => void;
}

const useSelfStore = create<SelfStore>((set, get) => ({
    userBaseInfo: {},
    selectedGender: 1,
    nickname: '',
    effectiveStatus: 0,
    localGender: 1,
    worldCoinBalance: '0.00',
    firstEnterPopupInfo: {
        complete: true,
        jumpKey: '',
        onlineTime: 0,
        registerTime: 0,
    },
    setSelectedGender: (gender: 1 | 2) => set({ selectedGender: gender }),
    setNickname: (nickname: string) => set({ nickname }),
    getUserBaseInfo: async () => {
        const res: UserDetailVo = await apiGetUserCenterInfo({});
        set({ userBaseInfo: res, selectedGender: res?.userBase?.gender as 1 | 2 });
    },
    getFirstEnterPopupInfo: async () => {
        const res = (await apiGetFirstEnterPopupInfo({})) as {
            complete: boolean;
            jumpKey: string;
            onlineTime: number;
            registerTime: number;
        };
        set({ firstEnterPopupInfo: res });
        return !res.complete;
    },
    updateUserBaseInfo: async (data: UpdateUser) => {
        try {
            await apiUpdateUserBaseInfo(data);
            get().getUserBaseInfo();
            useBalanceStore.getState().requestBalance();

            Toast.show({
                msg: '保存成功',
                duration: 1000,
                pos: 'center',
            });
            Taro.navigateBack();
        } catch (err) {
            coronaWarnMessage('updateUserBaseInfo', {
                error: (err || {}).stack || err,
            });
            Toast.show({
                msg: '保存失败',
                duration: 1000,
                status: 'error',
                pos: 'center',
            });
        }
    },
    validateInput: (value: string) => {
        const regex = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/;
        const isLengthValid = value.length >= 2 && value.length <= 20;
        if (!regex.test(value)) {
            get().setEffectiveStatus(1);
        } else if (!isLengthValid) {
            get().setEffectiveStatus(2);
        } else {
            get().setEffectiveStatus(0);
        }
    },
    setEffectiveStatus: (status: 0 | 1 | 2) => set({ effectiveStatus: status }),
    reuestApi: () => {
        get().getUserBaseInfo();
        useBalanceStore.getState().requestBalance();
    },
}));

export default useSelfStore;
