import { create } from 'zustand';
import { Page } from '@tarojs/taro';
import useHomeStore, { TabItemConfig } from '@/components/Home/useHomeStore';
import { debounce } from '@/utils';

interface AppPathState {
    currentPath: string; // 当前的页面路径
    pathHistory: string[]; // 路由历史记录
    currentPages: Page[]; // 当前的页面栈
    setAppPages: (pages: Page[]) => void; // 设置当前页面栈
    setTabbarIndex: (tabIndex: number) => void; // 设置 tabbarIndex
}

/// 记录应用内当前页面路由状态
const useAppPathStore = create<AppPathState>((set, get) => {
    // 更新页面路由变化(防抖在边界情况下需要验证)
    const updatePathChange = debounce((currentPath) => {
        set({ currentPath });
    }, 500);

    return {
        currentPath: '',
        pathHistory: [],
        currentPages: [],
        setAppPages: (pages) => {
            if (pages.length < 1) {
                // 冷启时第一次回调为空数组
                return;
            }
            const { pathHistory } = get();
            if (pages.length === pathHistory.length) {
                // Taro 重复回调，不处理
                return;
            }
            // 页面路由变化，开始设置
            do {
                if (pathHistory.length < pages.length) {
                    // push
                    const currentPath = pages[pathHistory.length].route || '';
                    pathHistory.push(currentPath);
                } else if (pathHistory.length > pages.length) {
                    // pop
                    pathHistory.splice(pages.length);
                }
            } while (pathHistory.length !== pages.length);

            set({ currentPages: pages, pathHistory });
            updatePathChange(pathHistory[pathHistory.length - 1]);
        },
        setTabbarIndex: (tabIndex) => {
            const selectItem: TabItemConfig = useHomeStore
                .getState()
                .tabBarConfig?.list.find((_, index) => {
                    return tabIndex === index;
                });
            if (!selectItem) {
                return;
            }
            set({ pathHistory: [selectItem.pagePath] });
            updatePathChange(selectItem.pagePath);
        },
    };
});

export default useAppPathStore;
