import Env, { compareVersion } from '@music/mobile-env';
import Taro, { showToast } from '@tarojs/taro';
import COIN_ICON_GOLD from '@/assets/market/ic_market_coin.png';
import COIN_ICON_WORLD from '@/assets/market/ic_world_coin.png';
import { isIOS } from '@/utils';
import { rpc } from '@music/mat-base-h5';

export const getAppSource = () => {
    if (IS_MOYI) {
        return 'moyi';
    }
    return 'mirth';
};

export const getAppName = () => {
    if (IS_MOYI) {
        return 'moyi';
    }
    return 'mirth';
};

export const getSupportPay = () => {
    if (IS_MOYI) {
        return true;
    }

    if (!Env.isInMirthapp()) {
        return false;
    }

    if (isIOS) {
        return compareVersion(Env.getMirthVersion(), '2.10.4') >= 0;
    }

    return compareVersion(Env.getMirthVersion(), '2.10.2') >= 0;
};

export const getRechargeUrl = () => {
    if (IS_MOYI) {
        return 'moyi://nmy/rnpage/main?component=moyi_pay';
    }

    return 'mirth://nmt/page/recharge?payType=recharge_ai_coin';
};

export const getUserInfoRPCName = () => {
    if (IS_MOYI) {
        return 'moyi.userInfo';
    }
    return 'mirth.userInfo';
};

export const imageUploadChannel = () => {
    if (IS_MOYI) {
        return 'nos';
    }
    return 'oss';
};

export const getCoinType = () => {
    if (IS_MOYI) {
        return 'moyi_coin';
    }
    return 'mirth_ai_coin';
};

// 金币ICON
export const getCoinIcon = () => {
    if (IS_MOYI) {
        return COIN_ICON_GOLD;
    }

    return COIN_ICON_WORLD;
};

// 金币名称：金币、世界币
export const getCoinName = () => {
    if (IS_MOYI) {
        return '金币';
    }

    return '世界币';
};

// 首页是否展示返回箭头
// 只有心颜新版本不需要展示，其他情况都展示
export const showBackIcon = () => {
    // 心颜2.11.0及以上版本，都不展示
    if (Env.isInMirthapp() && compareVersion(Env.getMirthVersion(), '2.11.0') >= 0) {
        return false;
    }

    return true;
};

// 强制实名认证处罚
export const gotoRealNameAuth = () => {
    if (IS_MIRTH) {
        Taro.eventCenter.trigger('kShowRealNameModal');
        return;
    }
    showToast({
        title: '账号存在风险，请完成实名认证后重试',
        icon: 'none',
    });
};

/// banner 过滤
export const filterBanner = (banner: any) => {
    if (IS_MIRTH) {
        return banner;
    }
    return banner.filter((item: any) => item?.jumpLink !== 'st_inviteNew');
};

// 任务过滤
export const filterTask = (task: any) => {
    if (IS_MIRTH) {
        return task;
    }
    return task.filter((item: any) => item?.taskJumpUrl !== 'st_inviteNew');
};

export const needRegisterKeyboardRPC = () => {
    // 下面代码后续要删掉，2025/05/16上线之后，待客户端上线后监控下版本，然后删掉。
    if (IS_MOYI) {
        if (Env.isIos() && compareVersion(Env.getMoyiVersion(), '2.05.0') < 0) {
            return true;
        }
    }
    if (IS_MIRTH) {
        if (Env.isIos() && compareVersion(Env.getMirthVersion(), '2.11.4') < 0) {
            return true;
        }
    }
    return false;
};

export const harborLogger = (tag: string, info: string) => {
    if (IS_MIRTH) {
        if (compareVersion(Env.getMirthVersion(), '2.11.6') < 0) {
            return;
        }
    }
    rpc.caller('local.log.saveLocal', {
        tag,
        info,
    });
};
