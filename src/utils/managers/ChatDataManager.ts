import Taro from '@tarojs/taro';

export interface ChatData {
    image: boolean;
    text: string;
    imageInfo: string;
    timestamp: number;
}

export interface ImageInfo {
    selectImage: string;
    selectImageNosKey: string;
    generateContent: string;
    selectImageWidth: number;
    selectImageHeight: number;
}

export class ChatDataManager {
    private static instance: ChatDataManager;

    private data: Map<string, ChatData>;

    private constructor() {
        this.data = new Map();
    }

    public static getInstance(): ChatDataManager {
        if (!ChatDataManager.instance) {
            ChatDataManager.instance = new ChatDataManager();
        }
        return ChatDataManager.instance;
    }

    // 设置数据，确保数据格式符合 ChatData
    public setChatData(key: string, value: ChatData): void {
        if (!this.validateData(value)) {
            throw new Error('Invalid data format. Image must be boolean and text must be string.');
        }
        this.data.set(key, value);
        this.notifySubscribers();
    }

    // 获取数据
    public getChatData(key: string): ChatData | undefined {
        return this.data.get(key);
    }

    // 删除会话数据
    public removeChatData(key: string): void {
        if (this.data.has(key)) {
            this.data.delete(key);
            this.notifySubscribers();
        }
    }

    // 验证数据格式
    private validateData(data: any): data is ChatData {
        return (
            typeof data === 'object' &&
            data !== null &&
            typeof data.image === 'boolean' &&
            typeof data.text === 'string' &&
            typeof data.timestamp === 'number'
        );
    }

    // 通知订阅者
    private notifySubscribers(): void {
        Taro.eventCenter.trigger('ChatDataManagerContentChanged');
    }

    // 更新部分数据
    public updateData(key: string, partialData: Partial<ChatData>): void {
        const currentData = this.data.get(key);
        if (currentData) {
            const newData = {
                ...currentData,
                ...partialData,
            };
            this.setChatData(key, newData);
        }
    }

    // 清除所有数据
    public clearAll(): void {
        this.data.clear();
        this.subscribers.clear();
    }
}
