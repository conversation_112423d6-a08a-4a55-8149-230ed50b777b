import MessageFlow, { NotificationListener } from '@/hooks/message/MessageFlow';
import Taro, { getStorageSync, setStorageSync, removeStorage } from '@tarojs/taro';
import { SystemMessage } from '@/types/im';
import { getUserInfo } from '@/utils/rpc';

export default class UpgradeAlertManager implements NotificationListener {
    private static instance: UpgradeAlertManager;

    private key = 'UpgradeAlert_';

    private userId = '';

    private constructor() {
        getUserInfo((e) => {
            this.userId = e.uid;
        });
        MessageFlow.addNotificationListener(4305, this);
    }

    public static getInstance(): UpgradeAlertManager {
        if (!UpgradeAlertManager.instance) {
            UpgradeAlertManager.instance = new UpgradeAlertManager();
        }
        return UpgradeAlertManager.instance;
    }

    public onSystemMessage = (message: SystemMessage) => {
        const content = message.contentExt?.serverExt;
        this.intimacyUpgrade(content);
    };

    public getIntimacyUpgradeContent = (targetUserId: string) => {
        const key = this.makeKey(targetUserId);
        return getStorageSync(key);
    };

    public deleteIntimacyUpgradeContent = (targetUserId: string) => {
        const key = this.makeKey(targetUserId);
        removeStorage({ key });
    };

    private intimacyUpgrade = (data) => {
        const content = data.content;
        if (!content) {
            return;
        }
        const key = this.makeKey(content.targetUserId);
        setStorageSync(key, content);
        Taro.eventCenter.trigger('taro_event_upgrade_manager_intimacy_upgrade_start', data);
    };

    private makeKey = (targetUserId: string) => {
        return `${this.key + this.userId}_${targetUserId}`;
    };
}
