import { rpc } from '@music/mat-base-h5';
import Env, { compareVersion } from '@music/mobile-env';

// rpcName定义
// file.image含义为：调起系统相册进行上传
// file.randomAvatar含义为：打开系统头像选择页面
export default function Upload(
    options: any = { ratio: -1 },
    rpcName: 'file.randomAvatar' | 'file.image' = 'file.image'
) {
    let result: any;
    let uploadImageRpc = 'file.image';
    const isHigher = IS_MIRTH ? compareVersion(Env.getMirthVersion(), '2.1.0') >= 0 : false;

    if (isHigher) {
        uploadImageRpc = rpcName;
    }

    return new Promise((resolve, reject) => {
        function onFileUploadSuccess(imageData: any) {
            rpc.off.bind(rpc)('file.fileUploadSuccess', onFileUploadSuccess);

            resolve({ ...result, ...imageData });
        }
        rpc.on.bind(rpc)('file.fileUploadSuccess', onFileUploadSuccess);

        function onFileUploadFailure(e: any) {
            rpc.off.bind(rpc)('file.fileUploadFailure', onFileUploadFailure);

            reject(e);
        }
        rpc.on.bind(rpc)('file.fileUploadFailure', onFileUploadFailure);

        rpc.caller(
            uploadImageRpc,
            { needBeauty: true, ...options },
            (res) => {
                if (res.status === 'cancel') {
                    resolve(res);
                    return;
                }

                result = {
                    ...res,
                    url: res.url || res.remoteUrl,
                    nosKey: res.nosKey,
                };
            },
            (err) => {
                if (err.message === '图片不存在') {
                    resolve({
                        status: 'cancel',
                    });
                    return;
                }
                reject(err);
            }
        );
    });
}
