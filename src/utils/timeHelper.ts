export function isToday(timestamp: number): boolean {
    const date = new Date(timestamp);
    const today = new Date();
    return (
        date.getDate() === today.getDate() &&
        date.getMonth() === today.getMonth() &&
        date.getFullYear() === today.getFullYear()
    );
}

function isYesterday(timestamp: number): boolean {
    const date = new Date(timestamp);
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return (
        date.getDate() === yesterday.getDate() &&
        date.getMonth() === yesterday.getMonth() &&
        date.getFullYear() === yesterday.getFullYear()
    );
}

function isThisYear(timestamp: number): boolean {
    return new Date(timestamp).getFullYear() === new Date().getFullYear();
}

function timeString(timestamp: number): string {
    return new Date(timestamp).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
}

function monthDayString2(timestamp: number): string {
    return new Date(timestamp).toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
}

function yearMonthDayString2(timestamp: number): string {
    return new Date(timestamp).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
    });
}

export const transformTimeToString = (timestamp: number) => {
    if (isToday(timestamp)) {
        return timeString(timestamp);
    }
    if (isYesterday(timestamp)) {
        return '昨天';
    }
    if (isThisYear(timestamp)) {
        return monthDayString2(timestamp);
    }
    return yearMonthDayString2(timestamp);
};
