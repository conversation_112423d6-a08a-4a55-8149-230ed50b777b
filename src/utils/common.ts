import { compareVersion } from '@music/mobile-env';

export const getAppKey = () => {
    if (IS_ONLINE) {
        return '27dd21f3b487838371c1c1f0bf50537e';
    }

    return '933352c7567fa499879047414ec8f83d';
};

export const getKeyBoardType = (appVersion: string) => {
    const featureAppVersion = '2.10.1';

    if (!appVersion || compareVersion(appVersion, featureAppVersion) === 1) {
        return 2;
    }
    return 1;
};
