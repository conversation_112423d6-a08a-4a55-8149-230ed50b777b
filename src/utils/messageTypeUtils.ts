import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import { MsgDataType } from '@/types/im';

/**
 * 统一的消息类型判断工具
 */
const MessageTypeUtils = {
    /**
     * 判断是否为流式消息
     */
    isStreamingMessage: (msg: MsgDataType): boolean => {
        return !!(
            msg.streamConfig &&
            msg.streamConfig.status !==
                V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_NONE
        );
    },

    /**
     * 判断是否为流式文本消息
     */
    isStreamingTextMessage: (msg: MsgDataType): boolean => {
        return (
            msg.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_TEXT &&
            MessageTypeUtils.isStreamingMessage(msg)
        );
    },

    /**
     * 判断是否为流式音频文本消息
     */
    isStreamingAudioTextMessage: (msg: MsgDataType): boolean => {
        return (
            msg.contentExt?.content?.type === 'aigcCustomTextAudioMsg' &&
            MessageTypeUtils.isStreamingMessage(msg)
        );
    },

    /**
     * 获取流式消息状态
     */
    getStreamingStatus: (msg: MsgDataType): V2NIMConst.V2NIMMessageStreamStatus | null => {
        return msg.streamConfig?.status || null;
    },

    /**
     * 判断是否为占位消息
     */
    isPlaceholderMessage: (msg: MsgDataType): boolean => {
        return (
            msg.streamConfig?.status ===
            V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_PLACEHOLDER
        );
    },

    /**
     * 判断是否正在流式传输中
     */
    isStreamingInProgress: (msg: MsgDataType): boolean => {
        return (
            msg.streamConfig?.status ===
            V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_STREAMING
        );
    },

    /**
     * 判断流式是否已完成
     */
    isStreamingComplete: (msg: MsgDataType): boolean => {
        return (
            msg.streamConfig?.status ===
            V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_COMPLETE
        );
    },

    /**
     * 判断流式是否异常或取消
     */
    isStreamingError: (msg: MsgDataType): boolean => {
        const status = msg.streamConfig?.status;
        return (
            status === V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_EXCEPTION ||
            status === V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_CANCEL
        );
    },

    /**
     * 获取消息类型的显示名称
     */
    getMessageTypeName: (msg: MsgDataType): string => {
        if (MessageTypeUtils.isStreamingTextMessage(msg)) {
            return '流式文本消息';
        }
        if (MessageTypeUtils.isStreamingAudioTextMessage(msg)) {
            return '流式音频文本消息';
        }
        if (msg.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_TEXT) {
            return '文本消息';
        }
        if (msg.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_IMAGE) {
            return '图片消息';
        }
        if (msg.contentExt?.content?.type === 'aigcCustomTextAudioMsg') {
            return '音频文本消息';
        }
        return '其他消息';
    },
};

export default MessageTypeUtils;
