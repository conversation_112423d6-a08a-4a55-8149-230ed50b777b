import { Image } from '@music/mat-base-h5';

// 图片、视频等客户端文件缓存后缀
export const cacheSuffix = 'clientCache=true&maxAge=604800';

// 客户端缓存方式
export function getImage(url: string | undefined) {
    if (!url || url?.includes(cacheSuffix)) return url;
    const [urlPath, hash] = url.split('#');
    const connector = urlPath.includes('?') ? '&' : '?';
    const newUrl = `${urlPath}${connector}${cacheSuffix}`;
    return hash ? `${newUrl}#${hash}` : newUrl;
}

interface IOptimizeImage {
    src: string;
    width?: number;
    height?: number;
    raw?: boolean;
    type?: string;
    noCache?: boolean;
}

// cdn图片剪裁优化
/**
 * https://qa.igame.163.com/st/sociallive-h5/base-components/image
 * @param src 图片原始地址
 * @param width 图片剪裁宽度
 * @param height 图片剪裁高度
 * @param raw 是否直接用原图，默认false走优化
 * @param noCache 不走客户端缓存，默认false走缓存
 * @param rest 其他参数
 * @returns
 */
export function optimizeImage({
    src,
    width = 40,
    height = 40,
    raw = false,
    type = 'jpg',
    noCache = false,
    ...rest
}: IOptimizeImage) {
    if (!src) return src;

    const params = {
        src,
        width,
        height,
        type,
        ...(raw && { raw }),
        ...rest,
    };

    if (noCache) {
        return Image.getImageUrl(params);
    }

    return getImage(Image.getImageUrl(params));
}

export default {};
