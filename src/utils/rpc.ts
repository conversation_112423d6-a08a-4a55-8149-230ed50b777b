import { rpc } from '@music/mat-base-h5';
import { getUserInfoRPCName } from './appSourceAdapter';

export const openUrl = (url: string) => rpc.caller('navigator.openURL', { url });

export const openKey = (key: string, params?: Record<string, unknown>) => {
    return rpc.caller('navigator.openKey', { key, parameters: params, params });
};

export const goBackNative = () => {
    rpc.caller('navigator.pop');
};

export const saveToPhotosAlbum = (base64Data: string, success: (e) => void, fail: (e) => void) => {
    return rpc.caller(
        'image.saveToPhotosAlbum',
        { data: base64Data, dataType: 'base64' },
        success,
        fail
    );
};

export const getUserInfo = (success = () => {}, fail = () => {}) => {
    return rpc.caller(getUserInfoRPCName(), {}, success, fail);
};

export const saveStartIndex = (index: string) => {
    if (IS_MIRTH) {
        /// 只在心颜内生效
        rpc.caller(
            'aigcChat.saveStartIndex',
            { index },
            (e) => {
                console.log(e);
            },
            (e) => {
                console.log(e);
            }
        );
    }
};

export const onLoadFinish = () => {
    rpc.caller(
        'web.app.onLoadFinish',
        {},
        (e) => {
            console.log(e);
        },
        (e) => {
            console.log(e);
        }
    );
};

export const startRecharge = (product: Record<string, unknown>) => {
    return rpc.caller('recharge.start', product);
};

export const getAppType = (success: (e) => void) => {
    return rpc.caller('recharge.appType', {}, success);
};

export const pageDidAppear = (success: () => void) => {
    return rpc.caller('page.didAppear', {}, success);
};

export const pageDidDisAppear = (success: () => void) => {
    return rpc.caller('page.didDisappear', {}, success);
};

export const copy = (content: string) => {
    rpc.caller(
        'clipboard.setContent',
        { content },
        (e) => {
            console.log(e);
        },
        (e) => {
            console.log(e);
        }
    );
};

export const toast = (text: string) => {
    rpc.caller('toast.show', { text, position: 'center' });
};

export const isBigBrotherVersion = (success: (data: { success: boolean }) => void) => {
    rpc.caller('versionInfo.isBigBrotherVersion', {}, success);
};

export const mirthLogout = () => {
    rpc.caller('mirth.logout');
};

export const onPushJump = (
    success: (data: { url: string }) => void = () => undefined,
    fail: (e: unknown) => void = () => undefined
) => {
    return rpc.caller('push.onPushJump', {}, success, fail);
};
