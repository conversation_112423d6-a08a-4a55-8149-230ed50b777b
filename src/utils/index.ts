import { getSystemInfoSync } from '@tarojs/taro';
import Env, { compareVersion } from '@music/mobile-env';

export * from './is-type';
export * from './global-data';
export * from './common';

export const isIOS = Env.isIos();
export const isAndroid = Env.isAndroid();

/**
 * 防抖函数 - 基于 lodash 实现
 * @param func 要防抖的函数
 * @param wait 等待时间，默认300ms
 * @param options 配置选项
 */
export const debounce = <T extends (...args: any[]) => any>(
    func: T,
    wait = 300,
    options?: { leading?: boolean; trailing?: boolean; maxWait?: number }
): ((...args: Parameters<T>) => any) & { cancel: () => void; flush: () => any } => {
    let lastArgs: Parameters<T> | undefined;
    let lastThis: any;
    const maxWait = options?.maxWait;
    let result: any;
    let timerId: ReturnType<typeof setTimeout> | undefined;
    let lastCallTime: number | undefined;
    let lastInvokeTime = 0;
    const leading = !!options?.leading;
    const trailing = options?.trailing !== false;

    // 确保调用时有效
    if (typeof func !== 'function') {
        throw new TypeError('Expected a function');
    }

    const invokeFunc = (time: number) => {
        const args = lastArgs;
        const thisArg = lastThis;

        lastArgs = undefined;
        lastThis = undefined;
        lastInvokeTime = time;
        result = func.apply(thisArg, args as Parameters<T>);
        return result;
    };

    const startTimer = (pendingFunc: () => any, w: number) => {
        return setTimeout(pendingFunc, w);
    };

    const cancelTimer = (id: ReturnType<typeof setTimeout> | undefined) => {
        if (id !== undefined) {
            clearTimeout(id);
        }
    };

    const remainingWait = (time: number) => {
        const timeSinceLastCall = time - (lastCallTime as number);
        const timeSinceLastInvoke = time - lastInvokeTime;
        const timeWaiting = wait - timeSinceLastCall;

        return maxWait !== undefined
            ? Math.min(timeWaiting, maxWait - timeSinceLastInvoke)
            : timeWaiting;
    };

    const shouldInvoke = (time: number) => {
        const timeSinceLastCall = time - (lastCallTime as number);
        const timeSinceLastInvoke = time - lastInvokeTime;

        // 以下情况应该调用函数：
        // 1. 这是第一次调用
        // 2. 自上次调用后已经过了 wait 时间
        // 3. 系统时间被调整了（如系统休眠后）
        // 4. 如果设置了 maxWait，并且已经超过了最大等待时间
        return (
            lastCallTime === undefined ||
            timeSinceLastCall >= wait ||
            timeSinceLastCall < 0 ||
            (maxWait !== undefined && timeSinceLastInvoke >= maxWait)
        );
    };

    const trailingEdge = (time: number) => {
        timerId = undefined;

        // 只有在有参数且启用了尾部调用时才执行
        if (trailing && lastArgs) {
            return invokeFunc(time);
        }
        lastArgs = undefined;
        lastThis = undefined;
        return result;
    };

    const timerExpired = () => {
        const time = Date.now();
        if (shouldInvoke(time)) {
            return trailingEdge(time);
        }
        // 重新启动定时器
        timerId = startTimer(timerExpired, remainingWait(time));
        return true;
    };

    const leadingEdge = (time: number) => {
        // 重置最后调用时间
        lastInvokeTime = time;
        // 开始定时器以执行尾部调用
        timerId = startTimer(timerExpired, wait);
        // 如果启用了前置调用，则立即调用函数
        return leading ? invokeFunc(time) : result;
    };

    const cancel = () => {
        if (timerId !== undefined) {
            cancelTimer(timerId);
        }
        lastInvokeTime = 0;
        lastArgs = undefined;
        lastCallTime = undefined;
        lastThis = undefined;
        timerId = undefined;
    };

    const flush = () => {
        return timerId === undefined ? result : trailingEdge(Date.now());
    };

    const debounced = (...args: Parameters<T>) => {
        const time = Date.now();
        const isInvoking = shouldInvoke(time);

        lastArgs = args;
        lastThis = this;
        lastCallTime = time;

        if (isInvoking) {
            if (timerId === undefined) {
                return leadingEdge(lastCallTime);
            }
            if (maxWait !== undefined) {
                // 处理最大等待时间的情况
                timerId = startTimer(timerExpired, wait);
                return invokeFunc(lastCallTime);
            }
        }
        if (timerId === undefined) {
            timerId = startTimer(timerExpired, wait);
        }
        return result;
    };

    debounced.cancel = cancel;
    debounced.flush = flush;

    return debounced;
};

/**
 * 节流函数 - 基于 lodash 实现
 * @param func 要节流的函数
 * @param wait 等待时间，默认300ms
 * @param options 配置选项
 */
export const throttle = <T extends (...args: any[]) => any>(
    func: T,
    wait = 300,
    options?: { leading?: boolean; trailing?: boolean }
): ((...args: Parameters<T>) => any) & { cancel: () => void; flush: () => any } => {
    // 使用 debounce 实现 throttle，这是 lodash 的实现方式
    return debounce(func, wait, {
        leading: options?.leading !== false,
        trailing: options?.trailing !== false,
        maxWait: wait,
    });
};

// 强制升级版本控制
export const forceUpdateVersion = () => {
    if (Env.isInMirthapp()) {
        if (isIOS && compareVersion(Env.getMirthVersion(), '2.10.6') < 0) {
            return true;
        }
        if (isAndroid && compareVersion(Env.getMirthVersion(), '2.10.10') < 0) {
            return true;
        }
    }
    return false;
};

// 获取设备信息和设计稿配置
const systemInfo = getSystemInfoSync();
const designWidth = 375 / 2;

// rem转px的计算函数
export const remToPx = (remValue: number): number => {
    // 计算公式：(rem值 * 设备宽度) / (设计稿宽度/10)
    return (remValue * systemInfo.windowWidth) / (designWidth / 10);
};
