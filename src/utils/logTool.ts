import { isIOS, isAndroid } from '@/utils';
import fetch from '@/utils/fetch';
import { getAppName } from './appSourceAdapter';

export const getLogOSName = () => {
    if (isIOS) {
        return 'iphone';
    }
    if (isAndroid) {
        return 'android';
    }
    return 'h5';
};

export const eventTrackRequest = (data: any) => {
    fetch('/api/mirth/home/<USER>/log', {
        method: 'post',
        data,
    });
};

export const addClickLog = (spm: string, others: Record<string, any> = undefined) => {
    const appearTime = Date.now();
    const requestParams = {
        _spm: spm,
        action: '_ec',
        userid: '',
        log_time: appearTime,
        os: getLogOSName(),
        appname: getAppName(),
    };
    if (others) {
        Object.assign(requestParams, others);
    }

    const paramsString = JSON.stringify(requestParams);
    try {
        eventTrackRequest({
            log: paramsString,
        });
    } catch (e) {
        console.error('埋点失败', e);
    }
};
