import Taro, { getStorageSync, setStorageSync, removeStorageSync } from '@tarojs/taro';

export interface FetchParams {
    method?: 'POST' | 'GET' | 'post';
    data?: Record<string, any>;
}

const devHost = 'https://api-qa.mirth.netease.com';

export default function fetch<D = any>(path: string, params?: FetchParams) {
    const method = params?.method || 'POST';
    const MUSIC_U = getStorageSync('MUSIC_U');
    const MIDDLE_U = '435633324';

    let cookieStr = '';
    if (MIDDLE_U) {
        cookieStr += `MIDDLE_U=${MIDDLE_U};`;
    }

    if (MUSIC_U) {
        cookieStr += `MUSIC_U=${MUSIC_U};`;
    }

    const header: Record<string, string> = {
        cookie: cookieStr,
    };

    if (method.toUpperCase() === 'POST') {
        header['Content-Type'] = 'application/x-www-form-urlencoded';
    }

    return Taro.request({
        header,
        method,
        data: params?.data,
        url: `${devHost}${path}`,
    }).then((res) => {
        // MUSIC_U parse
        const resCookies = res.cookies;
        if (resCookies?.length) {
            resCookies.forEach((resCookie) => {
                if (resCookie.indexOf('MUSIC_U') >= 0) {
                    const setMusicU = resCookie.match(/MUSIC_U=(.*?);/)?.[1];
                    setStorageSync('MUSIC_U', setMusicU);
                }
            });
        }

        if (res.data?.code === 301) {
            removeStorageSync('MUSIC_U');
        }

        if (res.data?.code === 200) {
            return res.data?.data as D;
        }

        const error = new Error(res.data?.message || res.data?.debugInfo);
        (error as any).code = res.data.code;
        throw error;
    });
}
