import { coronaWarnMessage, fetch } from '@music/mat-base-h5';
import { isAndroid } from '@/utils';
import Env, { compareVersion } from '@music/mobile-env';
import { mirthLogout } from '@/utils/rpc';
import { gotoRealNameAuth } from '@/utils/appSourceAdapter';
import domAdapter from '@/utils/adapter/domAdapter';

const devHost = 'http://api-qa-boom.mirth.netease.com';

const useNativeRequest = ['/api/mirth/user/invite/bind'];

export default <T>(url: string, option: any): Promise<T> => {
    let host = API_HOST || devHost;
    let encrypt = IS_ONLINE;
    if (~url.indexOf('/api/feedback/weblog')) {
        host = API_LOG_HOST || devHost;
        encrypt = false;
    }

    let noBridgeConfig = true;
    let needsGuardianToken = false;
    const mirthVersion = Env.getMirthVersion();
    if (IS_MIRTH && isAndroid && compareVersion(mirthVersion, '2.10.8') < 0) {
        noBridgeConfig = false;
    }

    // 如果当前请求含有 useNativeRequest中某个链接，则走native请求
    for (let i = 0; i < useNativeRequest.length; i++) {
        if (url.includes(useNativeRequest[i])) {
            noBridgeConfig = false;
            needsGuardianToken = true;
            break;
        }
    }

    return fetch(url, {
        encrypt,
        host,
        needsGuardianToken,
        noBridge: noBridgeConfig,
        method: 'POST',
        ...option,
    }).catch((e: Error) => {
        if (IS_MIRTH && (e as any).code === 301) {
            const cookie = domAdapter.getCookie('MUSIC_U');
            mirthLogout();
            coronaWarnMessage('token 过期，请求重新登录', `MUSIC_U:${cookie}`);
        } else if (e.message === 'Load failed' || e.message === 'Failed to fetch') {
            e.message = '连接中断，请检查网络';
        } else if ((e as any).code === 20038) {
            gotoRealNameAuth();
        }
        return Promise.reject(e);
    });
};
