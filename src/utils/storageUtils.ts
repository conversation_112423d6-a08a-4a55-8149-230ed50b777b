import Taro, { removeStorageSync, getStorageInfoSync } from '@tarojs/taro';

export const deleteFavorilityStorage = (currentSessionPeriodId) => {
    const favoirabilityAnimationKeyPrefix = `kfavoirabilityAnimationKey_${currentSessionPeriodId}`;
    const allStorageKeys = getStorageInfoSync().keys;
    for (const key of allStorageKeys) {
        if (key.startsWith(favoirabilityAnimationKeyPrefix)) {
            removeStorageSync(key);
        }
    }
};
