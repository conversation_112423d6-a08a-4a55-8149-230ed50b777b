import Taro, { getCurrentInstance } from '@tarojs/taro';

interface ISwipeBack {
    edgeDistance?: number;
    blackList?: string[];
}

// 在 move 中设置一个定时器，避免部分手机 touchend 没有触发
let touchEndTimer: any;

function log(...args: any[]) {
    // console.info('[swipeBack]', ...args);
}

class SwipeBack {
    // 手势开关, 默认开启
    static enabled = true;

    // 当前页面
    currentPage: Element | null = null;

    // 边缘距离
    edgeDistance = 50;

    // 动画时长
    transitionTime = 300;

    blackList: string[] = [];

    status: 'start' | 'move' | 'end' = 'end';

    constructor({ edgeDistance, blackList }: ISwipeBack) {
        this.edgeDistance = edgeDistance || 50;
        this.blackList = blackList || [];

        this.initEvent();
    }

    // 初始化事件
    initEvent() {
        if (window.localStorage.getItem('swipeBackDisable')) {
            return;
        }

        const app = document.getElementById('app');
        this.setupSwipeBack(app);
    }

    setupSwipeBack(app: HTMLElement) {
        let startX = -1;
        let startTime = 0;
        let moveX = 0;
        let element: HTMLElement;

        let handleTouchEnd: () => void;

        const resetStart = () => {
            if (startX >= 0) {
                log('移动过程中，无法重置 startX');
                return;
            }

            startX = -1;
        };

        const handleTouchStart = (e: TouchEvent) => {
            log('touchstart');
            if (!SwipeBack.enabled) {
                log('swipeBack 已禁止');
                resetStart();
                return;
            }

            // 增加安全检查，避免 getCurrentInstance() 为空
            const instance = getCurrentInstance();
            if (!instance || !instance.page) {
                log('getCurrentInstance 返回异常，无法获取当前页面路径');
                resetStart();
                return;
            }

            let currentPath = instance.page?.path;
            currentPath = currentPath.split('?')[0].replace(/^\/?[^/]+\/?(.*)$/, '/$1');

            log('currentPath', currentPath);
            if (this.blackList.some((i) => currentPath === i)) {
                log('该页面命中侧滑黑名单');
                resetStart();
                return;
            }

            const pages = document.querySelectorAll('.taro_page_show');
            const elem = pages?.[pages.length - 1] as HTMLElement;

            if (!elem.classList.contains('taro_page_stationed')) {
                log('当前页面过渡动画中');
                resetStart();
                return;
            }

            // 只响应左侧边缘的触摸
            if (startX < 0 && e.touches[0].clientX <= this.edgeDistance) {
                log('swipeback 出发touchstart');

                startX = e.touches[0].clientX;
                startTime = Date.now();
            } else if (e.touches[0].clientX > this.edgeDistance) {
                log('touch 超出侧滑区域');
                resetStart();
            } else {
                log('移动过程中');
            }
        };

        const handleTouchMove = (e: TouchEvent) => {
            log('handleTouchMove');
            if (startX < 0) {
                return;
            }

            if (!element) {
                const pages = document.querySelectorAll('.taro_page_show');
                const elem = pages?.[pages.length - 1] as HTMLElement;

                if (!elem.classList.contains('taro_page_stationed')) {
                    return;
                }

                element = elem;

                log('swipeback element', element);

                const sibling = element?.previousElementSibling;

                if (sibling) {
                    (sibling as HTMLDivElement).style.display = 'block';
                }
            }

            e.stopPropagation();
            moveX = Math.max(0, e.touches[0].clientX - startX);

            element.style.setProperty('transform', `translate3d(${moveX}px, 0, 0) `, 'important');

            // 重置 timer
            clearTimeout(touchEndTimer);
            /**
             * 在 move 中设置一个定时器，避免部分手机 touchend 没有触发
             * 比如pixel 手机，从最侧面滑动，触发 android 的侧滑侧滑返回功能
             * 偶现只触发了 touchstart、touchend 事件
             * */
            touchEndTimer = setTimeout(() => {
                log('move 1s后未执行 end，主动触发');
                handleTouchEnd();
            }, 1000);
        };

        handleTouchEnd = () => {
            log('swpiteback end');

            clearTimeout(touchEndTimer);

            if (element && startX >= 0 && moveX > 0) {
                const endTime = Date.now();
                const velocity = moveX / (endTime - startTime);

                if (moveX > 100 || (velocity > 0.3 && moveX > this.edgeDistance)) {
                    element.style.setProperty(
                        'transition',
                        `transform ${this.transitionTime / 1000}s ease`,
                        'important'
                    );
                    element.style.setProperty('transform', 'translate3d(100%, 0, 0)', 'important');

                    Taro.navigateBack();
                } else {
                    element.style.setProperty(
                        'transition',
                        `transform ${this.transitionTime / 1000}s ease`,
                        'important'
                    );
                    element.style.setProperty('transform', 'translate3d(0, 0, 0)', 'important');

                    // 重置状态
                    setTimeout(
                        (el) => {
                            log('swipeback 执行timeout');
                            el.style.setProperty('transition', '');
                            el.style.setProperty('transform', '');

                            const sibling = el.previousElementSibling;

                            if (sibling && element !== el) {
                                (sibling as HTMLDivElement).style.display = 'none';
                            }
                        },
                        this.transitionTime,
                        element
                    );
                }
            }
            startX = -1;
            moveX = 0;
            element = null;
        };

        app.addEventListener('touchstart', handleTouchStart, { passive: true });
        app.addEventListener('touchmove', handleTouchMove, { passive: false });
        document.addEventListener('touchend', handleTouchEnd);
    }
}

(window as any).SwipeBack = SwipeBack;

export default SwipeBack;
