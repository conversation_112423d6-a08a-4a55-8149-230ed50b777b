/**
 * env适配器 - 用于处理H5和小程序环境差异
 */
class EnvAdapter {
    /**
     * 获取当前运行环境
     * @returns 'h5' | 'weapp'
     */
    static getEnv(): 'h5' | 'weapp' {
        return process.env.TARO_ENV as 'h5' | 'weapp';
    }

    /**
     * 判断是否为H5环境
     */
    static isH5(): boolean {
        return this.getEnv() === 'h5';
    }

    /**
     * 判断是否为微信小程序环境
     */
    static isWeapp(): boolean {
        return this.getEnv() === 'weapp';
    }
}

export default EnvAdapter;
