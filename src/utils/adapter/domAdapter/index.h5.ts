import type { IDomAdapter } from '.';

/**
 * H5环境下的DOM适配器实现
 */
class H5DomAdapter implements IDomAdapter {
    /**
     * 获取cookie
     * @param name cookie名称
     * @returns cookie值
     */
    getCookie = (name: string): string | null => {
        const cookies = document.cookie.split('; ');
        const cookie = cookies?.find((c) => c.startsWith(`${name}=`));
        return cookie ? cookie.split('=')[1] : null;
    };

    /**
     * 设置CSS样式属性
     */
    setCSSProperty = (property: string, value: string): void => {
        document.documentElement.style.setProperty(property, value);
    };

    /**
     * 获取滚动位置
     */
    getScrollTop = (): number => {
        return document.documentElement.scrollTop || document.body.scrollTop;
    };

    /**
     * 设置滚动位置
     */
    setScrollTop = (value: number): void => {
        document.documentElement.scrollTop = value;
        document.body.scrollTop = value; // 兼容 Safari
    };

    /**
     * 使元素失去焦点
     */
    blurActiveElement = (): void => {
        if (document && document.activeElement) {
            (document.activeElement as HTMLElement).blur();
        }
    };

    /**
     * 重新加载页面
     */
    reloadPage = (): void => {
        window.location.reload();
    };
}

export type { IDomAdapter };

const adapter: IDomAdapter = new H5DomAdapter();

export default adapter;
