/**
 * DOM适配器接口 - 定义跨平台通用的DOM操作方法
 */
export interface IDomAdapter {
    /**
     * 获取cookie
     * @param name cookie名称
     * @returns cookie值
     */
    getCookie(name: string): string | null;

    /**
     * 设置CSS样式属性
     */
    setCSSProperty(property: string, value: string): void;

    /**
     * 获取滚动位置
     */
    getScrollTop(): number;

    /**
     * 设置滚动位置
     */
    setScrollTop(value: number): void;

    /**
     * 使元素失去焦点
     */
    blurActiveElement(): void;

    /**
     * 重新加载页面
     */
    reloadPage(): void;
}

// 这里增加一次导出是为了在import引入的地方能让ts识别到定义存在
declare const adapter: IDomAdapter;
export default adapter;
