import Taro, {
    hideKeyboard,
    getStorageSync,
    setStorageSync,
    createSelectorQuery,
    pageScrollTo,
    getCurrentPages,
} from '@tarojs/taro';
import { IDomAdapter } from './index.d';

/**
 * 微信小程序环境下的DOM适配器实现
 */
class WeappDomAdapter implements IDomAdapter {
    /**
     * 获取cookie
     * @param name cookie名称
     * @returns cookie值
     */
    getCookie = (name: string): string | null => {
        const cookieRes = getStorageSync('cookies') || [];
        const targetCookie = cookieRes.find((c: string) => c.indexOf(`${name}=`) !== -1);
        return targetCookie ? targetCookie.split('=')[1].split(';')[0] : null;
    };

    /**
     * 设置CSS样式属性
     */
    setCSSProperty = (property: string, value: string): void => {
        // 小程序中没有直接对应的API，如有必要可以存储到全局状态中
        setStorageSync('cssProperties', {
            ...(getStorageSync('cssProperties') || {}),
            [property]: value,
        });
    };

    /**
     * 获取滚动位置
     */
    getScrollTop = (): number => {
        // 小程序中可以通过createSelectorQuery获取元素信息
        let scrollTop = 0;
        try {
            const query = createSelectorQuery();
            query
                .selectViewport()
                .scrollOffset((res) => {
                    scrollTop = res.scrollTop;
                })
                .exec();
        } catch (error) {
            console.error('获取滚动位置失败', error);
        }
        return scrollTop;
    };

    /**
     * 设置滚动位置
     */
    setScrollTop = (value: number): void => {
        try {
            pageScrollTo({
                scrollTop: value,
                duration: 0,
            });
        } catch (error) {
            console.error('设置滚动位置失败', error);
        }
    };

    /**
     * 使元素失去焦点
     */
    blurActiveElement = (): void => {
        // 在小程序中，可以使用hideKeyboard来实现输入框失焦
        hideKeyboard();
    };

    /**
     * 重新加载页面
     */
    reloadPage = (): void => {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        Taro.redirectTo({
            url: `/${currentPage.route}`,
        });
    };
}

export type { IDomAdapter };

const adapter: IDomAdapter = new WeappDomAdapter();

export default adapter;
