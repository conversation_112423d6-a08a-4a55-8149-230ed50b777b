import Taro, { getCurrentPages } from '@tarojs/taro';
import type { IWinAdapter } from '.';

/**
 * H5环境下的DOM适配器实现
 */
class WeappWinAdapter implements IWinAdapter {
    /**
     * reload页面
     */
    reloadPage = (): void => {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        Taro.redirectTo({
            url: `/${currentPage.route}`,
        });
    };
}

export type { IWinAdapter };

const adapter: IWinAdapter = new WeappWinAdapter();

export default adapter;
