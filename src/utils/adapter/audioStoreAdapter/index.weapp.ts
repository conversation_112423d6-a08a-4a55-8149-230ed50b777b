import { create } from 'zustand';
import { createInnerAudioContext, InnerAudioContext } from '@tarojs/taro';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { AudioState } from '@/utils/adapter/audioStoreAdapter/AudioState';

interface InnerAudioContextState extends AudioState {
    audioContext: InnerAudioContext | null;
    initAudio: () => void;
}

export const AudioStore = () => {
    return create<InnerAudioContextState>((set, get) => ({
        audioContext: null,
        isPlaying: false,
        currentSrc: '',
        volume: 0.7,
        pauseByUser: false,

        initAudio: () => {
            const audioContext = createInnerAudioContext();
            audioContext.loop = true;
            audioContext.onPlay(() => {
                set({ isPlaying: true });
            });

            audioContext.onPause(() => {
                set({ isPlaying: false });
            });

            audioContext.onStop(() => {
                set({ isPlaying: false });
            });

            audioContext.onEnded(() => {
                set({ isPlaying: false });
            });

            audioContext.onError(() => {
                set({ isPlaying: false });
            });

            set({ audioContext });
        },

        playMusic: async (src) => {
            const { currentSrc } = get();
            let { audioContext } = get();
            if (!audioContext) {
                get().initAudio();
                audioContext = get().audioContext;
            }

            if (currentSrc !== src) {
                audioContext?.stop();
                audioContext.src = src;
                set({ currentSrc: src });
            }

            try {
                audioContext.volume = get().volume;
                audioContext?.play();
            } catch (error) {
                coronaWarnMessage('audioContext.play() error', error);
            }
            set({ isPlaying: true });
        },

        pauseMusic: (byUser?: boolean) => {
            const { audioContext } = get();
            if (audioContext) {
                audioContext.pause();
                set({ isPlaying: false, pauseByUser: byUser });
            }
        },

        resumeMusic: () => {
            const { audioContext } = get();
            if (audioContext) {
                try {
                    audioContext.volume = get().volume;
                    audioContext.play();
                } catch (error) {
                    coronaWarnMessage('audioContext.play() error', error);
                }
                set({ isPlaying: true, pauseByUser: false });
            }
        },

        stopMusic: async () => {
            const { audioContext } = get();
            if (audioContext) {
                audioContext.stop();
                try {
                    audioContext.destroy();
                } catch (error) {
                    coronaWarnMessage('audioContext.destroy() error', error);
                }
            }
        },

        setVolume: (volume) => {
            set({ volume });
            const { audioContext } = get();
            if (audioContext) {
                audioContext.volume = volume;
            }
        },
    }));
};

export default AudioStore;
