import { AudioState } from '@/utils/adapter/audioStoreAdapter/AudioState';
import { StoreApi, UseBoundStore } from 'zustand';
import audioStoreAdapterH5 from './index.h5';
import audioStoreAdapterWeapp from './index.weapp';

const audioStoreAdapter: () => UseBoundStore<StoreApi<AudioState>> =
    process.env.TARO_ENV === 'h5' ? audioStoreAdapterH5 : audioStoreAdapterWeapp;
export default audioStoreAdapter;
