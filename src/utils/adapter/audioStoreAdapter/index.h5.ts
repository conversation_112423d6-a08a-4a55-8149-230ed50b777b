/* eslint-disable no-console */
import { create } from 'zustand';
import { AudioState } from '@/utils/adapter/audioStoreAdapter/AudioState';

interface AudioContextState extends AudioState {
    audioContext: AudioContext | null;
    audioSource: AudioBufferSourceNode | null;
    gainNode: GainNode | null;
    audioBuffer: AudioBuffer | null;

    initAudio: () => void;
    loadAudio: (src: string) => Promise<void>;
}

export const AudioStore = () => {
    return create<AudioContextState>((set, get) => ({
        audioContext: null,
        audioSource: null,
        gainNode: null,
        isPlaying: false,
        currentSrc: '',
        volume: 0.7,
        pauseByUser: false,
        audioBuffer: null,

        initAudio: () => {
            // 创建音频上下文
            const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
            const audioContext = new AudioContext();

            // 创建增益节点用于控制音量
            const gainNode = audioContext.createGain();
            gainNode.gain.value = get().volume;
            gainNode.connect(audioContext.destination);

            // 关键修复：确保 iOS AudioContext 解锁
            if (audioContext.state === 'suspended') {
                // 设置触摸事件解锁音频
                const unlockAudio = () => {
                    audioContext.resume().then(() => {
                        console.log('AudioContext 已解锁');
                        document.body.removeEventListener('touchstart', unlockAudio);
                        document.body.removeEventListener('touchend', unlockAudio);
                        document.body.removeEventListener('click', unlockAudio);
                    });
                };

                document.body.addEventListener('touchstart', unlockAudio, false);
                document.body.addEventListener('touchend', unlockAudio, false);
                document.body.addEventListener('click', unlockAudio, false);
            }

            set({ audioContext, gainNode });
        },

        loadAudio: async (src) => {
            const { audioContext } = get();
            if (!audioContext) {
                get().initAudio();
            }

            try {
                const response = await fetch(src);
                const arrayBuffer = await response.arrayBuffer();
                const audioBuffer = await get().audioContext!.decodeAudioData(arrayBuffer);
                set({ audioBuffer, currentSrc: src });
                return await Promise.resolve();
            } catch (error) {
                console.error('加载音频失败:', error);
                return Promise.reject(error);
            }
        },

        playMusic: async (src) => {
            const { audioBuffer, currentSrc } = get();
            let { audioContext, gainNode } = get();

            if (!audioContext) {
                get().initAudio();
                audioContext = get().audioContext!;
                gainNode = get().gainNode!;
            }

            // 关键修复：确保 AudioContext 处于运行状态
            if (audioContext.state === 'suspended') {
                try {
                    await audioContext.resume();
                } catch (e) {
                    console.error('解锁音频上下文失败:', e);
                }
            }

            // 如果提供了新的音频源或尚未加载音频
            if ((src && src !== currentSrc) || !audioBuffer) {
                await get().loadAudio(src || currentSrc);
            }

            // 停止当前播放的音频
            if (get().audioSource) {
                get().stopMusic();
            }

            // 关键修复：在 iOS 设备上播放一个短暂的静音音频来激活扬声器
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            if (isIOS) {
                console.log('isIOS', isIOS);
                try {
                    const silentAudio = new Audio(
                        'data:audio/mp3;base64,//tAxAAAAAAAAAAAAAAAAAAAAAAASW5mbwAAAA8AAAAFAAAESAAzMzMzMzMzMzMzMzMzMzMzMzMzZmZmZmZmZmZmZmZmZmZmZmZmZmaZmZmZmZmZmZmZmZmZmZmZmZmZmczMzMzMzMzMzMzMzMzMzMzMzMzM//////////////////////////8AAAA5TEFNRTMuMTAwAZYAAAAAAAAAABQ4JAMGQgAAOAAABEhNIZS0AAAAAAD/+0DEAAPH3Yz0AAR8CPqyIEABp6AxjG/4x/XiInE4lfQDFwIIRE+uBgZoW4RL0OLMDFn6E5v+/u5ehf76bu7/6bu5+gAiIQGAABQIUJ0QolFghEn/9PhZQpcUTpXMjo0OGzRCZXyKxoIQzB2KhCtGobpT9TRVj/3Pmfp+f8X7Pu1B04sTnc3s0XhOlXoGVCMNo9X//9/r6a10TZEY5DsxqvO7mO5qFvpFCmKIjhpSItGsUYcRO//7QsQRgEiljQIAgLFJAbIhNBCa+JmorCbOi5q9nVd2dKnusTMQg4MFUlD6DQ4OFijwGAijRMfLbHG4nLVTjydyPlJTj8pfPflf9/5GD950A5e+jsrmNZSjSirjs1R7hnkia8vr//l/7Nb+crvr9Ok5ZJOylUKRxf/P9Zn0j2P4pJYXyKkeuy5wUYtdmOu6uobEtFqhIJViLEKIjGxchGev/L3Y0O3bwrIOszTBAZ7Ih28EUaSOZf/7QsQfg8fpjQIADN0JHbGgQBAZ8T//y//t/7d/2+f5m7MdCeo/9tdkMtGLbt1tqnabRroO1Qfvh20yEbei8nfDXP7btW7f9/uO9tbe5IvHQbLlxpf3DkAk0ojYcv///5/u3/7PTfGjPEPUvt5D6f+/3Lea4lz4tc4TnM/mFPrmalWbboeNiNyeyr+vufttZuvrVrt/WYv3T74JFo8qEDiJqJrmDTs///v99xDku2xG02jjunrICP/7QsQtA8kpkQAAgNMA/7FgQAGnobgfghgqA+uXwWQ3XFmGimSbe2X3ksY//KzK1a2k6cnNWOPJnPWUsYbKqkh8RJzrVf///P///////4vyhLKHLrCb5nIrYIUss4cthigL1lQ1wwNAc6C1pf1TIKRSkt+a//z+yLVcwlXKSqeSuCVQFLng2h4AFAFgTkH+Z/8jTX/zr//zsJV/5f//5UX/0ZNCNCCaf5lTCTRkaEdhNP//n/KUjf/7QsQ5AEhdiwAAjN7I6jGddBCO+WGTQ1mXrYatSAgaykxBTUUzLjEwMKqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqg=='
                    );
                    silentAudio.loop = true;
                    await silentAudio.play();
                } catch (e) {
                    console.warn('激活扬声器失败:', e);
                }
            }

            // 创建新的音频源
            const audioSource = audioContext.createBufferSource();
            audioSource.buffer = get().audioBuffer!;
            audioSource.loop = true;

            // 连接到增益节点
            audioSource.connect(gainNode!);

            // 开始播放
            audioSource.start(0);

            // 关键修复：使用更可靠的方式设置音量
            gainNode!.gain.setValueAtTime(get().volume, audioContext.currentTime);
            console.log('音频已开始播放，音量:', get().volume);

            set({ audioSource, isPlaying: true });
        },

        pauseMusic: (byUser?: boolean) => {
            const { audioContext } = get();
            if (audioContext && audioContext.state === 'running') {
                audioContext.suspend();
                set({ isPlaying: false, pauseByUser: byUser });
            }
        },

        resumeMusic: () => {
            const { audioContext } = get();
            if (audioContext && audioContext.state === 'suspended') {
                audioContext.resume();
                set({ isPlaying: true, pauseByUser: false });
            }
        },

        stopMusic: () => {
            const { audioSource } = get();
            if (audioSource) {
                try {
                    audioSource.stop();
                } catch (e) {
                    // 忽略已停止的音频的错误
                }
                set({ audioSource: null, isPlaying: false });
            }
        },

        setVolume: (volume) => {
            const { gainNode, audioContext } = get();
            if (gainNode && audioContext) {
                // 关键修复：使用更可靠的方法设置音量
                gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
            }
            set({ volume });
        },
    }));
};

export default AudioStore;
