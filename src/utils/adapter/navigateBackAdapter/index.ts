import Taro, { getCurrentPages } from '@tarojs/taro';

// 保存原始的 navigateBack 方法
const originalNavigateBack = Taro.navigateBack;

// 重写 navigateBack 方法
Taro.navigateBack = function (options = {}) {
    // 在返回前，设置上一个页面为可见
    const pages = getCurrentPages();
    if (pages.length > 1) {
        const prevPage = pages[pages.length - 2];

        // H5 环境下处理
        if (process.env.TARO_ENV === 'h5') {
            // 获取所有页面元素
            const pageElements = document.querySelectorAll('.taro_page_show'); // ALLOW document
            const elem = pageElements?.[pageElements.length - 1] as HTMLElement;

            if (elem.classList.contains('taro_page_stationed')) {
                const sibling = elem?.previousElementSibling;

                if (sibling) {
                    setTimeout(() => {
                        (sibling as HTMLDivElement).style.display = 'block';
                    }, 0);
                }
            }
        }

        // 小程序环境下，可以通过页面实例处理
        if (process.env.TARO_ENV === 'weapp' && prevPage) {
            // 小程序暂时不处理
        }
    }

    // 调用原始的 navigateBack 方法
    return originalNavigateBack.call(this, options);
};

export default function setupNavigateBackInterceptor() {
    // 初始化拦截器，可以在 app.tsx 中调用
}
