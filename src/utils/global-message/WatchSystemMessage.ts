/**
 * @description: 全局监听系统消息
 * 系统消息的处理必须使用懒加载的方式去分发，按需加载
 */
import MessageFlow, { NotificationListener } from '@/hooks/message/MessageFlow';

export const MESSAGE_TYPE = {
    PACKAGE_RED_NOTICE: 5000, // 背包红点通知
};

interface SystemMessageHandler extends NotificationListener {
    code: number;
}

const PackageRedHandler: SystemMessageHandler = {
    code: MESSAGE_TYPE.PACKAGE_RED_NOTICE,
    onSystemMessage: (message) => {
        import('@/pages/chat/components/Footer/redDotStore').then(({ onPackageRedNotify }) => {
            onPackageRedNotify(message);
        });
    },
};

const MessageHandlers: SystemMessageHandler[] = [PackageRedHandler];

const watchSystemMessage = () => {
    MessageHandlers.forEach((handler) => {
        MessageFlow.addNotificationListener(handler.code, handler);
    });
};

export default watchSystemMessage;
