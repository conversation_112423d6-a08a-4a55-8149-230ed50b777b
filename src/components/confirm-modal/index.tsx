import { CreateModalProps } from '@/components/dialog';
import { Text, View } from '@tarojs/components';
import React, { CSSProperties, useCallback } from 'react';
import './index.scss';

export interface ConfirmProps {
    title: string;
    info: string;
    confirmText?: string;
    confirmStyle?: CSSProperties;
    cancelText?: string;
    cancelStyle?: CSSProperties;
    onCancel?: () => void;
    onConfirm: () => void;
    closeOnClickOverlay?: boolean;
}

type ConfirmModalModalProps = ConfirmProps & {
    dismiss: () => void;
};

const ConfirmModal = ({
    title,
    info,
    onCancel,
    onConfirm,
    confirmText = '确定',
    cancelText = '取消',
    confirmStyle,
    cancelStyle,
    closeOnClickOverlay,
    dismiss,
}: ConfirmModalModalProps) => {
    // console.log('ConfirmModal-->render');

    const onClickBg = useCallback(() => {
        if (closeOnClickOverlay) {
            dismiss?.();
        }
    }, [dismiss, closeOnClickOverlay]);

    const onClickCancel = useCallback(() => {
        dismiss?.();
        onCancel?.();
    }, [dismiss, onCancel]);

    const onClickConfirm = useCallback(() => {
        dismiss?.();
        onConfirm?.();
    }, [dismiss, onConfirm]);

    return (
        <View className="confirm-modal" onClick={onClickBg}>
            <View className="modal-content">
                <Text className="text-title">{title}</Text>
                {info && <Text className="text-content">{info}</Text>}
                <View className="btn-group">
                    <View className="cancel" style={cancelStyle} onClick={onClickCancel}>
                        {cancelText}
                    </View>
                    <View className="confirm" style={confirmStyle} onClick={onClickConfirm}>
                        {confirmText}
                    </View>
                </View>
            </View>
        </View>
    );
};

export const ConfirmModalProvider: CreateModalProps = {
    type: 'modal',
    isModal: true,
    render(dismiss: () => void, confirmProps: ConfirmModalModalProps) {
        return <ConfirmModal {...confirmProps} dismiss={dismiss} />;
    },
};

export default React.memo(ConfirmModal);
