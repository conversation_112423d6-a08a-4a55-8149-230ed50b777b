.fullscreen-alert-modal {
    display: flex;
    flex-direction: column;
    width: 100%;

    .container {
        position: relative;
        display: flex;
        flex-direction: column;
        background-color: white;

        &--h {
            border-radius: 16px;
            margin-left: 30px;
            margin-right: 30px;
        }

        &--v {
            border-radius: 16px 16px 0 0;
            margin-left: 0;
            margin-right: 0;
        }

        .close {
            width: 46px;
            height: 46px;
            position: absolute;
            padding-right: 20px;
            padding-top: 20px;
            top: 0;
            right: 0;
        }

        .title-space {
            &--h {
                margin-top: 30px;
            }

            &--v {
                margin-top: 40px;
            }
        }

        .title {
            line-height: 21.6px;
            text-align: center;
            color: black;
            font-size: 18px;
            font-weight: 600;
            flex-shrink: 1;
            flex-wrap: wrap;
        }

        .desc {
            font-size: 13px;
            font-weight: normal;
            line-height: 120%;
            text-align: center;
            letter-spacing: 0;
            color: #000;
            margin-left: 19px;
            margin-right: 19px;
            opacity: 0.4;
            margin-top: 13px;
        }

        .space {
            height: 20px;
        }

        .btn-group {
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-top: 10px;

            &--h {
                flex-direction: row;
                padding-bottom: 30px;
                margin-left: 19px;
                margin-right: 19px;
            }

            &--v {
                flex-direction: column;
                padding-bottom: 40px;
                margin-left: 19px;
                margin-right: 19px;
            }

            .left {
                background: rgba(191, 191, 191, 1);
                border-radius: 128px;
                text-align: center;
                color: white;
                margin-top: 20px;

                &--bg-reversal {
                    background: rgba(255, 104, 158, 1);
                }

                &--h {
                    margin-left: 10.5px;
                    margin-right: 10.5px;
                    width: 116px;
                    height: 48px;
                    line-height: 48px;
                }

                &--v {
                    margin-left: 0;
                    margin-right: 0;
                    width: 337px;
                    height: 54px;
                    line-height: 54px;
                }
            }

            .right {
                background: rgba(255, 104, 158, 1);
                text-align: center;
                justify-content: center;
                border-radius: 128px;
                margin-top: 20px;
                color: white;

                &--bg-reversal {
                    background: rgba(191, 191, 191, 1);
                }

                &--h {
                    width: 116px;
                    height: 48px;
                    line-height: 48px;
                    margin-left: 10.5px;
                    margin-right: 10.5px;
                }

                &--v {
                    width: 337px;
                    height: 48px;
                    margin-left: 0;
                    margin-right: 0;
                    line-height: 54px;
                }
            }
        }
    }
}
