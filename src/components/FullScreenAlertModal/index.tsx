/* eslint-disable react/prop-types */
// eslint-disable-next-line max-classes-per-file
import React, { useRef } from 'react';
import FullscreenModal, { Props, RefObject } from '@/components/FullScreenModal';
import { View, Image, ITouchEvent } from '@tarojs/components';
import classNames from 'classnames';
import { alertFuncHStyle, alertFuncVStyle, IFuncStyle } from '../AlertModal/const';
import './index.scss';

interface AlertProps extends Props {
    /**
     * 白底可以使用 @/assets/alert/alert_close.png~
     */
    closeImg?: string;
    title?: string;
    desc?: string;
    // 底部功能区~
    leftFuncTxt?: string;
    rightFuncTxt?: string;
    onLeftFuncClick?: () => void;
    onRightFuncClick?: () => void;
    funcStyle?: IFuncStyle;
    bgReversal?: boolean;
}

const FullScreenAlertModal = React.forwardRef<RefObject, AlertProps>(
    ({
        visible,
        children,
        onClose,
        id,
        className,
        closeImg,
        title,
        desc,
        leftFuncTxt,
        rightFuncTxt,
        onLeftFuncClick,
        onRightFuncClick,
        fromBottom,
        funcStyle = fromBottom ? alertFuncVStyle : alertFuncHStyle,
        forceOpen,
        bgReversal = false,
    }) => {
        const modalRef = useRef<RefObject>(null);
        const handleLeftClick = (e: ITouchEvent) => {
            e.stopPropagation();
            modalRef.current.onModalClose();
            onLeftFuncClick?.();
        };

        const handleRightClick = (e: ITouchEvent) => {
            e.stopPropagation();
            modalRef.current.onModalClose();
            onRightFuncClick?.();
        };

        const handleBgClick = (e: ITouchEvent) => {
            e.stopPropagation();
        };

        const handleCloseClick = (e: ITouchEvent) => {
            e.stopPropagation();
            modalRef.current.onModalClose();
        };

        return (
            <FullscreenModal
                visible={visible}
                onClose={onClose}
                id={id}
                forceOpen={forceOpen}
                className={className}
                fromBottom={fromBottom}
                ref={modalRef}>
                <View className="fullscreen-alert-modal">
                    <View
                        className={classNames('container', { [funcStyle.container]: true })}
                        onClick={handleBgClick}>
                        {children}
                        {title && (
                            <View
                                className={classNames('title-space', {
                                    [funcStyle.titleSpace]: true,
                                })}
                            />
                        )}
                        {title &&
                            title.split('\n').map((line) => <View className="title">{line}</View>)}
                        {desc &&
                            desc.split('\n').map((line) => <View className="desc">{line}</View>)}
                        {fromBottom && <View className="space" />}
                        <View
                            className={classNames('btn-group', {
                                [funcStyle.funcContainer]: true,
                            })}>
                            {leftFuncTxt && (
                                <View
                                    className={classNames('left', {
                                        [funcStyle.funcLeft]: true,
                                        'left--bg-reversal': bgReversal,
                                    })}
                                    onClick={handleLeftClick}>
                                    {leftFuncTxt}
                                </View>
                            )}
                            {rightFuncTxt && (
                                <View
                                    className={classNames('right', {
                                        [funcStyle.funcRight]: true,
                                        'right--bg-reversal': bgReversal,
                                    })}
                                    onClick={handleRightClick}>
                                    {rightFuncTxt}
                                </View>
                            )}
                        </View>
                        {closeImg && (
                            <Image src={closeImg} className="close" onClick={handleCloseClick} />
                        )}
                        <View />
                    </View>
                </View>
            </FullscreenModal>
        );
    }
);

export default React.memo(FullScreenAlertModal);
