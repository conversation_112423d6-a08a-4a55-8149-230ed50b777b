.youth-mode-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    box-sizing: border-box;
    background-color: white;
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
    overflow: hidden;

    &.leaving {
        animation: slideDown 0.3s ease-in forwards;
    }

    &.entering {
        animation: slideUp 0.3s ease-out forwards;
    }

    .top-icon {
        width: 100%;
        height: auto;
        display: block;
    }

    .title {
        margin-top: 10px;
        font-size: 18px;
        font-weight: 600;
        color: #1f201d;
    }

    .desc {
        margin-top: 16px;
        margin-left: 20px;
        margin-right: 20px;
        font-size: 16px;
        font-weight: 400;
        text-align: center;
        color: rgba(31, 32, 29, 0.7);
    }

    .button-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 0 20px;
        margin-top: 29px;
        margin-bottom: calc(30px + env(safe-area-inset-bottom, 0));
        z-index: 3;

        .modal-button {
            width: 100%;
            height: 50px;
            text-align: center;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            line-height: 50px;

            &.confirm-button {
                background-color: #ff689e;
                color: white;
                margin-bottom: 15px;
            }

            &.cancel-button {
                background-color: #fff;
                color: #ff689e;
                border: 1px solid #ff689e;
            }
        }
    }
}
