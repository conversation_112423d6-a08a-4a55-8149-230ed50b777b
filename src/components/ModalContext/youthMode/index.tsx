import React, { useCallback, useEffect } from 'react';
import { setStorage } from '@tarojs/taro';
import { View, Text, Image } from '@tarojs/components';
import FullscreenModal from '@/components/FullScreenModal';
import { openKey } from '@/utils/rpc';
import { uploadClientEvent } from '@/service/modalApi';
import { coronaWarnMessage } from '@music/mat-base-h5';

import TOP_ICON from '@/assets/modal-context/youthMode/youth_top_icon.png';

import './index.scss';

interface IModalSign {
    show: boolean;
    onClose: () => void;
}

const YouthModeModal = ({ show, onClose }: IModalSign) => {
    useEffect(() => {
        setStorage({ key: 'teen_mode_show_date', data: new Date().getTime() });
    }, []);

    const openYouthMode = useCallback(() => {
        onClose();
        openKey('st_teen_open');
    }, [onClose]);

    const closeAction = useCallback(() => {
        try {
            uploadClientEvent({ type: 6, body: { userId: 0 } });
        } catch {
            coronaWarnMessage('uploadClientEvent', 'error');
        }
        onClose();
    }, [onClose]);

    return (
        <FullscreenModal fromBottom onClose={() => {}} visible={show} forceOpen>
            <View className="youth-mode-container">
                <Image className="top-icon" src={TOP_ICON} />
                <Text className="title">青少年模式</Text>
                <Text className="desc">
                    为呵护未成年人健康成长，心颜特别推出青少年模式，该模式下心颜应用无法正常使用。请监护人主动选择，并设置监护密码。
                </Text>
                <View className="button-container">
                    <Text className="modal-button confirm-button" onClick={closeAction}>
                        不再提醒
                    </Text>
                    <Text className="modal-button cancel-button" onClick={openYouthMode}>
                        立即开启
                    </Text>
                </View>
            </View>
        </FullscreenModal>
    );
};

export default YouthModeModal;
