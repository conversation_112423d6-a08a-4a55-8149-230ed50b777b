import React, { useEffect } from 'react';
import Taro, { getCurrentPages, getStorageSync } from '@tarojs/taro';
import { View } from '@tarojs/components';
import MessageFlow from '@/hooks/message/MessageFlow';
import useAppPathStore from '@/store/useAppPathStore';
import useHomeStore from '@/components/Home/useHomeStore';
import usePopupStore from '@/store/Popup/usePopupStore';
import { PopupType, PopupPageScope, PopupBusinessKey } from '@/store/Popup/type';
import { PopupRegistrationOptions } from '@/store/Popup/popupdata';
import useUserInfoStore from '@/store/useUserInfoStore';

import { queryTeenState } from '@/service/modalApi';
import { forceUpdateVersion, isIOS } from '@/utils';
import { isBigBrotherVersion, openKey } from '@/utils/rpc';
import { isToday } from '@/utils/timeHelper';

import { Modals } from './const';

type ModalType = keyof typeof Modals;

const ModalContext = () => {
    const pages = getCurrentPages();
    const tabbarIndex = useHomeStore((state) => state.current);
    const currentPopup = usePopupStore((state) => state.currentPopup);

    // 监听页面路由变化
    useEffect(() => {
        useAppPathStore.getState().setAppPages(pages);
    }, [pages]);
    // 监听 Tabbar 变化
    useEffect(() => {
        useAppPathStore.getState().setTabbarIndex(tabbarIndex);
    }, [tabbarIndex]);

    // 订阅路由变化，触发弹窗检查
    useEffect(() => {
        const unsubscribe = useAppPathStore.subscribe((state, prev) => {
            if (prev.currentPath && state.currentPath !== prev.currentPath) {
                usePopupStore.getState().showPopupIfNeeded(state.currentPath);
            }
        });
        return () => {
            unsubscribe();
        };
    }, []);

    // 注册启动弹窗
    useEffect(() => {
        if (IS_MIRTH) {
            // 强制升级弹窗
            const forcePopup: PopupRegistrationOptions = {
                key: PopupBusinessKey.FORCE_UPGRADE,
                priority: 0,
                isBlocking: true,
                condition: async () => {
                    return forceUpdateVersion();
                },
            };
            usePopupStore.getState().registerPopup(forcePopup);
            // 老用户告知
            const firstEnterPopup: PopupRegistrationOptions = {
                key: PopupBusinessKey.NEW_MIRTH,
                priority: 1,
                isBlocking: true,
                condition: async () => {
                    if (isIOS) {
                        // ios审核态禁止升级通知弹窗
                        isBigBrotherVersion((data) => {
                            if (data?.success) {
                                return false;
                            }
                            return useUserInfoStore.getState().getFirstEnterPopupInfo();
                        });
                    }
                    return useUserInfoStore.getState().getFirstEnterPopupInfo();
                },
            };
            usePopupStore.getState().registerPopup(firstEnterPopup);
            // 青少年模式
            const youthModePopup: PopupRegistrationOptions = {
                key: PopupBusinessKey.YOUTH_MODE,
                priority: 5,
                pageScope: PopupPageScope.MAIN,
                condition: async () => {
                    let shouldShow = false;
                    try {
                        const res = await queryTeenState();
                        if (res) {
                            if (res.state === 1) {
                                // 开启青少年模式，每次都显示
                                shouldShow = false;
                                openKey('st_teen_close');
                            } else if (res.finish) {
                                // 未开启青少年模式时，用户选择不再显示
                                shouldShow = false;
                            } else {
                                // 判断当天是否显示过
                                const teenDate = getStorageSync('teen_mode_show_date') || 0;
                                shouldShow = !isToday(teenDate);
                            }
                        }
                    } catch {
                        shouldShow = false;
                    }
                    return shouldShow;
                },
            };
            usePopupStore.getState().registerPopup(youthModePopup);
        }
        // 新手引导弹窗
        const newUserPopup: PopupRegistrationOptions = {
            type: PopupType.CUSTOM,
            key: PopupBusinessKey.APP_GUIDE,
            priority: 2,
            isBlocking: true,
        };
        usePopupStore.getState().registerPopup(newUserPopup);
    }, []);

    // 监听签到 im
    useEffect(() => {
        const listener = {
            onSystemMessage: () => {
                // 显示签到弹窗
                const signPopup: PopupRegistrationOptions = {
                    key: PopupBusinessKey.SIGN,
                    priority: 3,
                    blackPageList: ['/chat/'],
                };
                usePopupStore.getState().registerPopup(signPopup);
            },
        };

        MessageFlow.addNotificationListener(4500, listener);
        return () => {
            MessageFlow.removeNotificationListener(4500, listener);
        };
    }, []);

    // 监听新用户十连抽
    useEffect(() => {
        const listener = {
            onSystemMessage: () => {
                const popup: PopupRegistrationOptions = {
                    key: PopupBusinessKey.USER_REWARDS,
                    priority: 4,
                    pageScope: PopupPageScope.MAIN,
                };
                usePopupStore.getState().registerPopup(popup);
            },
        };
        MessageFlow.addNotificationListener(4600, listener);
        return () => {
            MessageFlow.removeNotificationListener(4600, listener);
        };
    }, []);

    if (!currentPopup) {
        return <View className="hidden" />;
    }

    let modalType: ModalType | null = null;
    switch (currentPopup.key) {
        case PopupBusinessKey.FORCE_UPGRADE:
            modalType = 'forceUpdateModal';
            break;
        case PopupBusinessKey.NEW_MIRTH:
            modalType = 'newVersionUpdate';
            break;
        case PopupBusinessKey.SIGN:
            modalType = 'sign';
            break;
        case PopupBusinessKey.USER_REWARDS:
            modalType = 'userReward';
            break;
        case PopupBusinessKey.YOUTH_MODE:
            modalType = 'youthMode';
            break;
        default:
            break;
    }

    if (!modalType) {
        return <View className="hidden" />;
    }

    const handleClose = () => {
        usePopupStore.getState().completePopup(currentPopup.key);
    };

    const ModalComponent = Modals[modalType];

    return <ModalComponent show onClose={handleClose} />;
};

export default ModalContext;
