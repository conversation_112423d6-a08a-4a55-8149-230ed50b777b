import React from 'react';
import { View, Text } from '@tarojs/components';
import FullscreenModal from '@/components/FullScreenModal';
import { openUrl, goBackNative } from '@/utils/rpc';

import './index.scss';
import { isAndroid } from '@/utils';

const Param = ({ children, className }: any) => (
    <View
        className={`text-[rgba(51,52,55,0.7)] text-[14px] leading-[21px] font-[14px] ${className}`}>
        {children}
    </View>
);

const Br = ({ children }: any) => <View className="mb-8">{children}</View>;

const Strong = ({ children }: any) => (
    <Text className="text-[rgba(51,52,55,1)] text-[14px] leading-[21px] font-medium">
        {children}
    </Text>
);

const Title = ({ children }: any) => (
    <View className="text-[rgba(51,52,55,1)] text-[15px] leading-[21px] font-medium">
        {children}
    </View>
);

const PreviewModal = () => {
    const goUpdateVersion = () => {
        if (isAndroid) {
            const url =
                'https://api.moyi.163.com/api/package/download/latest?market=netease1&productName=XINYAN';
            window.location.href = url;
        } else {
            const url = 'itms-appss://apps.apple.com/app/6471562166?ls=1';
            openUrl(url);
        }
    };

    return (
        <FullscreenModal fromBottom onClose={() => {}} visible forceOpen>
            <View className="force-update-modal">
                <View className="close" onClick={goBackNative} />
                <Title>亲爱的用户</Title>
                <Br />
                <Br />
                <Param>感谢您一直以来对我们平台的支持与喜爱！</Param>
                <Br />
                <Param>我们诚挚邀请您升级至最新版本，解锁精心打造的AI互动社交新功能~</Param>
                <Br />
                <Br />
                <Param className="whitespace-nowrap">
                    点击下方<Strong>【立即升级】</Strong>按钮，享受最新功能吧~~
                </Param>
                <Br />
                <Br />
                <View className="btn" onClick={goUpdateVersion} />
            </View>
        </FullscreenModal>
    );
};

export default PreviewModal;
