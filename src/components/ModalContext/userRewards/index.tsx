import React, { useCallback } from 'react';
import { Image, View } from '@tarojs/components';
import FullscreenModal from '@/components/FullScreenModal';

import TOP_ICON from '@/assets/modal-context/userRewards/user_rewards_icon.png';
import CLOSE_ICON from '@/assets/modal-context/userRewards/user_rewards_close_icon.png';
import DISMISS_ICON from '@/assets/modal-context/userRewards/user_rewards_dismiss_icon.png';
import GACHA_ICON from '@/assets/modal-context/userRewards/user_rewards_gacha_icon.png';

import { jump2Gacha } from '@/router';
import './index.scss';

interface IModalSign {
    show: boolean;
    onClose: () => void;
}

const UserRewardsModal = ({ show, onClose }: IModalSign) => {
    const gotoGacha = useCallback(() => {
        onClose();
        jump2Gacha({});
    }, [onClose]);

    return (
        <FullscreenModal onClose={onClose} visible={show} forceOpen>
            <View id="userReward" className="use-rewards-content">
                <Image className="top-image" src={TOP_ICON}>
                    <Image className="close-image" src={CLOSE_ICON} onClick={onClose} />
                </Image>
                <View className="button-group">
                    <Image className="button-item" src={DISMISS_ICON} onClick={onClose} />
                    <Image className="button-item" src={GACHA_ICON} onClick={gotoGacha} />
                </View>
            </View>
        </FullscreenModal>
    );
};

export default UserRewardsModal;
