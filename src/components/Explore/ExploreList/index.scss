.explore-list {
    height: 100%;
    width: 100vw;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .explore-list-bg {
        position: absolute;
        width: 100%;
        height: 278px;
        background-image: url('../../../assets/explore/icon-page-bg.jpg');
        background-repeat: no-repeat;
        background-size: cover;
        background-position: top center;
        top: 0;
    }

    .explore-window {
        height: 100%;
        width: 100vw;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: relative;

        .explore-icon-bg {
            position: absolute;
            width: 114px;
            height: 55px;
            background-image: url('../../../assets/explore/icon-page-logo.png');
            background-repeat: no-repeat;
            background-size: cover;
            background-position: top center;
            top: 0;
            right: 0;
        }

        .waterfall-container {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            padding: 0 15px 0 15px;
            margin-top: 12px;
            flex: 1;
            overflow-y: auto;
            column-gap: 12px;
            transform: translateZ(0); // 强制开启GPU加速层
            will-change: transform; // 提示浏览器优化

            .col-1,
            .col-2 {
                width: calc(50% - 6px);

                .explore-col {
                    display: flex;
                    flex-direction: column;

                    .explore-item {
                        margin-bottom: 11px;
                    }
                }
            }
        }

        .list-empty {
            flex: 1;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 260px;

            .empty-title {
                font-size: 14px;
                font-weight: normal;
                line-height: 100%;
                text-align: center;
                letter-spacing: 0;
                color: #9e9e9e;
            }
        }
    }
}
