/* eslint-disable no-console */
import React, { useEffect } from 'react';
import CircleLoading from '@/components/CircleLoading';
import winAdapter from '@/utils/adapter/winAdapter';
import ErrorBoundary from '@/components/ErrorBoundary';
import TaroSafeAreaView from '@/components/safe-area-view';
import TitleBar from '@/components/titleBar';
import { saveStartIndex } from '@/utils/rpc';
import { Text, View } from '@tarojs/components';
import { TabItemStateProps } from '@/components/Home/TabItemProps';
import EventTrackView from '@/components/EventTrack';
import useHomeStore from '@/components/Home/useHomeStore';
import EntryCard from '../EntryCard';
import RobotCard from '../RobotCard';
import useExploreListStore, { ExploreTypeConst } from './exploreListStore';
import './index.scss';

const ExploreList = ({ selected, pageShow }: TabItemStateProps) => {
    const list = useExploreListStore((state) => state.list);
    const loading = useExploreListStore((state) => state.loading);

    useEffect(() => {
        useExploreListStore.getState().reuestApi();
    }, []);

    useEffect(() => {
        if (!loading) {
            useHomeStore.getState().hideLoading();
        }
    }, [loading]);

    useEffect(() => {
        if (selected) {
            saveStartIndex('explore');
            useExploreListStore.getState().reuestApi();
        }
    }, [selected]);

    useEffect(() => {
        if (pageShow) {
            useExploreListStore.getState().reuestApi();
        }
    }, [pageShow]);

    const leftList = list.filter((_, index) => index % 2 === 0);
    const rightList = list.filter((_, index) => index % 2 !== 0);

    const renderColumn = (items: typeof list) => (
        <View className="explore-col">
            {items.map((item) => {
                const isEntry = item.type === ExploreTypeConst.entry;

                return (
                    <View
                        key={isEntry ? `entry` : `robot_${item.item?.robotBaseInfo?.userId}`}
                        className="explore-item">
                        {isEntry ? <EntryCard /> : <RobotCard item={item} />}
                    </View>
                );
            })}
        </View>
    );

    console.log('ExploreList', list);

    return (
        <ErrorBoundary
            text="系统繁忙，点击任意区域可刷新页面"
            onClick={() => {
                winAdapter.reloadPage();
            }}>
            <TaroSafeAreaView className="explore-list">
                <EventTrackView params={{ _spm: 'page_ai_discovery|page_h5_biz' }}>
                    <View />
                </EventTrackView>
                <View className="explore-list-bg" />
                <View className="explore-window">
                    <View className="explore-icon-bg" />
                    <TitleBar title="发现" hasRight={false} />

                    {loading && <CircleLoading />}

                    {!loading && list.length > 0 && (
                        <View className="waterfall-container">
                            <View className="col-1">{renderColumn(leftList)}</View>
                            <View className="col-2">{renderColumn(rightList)}</View>
                        </View>
                    )}

                    {!loading && list.length === 0 && (
                        <View className="list-empty">
                            <Text className="empty-title">暂无角色，等会再来看看吧~</Text>
                        </View>
                    )}
                </View>
            </TaroSafeAreaView>
        </ErrorBoundary>
    );
};

export default React.memo(ExploreList);
