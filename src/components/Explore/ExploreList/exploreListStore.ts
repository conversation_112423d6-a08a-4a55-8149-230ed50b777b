/* eslint-disable no-console */
import { recommendListApi, recommendRedApi } from '@/service/exploreApi';
import { showToast } from '@tarojs/taro';
import { create } from 'zustand';
import useHomeStore, { TabItemKey } from '../../Home/useHomeStore';
import { AigcRobotRecommend } from '../meta/AigcRobotRecommend';
import { AigcRedPoint } from '../meta/AigcRedPoint';

export const ExploreTypeConst = {
    robot: 'robot',
    entry: 'entry',
} as const;

export type ExploreItemType = (typeof ExploreTypeConst)[keyof typeof ExploreTypeConst];

export interface ExploreListItem {
    type: ExploreItemType;
    item?: AigcRobotRecommend;
}

interface ExploreStoreState {
    loading: boolean;
    list: ExploreListItem[];
    fetchData: () => Promise<void>;
    redPoint?: AigcRedPoint;
    requestRedPoint: () => Promise<void>;
    reuestApi: () => Promise<void>;
}

const useExploreListStore = create<ExploreStoreState>((set, get) => {
    let isFetching = false;
    let redRequesting = false;

    return {
        loading: true,
        list: [],
        fetchData: async () => {
            if (isFetching) {
                return;
            }
            isFetching = true;

            try {
                const res = await recommendListApi();
                console.log('recommendListApi', res);
                const robotList = res.map((item) => ({
                    type: ExploreTypeConst.robot,
                    item,
                }));
                set({
                    list: [{ type: ExploreTypeConst.entry }, ...robotList],
                });
            } catch (err) {
                console.log('API request error:', err);
                showToast({
                    title: err.message,
                    icon: 'none',
                });
            } finally {
                isFetching = false;
                set({ loading: false });
            }
        },
        requestRedPoint: async () => {
            if (redRequesting) {
                return;
            }
            redRequesting = true;
            try {
                const res = await recommendRedApi();
                set({
                    redPoint: res,
                });

                const showTabBarRedDot =
                    res.missionRedPoint || res.signInRedPoint || res.lotteryCardPoint;
                useHomeStore.getState().setRedDot(TabItemKey.explore, showTabBarRedDot);
            } catch (err) {
                showToast({
                    title: err.message,
                    icon: 'none',
                });
            } finally {
                redRequesting = false;
            }
        },
        reuestApi: async () => {
            get().fetchData();
            get().requestRedPoint();
        },
    };
});

export default useExploreListStore;
