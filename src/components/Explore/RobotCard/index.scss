.robot-item {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    width: 100%;
    height: auto;
    aspect-ratio: 167 / 306;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;

    .avatar-bg {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
    }

    .content-wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 144px;
        flex-shrink: 0;
        background: linear-gradient(180deg, rgba(60, 60, 60, 0) 0%, #3c3c3c 100%);
        justify-content: flex-end;
        padding: 12px 10px;

        .name {
            color: #fff;
            text-shadow: 0 0 2px rgba(0, 0, 0, 0.6);
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 16px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .label-wrapper {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            overflow: hidden;
            margin-top: 4px;
            height: 16px;

            .label {
                font-size: 10px;
                font-weight: 400;
                line-height: 10px;
                color: #fff;
                opacity: 0.6;
                padding: 3px 4px;
                margin-right: 4px;
                border-radius: 4px;
                background: rgba(79, 79, 79, 0.3);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                text-align: center;
            }
        }

        .info {
            color: #fff;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;
            opacity: 0.8;
            margin-top: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            white-space: normal;
            word-break: break-word;
        }
    }

    .taro-img__mode-aspectfill {
        object-fit: cover;
    }
}

// 对不支持aspect-ratio的浏览器
@supports not (aspect-ratio: 1/1) {
    .robot-item {
        height: 306px;
    }
}
