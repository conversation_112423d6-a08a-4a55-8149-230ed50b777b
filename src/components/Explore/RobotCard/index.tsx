import React, { useCallback } from 'react';
import { Image, Text, View } from '@tarojs/components';
import { getImage, optimizeImage } from '@/utils/image';
import { addClickLog } from '@/utils/logTool';

import { ExploreListItem } from '../ExploreList/exploreListStore';
import { AigcRobotRecommend } from '../meta/AigcRobotRecommend';
import './index.scss';
import { jump2Chat } from '@/router';

export interface RobotCardProps {
    item: ExploreListItem;
}

const RobotCard = ({ item }: RobotCardProps) => {
    const robotItem = item.item as AigcRobotRecommend;

    const onClick = useCallback(() => {
        const { robotBaseInfo, hasAiChapter, aigcChatResourceInfo } = robotItem;

        const optimizedUrl = optimizeImage({
            src: aigcChatResourceInfo?.botBackgroundUrl,
            width: 375,
            height: 812,
        });
        const optimizedMp4 = aigcChatResourceInfo?.botStandByUrls?.[0];
        const botContactUrl = aigcChatResourceInfo?.botContactUrl ?? '';
        jump2Chat({
            robotUserId: `${robotBaseInfo.userId}`,
            hasAiChapter: `${hasAiChapter}`,
            robotAccid: `${robotBaseInfo.accId}`,
            botBgUrl: `${optimizedUrl}`,
            botStandByUrl: `${optimizedMp4}`,
            botContactUrl: `${botContactUrl}`,
        })

        addClickLog('mod_ai_discovery_card|page_ai_discovery|page_h5_biz', {
            s_ctype: 'botid',
            s_cid: robotBaseInfo.userId,
        });
    }, [robotItem]);

    return (
        <View className="robot-item" onClick={onClick}>
            <Image
                className="avatar-bg"
                src={getImage(robotItem.aigcChatResourceInfo?.botContactUrl)}
                mode="aspectFill"
            />
            <View className="content-wrapper">
                <Text className="name">{robotItem.robotBaseInfo.nickname}</Text>
                <View className="label-wrapper">
                    {robotItem.robotBaseInfo?.labels?.map((label) => (
                        <Text key={label} className="label">
                            {label}
                        </Text>
                    ))}
                </View>
                <Text className="info">{robotItem.robotBaseInfo.roleDesc}</Text>
            </View>
        </View>
    );
};

export default React.memo(RobotCard);
