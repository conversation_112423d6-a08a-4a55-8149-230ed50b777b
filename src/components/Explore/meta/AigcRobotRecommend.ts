import { AigcChatResourceInfo } from '@/types/bot';

export interface AigcRobotBaseInfo {
    userId: number;
    nickname: string;
    avatarUrl: string;
    age: number;
    specialAge?: string;
    gender: number;
    labels: string[];
    roleDesc: string;
    intro: string;
    accId: string;
    profession?: string;
}

export interface AigcRobotRecommend {
    robotBaseInfo: AigcRobotBaseInfo;
    aigcChatResourceInfo: AigcChatResourceInfo;
    friend: boolean;
    hasAiChapter: boolean;
    order: number;
}
