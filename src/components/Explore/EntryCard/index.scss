.entry-item {
    .power-card {
        width: 100%;
        height: 71px;
        display: flex;
        flex-direction: row;
        align-items: center;
        background-image: url('../../../assets/explore/icon-power-bg.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        position: relative;

        .power-card-img {
            width: 70px;
            height: 70px;
            background-image: url('../../../assets/explore/icon-power.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: center;
        }

        .power-card-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-left: -4px;

            .power-card-title {
                color: #7f5c5b;
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                line-height: 16px;
            }

            .power-card-info {
                color: #7f5c5b;
                font-size: 11px;
                font-style: normal;
                font-weight: 400;
                line-height: 16px;
                margin-top: 3px;
            }
        }

        .power-card-red-dot {
            position: absolute;
            right: 10px;
            top: 10px;
            width: 7px;
            height: 7px;
            background-color: #ff561e;
            border-radius: 50%;
        }
    }

    .draw-card {
        width: 100%;
        height: 71px;
        display: flex;
        flex-direction: row;
        align-items: center;
        background-image: url('../../../assets/explore/icon-draw-bg.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        // border-radius: 8px;
        margin-top: 12px;
        position: relative;

        .draw-card-img {
            width: 70px;
            height: 70px;
            background-image: url('../../../assets/explore/icon-draw.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: center;
        }

        .draw-card-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-left: -4px;

            .draw-card-title {
                color: #fff;
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                line-height: 16px;
            }

            .draw-card-info {
                color: #fff;
                font-size: 11px;
                font-style: normal;
                font-weight: 400;
                line-height: 16px;
                margin-top: 3px;
            }
        }

        .draw-card-red-dot {
            position: absolute;
            right: -4.5px;
            top: -10px;
            width: 33px;
            height: 20px;
            background-image: url('../../../assets/explore/icon-free-mark.png');
            background-size: 100% 100%;
            transform: rotate(15deg);
        }
    }
}
