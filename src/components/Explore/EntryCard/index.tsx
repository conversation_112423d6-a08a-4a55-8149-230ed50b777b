import { debounce } from '@/utils';
import { addClickLog } from '@/utils/logTool';
import { Text, View } from '@tarojs/components';
import React, { useCallback } from 'react';
import { jump2Gacha, jump2Task } from '@/router';
import useExploreListStore from '../ExploreList/exploreListStore';

import './index.scss';

const EntryCard = () => {
    const redPoint = useExploreListStore((state) => state.redPoint);
    const showPowerRedPoint = redPoint?.missionRedPoint || redPoint?.signInRedPoint;

    const handlePowerClick = useCallback(() => {
        if (redPoint?.signInRedPoint) {
            jump2Task({});
        } else {
            jump2Task({ tab: 1 });
        }
        addClickLog('mod_ai_discovery_banner|page_ai_discovery|page_h5_biz', {
            btn_type: 'power',
        });
    }, [redPoint]);

    const handleDrawClick = useCallback(() => {
        jump2Gacha({});
        addClickLog('mod_ai_discovery_banner|page_ai_discovery|page_h5_biz', {
            btn_type: 'story',
        });
    }, []);

    return (
        <View className="entry-item">
            <View className="power-card" onClick={debounce(handlePowerClick)}>
                <View className="power-card-img" />
                <View className="power-card-content">
                    <Text className="power-card-title">免费体力</Text>
                    <Text className="power-card-info">完成任务领奖励</Text>
                </View>
                {showPowerRedPoint && <View className="power-card-red-dot" />}
            </View>
            <View className="draw-card" onClick={debounce(handleDrawClick)}>
                <View className="draw-card-img" />
                <View className="draw-card-content">
                    <Text className="draw-card-title">抽剧情卡</Text>
                    <Text className="draw-card-info">升维观测解锁剧情</Text>
                </View>
                {redPoint?.lotteryCardPoint && <View className="draw-card-red-dot" />}
            </View>
        </View>
    );
};

export default React.memo(EntryCard);
