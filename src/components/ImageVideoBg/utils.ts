// 视频格式后缀列表
export const videoExtensions = [
    'mp4',
    'webm',
    'ogg',
    'mov',
    'avi',
    'wmv',
    'flv',
    'm4v',
    '3gp',
    'mkv',
];

/**
 * 简单判断URL是否为视频资源
 * @param url 需要判断的URL
 * @returns 是否可能是视频资源
 */
export const isRealVideo = (url?: string): boolean => {
    if (!url) return false;

    // 转为小写以进行不区分大小写的比较
    const lowerUrl = url.toLowerCase();

    // 检查是否包含常见视频扩展名
    return videoExtensions.some((ext) => lowerUrl.includes(`.${ext}`));
};
