import { Image, View } from '@tarojs/components';
import React, { useMemo } from 'react';
import './index.scss';
import { NormalVideo, SizeMode } from '@music/ct-animation';
import { isRealVideo } from '@/components/ImageVideoBg/utils';

export interface ImageVideoBgProps {
    imgUrl?: string;
    videoUrl?: string;
    showMask?: boolean;
    maskColor?: string;
}

const ImageVideoBg = ({ imgUrl, videoUrl, showMask, maskColor }: ImageVideoBgProps) => {
    const color = maskColor || 'rgba(187, 187, 187, 0.2)';

    // 兼容videoUrl可能是图片的case
    const isVideo = useMemo(() => {
        return isRealVideo(videoUrl);
    }, [videoUrl]);

    return (
        <View className="image-video-bg">
            {imgUrl && <Image className="bg-img" src={imgUrl} mode="aspectFill" />}
            {!isVideo && videoUrl && (
                <Image className="bg-video" src={videoUrl} mode="aspectFill" />
            )}
            {isVideo && videoUrl && (
                <NormalVideo className="bg-video" src={videoUrl} loop sizeMode={SizeMode.Cover} />
            )}
            {showMask && <View className="bg-mask" style={{ background: color }} />}
        </View>
    );
};

export default React.memo(ImageVideoBg);
