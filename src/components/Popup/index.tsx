import React, { useCallback, useState } from 'react';
import { View } from '@tarojs/components';
import classNames from 'classnames';
import Taro from '@tarojs/taro';

import './index.scss';

interface PopupProps {
    visible: boolean;
    maskClosable?: boolean;
    onClose: () => void;
    children: (close: () => void) => React.ReactNode;
}

const Popup: React.FC<PopupProps> = ({ visible, children, maskClosable, onClose }: PopupProps) => {
    const [isLeaving, setIsLeaving] = useState(false);

    React.useEffect(() => {
        if (visible) {
            setIsLeaving(false);
        }
    }, [visible]);

    const handleClose = useCallback(() => {
        setIsLeaving(true);
        Taro.nextTick(() => {
            onClose();
        });
    }, [onClose]);

    const handleMaskClick = useCallback(() => {
        if (maskClosable) {
            handleClose();
        }
    }, [maskClosable, handleClose]);

    if (!visible && !isLeaving) {
        return null;
    }

    return (
        <React.Fragment>
            <View
                className={classNames('m-popup-mask', {
                    leaving: isLeaving,
                    entering: !isLeaving,
                })}
                onClick={handleMaskClick}
            />
            <View
                className={classNames('m-popup-content', {
                    leaving: isLeaving,
                    entering: !isLeaving,
                })}>
                {children(handleClose)}
            </View>
        </React.Fragment>
    );
};

Popup.defaultProps = {
    maskClosable: true,
};

export default Popup;
