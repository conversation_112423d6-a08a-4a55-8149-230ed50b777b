%full-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
}

.m-popup {
    &-mask {
        @extend %full-screen;
        background: rgba(0, 0, 0, 0.7);

        &.entering {
            animation: fadeIn 0.3s ease-in-out;
        }

        &.leaving {
            animation: fadeOut 0.3s ease-in-out;
        }
    }

    &-content {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        background-color: #fff;
        border-radius: 16px 16px 0 0;

        &.entering {
            animation: slideIn 0.3s ease-in-out;
        }

        &.leaving {
            animation: slideOut 0.3s ease-in-out;
        }
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(100%);
    }

    to {
        transform: translateY(0);
    }
}

@keyframes slideOut {
    from {
        transform: translateY(0);
    }

    to {
        transform: translateY(100%);
    }
}
