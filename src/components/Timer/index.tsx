/* eslint-disable react/forbid-prop-types */
import React, { Fragment, useEffect, useMemo, useState, memo, useCallback } from 'react';
import createCountdown from '@music/count-down';
import { TimeUtil } from '@music/helper';

interface ITimer {
    time: number;
    text?: string;
    format?: string;
    onEnd?: () => void;
    initTime?: string;
    pre?: string;
    downDuration?: number;
}

function Timer({
    time = 0,
    text = '',
    format = '{hh}:{mm}:{ss}',
    onEnd = () => {},
    initTime = '00:00:00',
    pre = '',
    downDuration = 1000, // 触发的时间间距 单位：毫秒
}: ITimer) {
    const initShowTimes = useMemo(() => TimeUtil.format(time, format), [format, time]);
    const [timeDown, setTimeDown] = useState(initTime);

    const count = useCallback(() => {
        createCountdown({
            time, // 倒计时剩余时间， 单位： 毫秒
            format, // 倒计时格式
            downDuration,
            cb: (t: string, { clear }: { clear: () => void }) => {
                // 非数字替换为 ''  比如 02分22秒
                const ct = t.replace(/\D/g, '');
                setTimeDown(t);

                if (+ct === 0) {
                    onEnd();
                    clear();
                }
            }, // 回调，定期执行, t: 格式化后的倒计时时间
        });
    }, [time, format, onEnd, downDuration]);

    useEffect(() => {
        count();
    }, [count]);

    return (
        <Fragment>
            {pre}
            {timeDown === initTime ? initShowTimes : timeDown}
            {text}
        </Fragment>
    );
}

export default memo(Timer);
