import React, { useEffect, useRef } from 'react';
import { Image, View } from '@tarojs/components';
import { useDidHide, useDidShow } from '@tarojs/taro';
import { assetBalance } from '@/service/physicalPowerApi';
import ICON_ADD1_PINK from '@/assets/game/gacha/gacha_icon_add.png';
import classNames from 'classnames';
import { debounce } from '@/utils';
import { pageDidAppear } from '@/utils/rpc';
import useGachaStore from '@/pages/gacha/useGachaStore';
import ICON_GLOD from '@/assets/market/ic_market_coin.png';
import { gotoRechargePage } from '@/pages/market/pay-panel/payPanelStore';
import { getCoinIcon, getCoinType } from '@/utils/appSourceAdapter';
import './index.scss';

const GoldView = () => {
    const glodAccount = useGachaStore((state) => state.glodAccount);
    const setGlodeAccount = useGachaStore((state) => state.setGlodeAccount);
    const exchangeInfoResp = useGachaStore((state) => state.exchangeInfoResp);
    const hasHide = useRef(false);

    function handleRecoverClick() {
        gotoRechargePage();
    }

    const assetBalanceReq = () => {
        assetBalance(getCoinType())
            .then((res: { balance: any }) => {
                const balance = `${Number.parseInt(res?.balance || '0', 10)}`;
                setGlodeAccount(Number.parseInt(balance, 10));
            })
            .catch((err: any) => {
                //
            });
    };

    useEffect(() => {
        pageDidAppear(() => {
            assetBalanceReq();
            // 3秒后再刷新一次
            setTimeout(() => {
                assetBalanceReq();
            }, 3000);
        });
    }, []);

    useDidShow(() => {
        if (hasHide.current === true) {
            assetBalanceReq();
        }
        hasHide.current = false;
    });

    useDidHide(() => {
        hasHide.current = true;
    });

    useEffect(() => {
        assetBalanceReq();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [exchangeInfoResp]);

    return (
        <View className="goldHistory">
            <View
                className={classNames('goldViewInfo')}
                onClick={debounce(() => {
                    handleRecoverClick();
                })}>
                <View className="at-row">
                    <View className="at-col at-col-1 at-col--auto">
                        <View className="goldImageContainer">
                            <Image src={getCoinIcon()} className="goldImage" />
                        </View>
                    </View>
                    <View className="at-col">
                        <View className="goldInfoContainer">
                            <View className="goldNum">{glodAccount}</View>
                        </View>
                    </View>
                    <View className="at-col at-col-1 at-col--auto">
                        <View className="goldImage2Container">
                            <Image src={ICON_ADD1_PINK} className="goldImage" />
                        </View>
                    </View>
                </View>
            </View>
        </View>
    );
};

export default React.memo(GoldView);
