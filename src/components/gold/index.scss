@import '~taro-ui/dist/style/components/nav-bar.scss';
@import '~taro-ui/dist/style/components/icon.scss';

.goldHistory {
    .goldViewInfo {
        border-radius: 45px;
        border: 0.5px solid rgba(255, 255, 255, 0.1);
        background: rgba(0, 0, 0, 0.2);
        max-width: 105px;
        min-width: 92px;
        height: 24px;
        position: relative;
        margin-left: 6px;

        .goldImageContainer {
            height: 24px;
            display: flex;
            margin-left: 4px;
            align-items: center;

            .goldImage {
                width: 18px;
                height: 18px;
            }
        }

        .goldImage2Container {
            height: 24px;
            display: flex;
            margin-right: 4px;
            align-items: center;

            .goldImage {
                margin-left: 4px;
                width: 16px;
                height: 16px;
            }
        }

        .goldInfoContainer {
            display: flex;
            height: 24px;
            margin-left: 3px;
            align-items: center;
            justify-content: flex-start;

            .goldNum {
                max-width: 50px;
                min-width: 39px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                font-size: 12px;
                color: #fff;
                margin-left: 3px;
            }
        }

        // 不同模式适配代码
        &.chip {
            background-color: #ffe5ee;

            .goldInfoContainer {
                .goldNum {
                    color: #ff689e;
                }

                .goldCount {
                    color: #ff689e;
                }
            }
        }
    }
}
