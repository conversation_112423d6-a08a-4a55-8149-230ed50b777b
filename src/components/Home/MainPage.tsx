/* eslint-disable no-console */
import React, { useEffect, useState } from 'react';
import { View } from '@tarojs/components';
import ErrorBoundary from '@/components/ErrorBoundary';
import winAdapter from '@/utils/adapter/winAdapter';
import { getCurrentInstance, useDidHide, useDidShow, useLoad } from '@tarojs/taro';
import ExploreList from '@/components/Explore/ExploreList';
import useExploreListStore from '@/components/Explore/ExploreList/exploreListStore';
import FriendPage from '@/components/Friend/Friend';
import useFriendStore from '@/components/Friend/useFriendStore';
import MessagePage from '../Message/Message';
import SelfPage from '../Self';
import useHomeStore, { TabBarConfig, TabItemKey } from './useHomeStore';
import './index.scss';

const tabItemOriginList = [
    {
        key: TabItemKey.message,
        pagePath: 'pages/message/index',
        text: '消息',
        childComponent: MessagePage,
        childComponentName: 'MessagePage',
        badge: 0,
    },
    {
        key: TabItemKey.friend,
        pagePath: 'pages/friends/index',
        text: '好友',
        childComponent: FriendPage,
        childComponentName: 'FriendPage',
        badge: 0,
        preRequest: () => {
            useFriendStore.getState().fetchData();
        },
    },
    {
        key: TabItemKey.explore,
        pagePath: 'pages/explore/index',
        text: '发现',
        childComponent: ExploreList,
        childComponentName: 'ExplorePage',
        badge: 0,
        preRequest: () => {
            useExploreListStore.getState().reuestApi();
        },
    },
];

const tabItemList = IS_MOYI
    ? tabItemOriginList
    : [
          ...tabItemOriginList,
          {
              key: TabItemKey.self,
              pagePath: 'pages/self/index',
              text: '我的',
              childComponent: SelfPage,
              childComponentName: 'SelfPage',
              badge: 0,
          },
      ];

const initTabBarConfig: TabBarConfig = {
    color: 'rgba(38,38,42,0.4)',
    selectedColor: 'rgba(38,38,42,1)',
    list: tabItemList,
};

useHomeStore.getState().setTabBarConfig(initTabBarConfig);

const MainPage = () => {
    useEffect(() => {
        useHomeStore.getState().checkGuide();
    }, []);

    const tabBarConfig = useHomeStore((state) => state.tabBarConfig);
    const currentIndex = useHomeStore((state) => state.current);
    const [pageShow, setPageShow] = useState<boolean>(true);

    const onChange = (index: number) => {
        useHomeStore.getState().setCurrent(index);
    };

    const formatBadgeCount = (count: number): string => {
        if (count <= 0) return '';
        if (count > 99) return '99+';
        return count.toString();
    };

    useDidShow(() => {
        // 定位tab
        const { tab = '' } = getCurrentInstance().router?.params || {};
        if (tab) {
            useHomeStore.getState().setCurrent(+tab);
            // 使用后重置tab，防止重复取到tab
            const params = getCurrentInstance().router?.params;
            params.tab = undefined;
            getCurrentInstance().router.params = params;
        }
        setPageShow(true);
        useHomeStore.getState().setPageShow(true);
    });

    useDidHide(() => {
        setPageShow(false);
        useHomeStore.getState().setPageShow(false);
    });

    useEffect(() => {
        useHomeStore.getState().preRequest?.();
    }, []);

    return (
        <ErrorBoundary
            text="系统繁忙，点击任意区域可刷新页面"
            onClick={() => {
                winAdapter.reloadPage();
            }}>
            <View className="home">
                <View className="content">
                    {tabBarConfig.list.map((item, index) => (
                        <View
                            key={item.pagePath}
                            style={{
                                height: '100%',
                                display: currentIndex === index ? 'block' : 'none',
                            }}>
                            <item.childComponent
                                pageShow={pageShow}
                                selected={currentIndex === index}
                            />
                        </View>
                    ))}
                </View>

                <View className="tab-bar">
                    {tabBarConfig.list.map((item, index) => (
                        <View
                            key={item.pagePath}
                            className="tab-bar-item"
                            onClick={() => onChange(index)}>
                            <View className="tab-bar-item-content">
                                <View
                                    className="tab-bar-text"
                                    style={{
                                        color:
                                            currentIndex === index
                                                ? tabBarConfig.selectedColor
                                                : tabBarConfig.color,
                                    }}>
                                    {item.text}
                                </View>
                                {item.badge > 0 && (
                                    <View className="tab-bar-badge">
                                        {formatBadgeCount(item.badge)}
                                    </View>
                                )}
                                {item.redDot && <View className="tab-bar-red-dot" />}
                            </View>
                        </View>
                    ))}
                </View>
            </View>
        </ErrorBoundary>
    );
};

export default MainPage;
