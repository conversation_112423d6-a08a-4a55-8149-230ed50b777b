import { ComponentType } from 'react';
import { create } from 'zustand';
import HomeApi from '@/components/Home/api';
import { onLoadFinish } from '@/utils/rpc';
import usePopupStore from '@/store/Popup/usePopupStore';
import { PopupBusinessKey } from '@/store/Popup/type';
import { jump2ChatGuide } from '@/router';

import { TabItemStateProps } from './TabItemProps';

export enum TabItemKey {
    message,
    friend,
    explore,
    self,
}

export interface TabItemConfig {
    key: TabItemKey;
    pagePath: string;
    text: string;
    badge: number;
    redDot?: boolean;
    childComponent: ComponentType<TabItemStateProps>;
    childComponentName: string;
    preRequest?: () => void;
}

export interface TabBarConfig {
    color: string;
    selectedColor: string;
    list: TabItemConfig[];
}

export const HomeTabIndex = {
    message: 0,
    friend: 1,
    explore: 2,
    self: 3,
};

export interface HomeState {
    tabBarConfig?: TabBarConfig;
    setTabBarConfig: (config: TabBarConfig) => void;
    pageShow: boolean;
    setPageShow: (show: boolean) => void;
    current: number;
    setCurrent: (index: number) => void;
    preRequest?: () => void;
    setBadgeCount: (key: TabItemKey, count: number) => void;
    setRedDot: (key: TabItemKey, show: boolean) => void;
    checkGuide: () => Promise<void>;
    canHideLoading: boolean;
    pendingHideLoading: boolean;
    hideLoading: () => void;
}

const useHomeStore = create<HomeState>((set, get) => ({
    tabBarConfig: undefined,
    setTabBarConfig: (config: TabBarConfig) => {
        set({ tabBarConfig: config });
    },
    pageShow: false,
    setPageShow: (show: boolean) => {
        set({ pageShow: show });
    },
    current: 0,
    setCurrent: (index: number) => {
        set({ current: index });
    },
    preRequest: () => {
        const { list } = get().tabBarConfig;
        const current = get().current;

        list.forEach((item, index) => {
            if (index !== current && item.preRequest) {
                item.preRequest();
            }
        });
    },
    setBadgeCount: (key: TabItemKey, count: number) => {
        const currentState = get();
        if (!currentState.tabBarConfig || !currentState.tabBarConfig.list) {
            return;
        }
        set((state) => ({
            tabBarConfig: {
                ...state.tabBarConfig,
                list: state.tabBarConfig?.list.map((item) =>
                    item.key === key ? { ...item, badge: count } : item
                ),
            },
        }));
    },
    setRedDot: (key: TabItemKey, show: boolean) => {
        set((state) => ({
            tabBarConfig: {
                ...state.tabBarConfig,
                list: state.tabBarConfig?.list.map((item) =>
                    item.key === key ? { ...item, redDot: show } : item
                ),
            },
        }));
    },
    checkGuide: async () => {
        let showGuide = false;
        try {
            const checkGuide = await HomeApi.checkGuide();
            showGuide = !checkGuide.complete;
        } finally {
            if (showGuide) {
                jump2ChatGuide();
                usePopupStore.getState().startPopup(PopupBusinessKey.APP_GUIDE);
            } else {
                set({ canHideLoading: true });
                if (get().pendingHideLoading) {
                    get().hideLoading();
                }
                usePopupStore.getState().completePopup(PopupBusinessKey.APP_GUIDE);
            }
        }
    },
    canHideLoading: false,
    pendingHideLoading: false,
    hideLoading: () => {
        if (get().canHideLoading) {
            onLoadFinish();
        } else {
            set({ pendingHideLoading: true });
        }
    },
}));

export default useHomeStore;
