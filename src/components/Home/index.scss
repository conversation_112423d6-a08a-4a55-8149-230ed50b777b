@import 'taro-ui/dist/style/components/float-layout.scss';
@import 'taro-ui/dist/style/components/progress.scss';
@import 'taro-ui/dist/style/components/icon.scss';

$at-progress-height: 100px;

.history {
    @import './index.rn.scss';

    .wrap {
        border-style: none;
        border-bottom: 1px solid #e7ebee;
    }

    &-content {
        margin-left: 300px;
    }
}

.half-screen-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 50%;
    background-color: #fff;
    border: 1px solid #000;
    padding: 20px;
}

.half-screen-bg {
    height: 30px;
    background-color: rgb(185, 185, 62);
}

.at-float-layout {
    &__container {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        background-color: aqua;
        width: 100%;
        min-height: 304px;
        max-height: 304px;
    }

    .layout {
        &-body {
            padding: 0;

            &__content {
                min-height: 40px;
            }
        }
    }
}

.home {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
}

.content {
    width: 100%;
    flex: 1;
    overflow: auto;
}

.tab-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background: white;
}

.tab-bar-item {
    display: flex;
    flex: 1;
    height: 50px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: white;
}

.tab-bar-text {
    font-size: 16px;
    font-weight: 600;
}

.tab-bar-mask {
    width: 100%;
    height: 50px;
    flex-shrink: 0;
}

.tab-bar-item-content {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.tab-bar-badge {
    position: absolute;
    top: -8px;
    right: -15px;
    min-width: 16px;
    height: 16px;
    padding: 0 5px;
    font-size: 11px;
    line-height: 16px;
    text-align: center;
    color: #fff;
    background-color: #ff689e;
    border-radius: 8px;
    box-sizing: border-box;
    white-space: nowrap;
}

.tab-bar-red-dot {
    position: absolute;
    right: -3px;
    top: 1px;
    width: 7px;
    height: 7px;
    background-color: #ff561e;
    border-radius: 50%;
}
