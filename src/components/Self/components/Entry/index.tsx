import React, { useEffect, useState } from 'react';
import { View, Text, Image } from '@tarojs/components';
import { showBackIcon } from '@/utils/appSourceAdapter';
import ServiceIcon from '@/assets/self/service-icon.png';
import SetIcon from '@/assets/self/set-icon.png';
import BackIcon from '@/assets/self/right-icon.png';
import InviteIcon from '@/assets/self/invite-icon.png';
import InviteBindIcon from '@/assets/self/invite-bind-icon.png';
import GroupIcon from '@/assets/self/group-icon.png';
import { openUrl, pageDidAppear } from '@/utils/rpc';
import { throttle, isAndroid } from '@/utils';
import { useDialog } from '@/components/dialog';
import EventTrackView from '@/components/EventTrack';
import { addClickLog } from '@/utils/logTool';
import { getStorageSync, setStorageSync, setStorage } from '@tarojs/taro';
import useHomeStore, { TabItemKey } from '@/components/Home/useHomeStore';

import { jump2JoinGroup } from '@/router';
import useSelfStore from '@/store/useUserInfoStore';

import useInviteStore from '../../store/invite';
import InviteCodeDialog from '../InviteCodeDialog';

import './index.scss';

interface EntryItemProps {
    leftIcon: string;
    text: string;
    onJump?: () => void;
    detail?: string;
    redDot?: boolean;
}

interface EntryProps {
    items?: EntryItemProps[];
}

const EntryItem = ({ leftIcon, text, detail, redDot, onJump }: EntryItemProps) => {
    return (
        <View className="entry-item" onClick={onJump}>
            <Image className="entry-item-licon" src={leftIcon} />
            <Text className="entry-item-text">{text}</Text>
            {detail && <Text className="entry-item-detail">{detail}</Text>}
            {redDot && <View className="entry-item-red-dot" />}
            <Image className="entry-item-ricon" src={BackIcon} />
        </View>
    );
};

const Entry: React.FC<EntryProps> = () => {
    const inviteEntryEnable = useInviteStore((state) => state.inviteEntryEnable);
    const inviteCodeBindVisible = useInviteStore((state) => state.inviteCodeBindVisible);

    const inviteCodeDialog = useDialog(InviteCodeDialog);
    const userInfo = useSelfStore.getState().userBaseInfo;
    const [hasJoinGroupTip, setHasJoinGroupTip] = useState(false);

    useEffect(() => {
        // 查询加入群聊状态
        let result = getStorageSync('kHasJoinGroupTip') || false;
        // 查询用户注册时间
        if (!result && userInfo && userInfo.userBase?.initTime) {
            const initTime = userInfo.userBase.initTime;
            const today = new Date();
            const initDate = new Date(initTime);
            if (initDate.getDate() === today.getDate()) {
                /// 新人首日不显示
                result = true;
            }
        }
        // 设置底部红点
        useHomeStore.getState().setRedDot(TabItemKey.self, !result);
        setHasJoinGroupTip(!result);
    }, [userInfo]);

    const ENTRY_CONFIG = React.useMemo(() => {
        const configs = [];

        // 加入群聊
        configs.push({
            leftIcon: GroupIcon,
            text: '加入群聊',
            redDot: hasJoinGroupTip,
            onJump: throttle(() => {
                jump2JoinGroup({
                    success: () => {
                        if (hasJoinGroupTip) {
                            setHasJoinGroupTip(false);
                            setStorageSync('kHasJoinGroupTip', true);
                            useHomeStore.getState().setRedDot(TabItemKey.self, false);
                        } else {
                            setStorage({ key: 'kHasJoinGroupTip', data: true });
                        }
                    },
                });
            }, 500),
        });

        // 邀请有礼入口（第一位）
        if (inviteEntryEnable) {
            configs.push({
                leftIcon: InviteIcon,
                text: '邀请有礼',
                onJump: throttle(() => {
                    openUrl('st_inviteNew');
                    addClickLog('btn_ai_my_setbar|page_ai_my|page_h5_biz', {
                        context: '邀请有礼',
                    });
                }, 500),
            });
        }

        // 账号设置入口（根据条件决定是否添加）
        if (isAndroid || !showBackIcon()) {
            configs.push({
                leftIcon: SetIcon,
                text: '账号设置',
                onJump: throttle(() => {
                    openUrl('mirth://nmt/local/settings');
                    addClickLog('btn_ai_my_setbar|page_ai_my|page_h5_biz', {
                        context: '账号设置',
                    });
                }, 500),
            });
        }

        // 我的客服入口（固定添加）
        configs.push({
            leftIcon: ServiceIcon,
            text: '我的客服',
            onJump: throttle(() => {
                openUrl('st_customerservice');
                addClickLog('btn_ai_my_setbar|page_ai_my|page_h5_biz', {
                    context: '我的客服',
                });
            }, 500),
        });

        // 填写邀请码入口（最后一位）
        if (inviteCodeBindVisible) {
            configs.push({
                leftIcon: InviteBindIcon,
                text: '填写邀请码',
                detail: '仅注册后24小时内',
                onJump: throttle(() => {
                    inviteCodeDialog.show({
                        success: () => {
                            useInviteStore.getState().refreshInviteConfig();
                        },
                    });
                    addClickLog('btn_ai_my_setbar|page_ai_my|page_h5_biz', {
                        context: '填写邀请码',
                    });
                }, 500),
            });
        }

        return configs;
    }, [inviteEntryEnable, inviteCodeBindVisible, inviteCodeDialog, hasJoinGroupTip]);

    useEffect(() => {
        pageDidAppear(() => {
            useInviteStore.getState().refreshInviteConfig();
        });
        useInviteStore.getState().refreshInviteConfig();
    }, []);

    return (
        <View className="m-self-entry">
            {ENTRY_CONFIG?.map((item) => (
                <View key={item?.text}>
                    <EventTrackView
                        params={{
                            _spm: 'btn_ai_my_setbar|page_ai_my|page_h5_biz',
                            context: item?.text,
                        }}
                        isPage={false}
                    />
                    <EntryItem
                        key={item?.text}
                        leftIcon={item?.leftIcon}
                        text={item?.text}
                        detail={item?.detail}
                        redDot={item?.redDot}
                        onJump={item?.onJump}
                    />
                </View>
            ))}
        </View>
    );
};

export default React.memo(Entry);
