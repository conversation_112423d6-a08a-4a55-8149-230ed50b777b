import React, { useCallback, useState } from 'react';
import { View, Text } from '@tarojs/components';
import classNames from 'classnames';
import Avatar from '@/components/avatar';
import { getImage } from '@/utils/image';
import { setClipboardData, hideToast } from '@tarojs/taro';
import { copy } from '@/utils/rpc';
import { isIOS } from '@/utils';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { jump2ProfileEdit } from '@/router';
import Toast from '@/components/Toast';
import useUserInfoStore from '@/store/useUserInfoStore';

import './index.scss';

interface FigureProps {
    avatar?: string;
    name?: string;
    id?: string;
}

const Figure: React.FC<FigureProps> = () => {
    const userBaseInfo = useUserInfoStore().userBaseInfo;
    const [isCopying, setIsCopying] = useState(false);

    const copyUserId = useCallback(() => {
        try {
            setIsCopying(true);
            const textMsg = userBaseInfo?.userBase?.userNo?.toString();
            if (isIOS) {
                copy(textMsg || '');
                Toast.show({
                    msg: '复制成功',
                });
            } else {
                setClipboardData({
                    data: textMsg || '',
                    success(a) {
                        hideToast();
                        Toast.show({
                            msg: '复制成功',
                        });
                    },
                    fail() {
                        hideToast();
                        Toast.show({
                            msg: '复制失败',
                            status: 'error',
                        });
                    },
                });
            }
            setTimeout(() => {
                setIsCopying(false);
            }, 300);
        } catch (error) {
            coronaWarnMessage('复制失败');
        }
    }, [userBaseInfo?.userBase?.userNo]);

    const onEdit = useCallback(() => {
        try {
            jump2ProfileEdit({ keyboard_action: 3 });
        } catch (error) {
            coronaWarnMessage('编辑资料');
        }
    }, []);

    return (
        <View className="m-self-figure">
            <Avatar
                width={72}
                height={72}
                lazyload={false}
                className="figure-avatar"
                src={getImage(userBaseInfo?.userBase?.avatarImgUrl)}
            />
            <View className="figure-name">
                <Text>{userBaseInfo?.userBase?.nickname ?? '\u00A0'}</Text>
            </View>
            <View className="figure-id">
                <Text>ID:{userBaseInfo?.userBase?.userNo}</Text>
                <View
                    className={classNames('id-icon', {
                        active: isCopying,
                    })}
                    onClick={copyUserId}
                />
            </View>
            <View className="figure-edit-btn" onClick={onEdit}>
                编辑资料
            </View>
        </View>
    );
};

export default React.memo(Figure);
