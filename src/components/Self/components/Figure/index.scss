.m-self-figure {
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 1;
    position: relative;

    .figure-avatar {
        border-radius: 50%;
        overflow: hidden;
        margin-bottom: 12px;
        border: 2px solid #fff;
        box-shadow: 0 0 20px 0 rgba(153, 153, 153, 0.58);

        .avatar-image {
            width: 100%;
            height: 100%;
        }
    }

    .figure-name {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 3px;
        text-align: center;
        width: calc(100% - 40px);

        taro-text-core {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            display: block;
        }
    }

    .figure-id {
        margin-bottom: 10px;
        font-size: 11px;
        color: rgba(0, 0, 0, 0.4);
        display: flex;
        justify-content: center;
        align-items: center;

        .id-icon {
            width: 12px;
            height: 12px;
            background: center / 100% 100% repeat url('../../../../assets/self/copy-icon.png');
            margin-left: 6px;
            transition: transform 0.2s ease;

            &:active {
                transform: scale(1.2);
            }
        }
    }

    .figure-edit-btn {
        width: 68px;
        height: 26px;
        border-radius: 83px;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.4);
        cursor: pointer;
        border: 1px solid rgba(0, 0, 0, 0.1);
        text-align: center;
        line-height: 26px;
    }
}
