import React, { useCallback, useState } from 'react';
import { AtModal } from 'taro-ui';
import { View, Text, Input, Image, Button } from '@tarojs/components';
import { CreateModalProps } from '@/components/dialog';
import ic_dialog_close from '@/assets/common/ic_dialog_close.png';
import { showToast } from '@tarojs/taro';

import './index.scss';
import { apiInviteCodeBind } from '../../api';

interface ContentInterface {
    isOpened?: boolean;
    dismiss: () => void;
    success?: () => void;
    data: {
        title?: string;
        subTitle?: string;
        buttonText?: string;
        placeholder?: string;
    };
}

const Content = ({ isOpened, dismiss, success, data }: ContentInterface) => {
    const [inputValue, setInputValue] = useState('');

    const handleClose = useCallback(() => {
        dismiss();
    }, [dismiss]);

    const handleSubmit = () => {
        if (!inputValue.trim()) {
            return;
        }
        apiInviteCodeBind({ inviteCode: inputValue })
            .then(() => {
                showToast({ title: '绑定成功', icon: 'none' });
                dismiss();
                if (success) {
                    success();
                }
            })
            .catch((err) => {
                showToast({
                    title: `${err.message ?? '校验不通过'}`,
                    icon: 'none',
                });
            });
    };

    const handleInput = useCallback((e) => {
        setInputValue(e.detail.value);
    }, []);

    return (
        <View className="invite-code-dialog-container">
            <AtModal
                isOpened={isOpened}
                className="custom-modal"
                onClose={handleClose}
                closeOnClickOverlay={false}>
                <View className="modal-content">
                    <Image src={ic_dialog_close} className="close-btn" onClick={handleClose} />
                    <View className="title-container">
                        <Text className="main-title">{data.title}</Text>
                    </View>

                    <View className="input-container">
                        <Input
                            className="custom-input"
                            type="text"
                            value={inputValue}
                            onInput={handleInput}
                            placeholder={data.placeholder}
                        />
                    </View>

                    <Button
                        className={`submit-button ${!inputValue.trim() ? 'disabled' : ''}`}
                        onClick={handleSubmit}
                        disabled={!inputValue.trim()}>
                        提交
                    </Button>
                </View>
            </AtModal>
        </View>
    );
};

const InviteCodeDialog: CreateModalProps = {
    type: 'modal_custom',
    isModal: true,
    render(dismiss, data) {
        return (
            <Content
                dismiss={dismiss}
                isOpened
                success={data.success}
                data={{
                    title: '填写邀请码',
                    buttonText: '提交',
                    placeholder: '请输入邀请码',
                }}
            />
        );
    },
};

export default InviteCodeDialog;
