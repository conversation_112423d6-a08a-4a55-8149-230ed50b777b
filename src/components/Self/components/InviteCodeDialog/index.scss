.invite-code-dialog-container {
    .custom-modal {
        .at-modal__container {
            width: 85%;
            border-radius: 12px;
            overflow: hidden;
        }

        .modal-content {
            position: relative;
            padding: 30px 20px 20px;

            .close-btn {
                position: absolute;
                top: 20px;
                right: 20px;
                width: 26px;
                height: 26px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .title-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-top: 20px;

                .main-title {
                    font-size: 18px;
                    font-weight: 600;
                    color: black;
                }
            }

            .input-container {
                margin-top: 20px;
                width: 100%;

                .custom-input {
                    background-color: #f5f5f5;
                    height: 48px;
                    border-radius: 8px;
                    padding: 0 15px;
                    font-size: 16px;
                    width: 100%;
                    box-sizing: border-box;
                    display: flex;
                    align-items: center;
                    line-height: 48px;

                    /* 与高度一致 */
                }
            }

            .submit-button {
                margin-top: 20px;
                width: 100%;
                margin-bottom: 20px;
                background-color: #ff689e;
                color: white;
                height: 50px;
                border-radius: 25px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                font-weight: 500;

                cursor: pointer;
                transition: background-color 0.3s ease;

                &.disabled {
                    background-color: #ff689e85;
                    cursor: not-allowed;
                }
            }
        }
    }
}
