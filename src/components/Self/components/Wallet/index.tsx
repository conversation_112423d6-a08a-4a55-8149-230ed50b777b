import React from 'react';
import { View } from '@tarojs/components';
import { openUrl } from '@/utils/rpc';
import { throttle } from '@/utils';
import { getRechargeUrl } from '@/utils/appSourceAdapter';

import './index.scss';
import { useBalanceStore } from '@/store/balanceStore';

const Wallet = () => {
    const balance = useBalanceStore((state) => state.balance);
    const jumpToRecharge = throttle(() => {
        openUrl(getRechargeUrl());
    }, 500);

    return (
        <View className="m-self-wallet" onClick={jumpToRecharge}>
            <View className="wallet-left">
                <View className="wallet-title">我的钱包</View>
                <View className="wallet-item">
                    <View className="item-label" />
                    <View className="item-value">{balance?.balance}</View>
                </View>
            </View>
            <View className="wallet-right">
                <View className="wallet-button">充值</View>
            </View>
        </View>
    );
};

export default React.memo(Wallet);
