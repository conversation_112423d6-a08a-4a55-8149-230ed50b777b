import { create } from 'zustand';
import { apiInviteConfig } from '../api';

interface InviteConfig {
    inviteEntryEnable: boolean;
    inviteCodeBindEnable: boolean;
    inviteCodeBindExpireTime: number;
}

interface SelfStore {
    inviteCodeBindVisible: boolean;
    inviteEntryEnable: boolean;
    refreshInviteConfig: () => void;
}

const useInviteStore = create<SelfStore>((set, get) => ({
    inviteEntryEnable: false,
    inviteCodeBindVisible: false,

    refreshInviteConfig: async () => {
        const inviteConfig = (await apiInviteConfig()) as InviteConfig;
        const inviteCodeBindVisible =
            inviteConfig.inviteCodeBindEnable && inviteConfig.inviteCodeBindExpireTime > Date.now();
        set({ inviteEntryEnable: inviteConfig.inviteEntryEnable, inviteCodeBindVisible });
    },
}));

export default useInviteStore;
