import fetch from '@/utils/fetch';

// 更新用户基本资料信息
export const apiUpdateUserBaseInfo = (data: any) =>
    fetch('/api/mirth/user/center/update', {
        method: 'post',
        data,
    });

// 新老弹窗
export const apiGetFirstEnterPopupInfo = (data: any) =>
    fetch('/api/mirth/user/aigc/entrance/popup/info', {
        method: 'post',
        data,
    });

// 邀请有礼入口配置
export const apiInviteConfig = () =>
    fetch('/api/mirth/user/aigc/invite/client', { method: 'post' });

// 邀请有礼绑定邀请码
export const apiInviteCodeBind = (data: any) =>
    fetch('/api/mirth/user/invite/bind', {
        method: 'post',
        data,
    });
