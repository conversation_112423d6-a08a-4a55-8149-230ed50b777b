import React, { useEffect } from 'react';
import TitleBar from '@/components/titleBar';
import TaroSafeAreaView from '@/components/safe-area-view';
import { pageDidAppear } from '@/utils/rpc';
import EventTrackView from '@/components/EventTrack';
import useUserInfoStore from '@/store/useUserInfoStore';
import { useBalanceStore } from '../../store/balanceStore';
import Figure from './components/Figure';
import Wallet from './components/Wallet';
import Entry from './components/Entry';
import TopBg from './components/TopBg';
import { TabItemStateProps } from '../Home/TabItemProps';

import './index.scss';

const Self = ({ selected }: TabItemStateProps) => {
    useEffect(() => {
        if (selected) {
            useUserInfoStore.getState().reuestApi();
        }

        pageDidAppear(() => {
            useBalanceStore.getState().requestBalance();
        });
    }, [selected]);

    return (
        <TaroSafeAreaView className="m-self-page">
            <EventTrackView
                params={{
                    _spm: 'page_ai_my|page_h5_biz',
                }}
            />
            <TopBg />
            <TitleBar title="我的" hasRight={false} />
            <Figure />
            <Wallet />
            <Entry />
        </TaroSafeAreaView>
    );
};

export default React.memo(Self);
