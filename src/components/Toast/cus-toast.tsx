/* eslint-disable no-nested-ternary */
import React, { useState, useEffect } from 'react';
import classNames from 'classnames';
import { View, Image } from '@tarojs/components';
import ICON_CLOCK from '@/assets/self-power/icon_clock.png';
import ICON_ERROR from '@/assets/self-power/icon_toast_error.png';
import ICON_SUCCESS from '@/assets/self-power/icon_toast_success.png';

import './index.scss';

const CusToast = ({
    message,
    duration = 3000,
    onClose,
    status = 'success',
    position = 'top',
}: {
    message: string;
    duration?: number;
    status?: 'success' | 'error' | 'loading';
    position?: 'top' | 'bottom' | 'left' | 'right' | 'center';
    onClose?: () => void;
}) => {
    const [visible, setVisible] = useState(false);
    const [isExiting, setIsExiting] = useState(false);

    const toastIcon =
        status === 'success' ? ICON_SUCCESS : status === 'error' ? ICON_ERROR : ICON_CLOCK;

    useEffect(() => {
        if (message) {
            setVisible(true);
            setIsExiting(false);
            const timer = setTimeout(() => {
                setIsExiting(true);
                setTimeout(() => {
                    setVisible(false);
                    if (onClose) onClose();
                }, 100);
            }, duration);

            return () => clearTimeout(timer);
        }

        return () => {};
    }, [message, duration, onClose]);

    if (!visible) return null;

    return (
        <View
            className={classNames('m-cus-toast', {
                'toast-exit': isExiting,
                'toast-top': position === 'top',
                'toast-bottom': position === 'bottom',
                'toast-left': position === 'left',
                'toast-right': position === 'right',
                'toast-center': position === 'center',
            })}>
            <Image src={toastIcon} className="toast-icon" />
            <View className="toast-message">{message}</View>
        </View>
    );
};

export default React.memo(CusToast);
