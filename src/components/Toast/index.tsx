import React, { useState, useEffect, useRef } from 'react';
import { eventCenter } from '@tarojs/taro';
import CusToast from './cus-toast';

// 定义事件名称
const SHOW_TOAST_EVENT = 'SHOW_CUSTOM_TOAST';
const HIDE_TOAST_EVENT = 'HIDE_CUSTOM_TOAST';

// Toast组件，用于在页面中渲染
export const ToastContainer: React.FC = () => {
    const [message, setMessage] = useState<string | null>(null);
    const [status, setStatus] = useState<'success' | 'error' | 'loading'>('success');
    const [position, setPosition] = useState<'top' | 'bottom' | 'left' | 'right' | 'center'>('top');
    const timerRef = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
        // 监听显示Toast事件
        const showListener = (data: {
            msg: string;
            duration: number;
            status: 'success' | 'error' | 'loading';
            pos: 'top' | 'bottom' | 'left' | 'right' | 'center';
        }) => {
            setMessage(data.msg);
            setStatus(data.status);
            setPosition(data.pos);

            if (timerRef.current) {
                clearTimeout(timerRef.current);
            }

            // 设置自动隐藏
            timerRef.current = setTimeout(() => {
                setMessage(null);
                timerRef.current = null;
            }, data.duration);
        };

        // 监听隐藏Toast事件
        const hideListener = () => {
            setMessage(null);
            if (timerRef.current) {
                clearTimeout(timerRef.current);
                timerRef.current = null;
            }
        };

        eventCenter.on(SHOW_TOAST_EVENT, showListener);
        eventCenter.on(HIDE_TOAST_EVENT, hideListener);

        return () => {
            eventCenter.off(SHOW_TOAST_EVENT, showListener);
            eventCenter.off(HIDE_TOAST_EVENT, hideListener);
            if (timerRef.current) {
                clearTimeout(timerRef.current);
            }
        };
    }, []);

    // 如果没有消息，不渲染任何内容
    if (!message) return null;

    return <CusToast message={message} status={status} position={position} />;
};

// Toast API
const toast = {
    show: ({
        msg,
        duration = 2000,
        status = 'success',
        pos = 'top',
    }: {
        msg: string;
        duration?: number;
        status?: 'success' | 'error' | 'loading';
        pos?: 'top' | 'bottom' | 'left' | 'right' | 'center';
    }) => {
        // 通过事件中心触发显示事件
        eventCenter.trigger(SHOW_TOAST_EVENT, { msg, duration, status, pos });
    },
    hide: () => {
        // 通过事件中心触发隐藏事件
        eventCenter.trigger(HIDE_TOAST_EVENT);
    },
    // 便捷方法
    success: (msg: string, duration = 2000) => {
        toast.show({ msg, status: 'success', duration });
    },
    error: (msg: string, duration = 2000) => {
        toast.show({ msg, status: 'error', duration });
    },
    loading: (msg: string, duration = 0) => {
        toast.show({ msg, status: 'loading', duration: duration || 3000 });
    },
};

export default toast;
