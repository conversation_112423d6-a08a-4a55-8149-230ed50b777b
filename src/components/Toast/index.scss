.m-cus-toast {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    z-index: 90;
    height: 40px;
    padding-left: 12px;
    padding-right: 12px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    min-width: 100px;
    background: #fff;
    box-shadow: 0 0 10px 0 rgba(165, 165, 165, 0.3);
    animation: toastFadeIn 0.1s linear;

    &.toast-top {
        top: calc(20px + constant(safe-area-inset-top));
        top: calc(20px + env(safe-area-inset-top));
        left: 50%;
        transform: translateX(-50%);
    }

    &.toast-bottom {
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
    }

    &.toast-left {
        left: 20px;
        top: 50%;
        transform: translateY(-50%);
    }

    &.toast-right {
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
    }

    &.toast-center {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    &.toast-exit {
        animation: toastFadeOut 0.1s ease-out;
    }

    .toast-icon {
        height: 16px;
        width: 16px;
    }

    .toast-message {
        margin-left: 6px;
        font-size: 13px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.6);
    }

    @keyframes toastFadeIn {
        from {
            opacity: 0;
            transform: translate(-50%, calc(30px));
        }

        to {
            opacity: 1;
            transform: translate(-50%, 0);
        }
    }

    @keyframes toastFadeOut {
        from {
            opacity: 1;
        }

        to {
            opacity: 0;
        }
    }
}
