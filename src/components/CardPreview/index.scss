.m-cardview {
    width: 100%;
    height: 100%;
    z-index: 1000;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;

    .card-filter {
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.47);
        backdrop-filter: blur(45.970943450927734px);
        z-index: 0;
    }

    .close-btn {
        width: 26px;
        height: 26px;
        position: absolute;
        top: 53px;
        right: 20px;
    }

    .card-content {
        margin-top: 60vh;
        padding: 0 27px;
        color: #fff;
        font-weight: 400;
        z-index: 10;
        position: relative;
    }

    .card-title {
        font-family: 'SourceHanSerifCN-Bold', sans-serif;
        font-size: 30px;
    }

    .card-name {
        font-family: 'SourceHanSerifCN-Bold', sans-serif;
        font-size: 26px;
        margin-top: -10px;
    }

    .subtitle-icon {
        position: absolute;
        top: 55px;
        left: 27px;
        width: 215px;
        height: 27px;
    }

    .lock-icon {
        width: 22px;
        height: 22px;
        margin-top: 22px;
    }

    .card-desc {
        font-size: 12px;
        margin-top: 25px;
        line-height: 18px;
        color: rgba(255, 255, 255, 0.7);
    }
}
