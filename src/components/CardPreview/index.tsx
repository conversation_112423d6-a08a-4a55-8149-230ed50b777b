import React from 'react';
import { View, Image } from '@tarojs/components';
import { LotteryRewardAward } from 'src/pages/gacha/store/useLotteryRewardStore';
import RootPortal from '@/components/RootPortal';
import CloseTransparentIcon from '@/assets/common/close_transparent_icon.png';
import LockIcon from '@/assets/common/lock_icon.png';
import SubtitleIcon from '@/assets/game/gacha/subtitle_lottery.png';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { CreateModalProps } from '../dialog';

import './index.scss';

/**
 * @description 卡片预览组件
 * @param {LotteryRewardAward} data 卡片数据
 * @param {Function} onClose 关闭回调
 * @param {boolean} isLock 是否锁定
 * @returns {React.ReactNode} 卡片预览组件
 */
const CardPreview = (
    {
        data,
        onClose,
    }: {
        data: LotteryRewardAward;
        onClose: () => void;
    } = {
        data: {
            isLock: true,
        },
        onClose: () => {},
    }
) => {
    return (
        <RootPortal>
            <View className="m-cardview" style={{ backgroundImage: `url(${data?.rewardIcon})` }}>
                {data?.isLock && <View className="card-filter" />}
                <Image className="close-btn" onClick={onClose} src={CloseTransparentIcon} />
                <View className="card-content">
                    <View className="card-title">{data?.rewardLevel}</View>
                    <View className="card-name">{data?.rewardName}</View>
                    <Image className="subtitle-icon" src={SubtitleIcon} />
                    {data?.isLock ? (
                        <Image className="lock-icon" src={LockIcon} />
                    ) : (
                        <View className="card-desc">{data?.rewardDesc}</View>
                    )}
                </View>
            </View>
        </RootPortal>
    );
};

export default React.memo(CardPreview);

export const CardPreviewModal: CreateModalProps = {
    type: 'modal_custom',
    isModal: true,
    render(dismiss, data, onClose) {
        return <CardPreview data={data} onClose={onClose} />;
    },
};
