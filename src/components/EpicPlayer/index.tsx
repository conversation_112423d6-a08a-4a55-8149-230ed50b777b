import React, { useCallback, useRef } from 'react';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import EpicPlayer from '@music/epic-player-sdk/react';
import useEpicPlayerStore from '@/store/useEpicPlayerStore';
import useChapterGuideStore from '@/store/useChapterGuideStore';
import { coronaWarnMessage } from '@music/mat-base-h5';
import useUserInfoStore from '@/store/useUserInfoStore';
import '@music/epic-player-sdk/style.css';
import domAdapter from '@/utils/adapter/domAdapter';

export interface IEpicPageData {
    roleId: string;
    chapterId: string;
}

const EpicPage = () => {
    const testHost = 'http://api-qa.mirth.netease.com';
    const playerRef = useRef<EpicPlayer.Ref>(null);

    const isVisible = useEpicPlayerStore((state) => state.isVisible);
    const roleId = useEpicPlayerStore((state) => state.roleId);
    const chapterId = useEpicPlayerStore((state) => state.chapterId);
    const recordId = useEpicPlayerStore((state) => state.recordId);
    const showSkipButton = useEpicPlayerStore((state) => state.showSkipButton);
    const hideEpicPlayer = useEpicPlayerStore((state) => state.hideEpicPlayer);

    const endEpicPlayer = useCallback(() => {
        Taro.eventCenter.trigger('EpicPlayerPlayEnd');
        hideEpicPlayer(false, true);
        playerRef.current?.engine?.reset();
        playerRef.current?.engine?.pause();
    }, [playerRef, hideEpicPlayer]);

    if (!isVisible || !roleId || !chapterId) {
        return null;
    }

    return (
        <EpicPlayer
            ref={playerRef}
            className="absolute top-0 left-0 w-full h-full z-[9999]"
            autoInit
            autoStart
            autoLoad
            userInfo={{
                userId: useUserInfoStore.getState().userBaseInfo?.userBase?.userId ?? null,
                nickname: useUserInfoStore.getState().userBaseInfo?.userBase?.nickname ?? '',
                avatarImgUrl:
                    useUserInfoStore.getState().userBaseInfo?.userBase?.avatarImgUrl ?? '',
            }}
            options={{
                unmuteHack: true,
                apiHost: API_HOST || testHost,
                basePath: `/${
                    getCurrentInstance().router?.path.split('/')[1] ?? 'st-mirth-aichat'
                }/epic-player-sdk`,
                logLevel: EpicPlayer.LogLevel.SILLY,
                confirmOnQuit: false,
                showRecordButton: false,
                showSkipButton,
            }}
            onReady={() => {
                // console.log('>>>>> EpicPlayer is READY <<<<<');
            }}
            onError={(error) => {
                coronaWarnMessage('EpicPlayer', error);
                hideEpicPlayer(true, false);
                useChapterGuideStore.getState().endGuideForError();
            }}
            onLoadingFinished={() => {
                domAdapter.blurActiveElement();
            }}
            onGameEnd={() => {
                Taro.eventCenter.trigger('EpicGameEnd', {
                    roleId,
                    chapterId,
                } as IEpicPageData);
            }}
            onGameSkip={() => {
                endEpicPlayer();
            }}
            onGameQuit={(type) => {
                endEpicPlayer();
                if (type === 'NAVIGATE_BACK' && useEpicPlayerStore.getState().backToChat) {
                    // 初遇剧情点返回时不进入聊天
                    Taro.navigateBack();
                }
            }}
            src={{
                roleId,
                chapterId,
                chapterPlayRecordId: recordId,
            }}
        />
    );
};

export default React.memo(EpicPage);
