import React from 'react';
import classNames from 'classnames';
import { Image } from '@tarojs/components';
import { optimizeImage } from '@/utils/image';
import Taro from '@tarojs/taro';
import './index.scss';

interface AvatarProps {
    src: string; // 图片地址
    width?: number; // 可选的宽度
    height?: number; // 可选的高度
    className?: string; // 可选的类名
    onClick?: () => void;
    lazyload?: boolean;
}

const Avatar = ({
    src,
    className,
    width = 40,
    height = 40,
    onClick = undefined,
    lazyload = false,
}: AvatarProps) => {
    const optimizedSrc = optimizeImage({
        src,
        width,
        height,
    });

    // TODO: Taro 3.x 不支持 width height
    return (
        <Image
            lazyLoad={lazyload}
            className={classNames('cus-avatar', className)}
            src={optimizedSrc}
            // width={width}
            // height={height}
            style={{
                width: `${Taro.pxTransform(width)}`,
                height: `${Taro.pxTransform(height)}`,
            }}
            onClick={onClick}
        />
    );
};

export default Avatar;
