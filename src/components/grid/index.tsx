import React from 'react';
import classNames from 'classnames';

/**
 * 居中Grid~~
 * @param param0
 * @returns
 */
const ResponsiveGrid = ({ items, renderItem }) => {
    const safeItems = Array.isArray(items) ? items : [];

    return (
        <div className="flex flex-wrap justify-center gap-x-4 gap-y-8">
            {/* 修改为居中 */}
            {safeItems.map((item, index) => {
                return (
                    <div
                        key={item.id}
                        className={classNames(
                            'flex',
                            'justify-center',
                            'items-center',
                            'flex-col',
                            'basis-[calc(25%-12px)]',
                            'lg:basis-[calc(25%-12px)]',
                            'md:basis-[calc(33.333%-12px)]',
                            'sm:basis-[calc(50%-12px)]',
                            {
                                // 移除最后一行特殊样式
                                '!basis-auto': false, // 禁用旧逻辑
                            }
                        )}>
                        {renderItem(item)}
                    </div>
                );
            })}
        </div>
    );
};
export default React.memo(ResponsiveGrid);
