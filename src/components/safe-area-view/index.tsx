/* eslint-disable react/require-default-props */
/* eslint-disable @typescript-eslint/naming-convention */
import { View } from '@tarojs/components';
import { getCurrentInstance } from '@tarojs/taro';
import { isAndroid } from '@/utils';
import React, { CSSProperties, ReactNode } from 'react';
import './index.scss';
import { NativeSafeAreaViewProps } from 'react-native-safe-area-context';

// NOTE: 这里兼容顶部导航栏用到了paddingtop, 因此子元素容器不要设置paddingTop，不然会覆盖兼容代码

let SafeAreaView: unknown;
if (process.env.TARO_ENV === 'rn') {
    // eslint-disable-next-line global-require, @typescript-eslint/no-var-requires
    SafeAreaView = require('react-native-safe-area-context').SafeAreaView;
}

let statusBarHeightCache: number | null = null;
let safeAreaHeightCache: number | null = null;

/**
 * 通过url中的参数获取状态栏高度
 * @returns 状态栏高度
 */
export const getStatusBarHeight = () => {
    if (statusBarHeightCache) {
        return statusBarHeightCache;
    }
    let statusBarHeight = 0;
    const { sys_sbh, sys_dpr } = getCurrentInstance().router?.params || {};
    if (sys_sbh && sys_dpr) {
        const sbh = Number(sys_sbh);
        const dpr = Number(sys_dpr);
        if (sbh && dpr && sbh > 0 && dpr > 0) {
            statusBarHeight = sbh / dpr;
        }
    }
    if (statusBarHeight > 0) {
        statusBarHeightCache = statusBarHeight;
        return statusBarHeight;
    }
    return 0;
};

export const getSafeAreaHeight = () => {
    if (safeAreaHeightCache) {
        return safeAreaHeightCache;
    }

    let safeAreaHeight = 0;
    const { sys_sbd, sys_dpr } = getCurrentInstance().router?.params || {};

    if (sys_sbd && sys_dpr) {
        const sbd = Number(sys_sbd);
        const dpr = Number(sys_dpr);

        safeAreaHeight = sbd / dpr;
    }

    if (safeAreaHeight > 0) {
        safeAreaHeightCache = safeAreaHeight;
        return safeAreaHeight;
    }

    return 0;
};

interface TaroSafeAreaViewProps extends NativeSafeAreaViewProps {
    className?: string;
    style?: CSSProperties;
    children: ReactNode;
}

const TaroSafeAreaView = ({
    className = '',
    style = {},
    edges = ['right', 'bottom', 'left'],
    children,
}: TaroSafeAreaViewProps) => {
    // console.log('TaroSafeAreaView-->statusBarStyle', statusBarStyle);

    if (process.env.TARO_ENV === 'rn') {
        return (
            <SafeAreaView edges={edges} className={`safe-area-view ${className}`} style={style}>
                {children}
            </SafeAreaView>
        );
    }

    const mergedStyle = {
        ...(style || {}),
        ...(isAndroid ? { paddingTop: getStatusBarHeight() } : {}),
    } as CSSProperties;

    return (
        <View className={`safe-area-view ${className}`} style={mergedStyle}>
            {children}
        </View>
    );
};

export default React.memo(TaroSafeAreaView);
