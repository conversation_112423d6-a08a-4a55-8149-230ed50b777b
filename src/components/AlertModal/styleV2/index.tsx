import { View, Image } from '@tarojs/components';
import React, { useCallback } from 'react';
import classNames from 'classnames';
import { alertFuncHStyle, alertFuncVStyle, AlertModalModalProps } from '../const';
import './index.scss';

const AlertModal = ({
    children,
    closeImg,
    title,
    desc,
    onLeftFuncClick,
    onRightFuncClick,
    leftFuncTxt,
    leftFuncImageRes,
    rightFuncTxt,
    rightFuncImageRes,
    fromBottom,
    funcStyle = fromBottom ? alertFuncVStyle : alertFuncHStyle,
    closeOnClickOverlay,
    bgReversal,
    dismiss,
}: AlertModalModalProps) => {
    // console.log('AlertModal-->render');

    const onClickBg = useCallback(() => {
        if (closeOnClickOverlay) {
            dismiss?.();
        }
    }, [dismiss, closeOnClickOverlay]);

    const handleLeftClick = useCallback(() => {
        dismiss?.();
        onLeftFuncClick?.();
    }, [dismiss, onLeftFuncClick]);

    const handleRightClick = useCallback(() => {
        dismiss?.();
        onRightFuncClick?.();
    }, [dismiss, onRightFuncClick]);

    const handleCloseClick = useCallback(() => {
        dismiss?.();
    }, [dismiss]);

    return (
        <View
            className={classNames('alert-root', { [funcStyle.alertRoot]: true })}
            onClick={onClickBg}>
            <View className={classNames('container', { [funcStyle.container]: true })}>
                <View className={classNames('bg', { [funcStyle.bg]: true })} />
                <Image
                    className={classNames('bg-mask', { [funcStyle.bgMask]: true })}
                    src={funcStyle.bgMaskImage}
                />
                <View className="relative">
                    {children}
                    {title && (
                        <View
                            className={classNames('title-space', { [funcStyle.titleSpace]: true })}
                        />
                    )}
                    {title &&
                        title.split('\n').map((line) => <View className="title">{line}</View>)}
                    {desc && desc.split('\n').map((line) => <View className="desc">{line}</View>)}
                    {fromBottom && <View className="space" />}
                    <View className={classNames('btn-group', { [funcStyle.funcContainer]: true })}>
                        {leftFuncTxt && (
                            <View
                                className={classNames('left', {
                                    [funcStyle.funcLeft]: true,
                                    'left--bg-reversal': bgReversal,
                                })}
                                onClick={handleLeftClick}>
                                {leftFuncTxt}
                            </View>
                        )}
                        {leftFuncImageRes && (
                            <Image src={leftFuncImageRes} onClick={handleLeftClick} />
                        )}
                        {rightFuncTxt && (
                            <View
                                className={classNames('right', {
                                    [funcStyle.funcRight]: true,
                                    'right--bg-reversal': bgReversal,
                                })}
                                onClick={handleRightClick}>
                                {rightFuncTxt}
                            </View>
                        )}
                        {rightFuncImageRes && (
                            <Image src={rightFuncImageRes} onClick={handleRightClick} />
                        )}
                    </View>
                    {closeImg && (
                        <Image src={closeImg} className="close" onClick={handleCloseClick} />
                    )}
                </View>
            </View>
        </View>
    );
};

export default React.memo(AlertModal);
