/* eslint-disable import/prefer-default-export */
import { CreateModalProps } from '@/components/dialog';
import React from 'react';

import { IFuncStyle } from './const';
import AlertModalV2 from './styleV2';
import AlertModalV1 from './styleV1';

export const createAlertModalProvider = (options?: {
    fromBottom?: boolean; // 是否显示在底部~
    styleType?: string; // 是否是新样式~
    type?: 'modal' | 'float' | 'modal_custom';
    funcStyle?: IFuncStyle; // 相关样式~
    closeOnClickOverlay?: boolean; // 点击其他区域是否关闭~
    bgReversal?: boolean; // 按钮的背景是否需要配色翻转~
}): CreateModalProps => {
    return {
        type: options?.type ?? options?.fromBottom ? 'float' : 'modal',
        isModal: true,
        render(dismiss, confirmProps) {
            return options?.styleType === 'v2' ? (
                <AlertModalV2
                    {...confirmProps}
                    dismiss={dismiss}
                    fromBottom={options?.fromBottom}
                    funcStyle={options?.funcStyle}
                    closeOnClickOverlay={options?.closeOnClickOverlay}
                    bgReversal={options?.bgReversal}
                />
            ) : (
                <AlertModalV1
                    {...confirmProps}
                    dismiss={dismiss}
                    fromBottom={options?.fromBottom}
                    funcStyle={options?.funcStyle}
                    closeOnClickOverlay={options?.closeOnClickOverlay}
                    bgReversal={options?.bgReversal}
                />
            );
        },
    };
};
