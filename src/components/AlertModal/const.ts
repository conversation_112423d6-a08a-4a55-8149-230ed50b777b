// eslint-disable-next-line max-classes-per-file
import mask_dialog_style_mask_h from '@/assets/common/mask_dialog_style_mask_h.png';
import mask_dialog_style_mask_v from '@/assets/common/mask_dialog_style_mask_v.png';

export interface IFuncStyle {
    alertRoot: string;
    container: string;
    bg: string;
    bgMask: string;
    bgMaskImage: string;
    titleSpace: string;
    funcContainer: string;
    funcLeft: string;
    funcRight: string;
}

export class AlertFuncHStyle implements IFuncStyle {
    alertRoot = 'alert-root--center';

    container = 'container--h';

    bg = 'bg--h';

    bgMask = 'bg-mask--h';

    bgMaskImage = mask_dialog_style_mask_h;

    titleSpace = 'title-space--h';

    funcContainer = 'btn-group--h';

    funcLeft = 'left--h';

    funcRight = 'right--h';
}

export class AlertFuncVStyle implements IFuncStyle {
    alertRoot = 'alert-root--bottom';

    container = 'container--v';

    bg = 'bg--v';

    bgMask = 'bg-mask--v';

    bgMaskImage = mask_dialog_style_mask_v;

    titleSpace = 'title-space--v';

    funcContainer = 'btn-group--v';

    funcLeft = 'left--v';

    funcRight = 'right--v';
}

export const alertFuncVStyle = new AlertFuncVStyle();
export const alertFuncHStyle = new AlertFuncHStyle();

export interface ConfirmProps {
    children?: React.ReactNode;
    /**
     * 白底可以使用 @/assets/alert/alert_close.png~
     */
    closeImg?: string;
    title: string;
    desc: string;
    leftFuncTxt?: string;
    leftFuncImageRes?: string;
    rightFuncTxt?: string;
    rightFuncImageRes?: string;
    onLeftFuncClick?: () => void;
    onRightFuncClick?: () => void;
    fromBottom?: string;
    funcStyle?: IFuncStyle;
    closeOnClickOverlay?: boolean;
    bgReversal?: boolean;
}

export type AlertModalModalProps = ConfirmProps & {
    dismiss: () => void;
};
