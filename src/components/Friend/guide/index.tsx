/* eslint-disable @typescript-eslint/no-shadow */
import bubbleIcon from '@/assets/friend/icon-guide-bubble.png';
import Avatar from '@/components/avatar';
import { Image, Text, View } from '@tarojs/components';
import Taro, { createSelectorQuery } from '@tarojs/taro';
import classNames from 'classnames';
import React, { useCallback, useEffect } from 'react';
import { AtModal } from 'taro-ui';
import usePopupStore from '@/store/Popup/usePopupStore';
import { PopupBusinessKey } from '@/store/Popup/type';

import useFriendStore, { FriendGuideMeta } from '../useFriendStore';
import './index.scss';

export interface FriendGuideProps {
    data: FriendGuideMeta;
}

const FriendGuide: React.FC<FriendGuideProps> = ({ data }: FriendGuideProps) => {
    const [top, setTop] = React.useState(0);

    const onClose = useCallback(() => {
        useFriendStore.getState().setGuide(undefined);
        usePopupStore.getState().completePopup(PopupBusinessKey.APP_GUIDE);
    }, []);

    useEffect(() => {
        Taro.nextTick(() => {
            const query = createSelectorQuery();
            query.select(`#friend-${data.userId}`).boundingClientRect();
            query.exec((res) => {
                if (!res || !res[0]) {
                    return;
                }
                const target = res[0];
                const contentQuery = createSelectorQuery();
                contentQuery.select(`#friend-guide-content`).boundingClientRect();
                contentQuery.exec((contentRes) => {
                    // 检查第二个查询结果是否存在
                    if (!contentRes || !contentRes[0]) {
                        return;
                    }

                    const top = target.bottom - contentRes[0].bottom;
                    setTop(top);
                });
            });
        });
    }, [data.userId]);

    return (
        <AtModal className="friend-guide-wrapper" isOpened onClose={onClose}>
            <View className="content-wrapper" onClick={onClose}>
                <View
                    id="friend-guide-content"
                    className="content-wrapper"
                    style={{ marginTop: top + 2 }}>
                    <View className="top-wrapper">
                        <View className="top-avatar-wrapper">
                            <Avatar
                                className="top-avatar"
                                src={data.topAvatar}
                                width={44}
                                height={44}
                                lazyload
                            />
                        </View>
                        <Text className="content">{data.topContent}</Text>
                        <Text className="confirm" onClick={onClose}>
                            好的
                        </Text>
                    </View>
                    <Image className="bubble" src={bubbleIcon} />
                    <View className="user-wrapper">
                        <View
                            className={classNames('item-friend', {
                                guide: true,
                            })}>
                            <Avatar
                                className="avatar"
                                src={data.userAvatar}
                                width={60}
                                height={60}
                                lazyload
                            />
                            <View className="center">
                                <Text className="name">{data.userName}</Text>
                                <Text className="content">{data.userSign}</Text>
                            </View>
                        </View>
                    </View>
                </View>
            </View>
        </AtModal>
    );
};

export default React.memo(FriendGuide);
