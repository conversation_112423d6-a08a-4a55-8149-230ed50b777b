.friend-guide-wrapper {
    width: 100%;
    height: 100%;

    .at-modal__overlay {
        background-color: rgba(0, 0, 0, 0.7);
    }

    .at-modal__container {
        width: 100%;
        background-color: transparent;
        top: 0;
        left: 0;
        transform: none;
    }

    .content-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        padding-top: 30px;

        .top-wrapper {
            width: calc(100% - 84px);
            border-radius: 12px;
            opacity: 1;
            background-color: #fff;
            min-height: 140px;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0 20px 25px 20px;

            .top-avatar-wrapper {
                flex-direction: row;
                display: flex;
                width: 100%;

                .top-avatar {
                    box-sizing: border-box;
                    width: 44px;
                    height: 44px;
                    border-radius: 421px;
                    opacity: 1;
                    border: 2px solid #fff;
                    box-shadow: 0 0 10px 0 rgba(147, 147, 147, 0.3);
                    margin-top: -20px;
                    margin-left: -2px;
                }
            }

            .content {
                font-size: 14px;
                font-weight: normal;
                line-height: 150%;
                letter-spacing: 0;
                color: #000;
                margin-top: 16px;
            }

            .confirm {
                font-size: 13px;
                font-weight: 600;
                line-height: 30px;
                text-align: center;
                letter-spacing: 0;
                color: #fff;
                border-radius: 53px;
                opacity: 1;
                background: #ff689e;
                backdrop-filter: blur(10px);
                align-items: center;
                width: 74px;
                height: 30px;
                margin-top: 6px;
            }
        }
    }

    .bubble {
        width: 20px;
        height: 9px;
    }

    .user-wrapper {
        width: 100%;
        border-radius: 16px;
        opacity: 1;
        box-shadow: 0 0 12px 0 #fff8f3, inset 0 0 3px 0 #fff8f3, inset 0 0 3px 0 #fff8f3;
        padding: 15px;
    }

    .item-friend {
        display: flex;
        align-items: center;
        height: 80px;
        padding: 10px 20px;
        background-color: #fff;
        width: 100%;
        overflow: hidden;
        border-radius: 16px;

        .avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            flex-shrink: 0;
            user-select: none;
            -webkit-user-drag: none;
        }

        .center {
            flex: 1;
            display: flex;
            flex-direction: column;
            margin-left: 12px;
            overflow: hidden;
        }

        .name {
            font-size: 14px;
            font-weight: 600;
            line-height: normal;
            letter-spacing: 0;
            color: #26262a;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
        }

        .content {
            font-size: 12px;
            font-weight: normal;
            line-height: normal;
            letter-spacing: 0;
            color: #26262a;
            opacity: 0.4;
            margin-top: 6px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
            display: inline;
            text-align: left;
        }
    }
}
