/* eslint-disable no-console */
import { listFriendApi, listPotentialdApi, removePotentialdApi } from '@/service/friendApi';
import { showToast } from '@tarojs/taro';
import { create } from 'zustand';
import { AigcRobotPotentialContact } from './AigcRobotPotentialContact';
import { AigcRobotRelation } from './AigcRobotRelation';

export interface FriendGuideMeta {
    topAvatar: string;
    topContent: string;
    userId: number;
    userAvatar: string;
    userName: string;
    userSign: string;
}

export interface FriendStoreState {
    guide?: FriendGuideMeta;
    setGuide: (guide?: FriendGuideMeta) => void;
    relationList: AigcRobotRelation[];
    setRelationList: (list: AigcRobotRelation[]) => void;
    potentialList: AigcRobotPotentialContact[];
    setPotentialList: (list: AigcRobotPotentialContact[]) => void;
    removePotential: (userId: number) => void;
    fetchData: () => Promise<void>;
    loading: boolean;
}

const useFriendStore = create<FriendStoreState>((set, get) => {
    let isFetching = false;
    return {
        loading: true,
        guide: undefined,
        setGuide: (guide) => {
            set({ guide });
        },
        relationList: [],
        setRelationList: (list) => {
            set({ relationList: list });
        },
        potentialList: [],
        setPotentialList: (list) => {
            set({ potentialList: list });
        },
        removePotential: (userId) => {
            removePotentialdApi(userId)
                .then(() => {
                    const potentialList = get().potentialList;
                    const updatedList = potentialList.filter(
                        (item) => item.robotBaseInfo.userId !== userId
                    );
                    set({ potentialList: updatedList });
                })
                .catch((err) => {
                    showToast({
                        title: err.message,
                        icon: 'none',
                    });
                });
        },
        fetchData: async () => {
            if (isFetching) {
                return;
            }
            isFetching = true;

            try {
                const [relationRes, potentialRes] = await Promise.all([
                    listFriendApi(),
                    listPotentialdApi(),
                ]);

                set({
                    relationList: relationRes,
                    potentialList: potentialRes,
                });
            } catch (err) {
                showToast({
                    title: err.message,
                    icon: 'none',
                });
            } finally {
                isFetching = false;
                set({ loading: false });
            }
        },
    };
});

export default useFriendStore;
