.friend-page-wrapper {
    @import './index.rn.scss';
    background: linear-gradient(to bottom, #fff7f9, #fff7fa);
    height: 100%;
    width: 100vw;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .friend-list-bg {
        position: absolute;
        width: 100%;
        height: 278px;
        background-image: url('../../assets/common/icon-page-bg.jpg');
        background-repeat: no-repeat;
        background-size: cover;
        background-position: top center;
        top: 0;
    }

    .friend-window {
        height: 100%;
        width: 100vw;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: relative;

        .page-content {
            flex: 1;
            height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
            scrollbar-width: none;
            -ms-overflow-style: none;
            appearance: none;
            -webkit-appearance: none;

            &::-webkit-scrollbar {
                display: none;
            }
        }

        .contact-wrapper {
            margin-bottom: 10px;

            .contact-title {
                padding: 12px 20px;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;

                .title {
                    font-size: 14px;
                    font-weight: 600;
                    line-height: 100%;
                    color: #26262a;
                    opacity: 0.6;
                }

                .all {
                    font-size: 12px;
                    font-weight: normal;
                    line-height: normal;
                    color: rgba(38, 38, 42, 0.6);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    text-align: center;
                }

                .all::after {
                    content: '';
                    display: inline-block;
                    background-image: url('../../assets/friend/icon-right-arrow.png');
                    background-size: 10px 10px;
                    background-repeat: no-repeat;
                    background-position: center;
                    width: 10px;
                    height: 10px;
                    vertical-align: middle;
                    margin-left: 1px;
                }
            }
        }

        .horizontal-scroll {
            display: flex;
            flex-direction: row;
            width: 100%;
            margin-top: 3px;
            padding-left: 20px;
            padding-right: 20px;

            .scroll-item {
                position: relative;
                flex-shrink: 0;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                box-sizing: border-box;
                width: 231px;
                padding: 14px 15px;
                background-color: #fff;
                border-radius: 12px;
                margin-right: 20px;

                &:last-child {
                    margin-right: 0;
                }

                .close {
                    position: absolute;
                    top: 15px;
                    right: 15px;
                    width: 10px;
                    height: 10px;
                }

                .top {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    overflow: hidden;

                    .right {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        margin-left: 10px;
                        overflow: hidden;
                    }
                }

                .avatar {
                    width: 70px;
                    height: 70px;
                    flex-shrink: 0;
                    flex-grow: 0;
                    user-select: none;
                    -webkit-user-drag: none;
                }

                .name-wrapper {
                    display: flex;
                    align-items: center;
                    overflow: hidden;
                    padding-right: 25px;
                }

                .name {
                    font-size: 14px;
                    font-weight: 600;
                    line-height: 100%;
                    text-align: right;
                    letter-spacing: 0;
                    color: #26262a;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .gender {
                    width: 10px;
                    height: 10px;
                    margin-left: 4px;
                    flex-shrink: 0;
                }

                .info {
                    font-size: 12px;
                    font-weight: normal;
                    line-height: 100%;
                    letter-spacing: 0;
                    color: #26262a;
                    opacity: 0.4;
                    margin-top: 5px;
                }

                .friend-label-wrapper {
                    display: flex;
                    flex-direction: row;
                    flex-wrap: wrap;
                    overflow: hidden;
                    margin-top: 4px;
                    height: 17px;

                    .label {
                        font-size: 10px;
                        font-weight: normal;
                        line-height: 17px;
                        letter-spacing: 0;
                        color: #996b71;
                        padding: 0 5px;
                        margin-right: 4px;
                        border-radius: 3px;
                        background: #f8eeee;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        margin-bottom: 3px;
                        height: 17px;
                    }
                }

                .bottom {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    border-radius: 8px;
                    background: #fafafa;
                    padding-left: 12px;
                    padding-right: 6px;
                    height: 42px;
                    flex-shrink: 0;
                    margin-top: 12px;
                }

                .content {
                    flex: 1;
                    font-size: 14px;
                    font-weight: normal;
                    line-height: 150%;
                    letter-spacing: 0;
                    color: #26262a;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    align-items: flex-start;
                }

                .reply {
                    font-size: 12px;
                    font-weight: 600;
                    line-height: 150%;
                    letter-spacing: 0;
                    color: #fff;
                    border-radius: 168px;
                    background: #ff689e;
                    box-sizing: border-box;
                    padding: 4px 10px;
                    height: 26px;
                    margin-left: 2px;
                }
            }
        }

        .relation-wrapper {
            margin-top: 10px;
        }

        .custom-accordion {
            .at-accordion__header {
                background: linear-gradient(180deg, #fffcfd 68%, #f6edf3 100%);
                padding: 12px 20px;
                min-height: 41px;
                height: 41px;

                .at-accordion__info__title {
                    font-size: 12px;
                    font-weight: 600;
                    line-height: normal;
                    letter-spacing: 0;
                    color: #996b71;
                    opacity: 0.6;
                }

                .at-accordion__arrow {
                    font-size: 12px;
                }

                .at-accordion__arrow--folded {
                    -webkit-transform: rotate(-90deg);
                    -ms-transform: rotate(-90deg);
                    transform: rotate(-90deg);
                }

                .at-icon-chevron-down::before {
                    content: '';
                    display: inline-block;
                    background-image: url('../../assets/common/icon_fold.png');
                    background-size: 13px 12px;
                    background-repeat: no-repeat;
                    background-position: center;
                    width: 13px;
                    height: 13px;
                    vertical-align: middle;
                }
            }

            .at-accordion__header::after {
                content: '';
                position: absolute;
                -webkit-transform-origin: center;
                -ms-transform-origin: center;
                transform-origin: center;
                -webkit-box-sizing: border-box;
                box-sizing: border-box;
                pointer-events: none;
                top: -50%;
                left: -50%;
                right: -50%;
                bottom: -50%;
                border: 0 solid #d6e4ef;
                -webkit-transform: scale(0.5);
                -ms-transform: scale(0.5);
                transform: scale(0.5);
                border-bottom-width: 0;
            }

            .at-accordion__content::after {
                content: '';
                position: absolute;
                -webkit-transform-origin: center;
                -ms-transform-origin: center;
                transform-origin: center;
                -webkit-box-sizing: border-box;
                box-sizing: border-box;
                pointer-events: none;
                top: -50%;
                left: -50%;
                right: -50%;
                bottom: -50%;
                border: 0 solid #d6e4ef;
                -webkit-transform: scale(0.5);
                -ms-transform: scale(0.5);
                transform: scale(0.5);
                border-top-width: 0;
            }
        }

        .item-friend {
            display: flex;
            align-items: center;
            height: 80px;
            padding: 10px 20px;
            background-color: transparent;
            width: 100%;
            overflow: hidden;

            &.guide {
                background-color: #fff;
            }

            .avatar {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                flex-shrink: 0;
            }

            .center {
                flex: 1;
                display: flex;
                flex-direction: column;
                margin-left: 12px;
                overflow: hidden;
            }

            .name {
                font-size: 14px;
                font-weight: 600;
                line-height: normal;
                letter-spacing: 0;
                color: #26262a;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 100%;
            }

            .content {
                font-size: 12px;
                font-weight: normal;
                line-height: normal;
                letter-spacing: 0;
                color: #26262a;
                opacity: 0.4;
                margin-top: 6px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 100%;
                display: inline;
                text-align: left;
            }
        }
    }

    .custom-at-list {
        background-color: transparent;
    }
}
