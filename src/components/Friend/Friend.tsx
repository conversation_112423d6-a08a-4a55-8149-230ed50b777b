import Avatar from '@/components/avatar';
import EventTrackView from '@/components/EventTrack';
import TaroSafeAreaView from '@/components/safe-area-view';
import TitleBar from '@/components/titleBar';
import ErrorBoundary from '@/components/ErrorBoundary';
import useHomeStore, { HomeTabIndex } from '@/components/Home/useHomeStore';
import { AigcChatResourceInfo } from '@/types/bot';
import { optimizeImage } from '@/utils/image';
import { addClickLog } from '@/utils/logTool';
import { saveStartIndex } from '@/utils/rpc';
import { Image, ITouchEvent, ScrollView, Text, View } from '@tarojs/components';
import React, { useCallback, useEffect } from 'react';
import winAdapter from '@/utils/adapter/winAdapter';
import { AtAccordion, AtList } from 'taro-ui';
import { GENDER, getAge } from '@/types/AigcRobotProfile';
import { jump2Chat, jump2Profile } from '@/router';
import femaleIcon from '../../assets/friend/icon-female.png';
import maleIcon from '../../assets/friend/icon-male.png';
import closeIcon from '../../assets/friend/icon-right-close.png';
import { AigcRobotBaseInfo } from '../Explore/meta/AigcRobotRecommend';
import { TabItemStateProps } from '../Home/TabItemProps';
import { AigcRobotPotentialContact } from './AigcRobotPotentialContact';
import Guide from './guide';

import useFriendStore from './useFriendStore';
import './index.scss';

function getSHowAgeAndProfession(item: AigcRobotPotentialContact) {
    if (item.robotBaseInfo.profession) {
        return `${getAge(item.robotBaseInfo)}·${item.robotBaseInfo.profession}`;
    }
    return `${getAge(item.robotBaseInfo)}`;
}

const Friend = ({ selected, pageShow }: TabItemStateProps) => {
    const relationList = useFriendStore((state) => state.relationList);
    const potentialList = useFriendStore((state) => state.potentialList);
    const guide = useFriendStore((state) => state.guide);
    const loading = useFriendStore((state) => state.loading);

    useEffect(() => {
        useFriendStore.getState().fetchData();
    }, []);

    useEffect(() => {
        if (!loading) {
            useHomeStore.getState().hideLoading();
        }
    }, [loading]);

    useEffect(() => {
        if (selected) {
            saveStartIndex('friends');
            useFriendStore.getState().fetchData();
        }
    }, [selected]);

    useEffect(() => {
        if (pageShow) {
            useFriendStore.getState().fetchData();
        }
    }, [pageShow]);

    const handleFold = useCallback(
        (index: number) => {
            const updatedList = relationList.map((item, idx) => {
                if (idx === index) {
                    return { ...item, close: !item.close };
                }
                return item;
            });
            useFriendStore.getState().setRelationList(updatedList);
        },
        [relationList]
    );

    const onShowMore = useCallback(() => {
        useHomeStore.getState().setCurrent(HomeTabIndex.explore);
        addClickLog(
            'btn_ai_friends_recommend_all|mod_ai_firends_recommend|page_ai_friends|page_h5_biz'
        );
    }, []);

    const onGotoProfile = useCallback(
        (robotBaseInfo: AigcRobotBaseInfo, hasAiChapter: boolean | undefined = undefined) => {
            jump2Profile({
                robotUserId: `${robotBaseInfo.userId}`,
                hasAiChapter,
            });
        },
        []
    );

    const onGotoChat = useCallback(
        (
            event: ITouchEvent,
            robotBaseInfo: AigcRobotBaseInfo,
            aigcChatResourceInfo: AigcChatResourceInfo,
            hasAiChapter: boolean
        ) => {
            event.stopPropagation();
            addClickLog(
                'mod_ai_friends_recommend_reply|mod_ai_firends_recommend|page_ai_friends|page_h5_biz'
            );

            const optimizedUrl = optimizeImage({
                src: aigcChatResourceInfo?.botBackgroundUrl,
                width: 375,
                height: 812,
            });
            const optimizedMp4 = aigcChatResourceInfo?.botStandByUrls?.[0];
            const botContactUrl = aigcChatResourceInfo?.botContactUrl ?? '';
            jump2Chat({
                robotUserId: `${robotBaseInfo.userId}`,
                hasAiChapter: `${hasAiChapter}`,
                robotAccid: `${robotBaseInfo.accId}`,
                botBgUrl: `${optimizedUrl}`,
                botStandByUrl: `${optimizedMp4}`,
                botContactUrl: `${botContactUrl}`,
            })
        },
        []
    );

    const onClose = useCallback((event: ITouchEvent, contactItem: AigcRobotPotentialContact) => {
        event.stopPropagation();
        addClickLog(
            'btn_ai_friends_recommend_close|mod_ai_firends_recommend|page_ai_friends|page_h5_biz'
        );
        useFriendStore.getState().removePotential(contactItem.robotBaseInfo.userId);
    }, []);

    const showGuide =
        !!guide &&
        relationList.some((relation) =>
            relation.robotInfos?.some((item) => item.robotBaseInfo?.userId === guide?.userId)
        );

    return (
        <ErrorBoundary
            text="系统繁忙，点击任意区域可刷新页面"
            onClick={() => {
                winAdapter.reloadPage();
            }}>
            <TaroSafeAreaView className="friend-page-wrapper">
                <EventTrackView params={{ _spm: 'page_ai_friends|page_h5_biz' }}>
                    <View />
                </EventTrackView>
                <View className="friend-list-bg" />
                <View className="friend-window">
                    <TitleBar title="好友" source="friend" />
                    <View className="page-content">
                        {potentialList.length > 0 && (
                            <View className="contact-wrapper">
                                <View className="contact-title">
                                    <Text className="title">可能想认识的人</Text>
                                    <Text className="all" onClick={onShowMore}>
                                        全部
                                    </Text>
                                </View>
                                <ScrollView className="horizontal-scroll" scrollX>
                                    {potentialList.map((item: AigcRobotPotentialContact) => (
                                        <View
                                            key={item.robotBaseInfo.userId}
                                            className="scroll-item"
                                            onClick={() => {
                                                onGotoProfile(
                                                    item.robotBaseInfo,
                                                    item.hasAiChapter
                                                );
                                            }}>
                                            <View className="top">
                                                <Avatar
                                                    className="avatar"
                                                    src={item.robotBaseInfo.avatarUrl}
                                                    width={70}
                                                    height={70}
                                                    lazyload
                                                />
                                                <View className="right">
                                                    <View className="name-wrapper">
                                                        <Text className="name">
                                                            {item.robotBaseInfo.nickname}
                                                        </Text>
                                                        <Image
                                                            className="gender"
                                                            src={
                                                                item.robotBaseInfo.gender ===
                                                                GENDER.MALE
                                                                    ? maleIcon
                                                                    : femaleIcon
                                                            }
                                                        />
                                                    </View>
                                                    <Text className="info">
                                                        {`${getSHowAgeAndProfession(item)}`}
                                                    </Text>
                                                    <View className="friend-label-wrapper">
                                                        {item.robotBaseInfo?.labels?.map(
                                                            (label) => (
                                                                <Text key={label} className="label">
                                                                    {label}
                                                                </Text>
                                                            )
                                                        )}
                                                    </View>
                                                </View>
                                            </View>
                                            <View
                                                className="bottom"
                                                onClick={(event) =>
                                                    onGotoChat(
                                                        event,
                                                        item.robotBaseInfo,
                                                        item.aigcChatResourceInfo,
                                                        item.hasAiChapter
                                                    )
                                                }>
                                                <Text className="content">{item.greetings}</Text>
                                                <Text className="reply">回复</Text>
                                            </View>
                                            <Image
                                                className="close"
                                                src={closeIcon}
                                                onClick={(e) => onClose(e, item)}
                                            />
                                        </View>
                                    ))}
                                </ScrollView>
                            </View>
                        )}

                        <View className="relation-wrapper">
                            {relationList.map((item, index) => (
                                <AtAccordion
                                    key={item.relationName}
                                    isAnimation={false}
                                    className="custom-accordion"
                                    open={item.close !== true}
                                    onClick={() => handleFold(index)}
                                    title={`${item.relationName} ${item.robotInfos?.length || 0}`}>
                                    <AtList hasBorder={false} className="custom-at-list">
                                        {item.robotInfos?.map(({ robotBaseInfo }) => (
                                            <View
                                                key={robotBaseInfo.userId}
                                                id={`friend-${robotBaseInfo.userId}`}
                                                className="item-friend"
                                                onClick={() => {
                                                    onGotoProfile(robotBaseInfo);
                                                }}>
                                                <Avatar
                                                    className="avatar"
                                                    src={robotBaseInfo.avatarUrl}
                                                    width={60}
                                                    height={60}
                                                    lazyload
                                                />
                                                <View className="center">
                                                    <Text className="name">
                                                        {robotBaseInfo.nickname}
                                                    </Text>
                                                    <Text className="content">
                                                        {robotBaseInfo.intro}
                                                    </Text>
                                                </View>
                                            </View>
                                        ))}
                                    </AtList>
                                </AtAccordion>
                            ))}
                        </View>
                        {showGuide && <Guide data={guide} />}
                    </View>
                </View>
            </TaroSafeAreaView>
        </ErrorBoundary>
    );
};

export default Friend;
