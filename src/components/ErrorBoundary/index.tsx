import React from 'react';
import { coronaErrorMessage } from '@music/mat-base-h5';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';

import './index.scss';

interface IProps {
    fallback?: React.ReactNode;
    children: React.ReactNode;
    mode?: 'dark' | 'light';
    text?: string;
    full?: boolean;
    className?: string;
    needback?: boolean;
    onClick?: () => void;
}

interface IState {
    hasError: boolean;
}

class ErrorBoundary extends React.Component<IProps, IState> {
    constructor(props: IProps) {
        super(props);
        this.state = { hasError: false };
    }

    // 用于展示备用UI
    static getDerivedStateFromError() {
        return { hasError: true };
    }

    // 用于处理log
    componentDidCatch(error: any) {
        console.error('ErrorBoundary 捕获异常：', error);
        coronaErrorMessage(`ErrorBoundary 捕获异常：${error?.message}`, {
            message: `errMsg: ${error?.message} errCode: ${error?.code}`,
        });
    }

    render() {
        const { hasError } = this.state;
        const {
            mode = 'light',
            text,
            full = false,
            className,
            fallback,
            children,
            needback,
            onClick,
        } = this.props;

        if (hasError) {
            if (fallback) {
                return fallback;
            }

            return (
                <View
                    onClick={() => {
                        if (needback) {
                            Taro.navigateBack();
                        } else if (onClick) {
                            onClick?.();
                        }
                    }}
                    className={`base-errorboundary mode-${mode} ${
                        full ? 'full' : ''
                    } ${className}`}>
                    <View>{text || '内容显示出错'}</View>
                </View>
            );
        }

        return children;
    }
}

export default ErrorBoundary;
