import { DialogLayerRef } from './types';

export interface Dialog {
    id?: string;
    prior: number;
    blocking: boolean;
    isModal: boolean;
    dismiss: () => void;
    toggle: (hide: boolean) => void;
}

declare type Later = () => boolean;

export declare interface DialogConfig {
    prior?: number;
    blocking?: boolean;
    isModal?: boolean;
}

export class DialogQueue {
    private static instance: DialogQueue;

    private map = new Map<DialogLayerRef, Array<DialogWrapper>>();

    public static getInstance(): DialogQueue {
        if (!DialogQueue.instance) {
            DialogQueue.instance = new DialogQueue();
        }
        return DialogQueue.instance;
    }

    isBlock(host: DialogLayerRef, path: string, params: object | undefined, later: Later): boolean {
        if (!params) {
            return false;
        }
        const config = params as DialogConfig;
        const isModal = config.isModal ?? false;
        if (isModal) {
            return false;
        }
        const prior = config.prior ?? 100;
        const stack = this.map.get(host);
        if (!stack) {
            return false;
        }
        const top = stack[stack.length - 1];
        if (!top) {
            return false;
        }
        if (top.dialog.prior > prior || (top.dialog.prior === prior && top.dialog.blocking)) {
            top.laterArray.push(later);
            return true;
        }
        return false;
    }

    addDialog(host: DialogLayerRef, dialog: Dialog) {
        let stack = this.map.get(host);
        if (!stack) {
            stack = [];
            this.map.set(host, stack);
        }
        const top = stack[stack.length - 1];
        if (top) {
            top.dialog.toggle(true);
        }
        const add = new DialogWrapper(dialog);
        if (top?.dialog.id === 'fake') {
            add.laterArray.push(...top.laterArray);
            stack.pop();
        }
        stack.push(add);
    }

    removeDialog(host: DialogLayerRef, dialog: Dialog) {
        const stack = this.map.get(host);
        if (!stack) {
            return;
        }
        const top = stack[stack.length - 1];
        if (!top) {
            // 按理不会出现这样的情况。但是NavPathStack可能和Dialog不是一个容器。就会导致这个问题。
            return;
        }
        if (top.dialog === dialog) {
            stack.pop();
            let next = stack[stack.length - 1];
            let laterJump = false;
            if (top.laterArray.length > 0) {
                while (top.laterArray.length > 0) {
                    const later = top.laterArray.shift();
                    if (!laterJump && later) {
                        laterJump = later();
                    } else if (later) {
                        if (!next) {
                            next = new DialogWrapper(fakeDialog());
                            stack.push(next);
                        }
                        if (next) {
                            next.laterArray.push(later);
                        }
                    }
                }
            }
            if (!laterJump && next) {
                next.dialog.toggle(false);
            }
        } else {
            const found = stack.find((item) => {
                return item.dialog === dialog;
            });
            if (!found) {
                return;
            }
            const index = stack.indexOf(found);
            if (index !== -1) {
                stack.splice(index, 1);
            }
            top.laterArray.push(...found.laterArray);
        }
    }
}

class DialogWrapper {
    dialog: Dialog;

    laterArray: Array<Later> = [];

    constructor(dialog: Dialog) {
        this.dialog = dialog;
    }
}

function fakeDialog(): Dialog {
    return {
        id: 'fake',
        prior: 0,
        blocking: false,
        isModal: false,
        dismiss: () => {},
        toggle: (hide: boolean) => {},
    };
}
