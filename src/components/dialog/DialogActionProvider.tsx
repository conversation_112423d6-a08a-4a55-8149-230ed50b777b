import React from 'react';

export const DialogAction = React.createContext<{
    dismiss?: () => void;
}>({});

export interface StoreContextDialogProps {
    children: React.ReactNode;
    dismiss: () => void;
}

const DialogActionProvider = ({ children, dismiss }: StoreContextDialogProps) => {
    return <DialogAction.Provider value={{ dismiss }}>{children}</DialogAction.Provider>;
};

export default DialogActionProvider;
