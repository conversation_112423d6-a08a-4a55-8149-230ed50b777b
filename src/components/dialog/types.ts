import { AtFloatLayoutProps } from 'taro-ui/types/float-layout';
import { AtModalProps } from 'taro-ui/types/modal';
import { ReactElement } from 'react';
import { ModalLayerProps } from './ModalLayer';

export type DialogLayerRef = {
    removeModalLayer(key: string): void;
    addModalLayer(modalLayer: ReactElement<ModalLayerProps, any>, callback?: () => void): void;
    clear(): void;
};

export interface CreateOptions {
    zIndex?: number;
}

export interface CreateModalProps extends CreateOptions, ModalControllerOptions {}

export interface ModalControllerOptions extends DialogControllerSetOptions {
    component?: React.FC<unknown> | React.ReactElement | null | Function;
    render?:
        | ((dismiss: () => void, ...args: any[]) => React.ReactElement)
        | React.ReactElement
        | null;
}

export interface DialogControllerSetOptions {
    // no_scroll_float: 在弹框中如果有输入框，在部分低设备的机型因为有scrollview会导致上移有问题，因此特殊处理。有类似场景的可以用这个
    type: 'modal' | 'float' | 'modal_custom' | 'no_scroll_float';
    hideInternalSync?: boolean;
    key?: string;
    prior?: number;
    blocking?: boolean;
    isModal?: boolean;
    props?: AtFloatLayoutProps | AtModalProps;
}
