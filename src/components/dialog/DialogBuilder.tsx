import React, { Component } from 'react';
import DialogController from './DialogController';
import { CreateModalProps, DialogLayerRef } from './types';

export default class DialogBuilder {
    private index = 0;

    private modalLayerControllers = new Set<DialogController>();

    private self: DialogLayerRef;

    public create(elem: CreateModalProps | React.ElementType): DialogController {
        let options: CreateModalProps;
        if (elem instanceof Function || (elem as any).prototype instanceof Component) {
            options = {
                component: (props) => React.createElement(elem as any, props as any),
                ...(elem as any).modalLayerOptions,
            };
        } else {
            options = elem as CreateModalProps;
        }
        const key = options.key || `layer_${this.index++}`;
        const oldLayer = this.getLayer(key);
        if (oldLayer) {
            oldLayer.setOptions(options);
            return oldLayer;
        }
        const modalLayerController = new DialogController(key, options, () => this.self);
        this.modalLayerControllers.add(modalLayerController);
        return modalLayerController;
    }

    public getLayer(key: string): DialogController | null {
        let layer: DialogController | null = null;
        this.modalLayerControllers.forEach((ml) => {
            if (ml.key === key) {
                layer = ml;
            }
        });
        return layer;
    }

    public delete(mlc: DialogController | DialogController[]): void {
        const self = this.self;
        if (mlc) {
            if (Array.isArray(mlc)) {
                mlc.forEach((ad) => this.delete(ad));
                return;
            }
            this.modalLayerControllers.delete(mlc as DialogController);
            self?.removeModalLayer((mlc as DialogController).key);
        } else {
            self?.clear();
            this.modalLayerControllers.clear();
        }
    }

    public hideAll(): void {
        this.forEach((mlc) => {
            mlc.hide();
        });
    }

    public forEach(func: (value: DialogController) => void): void {
        this.modalLayerControllers.forEach(func);
    }

    public setDialogLayerRef(input: DialogLayerRef): void {
        this.self = input;
    }
}
