import React, { useEffect, useMemo } from 'react';
import { useContextStore } from '@/components/storeContext/StoreContext';
import { dialogStoreCreator } from '@/components/dialog/dialogStore';
import DialogController from './DialogController';
import { CreateModalProps } from './types';

export default function useDialog(input: CreateModalProps | React.ElementType): DialogController {
    const dialogStore = useContextStore(dialogStoreCreator);
    const dialogBuilder = dialogStore((state) => state.dialogBuilder);

    const dialog = useMemo(() => dialogBuilder.create(input), [dialogBuilder, input]);

    useEffect(() => {
        const instance = dialog;
        return () => {
            if (instance) {
                dialogBuilder.delete(instance);
            }
        };
    }, [dialog, dialogBuilder, input]);

    return dialog;
}
