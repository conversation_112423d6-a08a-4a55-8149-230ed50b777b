/* eslint-disable @typescript-eslint/ban-ts-comment */
import React, { memo, useEffect, useRef, useState } from 'react';
import { View } from '@tarojs/components';
import { dialogStoreCreator } from '@/components/dialog/dialogStore';
import { useContextStore } from '@/components/storeContext/StoreContext';
import { ModalLayerProps } from './ModalLayer';
import { DialogManager, Listener } from './DialogManager';

const DialogRootLayer = memo(() => {
    const [modalLayers, setModalLayers] = useState<React.ReactElement<ModalLayerProps, any>[]>([]);
    const modalLayersMap = useRef(new Map<string, React.ReactElement<ModalLayerProps, any>>());
    const dialogLayerRef = React.useRef({
        removeModalLayer(key: string) {
            if (typeof key === 'string') {
                modalLayersMap.current.delete(key);
                setModalLayers(Array.from(modalLayersMap.current.values()));
            }
        },
        addModalLayer(modalLayer: React.ReactElement<ModalLayerProps, any>, callback?: () => void) {
            const key = modalLayer.key != null ? String(modalLayer.key) : '';
            modalLayersMap.current.set(key, modalLayer);
            setModalLayers(
                Array.from(modalLayersMap.current.values()).sort((a: any, b: any) => {
                    return a.props.zIndex! - b.props.zIndex!;
                })
            );
            // TODO: 回调时机有问题
            callback?.();
        },
        clear() {
            modalLayersMap.current.clear();
            setModalLayers([]);
        },
    });

    const dialogStore = useContextStore(dialogStoreCreator);
    const dialogBuilder = dialogStore((state) => state.dialogBuilder);
    dialogBuilder.setDialogLayerRef(dialogLayerRef.current);

    useEffect(() => {
        const listener: Listener = {
            onShow(key: string, show: boolean) {
                const dialog = dialogBuilder.getLayer(key);
                if (dialog) {
                    if (show) {
                        dialog.reshow();
                    } else {
                        dialog.hideInternal();
                    }
                }
            },
        };
        const ref = dialogLayerRef.current;
        DialogManager.getInstance().addListener(ref, listener);
        return () => {
            DialogManager.getInstance().removeListener(ref, listener);
        };
    }, [dialogBuilder]);

    return <View>{modalLayers}</View>;
});

export default DialogRootLayer;
