/* eslint-disable no-underscore-dangle */
/* eslint-disable no-param-reassign */
import React from 'react';
import SwipeBack from '@/utils/adapter/swipeBack';
import ModalLayer, { ModalLayerRef } from './ModalLayer';
import {
    CreateModalProps,
    DialogControllerSetOptions,
    DialogLayerRef,
    ModalControllerOptions,
} from './types';
import { DialogManager } from './DialogManager';

export default class DialogController {
    public modalLayerRef: ModalLayerRef | undefined;

    private args: any[] = [];

    private pendingShow = false;

    constructor(
        public key: string,
        private options: ModalControllerOptions,
        private getModalLayers: () => DialogLayerRef
    ) {
        this.options = options;
        this.key = key;
        this.getModalLayers = getModalLayers;
    }

    private createModalLayer(key: string, options: CreateModalProps, callback?: () => void) {
        const { zIndex = 0, ...reset } = options;
        const modalLayer = (
            <ModalLayer
                key={key}
                zIndex={zIndex}
                onRef={(modalLayerRef) => {
                    if (modalLayerRef) {
                        this.modalLayerRef = modalLayerRef;
                        modalLayerRef.emitHide = () => {
                            this.hide();
                        };
                        if (this.pendingShow) {
                            this.showInternal(...this.args);
                        }
                        this.pendingShow = false;
                    }
                }}
                {...reset}
            />
        );
        this.getModalLayers().addModalLayer(modalLayer, callback);
    }

    private getComponent(...args: any[]) {
        const component = this.options.component;
        const render = this.options.render;
        if (render) {
            const dismiss = () => {
                if (this.modalLayerRef) {
                    this.modalLayerRef.dismiss();
                } else {
                    this.hide();
                }
            };
            return typeof render === 'function' ? render.apply(null, [dismiss, ...args]) : render;
        }
        return typeof component === 'function' ? component.apply(null, args) : component;
    }

    reshow(): void {
        this.showInternal(...this.args);
    }

    show(...args: any[]): void {
        SwipeBack.enabled = false;
        const isModel = this.options.isModal;
        if (isModel) {
            this.showInternal(...args);
        } else {
            DialogManager.getInstance()
                .show(this.getModalLayers(), this.key, this.options)
                .then((result) => {
                    if (result) {
                        this.showInternal(...args);
                    }
                });
        }
    }

    showInternal(...args: any[]): void {
        this.args = args;
        const modalLayerRef = this.modalLayerRef;
        if (!modalLayerRef) {
            this.createModalLayer(this.key, this.options, () => {
                this.pendingShow = true;
            });
            return;
        }
        if (!modalLayerRef._isMounted) {
            this.createModalLayer(this.key, this.options, () => this.showInternal(...args));
            return;
        }
        modalLayerRef
            .show({
                component: this.getComponent(...args),
            })
            .then(() => this.didShow?.());
        this.onShow?.();
    }

    public preload(...args: any[]): void {
        if (this.modalLayerRef) {
            this.modalLayerRef.preload(this.getComponent(...args), () => this.onLoad?.());
        } else {
            setTimeout(() => this.preload(...args), 10);
        }
    }

    public setOptions(options: DialogControllerSetOptions): void {
        this.options = { ...this.options, ...options };
    }

    /** 异步关闭弹窗 */
    public hideInternal(destroy = false): void {
        this.onHide?.();
        if (this.modalLayerRef?._isMounted) {
            this.modalLayerRef.hide().then(() => {
                this.didHide?.();
                if (destroy) {
                    console.log('destroy');
                    this.modalLayerRef = undefined;
                    this.getModalLayers().removeModalLayer(this.key);
                }
            });
        }
    }

    /** 同步关闭弹窗 */
    public hideInternalSync(destroy = false): void {
        this.onHide?.();
        if (this.modalLayerRef?._isMounted) {
            this.didHide?.();
            if (destroy) {
                console.log('destroy');
                this.modalLayerRef = undefined;
                this.getModalLayers().removeModalLayer(this.key);
            }
        }
    }

    public hide(): void {
        SwipeBack.enabled = true;
        const isModel = this.options.isModal ?? false;
        const isCustom =
            (this.options.type === 'modal_custom' || this.options.hideInternalSync) ?? false;

        const hideFunc = isCustom ? this.hideInternalSync.bind(this) : this.hideInternal.bind(this);
        if (isModel) {
            hideFunc(true);
        } else {
            DialogManager.getInstance()
                .hide(this.getModalLayers(), this.key)
                .then(() => {
                    hideFunc(true);
                });
        }
    }

    didHide: ((this: DialogController) => void) | null = () => {};

    didShow: ((this: DialogController) => void) | null = () => {};

    onShow: ((this: DialogController) => void) | null = () => {};

    onHide: ((this: DialogController) => void) | null = () => {};

    onLoad: ((this: DialogController) => void) | null = () => {};
}
