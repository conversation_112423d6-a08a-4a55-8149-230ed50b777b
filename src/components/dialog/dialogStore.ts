import { StoreCreator, StoreLifecycle } from '@/components/storeContext/StoreContext';
import DialogBuilder from '@/components/dialog/DialogBuilder';
import { create } from 'zustand';

export interface DialogState extends StoreLifecycle {
    dialogBuilder: DialogBuilder;
}

export const dialogStoreCreator: StoreCreator<DialogState> = () => {
    return create<DialogState>(() => ({
        dialogBuilder: new DialogBuilder(),
    }));
};
