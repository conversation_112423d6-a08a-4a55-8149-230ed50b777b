import { Dialog, DialogConfig, DialogQueue } from './DialogQueue';
import { DialogLayerRef } from './types';

export interface Listener {
    onShow(key: string, show: boolean): void;
}

export class DialogManager {
    private static instance: DialogManager;

    private cache = new Map<DialogLayerRef, Map<string, Dialog>>();

    private listeners = new Map<DialogLayerRef, Set<Listener>>();

    public static getInstance(): DialogManager {
        if (!DialogManager.instance) {
            DialogManager.instance = new DialogManager();
        }
        return DialogManager.instance;
    }

    public show(host: DialogLayerRef, key: string, payload: DialogConfig): Promise<boolean> {
        let dialog: Dialog | undefined = this.cache.get(host)?.get(key);
        if (!dialog) {
            dialog = {
                id: key,
                prior: payload.prior ?? 100,
                blocking: payload.blocking ?? false,
                isModal: false,
                dismiss: () => {},
                toggle: (hide) => {
                    this.listeners.get(host)?.forEach((listener) => {
                        listener.onShow(key, !hide);
                    });
                },
            };
            if (!this.cache.has(host)) {
                this.cache.set(host, new Map());
            }
            this.cache.get(host)?.set(key, dialog);
        }
        return new Promise((resolve, reject) => {
            if (dialog) {
                this.doShowDialog(host, key, resolve);
            } else {
                resolve(true);
            }
        });
    }

    private doShowDialog(
        host: DialogLayerRef,
        key: string,
        resolve: (result: boolean) => void
    ): boolean {
        const jump = () => {
            return this.doShowDialog(host, key, resolve);
        };
        const dialog: Dialog | undefined = this.cache.get(host)?.get(key);
        const block = DialogQueue.getInstance().isBlock(host, '', dialog, jump);
        if (!block) {
            if (dialog) {
                DialogQueue.getInstance().addDialog(host, dialog);
            }
            resolve(dialog != null);
        }
        return block;
    }

    public hide(host: DialogLayerRef, key: string): Promise<boolean> {
        if (!this.cache.has(host)) {
            this.cache.set(host, new Map());
        }
        const dialog: Dialog | undefined = this.cache.get(host)?.get(key);
        this.cache.get(host)?.delete(key);
        if (dialog) {
            DialogQueue.getInstance().removeDialog(host, dialog);
        }
        return Promise.resolve(true);
    }

    public addListener(host: DialogLayerRef, listener: Listener) {
        if (!this.listeners.has(host)) {
            this.listeners.set(host, new Set());
        }
        this.listeners.get(host)?.add(listener);
    }

    public removeListener(host: DialogLayerRef, listener: Listener) {
        this.listeners.get(host)?.delete(listener);
        if (this.listeners.get(host)?.size === 0) {
            this.listeners.delete(host);
        }
    }
}
