@import '~taro-ui/dist/style/components/float-layout.scss';

.modalLayer {
    .at-float-layout {
        .at-float-layout__container {
            min-height: 0;
            max-height: fit-content;
            background-color: transparent;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .layout-body {
                min-height: 0;
                max-height: fit-content;
                display: flex;
                flex-direction: column;
                justify-content: center;
                height: auto;
                padding: 0;

                .layout-body__content {
                    min-height: 0;
                    max-height: fit-content;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    border-top-left-radius: 20px;
                    border-top-right-radius: 20px;
                }
            }
        }

        .layout {
            &-body {
                padding: 0;
            }
        }
    }

    .at-modal {
        .at-modal__overlay {
            background-color: 'transparent';
        }

        .at-modal__container {
            min-height: 0;
            max-height: fit-content;
            background-color: transparent;
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 100%;
            height: 100%;
            transform: none;
            top: 0;
            left: 0;
        }
    }
}
