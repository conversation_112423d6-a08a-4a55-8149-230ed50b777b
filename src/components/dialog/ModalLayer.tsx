import React, { useEffect, useMemo, useRef, useState } from 'react';
import { AtFloatLayout, AtModal } from 'taro-ui';
import { View } from '@tarojs/components';
import ErrorBoundary from '../ErrorBoundary';
import { DialogControllerSetOptions } from './types';
import NoScrollFloatLayout from './NoScrollFloatLayout';
import './index.scss';

export type ModalLayerRef = {
    preload: (component: React.ReactNode, callback?: () => void) => void;
    show: (options: ReactComponent) => Promise<void>;
    hide: () => Promise<void>;
    dismiss: () => void;
    emitHide: () => void;
    _isMounted: boolean;
    isShow: boolean;
};

export interface ModalLayerProps extends DialogControllerSetOptions {
    zIndex?: number;
    onRef: (ref: ModalLayerRef) => void;
    key?: any;
}

interface ReactComponent {
    component?: React.ReactNode;
}

function ModalLayer(props: ModalLayerProps) {
    const [isShow, setIsShow] = useState(false);
    const [state, setState] = React.useState<{
        pending: boolean;
    }>({
        pending: false,
    });
    const callbackRef = useRef<{ fn: () => void; timeoutTimer: any }>();
    const { onRef, type, props: realProps } = props;
    const component = useRef<React.ReactNode>(null);
    const modalLayerRef = React.useRef<ModalLayerRef>({
        preload: (component: React.ReactNode, callback?: () => void) => {
            setState({
                pending: false,
            });
        },
        show: (
            options: ReactComponent = {
                component: null,
            }
        ) => {
            component.current = options.component;
            setState({
                pending: true,
            });
            return new Promise<void>((resolve, reject) => {
                clearTimeout(callbackRef.current?.timeoutTimer);
                callbackRef.current = {
                    fn: resolve,
                    timeoutTimer: setTimeout(() => {
                        reject(new Error('ModalLayer show timeout'));
                    }, 1000),
                };
            });
        },
        hide: () => {
            setState({
                pending: false,
            });
            return new Promise<void>((resolve, reject) => {
                clearTimeout(callbackRef.current?.timeoutTimer);
                callbackRef.current = {
                    fn: resolve,
                    timeoutTimer: setTimeout(() => {
                        resolve();
                    }, 1000),
                };
            });
        },
        dismiss: () => {
            modalLayerRef.current.emitHide();
        },
        emitHide: () => {},
        _isMounted: false,
        isShow: false,
    });

    useEffect(() => {
        modalLayerRef.current.isShow = isShow;
        if (callbackRef.current) {
            const temp = callbackRef.current;
            if (isShow) {
                temp.fn();
                clearTimeout(temp.timeoutTimer);
            }
        }
    }, [isShow]);

    useEffect(() => {
        modalLayerRef.current._isMounted = true;
        onRef(modalLayerRef.current);
        return () => {
            modalLayerRef.current._isMounted = false;
        };
    }, [onRef, modalLayerRef]);

    const content = useMemo(() => {
        const onClose = (event: any) => {
            modalLayerRef.current.emitHide();
            realProps?.onClose && realProps?.onClose(event);
        };
        if (type === 'float') {
            return (
                <View className="modalLayer">
                    <AtFloatLayout isOpened={isShow} {...realProps} onClose={onClose}>
                        <ErrorBoundary>{component.current}</ErrorBoundary>
                    </AtFloatLayout>
                </View>
            );
        }
        if (type === 'modal_custom') {
            let clone = null;
            if (component.current) {
                clone = React.cloneElement(component.current as React.ReactElement, {
                    isOpened: isShow,
                    onClose,
                });
            }
            return <View>{clone}</View>;
        }
        if (type === 'no_scroll_float') {
            return (
                <View className="modalLayer">
                    <NoScrollFloatLayout isOpened={isShow} {...realProps} onClose={onClose}>
                        <ErrorBoundary>{component.current}</ErrorBoundary>
                    </NoScrollFloatLayout>
                </View>
            );
        }
        return (
            <View className="modalLayer">
                <AtModal isOpened={isShow} {...realProps} onClose={onClose}>
                    <ErrorBoundary>{component.current}</ErrorBoundary>
                </AtModal>
            </View>
        );
    }, [isShow, realProps, type]);
    useEffect(() => {
        setIsShow(state.pending);
    }, [state.pending]);
    return content;
}

export default ModalLayer;
