.bottom-action-modal {
    .at-float-layout {
        .at-float-layout__container {
            min-height: 0;
            background-color: transparent;
            background-color: #fff;

            .layout-body {
                min-height: 0;
                display: flex;
                flex-direction: column;
                justify-content: center;
                height: auto;
                padding: 0;

                .layout-body__content {
                    min-height: 0;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                }
            }
        }
    }

    .action-modal-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding-top: 15px;
        padding-bottom: 36px;

        .action-modal-item {
            height: 48px;
            width: 100%;
            text-align: center;
            font-size: 17px;
            font-weight: 600;
            line-height: 48px;
            letter-spacing: 0;
            color: #f15363;
            opacity: 1;

            &::after {
                content: '';
                display: block;
                margin-left: 20px;
                width: calc(100% - 40px);
                height: 0.5px;
                background-color: #000;
                opacity: 0.1;
            }
        }

        .action-modal-item-cancel {
            color: #000;
            opacity: 0.2;
        }

        .action-modal-item:last-child::after {
            display: none;
        }
    }
}
