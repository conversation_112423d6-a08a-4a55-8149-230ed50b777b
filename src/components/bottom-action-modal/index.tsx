import { Text, View } from '@tarojs/components';
import classNames from 'classnames';
import React, { useCallback, useMemo } from 'react';
import './index.scss';
import MyFloatLayout from './MyFloatLayout';

export interface ActionProps {
    title: string;
    onClick?: () => void;
    className?: string;
}

export interface ChapterUnlockModalProps {
    actions: ActionProps[];
    isOpened: boolean;
    hasCancel?: boolean;
    onClose: () => void;
}

const BottomActionModal = ({
    actions,
    isOpened,
    onClose,
    hasCancel = true,
}: ChapterUnlockModalProps) => {
    const clickItem = useCallback(
        (action: ActionProps) => {
            action.onClick?.();
            onClose();
        },
        [onClose]
    );

    const finalActions = useMemo(() => {
        if (!hasCancel) return actions;
        return [
            ...actions,
            {
                title: '取消',
                className: 'action-modal-item-cancel',
            },
        ];
    }, [actions, hasCancel]);

    // console.log('finalActions', finalActions);

    return (
        <View className="bottom-action-modal">
            <MyFloatLayout isOpened={isOpened} onClose={onClose}>
                <View className="action-modal-wrapper">
                    {finalActions.map((action, index) => {
                        return (
                            <Text
                                key={`${index}_${Date.now()}}`}
                                className={classNames('action-modal-item', action.className)}
                                onClick={() => clickItem(action)}>
                                {action.title}
                            </Text>
                        );
                    })}
                </View>
            </MyFloatLayout>
        </View>
    );
};

export default React.memo(BottomActionModal);
