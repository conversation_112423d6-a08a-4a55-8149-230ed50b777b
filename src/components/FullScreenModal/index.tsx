/* eslint-disable react/prop-types */
import React, { useCallback, useEffect, useState, useImperativeHandle } from 'react';
import { View } from '@tarojs/components';
import RootPortal from '@/components/RootPortal';
import SwipeBack from '@/utils/adapter/swipeBack';

import './index.scss';

export interface Props {
    /** 唯一标识 */
    id?: string;
    /** 是否显示 */
    visible: boolean;
    /** 子组件 */
    children?: React.ReactNode;
    /** 关闭回调 */
    onClose?: () => void;
    /** 强制打开，用户无法关闭 */
    forceOpen?: boolean;
    /** 自定义类名 */
    className?: string;
    /** 从底部弹出 */
    fromBottom?: boolean;
}

export interface RefObject {
    onModalClose: () => void;
}

const FullScreenModal = React.forwardRef<RefObject, Props>(
    ({ visible, children, onClose, id, forceOpen = false, className, fromBottom }, ref) => {
        const [show, setShow] = useState(visible);

        const onModalClose = useCallback(() => {
            if (forceOpen) return;
            setShow(false);
            if (onClose) {
                onClose();
            }
        }, [forceOpen, onClose]);

        useImperativeHandle(ref, () => ({ onModalClose }), [onModalClose]);

        useEffect(() => {
            setShow(visible);
        }, [visible]);

        useEffect(() => {
            SwipeBack.enabled = !visible;
            return () => {
                SwipeBack.enabled = true;
            };
        }, [visible]);

        if (!visible) return null;

        return (
            <RootPortal>
                <View
                    {...(id ? { id } : {})}
                    style={{
                        opacity: +!!show,
                        justifyContent: fromBottom ? 'flex-end' : 'center',
                    }}
                    onClick={onModalClose}
                    className={`fullscreen-modal ${className}`}>
                    {children}
                </View>
            </RootPortal>
        );
    }
);

export default React.memo(FullScreenModal);
