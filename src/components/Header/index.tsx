/* eslint-disable react/require-default-props */
import { View, Image, Text } from '@tarojs/components';
import React, { useCallback, useEffect, useState } from 'react';
import backIcon from '@/assets/header/icon-back.png';
import Taro, { createAnimation } from '@tarojs/taro';
import { pageShowDelayDur, pageShowDur } from '@/pages/gacha/const';
import { debounce } from '@/utils';
import './index.scss';

interface CommonHeaderProps {
    hasStatusBar?: boolean;
    headerBg?: string;
    /**
     * 左侧 左一 返回区域， 支持传入传入返回按钮资源~
     */
    backConfig?: {
        src?: string;
        needAnimator?: boolean;
    };
    /**
     * 左侧 左二 标题区域， 支持传入文本、node，支持展示默认动画~
     */
    titleConfig?: {
        title?: string;
        node?: React.ReactNode;
        needAnimator?: boolean;
    };
    /**
     * 右侧功能区域， 支持传入node，支持展示默认动画~
     */
    funcConfig?: {
        needAnimator?: boolean;
        node?: React.ReactNode;
    };
}

const CommonHeader = ({
    hasStatusBar = true,
    headerBg = 'transparent',
    backConfig = {
        src: backIcon,
        needAnimator: false,
    },
    titleConfig = {
        needAnimator: false,
    },
    funcConfig = {
        needAnimator: false,
    },
}: CommonHeaderProps) => {
    const onBack = useCallback(() => {
        Taro.navigateBack();
    }, []);

    const [backAnimationData, setBackAnimationData] = useState({});
    useEffect(() => {
        let timer: NodeJS.Timeout;
        if (backConfig.needAnimator) {
            timer = setTimeout(() => {
                const animation = createAnimation({
                    timingFunction: 'linear',
                });
                animation.translateX(0).step({ duration: pageShowDur });
                setBackAnimationData(animation.export());
            }, pageShowDelayDur);
        }
        return () => {
            if (timer) {
                clearTimeout(timer);
            }
        };
    }, [backConfig.needAnimator]);

    const [titleAnimationData, setTitleAnimationData] = useState({});
    useEffect(() => {
        let timer: NodeJS.Timeout;
        if (titleConfig.needAnimator) {
            timer = setTimeout(() => {
                const animation = createAnimation({
                    timingFunction: 'linear',
                });
                animation.opacity(1).step({ duration: pageShowDur });
                setTitleAnimationData(animation.export());
            }, pageShowDelayDur);
        }
        return () => {
            if (timer) {
                clearTimeout(timer);
            }
        };
    }, [titleConfig.needAnimator]);

    const [funcAnimationData, setFuncAnimationData] = useState({});
    useEffect(() => {
        let timer: NodeJS.Timeout;
        if (funcConfig.needAnimator) {
            timer = setTimeout(() => {
                const animation = createAnimation({
                    timingFunction: 'linear',
                });
                animation.translateX(0).scaleX(1).scaleY(1).step({ duration: pageShowDur });
                setFuncAnimationData(animation.export());
            }, pageShowDelayDur);
        }
        return () => {
            if (timer) {
                clearTimeout(timer);
            }
        };
    }, [funcConfig.needAnimator]);

    return (
        <View
            className={`header-common header-common--${hasStatusBar ? 'with' : 'no'}-top`}
            style={{ background: headerBg }}>
            <View
                className="header-back"
                animation={backAnimationData}
                style={{ transform: funcConfig.needAnimator ? 'translateX(-50px)' : '' }}>
                {backConfig?.src && (
                    <Image
                        src={backConfig.src}
                        onClick={debounce(() => {
                            onBack();
                        })}
                    />
                )}
            </View>

            <View
                className="header-title"
                animation={titleAnimationData}
                style={{ opacity: titleConfig.needAnimator ? 0 : 1 }}>
                {titleConfig?.title && <Text>{titleConfig?.title}</Text>}
                {titleConfig?.node && titleConfig?.node}
            </View>

            <View
                animation={funcAnimationData}
                className="header-func"
                style={{ transform: funcConfig.needAnimator ? 'translateX(100%)' : '' }}>
                {funcConfig?.node && funcConfig?.node}
            </View>
        </View>
    );
};

export default React.memo(CommonHeader);
