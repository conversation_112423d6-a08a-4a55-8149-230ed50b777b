.header-common {
    position: relative;
    left: 0;
    width: 100%;
    height: 44px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    padding-right: 20px;

    &--with-top {
        margin-top: var(--status-bar-height);
    }

    &--no-top {
        margin-top: 0;
    }

    .header-back {
        width: 26px;
        height: 26px;
        flex-shrink: 0;
    }

    .header-title-container {
        height: 44px;
        display: flex;
        align-items: center;

        .header-title {
            font-size: 18px;
            font-weight: 600;
            line-height: normal;
            letter-spacing: 0;
            color: #000;
            white-space: nowrap;
            margin-left: 8px;
        }
    }

    .header-func {
        height: 44px;
        margin-left: auto;
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
}
