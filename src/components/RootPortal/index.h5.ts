import { ReactElement, useLayoutEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { getCurrentInstance } from '@tarojs/taro';

interface IProps {
    children: ReactElement;
}

const RootPortal = ({ children }: IProps) => {
    const [dom, setDom] = useState<HTMLElement | null>(null);

    // 默认把弹框放在当前页面层级下
    useLayoutEffect(() => {
        const { router } = getCurrentInstance();
        const currentRoute = router?.path || 'app';
        const d = document.getElementById(currentRoute);
        if (d) {
            setDom(d);
        }
    }, []);

    return dom ? createPortal(children, dom) : children;
};

export default RootPortal;
