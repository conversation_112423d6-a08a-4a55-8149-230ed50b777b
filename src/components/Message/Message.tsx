import React, { lazy, Suspense, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AtList, AtModal } from 'taro-ui';
import Taro from '@tarojs/taro';
import { Text, View } from '@tarojs/components';
import { getUserInfo, saveStartIndex } from '@/utils/rpc';
import { transformTimeToString } from '@/utils/timeHelper';
import { ChatDataManager } from '@/utils/managers/ChatDataManager';
import UpgradeAlertManager from '@/utils/managers/UpgradeAlertManager';
import ErrorBoundary from '@/components/ErrorBoundary';
import TitleBar from '@/components/titleBar';
import TaroSafeAreaView from '@/components/safe-area-view';
import Avatar from '@/components/avatar';
import {
    officialAccountAccids,
    getSessionLastTimestamp,
    useSessionStore,
} from '@/hooks/sessionStore';
import NIMService from '@/hooks/useNewNIM';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import MessageFlow from '@/hooks/message/MessageFlow';
import { SystemMessage } from '@/types/im';
import classNames from 'classnames';
import { optimizeImage } from '@/utils/image';
import winAdapter from '@/utils/adapter/winAdapter';
import EventTrackView from '@/components/EventTrack';
import useHomeStore, { HomeTabIndex } from '@/components/Home/useHomeStore';
import GlobalMessage from '@/utils/global-message';
import { apiResetSessionRelation } from '@/service/imApi';
import { coronaWarnMessage } from '@music/mat-base-h5';
import PushReCallTip from '@/components/Message/pushRecall';
import { jump2Chat, jump2Official } from '@/router';
import NetErrorTip from './networkError';
import { Conversation } from './conversationModel';
import { TabItemStateProps } from '../Home/TabItemProps';
import './index.scss';

const BottomActionModal = lazy(() => import('@/components/bottom-action-modal'));

const SkeletonItem = () => (
    <View className="cell skeleton">
        <View className="avatar skeleton-avatar" />
        <View className="content">
            <View className="title-line">
                <View className="skeleton-title" />
                <View className="skeleton-time" />
            </View>
            <View className="detail-line">
                <View className="skeleton-detail" />
            </View>
        </View>
    </View>
);

const EmptyState = () => {
    const gotoChat = () => {
        useHomeStore.getState().setCurrent(HomeTabIndex.explore);
    };
    return (
        <View className="empty-wrapper">
            <Text className="empty-text">暂时没有消息</Text>
            <Text className="chat-text" onClick={gotoChat}>
                去添加好友
            </Text>
            <Text />
        </View>
    );
};

// eslint-disable-next-line consistent-return
const lastMsg = (item: Conversation) => {
    const draft = ChatDataManager.getInstance().getChatData(item.sessionId);
    const draftText = draft?.text;
    const image = draft?.image || false;
    const text = draftText && draftText.length > 0;
    if (image || text) {
        return (
            <View className="draft-content">
                <Text className="draft">[草稿]</Text>
                {image && <Text className="detail">[图片]</Text>}
                {text && <Text className="detail">{draftText}</Text>}
            </View>
        );
    }

    let lastMsgText = '';
    if (item.lastMessage?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_TEXT) {
        lastMsgText = item.lastMessage?.text ?? '';
    } else if (
        item.lastMessage?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_IMAGE
    ) {
        lastMsgText = '[图片]';
    } else if (
        item.lastMessage?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_AUDIO
    ) {
        lastMsgText = '[语音]';
    } else if (
        item.lastMessage?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_VIDEO
    ) {
        lastMsgText = '[视频]';
    } else if (
        item.lastMessage?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_CUSTOM
    ) {
        const content = JSON.parse(item.lastMessage?.attachment?.raw ?? '{}')?.content;
        if (content?.type === 'aigcCustomTextAudioMsg') {
            lastMsgText = content.content?.text;
        } else if (content?.type === 'AIGC_USER_MULTIMOAL_MSG') {
            lastMsgText = `[图片]${content.content?.text}`;
        } else if (content?.type === 'gift') {
            lastMsgText = `[图片]${content.content?.gift?.name}`;
        } else if (content?.type === 'sceneSettingStartMsg') {
            lastMsgText = '【场景开始】';
        } else if (content?.type === 'sceneSettingNoticeMsg') {
            lastMsgText = '【触发心跳场景】';
        } else if (content?.type === 'sceneSettingEndMsg') {
            lastMsgText = '【场景结束】';
        } else if (content?.type === 'secretaryText') {
            lastMsgText = `${content.content?.template ?? ''}${content.content?.bottom ?? ''}`;
        } else {
            lastMsgText = '[未知消息]';
        }
    }
    if (lastMsgText.length) {
        return <Text className="detail">{lastMsgText}</Text>;
    }
};

const ConversationItem: React.FC<{ item: Conversation; onClick: any; onLongPress: any }> = ({
    item,
    onClick,
    onLongPress,
}: any) => {
    const [touchStart, setTouchStart] = useState(false);
    const handleTouchEnd = () => {
        setTouchStart(false);
    };
    const handleTouchStart = () => {
        setTouchStart(true);
    };
    const handleTouchCanceled = () => {
        setTouchStart(false);
    };

    const getBgColor = (isTouchStart: boolean, isStickTop: boolean) => {
        if (isTouchStart) {
            return 'rgba(97,97,97,0.1)';
        }
        if (isStickTop) {
            return 'rgba(232, 198, 209, 0.3)';
        }
        return 'transparent';
    };

    return (
        <View
            className="cell"
            onClick={() => onClick(item)}
            onLongPress={() => {
                onLongPress(item);
            }}
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
            onTouchCancel={handleTouchCanceled}
            style={{
                backgroundColor: getBgColor(touchStart, item.stickTop),
            }}>
            <Avatar
                src={item.toUserDetail?.userInfo?.avatarImgUrl ?? ''}
                width={60}
                height={60}
                className="avatar"
                lazyload
            />
            <View className="content">
                <View className="title-line">
                    <Text className="title">{item.toUserDetail?.userInfo?.nickname}</Text>
                    {officialAccountAccids.includes(item.toUserDetail?.userInfo?.imAccId) && (
                        <Text className="official">官方</Text>
                    )}
                    <Text className="time">
                        {transformTimeToString(getSessionLastTimestamp(item))}
                    </Text>
                </View>
                <View className="detail-line">
                    {lastMsg(item)}
                    {item.unreadCount > 0 && (
                        <Text className="unread-count">{item.unreadCount}</Text>
                    )}
                </View>
            </View>
        </View>
    );
};

export default function Self({ selected }: TabItemStateProps) {
    useEffect(() => {
        // 初始化
        UpgradeAlertManager.getInstance();
        GlobalMessage.watchSystemMessage();
    }, []);

    useEffect(() => {
        if (selected) {
            saveStartIndex('');
        }
    }, [selected]);

    const [showOperatorModal, setShowOperatorModal] = useState(false);
    const [operatorItem, setOperatorItem] = useState<Conversation | null>(null);

    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [deleteConversation, setDeleteConversation] = useState<Conversation | null>(null);

    const stickySessions = useSessionStore((state) => state.stickySessions);
    const finalSessionList = useSessionStore((state) => state.finalSessionList);
    const lostNetworking = useSessionStore((state) => state.lostNetworking);
    const isLoading = useSessionStore((state) => state.sessionLoading);

    // 使用 useRef 存储最后一次调用的时间戳
    const lastCallTimeRef = useRef(0);
    // 使用 useRef 存储节流后的函数实例
    const throttledFnRef = useRef<((item: Conversation) => void) | null>(null);

    const handleLongPress = (item: Conversation) => {
        setOperatorItem(item);
        setShowOperatorModal(true);
    };

    const handleDeleteSession = async () => {
        setShowDeleteModal(false);
        NIMService.deleteConversation(deleteConversation.conversationId);
        try {
            const robotUserId = deleteConversation.toUserDetail?.userInfo?.userId;
            if (robotUserId) {
                await apiResetSessionRelation({ robotUserId });
            }
        } catch (error) {
            coronaWarnMessage('Message', `删除会话失败 ${error}`);
        }
    };

    const actionList = useMemo(() => {
        if (!operatorItem) return [];

        const topItem = {
            title: '置顶',
            onClick: () => {
                setShowOperatorModal(false);
                NIMService.stickTopConversation(operatorItem.conversationId, true);
                // 记录该条会话置顶时间
                const prev = useSessionStore.getState().stickySessions;
                useSessionStore.getState().setStickySessions({
                    ...prev,
                    [operatorItem.conversationId ?? '']: Date.now(),
                });
            },
            className: 'operator-top',
        };
        const cancelTopItem = {
            title: '取消置顶',
            onClick: () => {
                setShowOperatorModal(false);
                NIMService.stickTopConversation(operatorItem.conversationId, false);
                // 移除该条会话置顶时间
                const prev = useSessionStore.getState().stickySessions;
                delete prev[operatorItem.conversationId];
                useSessionStore.getState().setStickySessions(prev);
            },
            className: 'operator-top',
        };
        const deleteItem = {
            title: '删除',
            onClick: () => {
                setShowOperatorModal(false);
                setDeleteConversation(operatorItem);
                setShowDeleteModal(true);
            },
        };
        if (operatorItem.stickTop) {
            return [cancelTopItem, deleteItem];
        }
        return [topItem, deleteItem];
    }, [operatorItem]);

    // 在 useEffect 中创建节流函数，确保只创建一次
    useEffect(() => {
        // 自定义节流实现，确保更可靠的控制
        throttledFnRef.current = (item: Conversation) => {
            const now = Date.now();
            if (now - lastCallTimeRef.current >= 1000) {
                lastCallTimeRef.current = now;

                // 是否是官方通知
                if (
                    officialAccountAccids.some((accid) =>
                        item.toUserDetail?.userInfo?.imAccId?.includes(accid)
                    )
                ) {
                    jump2Official({ robotAccid: item.sessionId });
                    return;
                }
                const { aigcInfo, userInfo } = item.toUserDetail || {};
                const { aigcChatResourceInfo } = aigcInfo || {};

                const optimizedUrl = optimizeImage({
                    src: aigcChatResourceInfo?.botBackgroundUrl,
                    width: 375,
                    height: 812,
                });
                const optimizedMp4 = aigcChatResourceInfo?.botStandByUrls?.[0];
                const botContactUrl = aigcChatResourceInfo?.botContactUrl ?? '';
                jump2Chat({
                    robotUserId: `${userInfo?.userId}`,
                    hasAiChapter: `${aigcInfo?.hasAiChapter}`,
                    robotAccid: `${item.sessionId}`,
                    botBgUrl: `${optimizedUrl}`,
                    botStandByUrl: `${optimizedMp4}`,
                    botContactUrl: `${botContactUrl}`,
                });
            }
        };

        // 组件卸载时清理
        return () => {
            throttledFnRef.current = null;
        };
    }, []);

    const handleGotoDetail = useCallback((item: Conversation) => {
        if (throttledFnRef.current) {
            throttledFnRef.current(item);
        }
    }, []);

    const listener = useMemo(
        () => ({
            onSystemMessage(message: SystemMessage) {
                const content = message.contentExt?.serverExt?.content;
                if (content?.bind === false) {
                    finalSessionList.forEach((session) => {
                        if (+session.toUserDetail?.userInfo?.userId === +content.bindUserId) {
                            NIMService.deleteConversation(session.conversationId);
                        }
                    });
                }
            },
        }),
        [finalSessionList]
    );

    useEffect(() => {
        MessageFlow.addNotificationListener(4306, listener);
        return () => {
            MessageFlow.removeNotificationListener(4306, listener);
        };
    }, [listener]);

    useEffect(() => {
        const handleContentChanged = () => {
            useSessionStore.getState().sortFinalSessions(finalSessionList);
        };
        Taro.eventCenter.on('ChatDataManagerContentChanged', handleContentChanged);

        return () => {
            Taro.eventCenter.off('ChatDataManagerContentChanged', handleContentChanged);
        };
    }, [finalSessionList]);

    useEffect(() => {
        getUserInfo(
            () => {
                useHomeStore.getState().hideLoading();
            },
            () => {
                useHomeStore.getState().hideLoading();
            }
        );
    }, []);

    // 添加渲染函数，分别处理不同状态的内容
    const renderContent = () => {
        // 加载中状态
        if (isLoading) {
            return (
                <AtList className="message-list">
                    {Array.from({ length: 5 }, (_, i) => (
                        <SkeletonItem key={i} />
                    ))}
                </AtList>
            );
        }

        // 有数据状态
        if (finalSessionList && finalSessionList.length > 0) {
            return (
                <AtList
                    className={classNames('message-list', {
                        lost: lostNetworking,
                    })}>
                    {finalSessionList.map((item) => (
                        <ConversationItem
                            key={`${item.conversationId}${stickySessions[item.conversationId]}${
                                item.stickTop
                            }`}
                            item={item}
                            onClick={() => handleGotoDetail(item)}
                            onLongPress={handleLongPress}
                        />
                    ))}
                </AtList>
            );
        }

        // 无数据状态
        return <EmptyState />;
    };

    return (
        <ErrorBoundary
            text="系统繁忙，点击任意区域可刷新页面"
            onClick={() => {
                winAdapter.reloadPage();
            }}>
            <TaroSafeAreaView className="message-pagep-wrapper">
                <EventTrackView params={{ _spm: 'page_ai_message|page_h5_biz' }}>
                    <View />
                </EventTrackView>
                <View className="message-list-bg" />

                <TitleBar title="消息" style={{ zIndex: 2 }} source="message" />
                {lostNetworking && <NetErrorTip />}
                {renderContent()}
                <PushReCallTip />
                <Suspense fallback={<View />}>
                    <BottomActionModal
                        isOpened={showOperatorModal}
                        actions={actionList}
                        onClose={() => setShowOperatorModal(false)}
                    />
                </Suspense>
                <Suspense fallback={<View />}>
                    <AtModal
                        isOpened={showDeleteModal}
                        className="custom-modal"
                        onClose={() => setShowDeleteModal(false)}>
                        <View className="modal-content">
                            <Text className="text-title">确定要删除会话吗？</Text>
                            <Text className="text-content">删除后，所有聊天记录将被永久删除</Text>
                            <View className="btn-group">
                                <View className="cancel" onClick={() => setShowDeleteModal(false)}>
                                    取消
                                </View>
                                <View className="confirm" onClick={handleDeleteSession}>
                                    确定
                                </View>
                            </View>
                        </View>
                    </AtModal>
                </Suspense>
            </TaroSafeAreaView>
        </ErrorBoundary>
    );
}
