.push-recall-container {
    z-index: 99;
    position: absolute;
    bottom: 56px;
    @supports (bottom: env(safe-area-inset-bottom)) {
        bottom: calc(56px + env(safe-area-inset-bottom));
    }
    left: 10px;
    width: 355px;
    height: 59px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 12px;
    background-color: white;
    padding: 8px 15px 8px 15px;

    .push-recall-left-view {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .push-recall-left-view-tips {
            width: 32px;
            height: 32px;
            margin-right: 4px;
        }

        .push-recall-left-view-texts {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;

            font-family: PingFang SC, sans-serif;
            font-size: 12px;
            line-height: 100%;
            letter-spacing: 0;
            color: #26262a;

            .push-recall-left-view-text-1 {
                font-weight: 600;
            }

            .push-recall-left-view-text-2 {
                font-weight: 400;
                margin-top: 6px;
            }
        }
    }

    .push-recall-right-view {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .push-recall-left-view-goto-open {
            width: 56px;
            height: 27px;
            margin-right: 10px;
        }

        .push-recall-left-view-close {
            width: 16px;
            height: 16px;
        }
    }
}
