import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, Image } from '@tarojs/components';
import { rpc } from '@music/mat-base-h5';
import message_push_recall_close from '@/assets/common/message_push_recall_close.png';
import message_push_recall_goto_open from '@/assets/common/message_push_recall_goto_open.png';
import message_push_recall_tips from '@/assets/common/message_push_recall_tips.png';
import './index.scss';
import { getStorageSync, setStorageSync, onAppShow, offAppShow } from '@tarojs/taro';
import { isToday } from '@/utils/timeHelper';
import Env, { compareVersion } from '@music/mobile-env';
import useUserInfoStore from '@/store/useUserInfoStore';

const PushReCallTip = () => {
    const [isShow, setIsShow] = useState(false);
    const selfUserInfo = useUserInfoStore((state) => state.userBaseInfo);

    const onGotoOpenPushClick = () => {
        rpc.caller('push.jumpToPushSetting', {});
    };

    const onCloseClick = () => {
        const recallCloseTimes = getStorageSync('storage.push.recallCloseTimes') || 0;
        setStorageSync('storage.push.recallCloseTimes', recallCloseTimes + 1);

        const timestamp = Date.now();
        setStorageSync('storage.push.recallCloseLastTime', timestamp);
        setIsShow(false);
    };

    const refreshUI = useCallback(() => {
        if (IS_MIRTH) {
            if (compareVersion(Env.getMirthVersion(), '2.11.6') < 0) {
                return;
            }
        }
        if (!IS_MIRTH) {
            return;
        }
        rpc.caller('push.getPushStatus', {}, (params) => {
            const { status } = params;
            if (status !== 1) {
                // 推送关闭
                setIsShow(false);
                return;
            }
            const initTime = selfUserInfo?.userBase?.initTime;
            if (!initTime || isToday(initTime)) {
                // 没获取到或者首日登录。不展示
                return;
            }
            const recallCloseTimes = getStorageSync('storage.push.recallCloseTimes') || 0;
            if (recallCloseTimes >= 3) {
                // 超过3次，不展示
                return;
            }
            const recallCloseLastTime = getStorageSync('storage.push.recallCloseLastTime') || 0;
            if (isToday(recallCloseLastTime)) {
                // 如果上次关闭时间是今天。不展示
                return;
            }

            setIsShow(true);
        });
    }, [setIsShow, selfUserInfo]);

    useEffect(() => {
        refreshUI();
        onAppShow(refreshUI);
        return () => {
            offAppShow(refreshUI);
        };
    }, [refreshUI]);

    return isShow ? (
        <View className="push-recall-container">
            <View className="push-recall-left-view">
                <Image className="push-recall-left-view-tips" src={message_push_recall_tips} />
                <View className="push-recall-left-view-texts">
                    <Text className="push-recall-left-view-text-1">开启消息通知</Text>
                    <Text className="push-recall-left-view-text-2">
                        Ta想你的时候也会给你发消息哦~
                    </Text>
                </View>
            </View>

            <View className="push-recall-right-view">
                <Image
                    src={message_push_recall_goto_open}
                    onClick={onGotoOpenPushClick}
                    className="push-recall-left-view-goto-open"
                />
                <Image
                    src={message_push_recall_close}
                    onClick={onCloseClick}
                    className="push-recall-left-view-close"
                />
            </View>
        </View>
    ) : null;
};

export default PushReCallTip;
