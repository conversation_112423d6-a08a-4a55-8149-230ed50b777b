import { V2NIMConversation } from 'nim-web-sdk-ng/dist/esm/nim/src/V2NIMConversationService';
import { AigcChatResourceInfo } from '@/types/bot';

// 定义消息对象接口
export interface LastMessage {
    scene: string;
    from: string;
    fromNick: string;
    fromClientType: string;
    fromDeviceId: string;
    to: string;
    time: number;
    type: string;
    text: string;
    isHistoryable: boolean;
    isRoamingable: boolean;
    isSyncable: boolean;
    cc: boolean;
    isPushable: boolean;
    isOfflinable: boolean;
    isUnreadable: boolean;
    isReplyMsg: boolean;
    needPushNick: boolean;
    needMsgReceipt: boolean;
    isLocal: boolean;
    resend: boolean;
    idClient: string;
    userUpdateTime: number;
    needUpdateSession: boolean;
    status: string;
    target: string;
    sessionId: string;
    flow: string;
    content?: string;
    custom?: string;
}

// 定义用户对象接口
export interface ToUser {
    avatarUrl: string;
}

// 定义主要的会话接口
export interface Conversation extends V2NIMConversation {
    sessionId: string;
    toUserDetail?: ChatContactListDto;
}

export interface ContactListResult {
    /** 返回结果 */
    data?: ChatContactListDto[];
    ChatContactListDto;
    message?: string;
    code: number;
}

export interface ChatContactListDto {
    /** 会话ID */
    contactId: string;
    /** 会话类型 */
    sessionType?: number;
    /** 用户信息 */
    userInfo?: UserBaseInfoDto;
    /** 最近一次上线时间 */
    lastOnlineTime?: number;
    /** 最近一次离线时间 */
    lastOfflineTime?: number;
    /** 业务扩展信息 */
    bizExtInfo?: Record<string, any>;

    aigcSessionInfo?: AigcRobotProfile;
    /** AIGCinfo */
    aigcInfo?: {
        aigcSessionInfo?: AigcRobotProfile;
        aigcChatResourceInfo: AigcChatResourceInfo;
        /** 是否有剧情 */
        hasAiChapter?: boolean;
    };
}

interface AigcRobotProfile {
    /** 用户id */
    robotUserId?: number;
    /** 刷新时间 */
    refreshTime?: number;
    /** 会话周期id */
    sessionPeriodId?: number;
}

interface UserBaseInfoDto {
    /** 用户id */
    userId?: number;
    /** 用户号 */
    userNo?: number;
    /** 昵称 */
    nickname?: string;
    /** 头像 */
    avatarImgUrl?: string;
    /** 人生态度 */
    signature?: string;
    /** 生日 */
    birthday?: string;
    /** 年龄 */
    age?: number;
    /** 星座 */
    constellation?: string;
    /** 性别 */
    gender?: number;
    /** 省 */
    province?: string;
    /** 市 */
    city?: string;
    /** 用户状态 */
    status?: number;
    /** 账号是否被封禁 */
    ban?: boolean;
    /** im账号ID */
    imAccId?: string;
    /** 账号注册时间 */
    initTime?: number;
    /** 是否真人认证 */
    realMan?: boolean;
    /** 是否实名认证 */
    certificated?: boolean;
    /** 额外属性 */ // key value形式
    userBaseInfoExt?: Map<string, any>;
    /** 头像 */
    avatarNosKey?: string;
}
