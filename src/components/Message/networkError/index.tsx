import React, { useEffect, useState } from 'react';
import { View, Text, Image } from '@tarojs/components';
import session_top_no_network from '@/assets/common/session_top_no_network.png';
import './index.scss';

interface WeakNetworkBannerProps {
    /** 提示文本 */
    message?: string;
    /** 弱网的自定义判断条件，传入此函数则使用自定义判断逻辑 */
}

const NetErrorTip: React.FC<WeakNetworkBannerProps> = ({
    message = '网络连接异常，请稍后再试',
}) => {
    return (
        <View className="net-error-tip">
            <Image className="banner-icon" src={session_top_no_network} />
            <Text className="banner-text">{message}</Text>
        </View>
    );
};

export default NetErrorTip;
