.message-pagep-wrapper {
    background: linear-gradient(to bottom, #fff7f9, #fff7fa);
    height: 100%;
    width: 100vw;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .message-list-bg {
        position: absolute;
        width: 100%;
        height: 278px;
        background-image: url('../../assets/common/icon-page-bg.jpg');
        background-repeat: no-repeat;
        background-size: cover;
        background-position: top center;
        top: 0;
        z-index: 0;
    }
}

.operator-top {
    color: #26262a !important;
}

.message-list {
    flex: 1;
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background-color: transparent;
    margin-top: 15px;
    z-index: 5;

    &.lost {
        margin-top: 0;
    }

    &.at-list::after {
        border: none !important;
    }

    &.at-list {
        background-color: #0000;
    }

    .cell {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100vw;
        height: 80px;
        background-color: transparent;
        flex-shrink: 0;

        .avatar {
            margin-left: 20px;
            flex-shrink: 0;
            width: 60px;
            height: 60px;
            border-radius: 50%;
        }

        .content {
            display: flex;
            flex-direction: column;
            margin-left: 12px;
            margin-right: 20px;
            overflow: hidden; // 防止内容溢出导致滚动
            height: 40px;

            .title-line {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: flex-start;
                width: 100%;
                margin-top: -2px;

                .title {
                    font-weight: bold;
                    font-size: 14px;
                    color: #26262a;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .official {
                    color: #fff;
                    margin-left: 2px;
                    font-size: 10px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                    flex-shrink: 0;
                    border-radius: 3px;
                    background: #ff689e;
                    padding: 1px 2px;
                }

                .time {
                    color: rgba(38, 38, 42, 0.2);
                    font-size: 11px;
                    padding-left: 10px;
                    margin-left: auto;
                    white-space: nowrap;
                }
            }

            .detail-line {
                display: flex;
                flex-direction: row;
                margin-top: 1px;
                flex: 1;
                justify-content: space-between;
                align-items: center;
                width: 100%;

                .detail {
                    font-size: 12px;
                    color: rgba(38, 38, 42, 0.4);
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: calc(100vw - 154px); // 添加这行，20px 是一个估计值，可能需要调整
                }

                .detail-image {
                    font-size: 12px;
                    font-weight: 400;
                    color: #ff689e;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: calc(100vw - 154px); // 添加这行，20px 是一个估计值，可能需要调整
                }

                .draft-content {
                    display: flex;
                    align-items: center;
                    font-size: 12px;
                    color: rgba(38, 38, 42, 0.4);
                    max-width: calc(100vw - 154px); // 添加这行，20px 是一个估计值，可能需要调整
                }

                .draft {
                    color: #ff689e;
                    font-size: 12px;
                    white-space: nowrap;
                }

                .unread-count {
                    display: flex;
                    background-color: #ff689e;
                    font-size: 12px;
                    padding-left: 5px;
                    padding-right: 5px;
                    height: 16px;
                    align-items: center;
                    justify-content: center;
                    border-radius: 8px;
                    color: white;
                }
            }
        }
    }
}

.custom-modal {
    .at-modal__container {
        width: 80%;
        max-width: 300px;
        border-radius: 16px;
    }

    .modal-content {
        padding: 30px 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .text-title {
        font-size: 18px;
        text-align: center;
        font-weight: 600;
        color: rgba(0, 0, 0, 1);
    }

    .text-content {
        margin-top: 12px;
        font-size: 13px;
        text-align: center;
        color: rgba(0, 0, 0, 0.4);
    }

    .btn-group {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: 30px;
    }

    .cancel,
    .confirm {
        width: 45%;
        height: 44px;
        font-size: 18px;
        font-weight: 600;
        border-radius: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        color: white;
    }

    .cancel {
        background-color: #bfbfbf;
    }

    .confirm {
        background-color: #ff689e;
    }
}

.empty-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;

    .empty-text {
        margin-top: 230px;
        font-size: 14px;
        font-weight: 400;
        color: #9e9e9e;
    }

    .chat-text {
        margin-top: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 94px;
        height: 36px;
        margin-left: 20px;
        margin-right: 20px;
        border-radius: 19px;
        background-color: #ff689e;
        font-size: 12px;
        font-weight: 600;
        color: #fff;
        text-align: center;
    }
}
// 添加骨架屏样式
.skeleton {
    &-avatar,
    &-title,
    &-time,
    &-detail {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 4px;
    }

    &-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
    }

    &-title {
        width: 120px;
        height: 20px;
    }

    &-time {
        width: 60px;
        height: 16px;
    }

    &-detail {
        width: 180px;
        height: 16px;
    }
}
