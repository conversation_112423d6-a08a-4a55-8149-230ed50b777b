import { MutableRefObject, useEffect } from 'react';
import { create } from 'zustand';
import configKeyboardWrapper from './keyboardWrapperRpc';

export type HTMLElementFinder = () => HTMLElement;
type KeyboardWrapAction = (event: string, params: any) => void;

interface IKeyboardWrapStackItem {
    // 弹起操作
    wrapAction: KeyboardWrapAction;
    // 弹起时长
    duration: number;
    // 弹起动作
    action: string;
    // 额外信息，可用于调试等。
    extra?: any;
}

interface IKeyboardWrapResponserStore {
    // 响应器堆栈
    wrapStack: Array<IKeyboardWrapStackItem>;
    // 响应器堆栈push
    push: (element: IKeyboardWrapStackItem) => void;
    // 响应器堆栈pop
    pop: (ele?: IKeyboardWrapStackItem) => void;

    // 注册RPC响应器
    registerRPCIfNeeded: () => void;
    // 是否需要注册
    registerNeeded: boolean;
}

export const useKeyboardWrapResponseStore = create<IKeyboardWrapResponserStore>((set, get) => {
    const registerRPCIfNeeded = () => {
        if (!get().registerNeeded) {
            return;
        }
        set({
            registerNeeded: false,
        });
        configKeyboardWrapper({ action: 'cover', duration: 0 }, (event: string, params: any) => {
            const wrapStack = get().wrapStack;

            for (let i = 0; i < wrapStack.length; i++) {
                const item = wrapStack[i];
                item?.wrapAction(event, params);
            }
        });
    };

    const push = (element: IKeyboardWrapStackItem) => {
        registerRPCIfNeeded();
        const wrapStack = get().wrapStack;
        set({
            wrapStack: [...wrapStack, element],
        });
    };

    const pop = (ele?: IKeyboardWrapStackItem) => {
        const wrapStack = get().wrapStack;
        if (wrapStack.length <= 0) {
            return;
        }
        if (ele) {
            for (let i = 0; i < wrapStack.length; i++) {
                const item = wrapStack[i];
                if (item === ele) {
                    set({
                        wrapStack: [...wrapStack.slice(0, i), ...wrapStack.slice(i + 1)],
                    });
                    break;
                }
            }
        } else {
            set({
                wrapStack: [...wrapStack.slice(0, wrapStack.length - 1)],
            });
        }
    };

    return {
        wrapStack: [],
        push,
        pop,

        registerRPCIfNeeded,
        registerNeeded: true,
    };
});

const findElement = (value: MutableRefObject<HTMLElement> | HTMLElement | HTMLElementFinder) => {
    if (value instanceof HTMLElement) {
        return value;
    }
    if (typeof value === 'function') {
        return value();
    }
    if (value && value.current && value.current instanceof HTMLElement) {
        return value.current;
    }
    return null;
};

const forEachElement = (
    values: Array<MutableRefObject<HTMLElement> | HTMLElement | HTMLElementFinder>,
    action: (element: HTMLElement) => void
) => {
    if (!values || values.length <= 0) {
        return;
    }
    for (const item of values) {
        const element = findElement(item);
        if (element) {
            action(element);
        }
    }
};

const useKeyboardWrapper = ({
    action = 'cover',
    duration = 0,
    callback = undefined,
    animate = true,
    transformOffset = 0,
    wrapper = undefined,
    containers = undefined,
    moveUp = 'wrapperWithKeyboard',
}: {
    action?: string;
    duration?: number;
    callback?: (event: string, params: any) => void;
    animate?: boolean;
    transformOffset?: number;
    wrapper?: MutableRefObject<HTMLElement> | HTMLElement | HTMLElementFinder;
    containers?: Array<MutableRefObject<HTMLElement> | HTMLElement | HTMLElementFinder>;
    moveUp: 'wrapper' | 'input' | 'wrapperWithKeyboard';
}) => {
    useEffect(() => {
        const animateElement = (element: HTMLElement, translate: number, dur: number) => {
            const style = element.style;
            style.transform = `translateY(${translate}px)`;
            style.transition = `transform ${dur}s cubic-bezier(0.17,0.84,0.44,1)`;
            style.willChange = 'transform';
        };

        const observeKeyboardWrapper = (event: string, params: any) => {
            let finalTranslate = 0;
            const finalDuration = (params.duration > 0 ? params.duration : duration) / 1000;
            if (event === 'show') {
                const currentFocus = document.activeElement;
                const isContained = findElement(wrapper).contains(currentFocus);
                if (!isContained) {
                    // 如果不在容器内，则不执行动画
                    return;
                }

                const keyboardHeight = (params.height / params.screenHeight) * window.outerHeight;

                // const keyborardPopupSafeSpace = 20;
                if (moveUp === 'input') {
                    // 根据输入框来计算需要弹起的高度
                    const rect = currentFocus.getBoundingClientRect();
                    const marginBottom = window.outerHeight - rect.bottom;
                    if (marginBottom > keyboardHeight) {
                        return;
                    }
                    finalTranslate = (keyboardHeight - marginBottom) * -1;
                } else if (moveUp === 'wrapper') {
                    // 根据wrapper来计算需要弹起的高度
                    const rect = findElement(wrapper).getBoundingClientRect();
                    const marginBottom = window.outerHeight - rect.bottom;
                    if (marginBottom > keyboardHeight) {
                        return;
                    }
                    finalTranslate = (keyboardHeight - marginBottom) * -1;
                } else {
                    // wrapper 整体根据键盘高度上移。
                    finalTranslate = keyboardHeight * -1;
                }

                finalTranslate += transformOffset;
            } else if (event === 'hide') {
                finalTranslate = 0;
            } else {
                return;
            }

            if (callback) {
                callback(event, params);
            }

            if (animate && action === 'cover') {
                const wrapperElement = findElement(wrapper);
                if (wrapperElement) {
                    console.info(
                        'fqfqfqfq wrap 开始动画弹起',
                        event,
                        params,
                        wrapper,
                        finalTranslate,
                        wrapperElement
                    );
                    animateElement(wrapperElement, finalTranslate, finalDuration);
                }
                forEachElement(containers, (container: HTMLElement) => {
                    animateElement(container, finalTranslate, finalDuration);
                });
            }
        };

        const item = {
            wrapAction: observeKeyboardWrapper,
            duration,
            action,
            extra: wrapper,
        };

        useKeyboardWrapResponseStore.getState().push(item);
        console.info(
            'fqfqfqfq useKeyboardWrapper 入栈完成',
            useKeyboardWrapResponseStore.getState().wrapStack
        );

        return () => {
            useKeyboardWrapResponseStore.getState().pop(item);
            console.info(
                'fqfqfqfq useKeyboardWrapper 出栈完成',
                useKeyboardWrapResponseStore.getState().wrapStack
            );
        };
    }, [action, duration, callback, animate, transformOffset, wrapper, containers, moveUp]);
};

export default useKeyboardWrapper;
