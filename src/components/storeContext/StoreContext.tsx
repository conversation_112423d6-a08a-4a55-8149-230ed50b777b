import React, { createContext, useContext, useEffect, useRef } from 'react';
import { StoreApi, UseBoundStore } from 'zustand';

export interface StoreLifecycle {
    onCreated?: () => void;
    onCleared?: () => void;
}

export function triggerOnCreated<T>(store: UseBoundStore<StoreApi<T>>) {
    const state = store.getState();
    const lifeState = state as StoreLifecycle;

    if (typeof lifeState?.onCreated === 'function') {
        lifeState?.onCreated();
    }
}

export function triggerOnCleared<T>(store: UseBoundStore<StoreApi<T>>) {
    const state = store.getState();
    const lifeState = state as StoreLifecycle;

    if (typeof lifeState?.onCleared === 'function') {
        lifeState?.onCleared();
    }
}

export interface ContextStore {
    get: <T>(storeProvider: StoreCreator<T>) => UseBoundStore<StoreApi<T>>;
}

export type StoreCreator<T> = (contextStore: ContextStore) => UseBoundStore<StoreApi<T>>;

export type StoreContextType = 'app' | 'page' | 'dialog' | 'component';

export interface StoreContextValue {
    stores: Map<StoreCreator<unknown>, UseBoundStore<StoreApi<unknown>>>;
    type?: StoreContextType;
    parent?: StoreContextValue;
}

const StoreContext = createContext<StoreContextValue>({
    stores: new Map(),
});

export interface StoreProviderProps {
    children: React.ReactNode;
    type?: StoreContextType;
}

const StoreProvider = ({ children, type }: StoreProviderProps) => {
    const parentContext = useContext(StoreContext);
    const storeContextRef = useRef<StoreContextValue>({
        stores: new Map(),
        type,
        parent: parentContext,
    });

    useEffect(() => {
        const currentStores = storeContextRef.current.stores;
        return () => {
            currentStores.forEach((store) => {
                triggerOnCleared(store);
            });
        };
    }, []);

    return (
        <StoreContext.Provider value={storeContextRef.current}>{children}</StoreContext.Provider>
    );
};

function getContextStoreInternal<T>(
    storeCreator: StoreCreator<T>,
    stores: Map<StoreCreator<unknown>, UseBoundStore<StoreApi<unknown>>>
): UseBoundStore<StoreApi<T>> {
    let store = stores.get(storeCreator);

    if (!store) {
        const contextStore: ContextStore = {
            get: (storeProvider) => getContextStoreInternal(storeProvider, stores),
        };
        store = storeCreator(contextStore);
        triggerOnCreated(store);
        stores.set(storeCreator, store);
    }

    return store as UseBoundStore<StoreApi<T>>;
}

/**
 * 建议使用 useStore 这种简化写法，功能与 useStore 相同
 * @param storeCreator
 */
export function useContextStore<T>(storeCreator: StoreCreator<T>): UseBoundStore<StoreApi<T>> {
    const { stores } = useContext(StoreContext);
    return getContextStoreInternal(storeCreator, stores);
}

/**
 * useContextStore的简化命名版本
 * @param storeCreator
 */
export function useStore<T>(storeCreator: StoreCreator<T>): UseBoundStore<StoreApi<T>> {
    const { stores } = useContext(StoreContext);
    return getContextStoreInternal(storeCreator, stores);
}

/**
 * 从父级 StoreContext 获取 store 实例
 * 如果父级不存在该 store，则会创建一个新的 store 实例
 */
export function useParentStore<T>(storeCreator: StoreCreator<T>): UseBoundStore<StoreApi<T>> {
    const contextValue = useContext(StoreContext);

    // 如果没有父级 context，则抛出错误
    if (!contextValue.parent) {
        throw new Error('useParentContextStore must be used within a nested StoreProvider');
    }

    return getContextStoreInternal(storeCreator, contextValue.parent.stores);
}

/**
 * 从当前开始查找第一个符合指定类型的 StoreContext，并从中获取 store 实例
 * @param storeCreator 创建 store 的函数
 * @param targetType 目标 context 类型
 * @returns store 实例
 */
export function useStoreByType<T>(
    storeCreator: StoreCreator<T>,
    targetType: StoreContextType
): UseBoundStore<StoreApi<T>> {
    // 从当前 context 开始查找
    let currentContext: StoreContextValue | undefined = useContext(StoreContext);

    // 查找第一个符合类型的 context
    while (currentContext && currentContext.type !== targetType) {
        currentContext = currentContext.parent;
    }

    // 如果找到匹配类型的 context，则使用它的 stores
    if (currentContext) {
        return getContextStoreInternal(storeCreator, currentContext.stores);
    }

    // 如果未找到匹配类型的 context，抛出错误
    throw new Error(
        `No StoreProvider with type "${targetType}" found. Make sure you have a StoreProvider with this type in the component tree.`
    );
}

/**
 * 从 app 类型的 StoreContext 获取 store 实例
 * @param storeCreator
 */
export function useAppStore<T>(storeCreator: StoreCreator<T>): UseBoundStore<StoreApi<T>> {
    return useStoreByType(storeCreator, 'app');
}

/**
 * 从 page 类型的 StoreContext 获取 store 实例
 * @param storeCreator
 */
export function usePageStore<T>(storeCreator: StoreCreator<T>): UseBoundStore<StoreApi<T>> {
    return useStoreByType(storeCreator, 'page');
}

/**
 * 从 dialog 类型的 StoreContext 获取 store 实例
 * @param storeCreator
 */
export function useDialogStore<T>(storeCreator: StoreCreator<T>): UseBoundStore<StoreApi<T>> {
    return useStoreByType(storeCreator, 'dialog');
}

/**
 * 从 component 类型的 StoreContext 获取 store 实例
 * @param storeCreator
 */
export function useComponentStore<T>(storeCreator: StoreCreator<T>): UseBoundStore<StoreApi<T>> {
    return useStoreByType(storeCreator, 'component');
}

export default StoreProvider;
