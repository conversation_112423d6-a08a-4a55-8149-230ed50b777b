import ErrorBoundary from '@/components/ErrorBoundary';
import StoreProvider from '@/components/storeContext/StoreContext';
import { DialogRootLayer } from '@/components/dialog';
import React from 'react';

export interface PageProps {
    children: React.ReactNode;
}

const Page = ({ children }: PageProps) => {
    return (
        <ErrorBoundary text="系统繁忙，请点击任意区域退出" needback>
            <StoreProvider type="page">
                {children}
                <DialogRootLayer />
            </StoreProvider>
        </ErrorBoundary>
    );
};

export default React.memo(Page);
