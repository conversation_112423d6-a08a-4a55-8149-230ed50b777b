.circle-loading-root {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .circle-img {
        width: 20px;
        height: 20px;
        background-image: url('../../assets/common/icon-circle-loading.png');
        background-size: 100% 100%;

        animation: rotate 1.2s linear infinite;
        transform-origin: center;
        display: block; // 确保正确应用变换
        backface-visibility: hidden;
        transform: translateZ(0);
        will-change: transform;
    }

    @keyframes rotate {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    // 兼容旧浏览器
    @-webkit-keyframes rotate {
        0% {
            -webkit-transform: rotate(0deg);
        }

        100% {
            -webkit-transform: rotate(360deg);
        }
    }
}
