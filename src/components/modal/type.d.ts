export interface TaroModalProps {
    visible: boolean;
    closable?: boolean;
    maskClosable?: boolean;
    style?: {};
    bodyStyle?: {};
    animationType?: string;
    onClose?(): void;
    footer?: never[];
    transparent?: boolean;
    popup?: boolean;
    animateAppear?: boolean;
    operation?: boolean;
    closeIconStyle?: any;
    closeIconName?: string;
    className?: string;
    closeIcon?: string;
}
