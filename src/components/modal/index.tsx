import { Image, View } from '@tarojs/components';
import React, { FunctionComponent } from 'react';
import { AtModal } from 'taro-ui';
import { CLOSE_ICON } from './constant';
import './index.scss';
import { TaroModalProps } from './type';

const TaroModal: FunctionComponent<TaroModalProps> = (props) => {
    const { visible = false, closable = false, onClose = () => {}, closeIcon = CLOSE_ICON } = props;

    return (
        <AtModal isOpened={visible} onClose={onClose}>
            <View className="at-modal-content">{props.children}</View>
            {closable && (
                <Image src={closeIcon} onClick={onClose} className="at-modal-content-close-iocn" />
            )}
        </AtModal>
    );
};

export default TaroModal;
