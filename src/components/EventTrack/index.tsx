/* eslint-disable react/prop-types */
import { View } from '@tarojs/components';
import Taro, { createIntersectionObserver } from '@tarojs/taro';
import fetch from '@/utils/fetch';
import React, { useEffect, useRef } from 'react';
import { useSessionStore } from '@/hooks/sessionStore';
import { isIOS, isAndroid } from '@/utils';
import { getAppName } from '@/utils/appSourceAdapter';

const eventTrackApi = (data: any) =>
    fetch('/api/mirth/home/<USER>/log', {
        method: 'post',
        data,
    });

const OSString = () => {
    if (isIOS) {
        return 'iphone';
    }
    if (isAndroid) {
        return 'android';
    }
    return 'h5';
};

function EventTrackView({ children, params, isPage = true }: any) {
    const trackRef = useRef(null);
    const durationRef = useRef(0);

    const reportPageAppear = async () => {
        const appearTime = Date.now();
        durationRef.current = Date.now();
        const requestParams = {
            action: isPage ? '_pv' : '_ev',
            userid: useSessionStore.getState().myUserId,
            log_time: appearTime,
            os: OSString(),
            appname: getAppName(),
            ...params,
        };
        const paramsString = JSON.stringify(requestParams);
        try {
            eventTrackApi({
                log: paramsString,
            });
        } catch (e) {
            console.error('埋点失败', e);
        }
    };

    const reportPageDisappear = async () => {
        if (!durationRef.current || durationRef.current === 0) {
            return;
        }
        const leaveTime = Date.now();
        const duration = leaveTime - durationRef.current;
        const requestParams = {
            action: isPage ? '_pd' : '_ed',
            userid: useSessionStore.getState().myUserId,
            log_time: leaveTime,
            os: OSString(),
            appname: getAppName(),
            _duration: duration,
            ...params,
        };
        const paramsString = JSON.stringify(requestParams);
        try {
            eventTrackApi({
                log: paramsString,
            });
        } catch (e) {
            console.error('埋点失败', e);
        }
        durationRef.current = 0;
    };

    useEffect(() => {
        // 判断环境，使用不同的 IntersectionObserver 实现
        if (process.env.TARO_ENV === 'weapp') {
            // 小程序环境
            const observer = createIntersectionObserver({
                thresholds: [0, 0.5, 1],
                observeAll: false,
            });
            observer.relativeToViewport().observe('.track-container', (res) => {
                if (res.intersectionRatio > 0) {
                    reportPageAppear();
                } else {
                    reportPageDisappear();
                }
            });

            return () => {
                reportPageDisappear();
                observer.disconnect();
            };
        }
        // H5 环境
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    reportPageAppear();
                } else {
                    reportPageDisappear();
                }
            });
        });
        const currentRef = trackRef.current;
        if (currentRef) {
            observer.observe(currentRef);
        }
        return () => {
            reportPageDisappear();
            if (currentRef) {
                observer.unobserve(currentRef);
            }
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return <View ref={trackRef}>{children}</View>;
}

export default EventTrackView;
