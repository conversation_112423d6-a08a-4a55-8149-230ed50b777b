import { Image, Text, View } from '@tarojs/components';
import { goBackNative } from '@/utils/rpc';
import classNames from 'classnames';
import { addClickLog } from '@/utils/logTool';
import { showBackIcon } from '@/utils/appSourceAdapter';
import React, { CSSProperties, useCallback } from 'react';
import addFriendIcon from '../../assets/header/icon-add-friend.png';
import backIcon from '../../assets/header/icon-back.png';
import useHomeStore, { HomeTabIndex } from '../Home/useHomeStore';
import './index.scss';

interface TitleBarProps {
    className?: string;
    style?: CSSProperties;
    title?: string;
    right?: React.ReactNode;
    hasRight?: boolean;
    source?: 'message' | 'friend';
}

const DefaultRightComponent = ({ source }: { source: 'message' | 'friend' }) => {
    const handleAddFriendClick = useCallback(() => {
        useHomeStore.getState().setCurrent(HomeTabIndex.explore);

        if (source === 'message') {
            addClickLog('btn_ai_message_addition|page_ai_message|page_h5_biz');
        } else if (source === 'friend') {
            addClickLog('btn_ai_friends_addition|page_ai_friends|page_h5_biz');
        }
    }, [source]);

    return (
        <View className="right-wrapper" onClick={handleAddFriendClick}>
            <Image className="add-friend-img" src={addFriendIcon} />
            <Text className="add-friend-txt">添加好友</Text>
        </View>
    );
};

const TitleBar: React.FC<TitleBarProps> = ({
    className = '',
    style,
    title,
    right,
    source,
    hasRight = true,
}: TitleBarProps) => {
    const renderRightContent = useCallback(() => {
        if (!hasRight) {
            return null;
        }
        return right || <DefaultRightComponent source={source} />;
    }, [hasRight, right, source]);

    return (
        <View
            className={classNames('title-wrapper', {
                className,
                noback: !showBackIcon(),
            })}
            style={{ ...style }}>
            <View className="left-wrapper">
                <Image className="header-back" src={backIcon} onClick={goBackNative} />
                {title && <Text className="header-title">{title}</Text>}
            </View>

            {renderRightContent()}
        </View>
    );
};

export default React.memo(TitleBar);
