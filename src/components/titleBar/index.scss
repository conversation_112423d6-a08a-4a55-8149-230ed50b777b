.header-wrapper {
    display: flex;
    flex-direction: column;
}

.status-bar {
    height: 44px;
}

.title-wrapper {
    width: 100%;
    height: 44px;
    padding-left: 10px;
    padding-right: 20px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    // background: #FAF7F9;

    .left-wrapper {
        display: flex;
        flex-direction: row;
        align-items: center;

        .header-back {
            width: 44px;
            height: 44px;
            padding: 9px;
        }

        .header-title {
            font-size: 22px;
            font-weight: 600;
            line-height: normal;
            letter-spacing: 0;
            color: #26262a;
            margin-left: -5px;
            margin-top: -1px;
        }
    }

    .right-wrapper {
        display: flex;
        flex-direction: row;
        align-items: center;

        .add-friend-img {
            width: 16px;
            height: 16px;
        }

        .add-friend-txt {
            margin-left: 2px;
            font-size: 14px;
            font-weight: 600;
            line-height: normal;
            text-align: right;
            letter-spacing: 0;
            color: #26262a;
            opacity: 0.8;
        }
    }

    &.noback {
        .header-back {
            display: none;
        }

        .header-title {
            padding-left: 10px;
        }
    }
}
