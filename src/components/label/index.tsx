import React, { useRef, useEffect, useState } from 'react';
import { View, Text, ViewProps } from '@tarojs/components';
import { createSelectorQuery } from '@tarojs/taro';
import './index.scss';

export interface LabelWrapperProps {
    labels: string[];
    marginRight?: number;
    lines?: number;
}

const LabelWrapper = ({ labels, marginRight = 4, lines = 999 }: LabelWrapperProps) => {
    const wrapperRef = useRef<ViewProps | null>(null);
    const [visibleLabels, setVisibleLabels] = useState<string[][]>([]);

    useEffect(() => {
        const updateVisibleLabels = async () => {
            if (wrapperRef.current) {
                const query = createSelectorQuery();
                const wrapperRect = await new Promise<any>((resolve) => {
                    query
                        .select('.label-wrapper')
                        .boundingClientRect()
                        .exec((res) => {
                            resolve(res[0]);
                        });
                });

                // console.log('LabelWrapper-->wrapperRect', wrapperRect);

                const wrapperWidth = wrapperRect?.width || 0;
                let currentWidth = 0;
                let currentRow: string[] = [];
                const rows: string[][] = [];

                for (const [index, label] of labels?.entries()) {
                    const labelRect = await new Promise<any>((resolve) => {
                        const labelQuery = createSelectorQuery();
                        labelQuery
                            .select(`#label-measure-${label}`)
                            .boundingClientRect()
                            .exec((res) => {
                                resolve(res[0]);
                            });
                    });

                    // console.log('LabelWrapper-->labelRect', labelRect);

                    const labelWidth = labelRect?.width || 0;

                    if (currentWidth + labelWidth + marginRight <= wrapperWidth) {
                        currentWidth += labelWidth + marginRight;
                        currentRow.push(label);
                    } else {
                        if (currentRow.length === 0) {
                            // 本组标签为空，即使宽度不足也要放在本组标签中
                            currentRow.push(label);
                            rows.push(currentRow);
                            currentWidth = 0;
                            currentRow = [];
                            // console.log('LabelWrapper-->currentRow1', rows, currentRow);
                        } else {
                            // 添加到下一组标签中
                            rows.push(currentRow);
                            currentRow = [];
                            currentRow.push(label);
                            currentWidth = labelWidth + marginRight;
                            // console.log('LabelWrapper-->currentRow2', rows, currentRow);
                        }

                        if (rows.length >= lines) {
                            break;
                        }
                    }

                    if (index === labels?.length - 1) {
                        rows.push(currentRow);
                        // console.log('LabelWrapper-->currentRow3', rows, currentRow);
                    }
                }

                // console.log('LabelWrapper-->rows', rows);

                setVisibleLabels(rows);
            }
        };

        updateVisibleLabels();
    }, [labels, marginRight, lines]);

    // console.log('LabelWrapper-->labels', labels);
    // console.log('LabelWrapper-->visibleLabels', visibleLabels);

    return (
        <View className="label-wrapper" ref={wrapperRef}>
            {visibleLabels?.map((row, rowIndex) => (
                <View key={rowIndex} className="label-row">
                    {row.map((label, index) => (
                        <Text
                            key={index}
                            className="label"
                            style={{
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                            }}>
                            {label}
                        </Text>
                    ))}
                </View>
            ))}
            {/* 隐藏的元素用于测量每个标签的宽度 */}
            {labels?.map((label) => (
                <Text
                    key={`measure-${label}`}
                    id={`label-measure-${label}`}
                    className="label label-measure">
                    {label}
                </Text>
            ))}
        </View>
    );
};

export default LabelWrapper;
