.label-wrapper {
    margin-top: 8px;
    display: flex;
    flex-direction: row;
    flex: 1;
    flex-wrap: wrap;
    overflow: hidden;
}

.label-row {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    overflow: hidden;
    margin-bottom: 4px;
}

.label {
    font-size: 11px;
    font-weight: normal;
    line-height: 100%;
    letter-spacing: 0;
    border-radius: 98px;
    background: #f8eeee;
    color: #c56f92;
    padding: 4px 8px;
    margin-right: 4px;
}

.label-measure {
    // 继承 .label 的所有样式
    @extend .label;

    // 使元素不可见但保持布局
    visibility: hidden;

    // 将元素移出视口，但保持其存在于 DOM 中
    position: absolute;
    top: -9999px;
    left: -9999px;

    // 确保文本不换行，以准确测量宽度
    white-space: nowrap;

    // 重置一些可能影响测量的属性
    margin: 0;
    padding: 4px 8px; // 保持与 .label 相同的内边距

    // 确保元素不会被压缩或拉伸
    flex-shrink: 0;
    flex-grow: 0;
}
