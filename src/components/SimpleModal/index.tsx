import { CreateModalProps } from '@/components/dialog';
import { View } from '@tarojs/components';
import React, { useCallback } from 'react';

import { ITouchEvent } from '@tarojs/components/types/common';
import DialogActionProvider from '@/components/dialog/DialogActionProvider';
import './index.scss';

export interface SimpleModalProps {
    /**
     * 是否居中显示~
     */
    isCenter?: boolean;
    /**
     * 待显示view~
     */
    children?: React.ReactNode;
    /**
     * 点击外部区域 关闭弹框~
     */
    closeOnClickOverlay?: boolean;
}

type ModalProps = SimpleModalProps & {
    dismiss: () => void;
};

const SimpleModal = ({ isCenter, children, closeOnClickOverlay, dismiss }: ModalProps) => {
    const onClickBg = useCallback(
        (e?: ITouchEvent) => {
            if (closeOnClickOverlay && e?.target === e?.currentTarget) {
                // 只有当点击的是背景元素本身(而不是children)时才触发dismiss
                dismiss?.();
            }
        },
        [dismiss, closeOnClickOverlay]
    );

    return (
        <View className={isCenter ? 'center-modal-root' : 'bottom-modal-root'} onClick={onClickBg}>
            {children}
        </View>
    );
};

export default React.memo(SimpleModal);

export const createSimpleModalProvider = (options?: {
    isCenter?: boolean;
    isModal?: boolean;
    closeOnClickOverlay?: boolean; // 点击其他区域是否关闭~
}): CreateModalProps => {
    return {
        type: options?.isCenter ? 'modal' : 'float',
        isModal: options?.isModal,
        hideInternalSync: true,
        render(dismiss, simpleModalProps) {
            return (
                <DialogActionProvider dismiss={dismiss}>
                    <SimpleModal
                        {...simpleModalProps}
                        dismiss={dismiss}
                        closeOnClickOverlay={options?.closeOnClickOverlay ?? true}
                        isCenter={options?.isCenter}
                    />
                </DialogActionProvider>
            );
        },
    };
};
