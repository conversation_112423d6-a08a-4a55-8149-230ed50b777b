import React, { useRef } from 'react';
import { View } from '@tarojs/components';
import './index.scss';
import { AlphaVideo, SizeMode } from '@music/ct-animation';

const AnimatorFavorAbility = ({
    url,
    loop,
    ready,
    fade,
    sizeMode,
    preFinish,
    finish,
}: {
    url: string;
    loop: boolean;
    fade?: boolean;
    sizeMode?: SizeMode;
    ready?: () => void;
    preFinish?: () => void;
    finish?: () => void;
}) => {
    const ref = useRef<any>(null);
    const eventHandler = [
        'onDataReady',
        'onReady',
        'onPause',
        'onPlay',
        'onError',
        'onLoopComplete',
        'onComplete',
        'onDestroy',
        'onDataError',
    ].reduce(
        (acc, e) => ({
            ...acc,
            [e]: () => {
                console.log('video', '组件绑定当前事件为：', e);

                if (e === 'onDataReady') {
                    if (fade && ref.current) {
                        ref.current.style.opacity = 0;
                    }
                }

                if (
                    e === 'onComplete' ||
                    e === 'onError' ||
                    e === 'onLoopComplete' ||
                    e === 'onDataError'
                ) {
                    if (e === 'onLoopComplete' && loop) {
                        // 不做任何处理~
                    } else {
                        preFinish?.();
                        if (fade === true) {
                            if (ref.current) {
                                ref.current.style.opacity = 0;
                            }
                            setTimeout(() => {
                                finish?.();
                            }, 500);
                        } else {
                            finish?.();
                        }
                    }
                }

                if (e === 'onPlay') {
                    ready?.();
                    if (fade === true) {
                        if (ref.current) {
                            ref.current.style.opacity = 1;
                        }
                    }
                }
            },
        }),
        {}
    );
    return url === '' ? null : (
        <View ref={ref} className="video-player">
            <AlphaVideo
                src={url}
                style={{ width: '100%', height: '100%' }}
                prefetchMp4={false}
                loop={!!loop}
                sizeMode={sizeMode}
                {...eventHandler}
            />
        </View>
    );
};

export default React.memo(AnimatorFavorAbility);
