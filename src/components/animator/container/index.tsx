import { View } from '@tarojs/components';
import React, { useEffect, useRef, useState } from 'react';
import './index.scss';
import AnimatorFavorability from '@/components/animator/favorability';
import { Property } from 'csstype';

const CenterAnimatorContainer = ({
    videoUrl,
    loop,
    fade,
    topZIndex,
    top,
    ready,
    finish,
}: {
    videoUrl: string;
    loop?: boolean;
    fade?: boolean;
    topZIndex?: boolean;
    top?: Property.Top<any> | undefined;
    ready?: () => void;
    finish?: () => void;
}) => {
    return videoUrl == '' ? null : (
        <View
            className={`animatorStyle ${topZIndex == true ? 'zindex_110' : 'zindex_40'}`}
            style={{ top: top || 0 }}>
            <AnimatorFavorability
                url={videoUrl}
                loop={loop || false}
                fade={fade}
                ready={ready}
                finish={finish}
            />
        </View>
    );
};
export default React.memo(CenterAnimatorContainer);
