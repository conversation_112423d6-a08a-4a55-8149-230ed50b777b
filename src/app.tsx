import React, { lazy, Suspense } from 'react';
import { View } from '@tarojs/components';
import ErrorBoundary from '@/components/ErrorBoundary';
import ModalContext from '@/components/ModalContext';
import StoreProvider from '@/components/storeContext/StoreContext';

import { useSessionStore } from '@/hooks/sessionStore';

import { useHeartbeat } from './hooks/app/useHeartbeat';
import { useSwipeBackConfig } from './hooks/app/useSwipeBackConfig';
import { useIMInit, useUserInfoInit, useEnvInit } from './hooks/app/useInit';
import { usePreloadEpicPlayer } from './hooks/app/usePreload';

import usePushJump from './router/usePushJump';
import './app.scss';

// 使用lazy加载DialogRootLayer组件
const DialogRootLayer = lazy(() =>
    import('@/components/dialog').then((module) => ({
        default: module.DialogRootLayer,
    }))
);

const ToastContainer = lazy(() =>
    import('@/components/Toast').then((module) => ({
        default: module.ToastContainer,
    }))
);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function App(props: any) {
    const loginstatus = useSessionStore((state) => state.loginstatus);
    // 初始化
    useHeartbeat(loginstatus);
    useIMInit();
    useUserInfoInit();
    useEnvInit();
    useSwipeBackConfig();
    usePushJump();
    // 预加载
    usePreloadEpicPlayer();

    return (
        <ErrorBoundary
            text="系统繁忙，请点击任意区域重进"
            onClick={() => {
                window?.location?.reload();
            }}>
            <Suspense fallback={<View />}>
                <ToastContainer />
            </Suspense>
            <StoreProvider type="app">
                <Suspense fallback={<View />}>
                    <DialogRootLayer />
                </Suspense>
                <ModalContext />
                {/* 升级到taro3.6+版本以后，这里必须放在最后一个节点 */}
                {props.children}
            </StoreProvider>
        </ErrorBoundary>
    );
}

export default App;
