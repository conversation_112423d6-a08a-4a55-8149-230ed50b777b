module.exports = {
    animation: { duration: 200, delay: 0 },
    window: {
        backgroundTextStyle: 'light',
        navigationBarBackgroundColor: '#fff',
        navigationBarTitleText: 'WeChat',
        navigationBarTextStyle: 'black',
        navigationStyle: 'custom',
    },
    rn: {
        screenOptions: {
            // 设置页面的options，参考https://reactnavigation.org/docs/stack-navigator/#options
            shadowOffset: {
                width: 0,
                height: 0,
            },

            borderWidth: 0,
            elevation: 0,
            shadowOpacity: 1,
            borderBottomWidth: 0,
        },
    },
};
