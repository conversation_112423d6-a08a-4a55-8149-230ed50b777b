import { useEffect } from 'react';
import Dawn from '@dawn/react';
import fetch from '@/utils/fetch';
import { getAppSource } from '@/utils/appSourceAdapter';

const useDawnInit = (init = true) => {
    useEffect(() => {
        if (init) {
            Dawn.init({
                // 初始化时设置公共参数
                globalParams: {
                    app_source: getAppSource(),
                },
                onPageAppear: (callback: () => void) => {
                    callback();
                },
                onPageDisappear: (callback: () => void) => {
                    callback();
                },
                reportLogs: ({ logs: originalLogs }) => {
                    if (!originalLogs?.length) return;

                    const logs = originalLogs.map((item: any) => {
                        const params = item.params || {};
                        // eslint-disable-next-line no-underscore-dangle
                        const spm = params?._spm || '';
                        return {
                            action: item?.event || '',
                            json: {
                                ...params,
                                _spm: `${spm}|page_h5_biz`,
                            },
                        };
                    });

                    fetch('/api/feedback/weblog', {
                        method: 'POST',
                        data: {
                            logs: JSON.stringify(logs),
                            multiupload: true,
                        },
                    })
                        .then(() => {
                            console.log('接口上报日志', logs);
                        })
                        .catch((err) => {
                            console.error('接口上报日志', logs, err);
                        });
                },
            });
        }
    }, [init]);
};

export default useDawnInit;
