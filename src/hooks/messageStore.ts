/* eslint-disable @typescript-eslint/no-use-before-define */
import { create } from 'zustand';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import { V2NIMMessage } from 'nim-web-sdk-ng/dist/esm/nim/src/V2NIMMessageService';
import { MsgDataType } from '@/types/im';
import { coronaWarnMessage } from '@music/mat-base-h5';
import injectExt from './message/injectMessage';
import { nim } from './nimInstance';

export enum LastMsgStatus {
    received,
    sent,
}

export interface MessageStore {
    conversationId: string;
    sessionId: string;
    periodId: string;
    filterIgnorePeriodId: boolean; // 在删选会话可见时，是否比对periodId
    originalList: V2NIMMessage[]; // 原始消息列表
    filteredMessages: MsgDataType[]; // 转换和过滤后的消息列表
    messageList: MsgDataType[]; // 最终展示的消息列表
    lastMsgStatus: LastMsgStatus;
    showLoading: boolean;
    loadingData: boolean;
    pushMsgByPage: boolean;
    // 加载超时时间(默认 15s，长文模式下 35s)
    loadingTimeout: number;

    setLastMsgStatus: (status: LastMsgStatus) => void;
    setConversationIdAndSessionId: (conversationId: string, sessionId: string) => void;
    setPeriodId: (periodId: string) => void;
    pushMsgs: (msgs: V2NIMMessage | V2NIMMessage[], type: PushMsgType) => void;
    deleteMsg: (msgsClientId: string | string[]) => void;
    replaceMsg: (msg: V2NIMMessage) => void;
    reloadMsgList: () => void;
    leaveSession: () => void;
    updateLoadingTimeout: (loadingTimeout: number) => void;
}

export enum PushMsgType {
    send = 'send',
    receive = 'receive',
    history = 'history',
}

type IllegalChecker = {
    (message: MsgDataType): boolean;
};

const TextMessageChecker: IllegalChecker = (message: MsgDataType) => {
    return (
        message?.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_TEXT &&
        !message?.text
    );
};

const checkers = [TextMessageChecker];

export const useMessageStore = create<MessageStore>((set, get) => {
    let timeout: ReturnType<typeof setTimeout> | null = null;

    return {
        conversationId: '',
        sessionId: '',
        periodId: '',
        filterIgnorePeriodId: false,
        messageList: [],
        originalList: [],
        filteredMessages: [],
        lastMsgStatus: LastMsgStatus.received,
        showLoading: false,
        loadingData: false,
        // 区分是否分页推送，还是单条触发推送：发送消息、收到消息、送礼等
        pushMsgByPage: false,
        // 加载默认超时时间 15s
        loadingTimeout: 15000,

        setConversationIdAndSessionId: (conversationId, sessionId) => {
            set({ conversationId, sessionId });
        },

        setPeriodId: (periodId) => {
            set({ periodId });
            // 更新会话列表
            const messages = filterPeriodMessage(get().originalList);
            coronaWarnMessage(
                'NIMService',
                `更新periodId：${periodId},原始数据数量:${
                    get().originalList.length
                },过滤后的数据数量:${messages.length}`
            );
            set({ filteredMessages: messages });
            get().reloadMsgList();
        },

        leaveSession: () => {
            set({
                messageList: [],
                originalList: [],
                filteredMessages: [],
                periodId: '',
                filterIgnorePeriodId: false,
                sessionId: '',
                conversationId: '',
                lastMsgStatus: LastMsgStatus.received,
                showLoading: false,
                pushMsgByPage: false,
            });
            if (timeout !== null) {
                clearTimeout(timeout);
            }
        },

        setLastMsgStatus: (status) => {
            set({ lastMsgStatus: status, showLoading: status === LastMsgStatus.sent });
            get().reloadMsgList();
            if (status === LastMsgStatus.sent) {
                if (timeout !== null) {
                    clearTimeout(timeout);
                }
                timeout = setTimeout(() => {
                    set({ lastMsgStatus: LastMsgStatus.received, showLoading: false });
                    get().reloadMsgList();
                }, get().loadingTimeout);
            }
        },

        pushMsgs: (msgs, type) => {
            let msgsArray = Array.isArray(msgs) ? msgs : [msgs];

            if (timeout !== null) {
                clearTimeout(timeout);
            }

            msgsArray = msgsArray.filter((item) => {
                return get().conversationId === item.conversationId;
            });
            if (msgsArray.length === 0) {
                return;
            }

            set((state: MessageStore) => {
                const newOriginalList =
                    type === PushMsgType.history
                        ? [...state.originalList, ...msgsArray]
                        : [...msgsArray, ...state.originalList];
                const newFilterList =
                    type === PushMsgType.history
                        ? [...state.filteredMessages, ...filterPeriodMessage(msgsArray)]
                        : [...filterPeriodMessage(msgsArray), ...state.filteredMessages];
                return {
                    ...state,
                    originalList: newOriginalList,
                    filteredMessages: newFilterList,
                    pushMsgByPage: type === PushMsgType.history,
                    lastMsgStatus:
                        type === PushMsgType.history ? state.lastMsgStatus : LastMsgStatus.received,
                };
            });

            get().reloadMsgList();

            if (type === PushMsgType.receive) {
                try {
                    const conversationId = get().conversationId;
                    nim.V2NIMConversationService.clearUnreadCountByIds([conversationId]);
                } catch (e) {
                    console.warn('清除未读数失败', e);
                }
            }
        },

        deleteMsg: (msgsClientId: string | string[]) => {
            const msgsClientIdArray = Array.isArray(msgsClientId) ? msgsClientId : [msgsClientId];
            if (msgsClientIdArray.length === 0) {
                return;
            }

            set((state: MessageStore) => {
                return {
                    ...state,
                    originalList: state.originalList.filter((item) => {
                        return !msgsClientIdArray.includes(item.messageClientId);
                    }),
                    filteredMessages: state.filteredMessages.filter((item) => {
                        return !msgsClientIdArray.includes(item.messageClientId);
                    }),
                };
            });

            get().reloadMsgList();
        },

        replaceMsg: (msg) => {
            const originalList = get().originalList;
            const filterList = get().filteredMessages;
            const originalIndex = originalList.findIndex((item) => {
                return item.messageClientId === msg.messageClientId;
            });
            const filterIndex = filterList.findIndex((item) => {
                return item.messageClientId === msg.messageClientId;
            });
            if (originalIndex === -1 || filterIndex === -1) {
                return;
            }

            const newList = [...get().originalList];
            const newFilterList = [...get().filteredMessages];
            newList[originalIndex] = msg;

            const filterMessage = filterPeriodMessage([msg]);
            if (filterMessage.length) {
                newFilterList[filterIndex] = filterMessage[0];
            }

            set({ originalList: newList });
            set({ filteredMessages: newFilterList });

            get().reloadMsgList();
        },

        reloadMsgList: () => {
            const loading = get().lastMsgStatus === LastMsgStatus.sent;
            const displayMessages = loading
                ? [{ messageClientId: 'loading' } as MsgDataType, ...get().filteredMessages]
                : [...get().filteredMessages];
            set({ messageList: displayMessages });
        },
        updateLoadingTimeout: (loadingTimeout) => {
            set({ loadingTimeout });
        },
    };
});

/**
 * @description 会话筛选 包括可见性、有效性
 * @param sessionId 当前会话id
 * @param msgs 消息列表
 * @returns
 */

const filterPeriodMessage = (messages: V2NIMMessage[]) => {
    const { periodId, filterIgnorePeriodId } = useMessageStore.getState();
    return (messages as MsgDataType[])
        .map((item) => {
            if (Object.isExtensible(item)) {
                injectExt(item);
                return item;
            }
            const message = { ...item };
            injectExt(message);
            return message;
        })
        .filter((msg) => {
            if (msg.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_TEXT) {
                return true;
            }
            const custom = msg?.customExt || JSON.parse(msg?.serverExtension);
            const sessionPeriodIdServer = custom?.serverExt?.sessionPeriodId?.toString();
            const sessionPeriodIdClient = custom?.clientExt?.sessionPeriodId?.toString();

            const senderInvisible = custom?.senderInvisible; // 目前没有用到，后续可能会用到
            const receiverInvisible = custom?.receiverInvisible;
            const isSelf = msg.isSelf;
            if (!periodId) {
                return false;
            }
            if (!msg?.serverExtension) {
                return false;
            }

            if (
                !filterIgnorePeriodId &&
                sessionPeriodIdServer !== periodId &&
                sessionPeriodIdClient !== periodId
            ) {
                // 会话id不匹配
                return false;
            }

            if (!isSelf && receiverInvisible === true) {
                // 远端发送的消息，接收者不可见
                return false;
            }
            if (isSelf && senderInvisible === true) {
                // 自己发的消息，发送者不可见（一些隐藏消息）
                return false;
            }

            const illegalMessage = checkers.find((check) => {
                return check(msg);
            });

            if (illegalMessage) {
                return false;
            }
            // Hide 和 Drop 能力目前没有用到（需要配置时间和版本等）。
            return true;
        });
};
