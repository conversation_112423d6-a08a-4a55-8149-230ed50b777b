import { useCallback, useEffect, useState } from 'react';
import { rpc } from '@music/mat-base-h5';
import { isIOS, isAndroid } from '@/utils';

interface IUploadAvatarProps {
    needBeauty?: boolean;
    ratio?: number;
    uploadSuccessCallback: (
        avatarImgUrlKey: string,
        avatarImgNosKey: string,
        width: number,
        height: number
    ) => unknown;
    uploadFailCallback: () => unknown;
}

interface IUploadResult {
    avatarImgUrl: string;
    avatarImgNosKey: string;
    width: number;
    height: number;
}

let loading = false;
function useUploadImage({
    needBeauty = false,
    ratio = -1,
    uploadSuccessCallback,
    uploadFailCallback,
}: IUploadAvatarProps): any[] {
    const [uploadRes, setUploadRes] = useState<IUploadResult>({
        avatarImgUrl: '',
        avatarImgNosKey: '',
        width: 0,
        height: 0,
    });
    // pending success failure
    const [uploadStatus, setUploadStatus] = useState('pending');

    const onFileUploadSuccess = useCallback(
        async (imageData: { url: string; nosKey: string; width: number; height: number }) => {
            const params = {
                base: JSON.stringify({
                    avatarImgNosKey: imageData.nosKey,
                }),
            };
            setUploadRes({
                avatarImgUrl: imageData.url,
                avatarImgNosKey: imageData.nosKey,
                width: imageData.width,
                height: imageData.height,
            });
            setUploadStatus('success');
            uploadSuccessCallback(
                imageData.url,
                imageData.nosKey,
                imageData.width,
                imageData.height
            );
        },
        []
    );

    const toUpload = async () => {
        try {
            if (loading) return;
            loading = true;

            const result: any = await rpc.caller('file.image', {
                minHeight: 0,
                minWidth: 0,
                ratio,
                showCamera: true,
                needBeauty,
            });
            if (!result || result.status !== 'success') {
                setUploadStatus('failure');
                uploadFailCallback();
            }
            loading = false;
        } catch (error) {
            loading = false;
        }
    };

    useEffect(() => {
        if (isIOS || isAndroid) {
            rpc.on('file.fileUploadSuccess', onFileUploadSuccess);

            return () => {
                rpc.off('file.fileUploadSuccess', onFileUploadSuccess);
            };
        }
    }, [onFileUploadSuccess, uploadRes]);

    return [toUpload, uploadStatus, uploadRes];
}

export default useUploadImage;
