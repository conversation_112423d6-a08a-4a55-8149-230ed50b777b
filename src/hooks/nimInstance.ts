import {
    NIM,
    setAdapters,
    wxAdapters,
    browserAdapters,
    V2NIMMessageService,
    V2NIMConversationService,
    V2NIMMessageLogUtil,
    V2NIMConversationGroupService,
    V2NIMNotificationService,
    V2NIMMessageExtendUtil,
    V2NIMAIService,
} from 'nim-web-sdk-ng/dist/esm/nim';
import { getAppKey } from '@/utils';

// 设置浏览器环境的适配器
if (process.env.TARO_ENV === 'weapp') {
    setAdapters(wxAdapters);
} else {
    setAdapters(browserAdapters);
}
NIM.registerService(V2NIMMessageService, 'V2NIMMessageService');
NIM.registerService(V2NIMConversationService, 'V2NIMConversationService');
NIM.registerService(V2NIMConversationGroupService, 'V2NIMConversationGroupService'); // 会话分组模块。
NIM.registerService(V2NIMMessageLogUtil, 'V2NIMMessageLogUtil'); // 消息模块-消息记录相关工具类. 包含查询消息历史等功能
NIM.registerService(V2NIMMessageExtendUtil, 'V2NIMMessageExtendUtil'); // 消息模块-扩展功能. 包含 pin 消息, 收藏消息等功能
NIM.registerService(V2NIMNotificationService, 'V2NIMNotificationService');
NIM.registerService(V2NIMAIService, 'V2NIMAIService');

export { NIM, V2NIMConversationService };

export const nim = NIM.getInstance(
    {
        appkey: getAppKey(),
        debugLevel: IS_ONLINE ? 'warn' : 'debug',
        apiVersion: 'v2',
        enableV2CloudConversation: true,
    },
    {
        syncConfig: {
            teams: false,
            relations: false,
            friends: false,
            friendUsers: false,
            myTeamMembers: false,
            superTeams: false,
            mySuperTeamMembers: false,
            superTeamRoamingMsgs: false,
            deleteSuperTeamMsg: false,
            superTeamModifyMessage: false,
        },
        loggerConfig: {
            debugLevel: IS_ONLINE ? 'off' : 'debug',
            storageEnable: !IS_ONLINE,
        },
    }
);
