import Taro, { onAppHide, setStorageSync, showToast } from '@tarojs/taro';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { MessageErrorCode, MsgDataType, SystemMessage } from '@/types/im';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import {
    V2NIMMessage,
    V2NIMMessageListOption,
} from 'nim-web-sdk-ng/dist/esm/nim/src/V2NIMMessageService';
import { V2NIMConversation } from 'nim-web-sdk-ng/dist/esm/nim/src/V2NIMConversationService';
import useScenarioStore from '@/pages/chat/store/useScenarioStore';
import { gotoRealNameAuth } from '@/utils/appSourceAdapter';
import MessageTypeUtils from '@/utils/messageTypeUtils';
import { useStreamingStore } from './useStreamingStore';
import injectExt from './message/injectMessage';
import { useSessionStore } from './sessionStore';
import MessageFlow from './message/MessageFlow';
import { nim } from './nimInstance';
import { LastMsgStatus, PushMsgType, useMessageStore } from './messageStore';

let syncFinished = false;
export default class NIMService {
    static getNIMSessionStorage = () => {
        useSessionStore.getState().getStorageSessions();
    };

    static loginNIM = async (accountId: string, token: string) => {
        this.addEvents();
        useSessionStore.getState().setMySessionId(accountId);

        try {
            await nim.V2NIMLoginService.login(accountId, token, {
                retryCount: 3,
                timeout: 60000,
                forceMode: true,
                authType: 0,
            });
            coronaWarnMessage('NIMService', `${accountId} 云信登录成功`);
        } catch (e) {
            // 登录错误
            coronaWarnMessage('NIMService', `${accountId} 云信登录失败 ${JSON.stringify(e)}`);
        }
    };

    static addEvents = () => {
        nim.V2NIMLoginService.on('onConnectStatus', (data) => {
            useSessionStore.getState().setConnectstatus(data);
        });

        nim.V2NIMLoginService.on('onLoginStatus', (data) => {
            useSessionStore.getState().setLoginstatus(data);
        });

        nim.V2NIMConversationService.on('onConversationCreated', (data) => {
            const { sessionList } = useSessionStore.getState();
            useSessionStore.getState().setSessionList([data, ...sessionList]);
        });

        nim.V2NIMConversationService.on('onConversationChanged', (data) => {
            const { sessionList } = useSessionStore.getState();
            const updatedSessions: V2NIMConversation[] = [];

            // 这段逻辑是为了维护会话列表的lastMsg
            // 当消息发送被拦截后，lastMsg会被更新为被拦截的消息，导致展示错误
            // 期望是使用上一条正确的lastMsg作为展示
            data.forEach((newSession) => {
                const existingSession = sessionList.find(
                    (session) => session.conversationId === newSession.conversationId
                );
                if (
                    existingSession &&
                    newSession.lastMessage?.lastMessageState ===
                        V2NIMConst.V2NIMLastMessageState.V2NIM_MESSAGE_STATUS_BACKFILL
                ) {
                    updatedSessions.push({
                        ...newSession,
                        lastMessage: existingSession.lastMessage,
                    });
                } else {
                    updatedSessions.push(newSession);
                }
            });

            // 获取不在更新列表中的会话
            const unchangedSessions = sessionList.filter(
                (session) =>
                    !data.some((newSession) => newSession.conversationId === session.conversationId)
            );

            // 更新会话列表
            useSessionStore.getState().setSessionList([...updatedSessions, ...unchangedSessions]);
        });

        nim.V2NIMConversationService.on('onConversationDeleted', (data) => {
            const { sessionList } = useSessionStore.getState();
            const oldValues = sessionList.filter((item) => {
                return !data.includes(item.conversationId);
            });
            useSessionStore.getState().setSessionList(oldValues);
        });

        nim.V2NIMConversationService.on('onSyncFinished', () => {
            coronaWarnMessage('NIMService', `onSyncFinished`);
            NIMService.loadConversations();
        });

        nim.V2NIMLoginService.on('onDataSync', (type, state) => {
            if (type === V2NIMConst.V2NIMDataSyncType.V2NIM_DATA_SYNC_TYPE_MAIN) {
                coronaWarnMessage('NIMService', `onDataSync ${state}`);
                if (state === V2NIMConst.V2NIMDataSyncState.V2NIM_DATA_SYNC_STATE_COMPLETED) {
                    syncFinished = true;
                } else {
                    syncFinished = false;
                }
            }
        });

        nim.V2NIMMessageService.on('onSendMessage', (data) => {
            if (
                data.sendingState ===
                V2NIMConst.V2NIMMessageSendingState.V2NIM_MESSAGE_SENDING_STATE_SUCCEEDED
            ) {
                MessageFlow.onSendMessage(data as MsgDataType);
                NIMService.markSessionCanShow(data.conversationId);
            }

            if (
                data.sendingState !==
                V2NIMConst.V2NIMMessageSendingState.V2NIM_MESSAGE_SENDING_STATE_SENDING
            ) {
                NIMService.handleSendResult(data);
            }
        });

        nim.V2NIMMessageService.on('onReceiveMessages', (data) => {
            if (!syncFinished) {
                return;
            }
            data.forEach((item) => {
                const injectItem = item as MsgDataType;
                injectExt(injectItem);

                // 获取当前会话ID
                const currentConversationId = useMessageStore.getState().conversationId;

                // 只有属于当前会话的消息才添加到消息列表
                if (item.conversationId === currentConversationId) {
                    if (!item.isSelf) {
                        handleFavoirabilityAnimationPlayed(injectItem);
                    }
                    useMessageStore.getState().pushMsgs(injectItem, PushMsgType.receive);

                    // 如果是流式消息占位符，初始化流式状态
                    if (MessageTypeUtils.isStreamingMessage(injectItem)) {
                        if (
                            item.streamConfig?.status ===
                            V2NIMConst.V2NIMMessageStreamStatus
                                .NIM_MESSAGE_STREAM_STATUS_PLACEHOLDER
                        ) {
                            useStreamingStore
                                .getState()
                                .initStreamingMessage(item.messageClientId, item.conversationId);
                        }
                    }
                }

                MessageFlow.onReceiveMessage(injectItem);

                if (item.isSelf) {
                    this.markSessionCanShow(item.conversationId);
                }
            });
        });

        nim.V2NIMMessageService.on('onMessageDeletedNotifications', (data) => {
            const msgs = data.map((item) => {
                return item.messageRefer.messageClientId;
            });
            useMessageStore.getState().deleteMsg(msgs);

            // 清理流式消息状态
            const streamingStore = useStreamingStore.getState();
            msgs.forEach((messageId) => {
                streamingStore.clearStreamingMessage(messageId);
            });
        });

        nim.V2NIMMessageService.on('onMessageRevokeNotifications', (data) => {
            const msgs = data.map((item) => {
                return item.messageRefer.messageClientId;
            });
            // TODO 显示消息被撤回的样式
            useMessageStore.getState().deleteMsg(msgs);

            // 清理流式消息状态
            const streamingStore = useStreamingStore.getState();
            msgs.forEach((messageId) => {
                streamingStore.clearStreamingMessage(messageId);
            });
        });

        nim.V2NIMNotificationService.on('onReceiveCustomNotifications', (data) => {
            if (!syncFinished) {
                return;
            }
            data.forEach((item) => {
                const sysItem = item as SystemMessage;
                MessageFlow.onSystemMessage(sysItem);
            });
        });

        // 监听消息更新回调，用于处理流式消息
        nim.V2NIMMessageService.on('onReceiveMessagesModified', (data) => {
            if (!syncFinished) {
                return;
            }
            data.forEach((item) => {
                const injectItem = item as MsgDataType;
                injectExt(injectItem);

                // 获取当前会话ID
                const currentConversationId = useMessageStore.getState().conversationId;

                // 只处理当前会话的消息更新
                if (item.conversationId !== currentConversationId) {
                    return;
                }
                // 处理流式消息更新
                if (MessageTypeUtils.isStreamingMessage(injectItem) && item.streamConfig) {
                    const { status } = item.streamConfig;
                    const messageStore = useMessageStore.getState();
                    const streamingStore = useStreamingStore.getState();

                    switch (status) {
                        case V2NIMConst.V2NIMMessageStreamStatus
                            .NIM_MESSAGE_STREAM_STATUS_STREAMING:
                            // 流式更新中，替换消息内容
                            messageStore.replaceMsg(injectItem);

                            // 更新流式消息状态
                            const chunk = item.streamConfig.lastChunk;
                            if (chunk) {
                                streamingStore.updateStreamingContent(
                                    item.messageClientId,
                                    item.text || '',
                                    chunk.index
                                );
                            }
                            break;

                        case V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_COMPLETE:
                            // 流式完成，最终更新
                            messageStore.replaceMsg(injectItem);
                            streamingStore.completeStreaming(item.messageClientId);
                            break;

                        case V2NIMConst.V2NIMMessageStreamStatus
                            .NIM_MESSAGE_STREAM_STATUS_EXCEPTION:
                        case V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_CANCEL:
                            // 流式异常或取消
                            messageStore.replaceMsg(injectItem);
                            streamingStore.cancelStreaming(
                                item.messageClientId,
                                status ===
                                    V2NIMConst.V2NIMMessageStreamStatus
                                        .NIM_MESSAGE_STREAM_STATUS_EXCEPTION
                                    ? '消息加载失败'
                                    : '消息已取消'
                            );
                            break;

                        default:
                            // 其他状态的消息更新
                            messageStore.replaceMsg(injectItem);
                            break;
                    }
                } else {
                    // 非流式消息的普通更新
                    useMessageStore.getState().replaceMsg(injectItem);
                }
            });
        });

        onAppHide(() => {
            useSessionStore.getState().setEnteredBackground(true);
        });
    };

    static loadConversations = async () => {
        try {
            const res = await nim.V2NIMConversationService.getConversationList(0, 100);
            const conversationList = res.conversationList ?? [];
            useSessionStore.getState().setSessionList(conversationList);

            coronaWarnMessage('NIMService', `加载会话第一页 ${conversationList.length}`);

            if (!conversationList.length) {
                return;
            }

            let offset = 100;
            while (offset < 1000) {
                const res = await nim.V2NIMConversationService.getConversationList(offset, 100);
                if (res.conversationList.length > 0) {
                    useSessionStore
                        .getState()
                        .setSessionList([
                            ...useSessionStore.getState().sessionList,
                            ...res.conversationList,
                        ]);
                } else {
                    break;
                }
                offset += 100;
            }
        } catch (e) {
            coronaWarnMessage('NIMService', `加载会话失败 ${JSON.stringify(e)}`);
        }
    };

    static stickTopConversation = async (conversationId: string, stickTop: boolean) => {
        try {
            await nim.V2NIMConversationService.stickTopConversation(conversationId, stickTop);
        } catch (e) {
            // TODO 置顶会话失败
        }
    };

    static deleteConversation = async (conversationId: string) => {
        // 删除store中的用户信息
        useSessionStore.getState().deleteUserInfo(conversationId);
        try {
            await nim.V2NIMConversationService.deleteConversation(conversationId, true);
        } catch (e) {
            // TODO 删除会话失败
        }
    };

    static enterSession = async (sessionId: string, option?: Partial<V2NIMMessageListOption>) => {
        const conversationId = this.convertSessionIdToConversationId(sessionId);
        useMessageStore.getState().setConversationIdAndSessionId(conversationId, sessionId);
        this.getMessageList(sessionId, option);
    };

    static leaveSession = async () => {
        this.cleanUnreadCount(useMessageStore.getState().sessionId);
        useMessageStore.getState().leaveSession();
        // 清理所有流式消息状态
        useStreamingStore.getState().clearAllStreamingMessages();
    };

    static cleanUnreadCount = async (sessionId: string) => {
        try {
            const conversationId = NIMService.convertSessionIdToConversationId(sessionId);
            await nim.V2NIMConversationService.clearUnreadCountByIds([conversationId]);
        } catch (e) {
            // TODO 清除未读数失败
        }
    };

    static getMessageList = async (
        sessionId: string,
        option?: Partial<V2NIMMessageListOption>
    ): Promise<boolean> => {
        if (!sessionId) {
            return false;
        }
        useMessageStore.getState().loadingData = true;

        const conversationId = NIMService.convertSessionIdToConversationId(sessionId);
        const { originalList } = useMessageStore.getState();
        let anchorMessage: V2NIMMessage | undefined;

        if (originalList.length > 0) {
            anchorMessage = originalList[originalList.length - 1] ?? null;
        }

        try {
            const result = await nim.V2NIMMessageService.getMessageList({
                conversationId,
                messageTypes: [],
                anchorMessage,
                limit: 50,
                direction: 0,
                ...option,
            });

            useMessageStore.getState().pushMsgs(result, PushMsgType.history);
            coronaWarnMessage(
                'NIMService',
                `加载消息成功 getMessageList ${sessionId} ${result.length}`
            );
            useMessageStore.getState().loadingData = false;
            return result.length > 0;
        } catch (e) {
            useMessageStore.getState().loadingData = false;
            console.log('useNewNIM 加载消息失败 getMessageList =====', JSON.stringify(e));
            coronaWarnMessage(
                'NIMService',
                `加载消息失败 getMessageList ${sessionId} ${JSON.stringify(e)}`
            );
        }

        return false;
    };

    static sendText = async (text: string, to = useMessageStore.getState().conversationId) => {
        const message = nim.V2NIMMessageCreator.createTextMessage(text);
        const localMsgCustom = JSON.stringify({
            clientExt: {
                sessionPeriodId: useMessageStore.getState().periodId,
                chatMode: useScenarioStore.getState().chatMode,
            },
        });
        message.serverExtension = localMsgCustom;
        message.conversationId = to;
        useMessageStore.getState().pushMsgs(message, PushMsgType.send);

        // 两秒后将消息状态改为发送中
        setTimeout(() => {
            const currentState = useMessageStore.getState();
            const messageList = currentState.messageList;
            const msg = messageList.find(
                (item) => item.messageClientId === message.messageClientId
            );
            if (
                msg &&
                msg.sendingState ===
                    V2NIMConst.V2NIMMessageSendingState.V2NIM_MESSAGE_SENDING_STATE_UNKNOWN
            ) {
                // 创建更新后的消息对象
                const updatedMsg = {
                    ...msg,
                    sendingState:
                        V2NIMConst.V2NIMMessageSendingState.V2NIM_MESSAGE_SENDING_STATE_SENDING,
                };

                currentState.replaceMsg(updatedMsg);
            }
        }, 2000);

        try {
            await nim.V2NIMMessageService.sendMessage(message, to);
        } catch (e) {}
    };

    static resendMessage = async (
        message: V2NIMMessage,
        to = useMessageStore.getState().conversationId
    ) => {
        useMessageStore.getState().deleteMsg(message.messageClientId);
        if (message.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_TEXT) {
            NIMService.sendText(message.text ?? '', to);
        }
    };

    static deleteMessage = async (message: V2NIMMessage) => {
        try {
            await nim.V2NIMMessageService.deleteMessage(message);
        } catch (e) {
            console.log('deleteMessage error', e);
        }
    };

    static modifyMessage = async (message: MsgDataType, customExt: string) => {
        try {
            const result = await nim.V2NIMMessageService.modifyMessage(message, {
                serverExtension: customExt,
            });
            useMessageStore.getState().replaceMsg(result.message);
        } catch (e) {
            console.log('modifyMessage error', e);
        }
    };

    static async handleSendResult(msg: V2NIMMessage) {
        const messageStore = useMessageStore.getState();
        if (
            msg.sendingState ===
            V2NIMConst.V2NIMMessageSendingState.V2NIM_MESSAGE_SENDING_STATE_FAILED
        ) {
            messageStore.setLastMsgStatus(LastMsgStatus.received);
            messageStore.replaceMsg(msg);
            return;
        }

        const responseCode = JSON.parse(msg.callbackExtension ?? '{}').responseCode;
        if (!responseCode || responseCode === 0 || responseCode === 200) {
            Taro.eventCenter.trigger('powerEvent', {
                type: 'msgSendSuccess',
                time: msg.createTime,
            });
            messageStore.setLastMsgStatus(LastMsgStatus.sent);
            messageStore.replaceMsg(msg);
            return;
        }
        Taro.eventCenter.trigger('msgSendFail');

        if (responseCode === MessageErrorCode.balaceInsufficient) {
            Taro.eventCenter.trigger('powerEvent', {
                type: 'msgSendFiledForHypodynamia',
                time: msg.createTime,
            });
        } else if (responseCode === MessageErrorCode.antiSpam) {
            showToast({
                title: '消息违规，未发送成功',
                icon: 'none',
                duration: 2000,
            });
        } else if (responseCode === MessageErrorCode.antiCheat) {
            showToast({
                title: '操作异常，请稍后再试',
                icon: 'none',
                duration: 2000,
            });
        } else if (responseCode === MessageErrorCode.realNameAuth) {
            gotoRealNameAuth();
        } else {
            showToast({
                title: `发送失败，请稍后再试(${responseCode})`,
                icon: 'none',
                duration: 2000,
            });
        }

        const errorMsg = {
            ...msg,
            sendingState: V2NIMConst.V2NIMMessageSendingState.V2NIM_MESSAGE_SENDING_STATE_FAILED,
        };
        useMessageStore.getState().setLastMsgStatus(LastMsgStatus.received);
        useMessageStore.getState().replaceMsg(errorMsg);
    }

    static updatePeriodId = (periodId: string) => {
        coronaWarnMessage('NIMService', `updatePeriodId ${periodId}`);
        useMessageStore.getState().setPeriodId(periodId);
        NIMService.cleanUnreadCount(useMessageStore.getState().sessionId);
    };

    static markSessionCanShow = async (conversationId: string) => {
        const conversation = useSessionStore
            .getState()
            .sessionList.find((item) => item.conversationId === conversationId);
        let serverExt = conversation?.serverExtension;
        if (!serverExt || !serverExt.length) {
            serverExt = '{}';
        }
        const serverExtension = JSON.parse(serverExt);
        if (serverExtension.canShow === true) {
            return;
        }
        serverExtension.canShow = true;

        try {
            await nim.V2NIMConversationService.updateConversation(conversationId, {
                serverExtension: JSON.stringify(serverExtension),
            });
        } catch (e) {}
    };

    static convertSessionIdToConversationId = (sessionId: string) => {
        const mySessionId = useSessionStore.getState().mySessionId;
        return `${mySessionId}|1|${sessionId}`;
    };
}

interface NumberInfo {
    numberType: number;
    number: number;
}

const handleFavoirabilityAnimationPlayed = (msg: MsgDataType) => {
    if (!msg?.serverExtension || !msg?.attachment) {
        return;
    }
    if (msg?.contentExt?.content?.type === 'aigcCustomTextAudioMsg') {
        const serverExt = msg?.customExt?.serverExt;
        const persionId = serverExt?.sessionPeriodId;
        const idServer = msg?.messageClientId;
        const favoirabilityInfo = JSON.parse(serverExt?.numberInfo ?? '[]')?.filter(
            (item: NumberInfo) => item.numberType === 1
        );

        const favoirabilityScore =
            favoirabilityInfo && favoirabilityInfo.length > 0 ? favoirabilityInfo[0]?.number : null;
        if (persionId && favoirabilityScore) {
            const favoirabilityAnimationKey = `kfavoirabilityAnimationKey_${persionId}_${idServer}`;
            setStorageSync(favoirabilityAnimationKey, 'needPlay');
        }
    }
};
