import { useState, useRef, useCallback, useEffect } from 'react';
import { AnimationConfig } from './useStreamingStore';

interface UseTypewriterAnimationOptions {
    text: string;
    onComplete?: () => void;
    config?: AnimationConfig;
    enabled?: boolean;
}

interface UseTypewriterAnimationReturn {
    displayText: string;
    start: (fromIndex?: number) => void;
    stop: () => void;
    isAnimating: boolean;
    progress: number;
}

/**
 * 优化的打字机动画 Hook
 * 使用 requestAnimationFrame 实现平滑动画
 */
export const useTypewriterAnimation = ({
    text,
    onComplete,
    config = {
        baseSpeed: 30,
        batchSizeMin: 1,
        batchSizeMax: 5,
        useDynamicBatch: true,
    },
    enabled = true,
}: UseTypewriterAnimationOptions): UseTypewriterAnimationReturn => {
    const [displayText, setDisplayText] = useState('');
    const [isAnimating, setIsAnimating] = useState(false);
    const animationFrameRef = useRef<number>();
    const currentIndexRef = useRef(0);
    const lastTimeRef = useRef(0);
    const startTimeRef = useRef(0);

    // 计算进度
    const progress = text.length > 0 ? currentIndexRef.current / text.length : 0;

    // 计算动态批量大小
    const calculateBatchSize = useCallback(
        (progress: number): number => {
            if (!config.useDynamicBatch) {
                return config.batchSizeMin;
            }

            // 根据进度线性插值计算批量大小
            const batchSize = Math.floor(
                config.batchSizeMin + (config.batchSizeMax - config.batchSizeMin) * progress
            );

            return Math.max(config.batchSizeMin, Math.min(config.batchSizeMax, batchSize));
        },
        [config]
    );

    // 动画帧函数
    const animate = useCallback(
        (timestamp: number) => {
            if (!enabled || !text) {
                return;
            }

            // 初始化时间戳
            if (!startTimeRef.current) {
                startTimeRef.current = timestamp;
                lastTimeRef.current = timestamp;
            }

            const elapsed = timestamp - lastTimeRef.current;
            const currentIndex = currentIndexRef.current;

            // 计算当前进度和批量大小
            const progress = text.length > 0 ? currentIndex / text.length : 0;
            const batchSize = calculateBatchSize(progress);

            // 根据批量大小调整延迟
            const delay = config.baseSpeed * (config.useDynamicBatch ? 1 : batchSize);

            // 检查是否到达更新时间
            if (elapsed >= delay) {
                const nextIndex = Math.min(currentIndex + batchSize, text.length);

                // 更新显示文本
                setDisplayText(text.substring(0, nextIndex));
                currentIndexRef.current = nextIndex;
                lastTimeRef.current = timestamp;

                // 检查是否完成
                if (nextIndex >= text.length) {
                    setIsAnimating(false);
                    onComplete?.();
                } else {
                    // 继续动画
                    animationFrameRef.current = requestAnimationFrame(animate);
                }
            } else {
                // 继续等待下一帧
                animationFrameRef.current = requestAnimationFrame(animate);
            }
        },
        [text, enabled, config, calculateBatchSize, onComplete]
    );

    // 启动动画
    const start = useCallback(
        (fromIndex = 0) => {
            if (!enabled || !text) {
                setDisplayText(text || '');
                return;
            }

            // 停止现有动画
            if (animationFrameRef.current) {
                cancelAnimationFrame(animationFrameRef.current);
            }

            // 重置状态
            currentIndexRef.current = fromIndex;
            lastTimeRef.current = 0;
            startTimeRef.current = 0;
            setIsAnimating(true);

            // 如果起始位置大于0，先显示部分文本
            if (fromIndex > 0) {
                setDisplayText(text.substring(0, fromIndex));
            }

            // 启动动画
            animationFrameRef.current = requestAnimationFrame(animate);
        },
        [text, enabled, animate]
    );

    // 停止动画
    const stop = useCallback(() => {
        if (animationFrameRef.current) {
            cancelAnimationFrame(animationFrameRef.current);
            animationFrameRef.current = undefined;
        }
        setIsAnimating(false);
    }, []);


    // 文本变化时的处理
    useEffect(() => {
        if (!enabled) {
            setDisplayText(text);
            return;
        }

        // 如果文本长度增加，从当前位置继续动画
        if (text.length > currentIndexRef.current && isAnimating) {
            // 继续动画
            if (!animationFrameRef.current) {
                animationFrameRef.current = requestAnimationFrame(animate);
            }
        } else if (text.length < currentIndexRef.current) {
            // 文本缩短，重置动画
            currentIndexRef.current = 0;
            setDisplayText('');
            if (isAnimating) {
                start(0);
            }
        }
    }, [text, enabled, isAnimating, animate, start]);

    // 清理
    useEffect(() => {
        return () => {
            if (animationFrameRef.current) {
                cancelAnimationFrame(animationFrameRef.current);
            }
        };
    }, []);

    // 禁用时直接显示全部文本
    useEffect(() => {
        if (!enabled) {
            setDisplayText(text);
            setIsAnimating(false);
        }
    }, [enabled, text]);

    return {
        displayText,
        start,
        stop,
        isAnimating,
        progress,
    };
};
