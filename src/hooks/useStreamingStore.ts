import { create } from 'zustand';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';

// 流式消息状态
export interface StreamingState {
    messageId: string;
    conversationId: string;
    status: V2NIMConst.V2NIMMessageStreamStatus;
    displayText: string; // 当前显示的文本
    fullText: string; // 完整文本
    lastChunkIndex: number; // 最后处理的分片索引
    isAnimating: boolean; // 是否正在播放打字机动画
    error?: string; // 错误信息
}

// 动画配置
export interface AnimationConfig {
    baseSpeed: number; // 基础速度 (ms/字符)
    batchSizeMin: number; // 最小批量大小
    batchSizeMax: number; // 最大批量大小
    useDynamicBatch: boolean; // 是否使用动态批量
}

// 批量更新数据
export interface StreamingUpdate {
    messageId: string;
    status: V2NIMConst.V2NIMMessageStreamStatus;
    content: string;
    chunkIndex: number;
}

interface StreamingStore {
    // 流式消息状态映射
    streamingStates: Map<string, StreamingState>;

    // 动画配置
    animationConfig: AnimationConfig;

    // 方法
    initStreamingMessage: (messageId: string, conversationId: string) => void;
    updateStreamingContent: (messageId: string, content: string, chunkIndex: number) => void;
    completeStreaming: (messageId: string) => void;
    cancelStreaming: (messageId: string, error?: string) => void;
    setAnimationConfig: (config: Partial<AnimationConfig>) => void;
    batchUpdateStreaming: (updates: Map<string, StreamingUpdate>) => void;
    clearStreamingMessage: (messageId: string) => void;
    clearAllStreamingMessages: () => void;

    // 选择器
    getStreamingState: (messageId: string) => StreamingState | undefined;
    isMessageStreaming: (messageId: string) => boolean;
}

export const useStreamingStore = create<StreamingStore>((set, get) => ({
    streamingStates: new Map(),

    animationConfig: {
        baseSpeed: 30,
        batchSizeMin: 1,
        batchSizeMax: 5,
        useDynamicBatch: true,
    },

    initStreamingMessage: (messageId: string, conversationId: string) => {
        const streamingStates = new Map(get().streamingStates);
        streamingStates.set(messageId, {
            messageId,
            conversationId,
            status: V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_PLACEHOLDER,
            displayText: '',
            fullText: '',
            lastChunkIndex: -1,
            isAnimating: true,
        });
        set({ streamingStates });
    },

    updateStreamingContent: (messageId: string, content: string, chunkIndex: number) => {
        const streamingStates = new Map(get().streamingStates);
        const existing = streamingStates.get(messageId);

        if (existing) {
            streamingStates.set(messageId, {
                ...existing,
                status: V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_STREAMING,
                fullText: content,
                lastChunkIndex: chunkIndex,
            });
            set({ streamingStates });
        }
    },

    completeStreaming: (messageId: string) => {
        const streamingStates = new Map(get().streamingStates);
        const existing = streamingStates.get(messageId);

        if (existing) {
            streamingStates.set(messageId, {
                ...existing,
                status: V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_COMPLETE,
                isAnimating: false,
                displayText: existing.fullText,
            });
            set({ streamingStates });
        }
    },

    cancelStreaming: (messageId: string, error?: string) => {
        const streamingStates = new Map(get().streamingStates);
        const existing = streamingStates.get(messageId);

        if (existing) {
            streamingStates.set(messageId, {
                ...existing,
                status: V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_CANCEL,
                isAnimating: false,
                error: error || '消息已取消',
            });
            set({ streamingStates });
        }
    },

    setAnimationConfig: (config: Partial<AnimationConfig>) => {
        set({ animationConfig: { ...get().animationConfig, ...config } });
    },

    batchUpdateStreaming: (updates: Map<string, StreamingUpdate>) => {
        const streamingStates = new Map(get().streamingStates);

        updates.forEach((update, messageId) => {
            const existing = streamingStates.get(messageId);
            if (existing) {
                streamingStates.set(messageId, {
                    ...existing,
                    status: update.status,
                    fullText: update.content,
                    lastChunkIndex: update.chunkIndex,
                });
            }
        });

        set({ streamingStates });
    },

    clearStreamingMessage: (messageId: string) => {
        const streamingStates = new Map(get().streamingStates);
        streamingStates.delete(messageId);
        set({ streamingStates });
    },

    clearAllStreamingMessages: () => {
        set({ streamingStates: new Map() });
    },

    getStreamingState: (messageId: string) => {
        return get().streamingStates.get(messageId);
    },

    isMessageStreaming: (messageId: string) => {
        const state = get().streamingStates.get(messageId);
        return (
            state?.status ===
            V2NIMConst.V2NIMMessageStreamStatus.NIM_MESSAGE_STREAM_STATUS_STREAMING
        );
    },
}));
