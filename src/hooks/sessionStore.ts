import { create } from 'zustand';
import Taro, { getStorageSync, setStorage, showToast } from '@tarojs/taro';
import { apiContactList } from '@/service/imApi';
import { rpc } from '@music/mat-base-h5';
import { V2NIMConversation } from 'nim-web-sdk-ng/dist/esm/nim/src/V2NIMConversationService';
import { ChatContactListDto, Conversation } from '@/components/Message/conversationModel';
import { debounce } from '@/utils';
import { ChatDataManager } from '@/utils/managers/ChatDataManager';
import { getUserInfo } from '@/utils/rpc';
import useHomeStore, { TabItemKey } from '@/components/Home/useHomeStore';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import Env, { compareVersion } from '@music/mobile-env';
import conversationIdToSessionId from './conversationIdTransition';

/// 官方号配置
export const officialAccountAccids = ['4ad5fd353d55fbcdd35ca881a9107be0'];
/// 官方号的消息拉取时间戳(2025-6-1 0:0:0)
export const officialAccountLastTimestamp = *************;

export interface SessionStore {
    sessionLoading: boolean;
    sessionList: V2NIMConversation[];
    setSessionList: (sessionList: V2NIMConversation[]) => void;
    finalSessionList: Conversation[];
    userList: { [sessionId: string]: ChatContactListDto };
    myUserId: string;
    mySessionId: string;

    localStorageSaveTime: number;
    lostNetworking: boolean;
    enteredBackground: boolean; // 用于辅助判断网络状态，后台切前台时不弹网络错误弹窗

    getStorageSessions: () => Promise<void>;
    getUsersInfo: (conversations: Conversation[]) => Promise<Conversation[]>;
    deleteUserInfo: (conversationId: string) => void;
    sortFinalSessions: (sessions: Conversation[]) => void;
    setMySessionId: (sessionId: string) => void;
    debouncedSaveToStorage: (final: Conversation[]) => void;
    setConnectstatus: (connectStatus: V2NIMConst.V2NIMConnectStatus) => void;
    loginstatus: V2NIMConst.V2NIMLoginStatus;
    setLoginstatus: (loginstatus: V2NIMConst.V2NIMLoginStatus) => void;
    setEnteredBackground: (enteredBackground: boolean) => void;

    stickySessions: { [key: string]: number };
    setStickySessions: (stickySessions: { [key: string]: number }) => void;
}

export const getSessionLastTimestamp = (item: Conversation) => {
    const draft = ChatDataManager.getInstance().getChatData(item.sessionId)?.text;
    const draftTime = ChatDataManager.getInstance().getChatData(item.sessionId)?.timestamp;
    let timestamp = item.lastMessage?.messageRefer.createTime ?? 0;
    if (draft && draft.length > 0 && draftTime && draftTime > timestamp) {
        timestamp = draftTime;
    }
    return timestamp;
};

export const useSessionStore = create<SessionStore>((set, get) => ({
    sessionList: [],
    finalSessionList: [],
    userList: {},
    stickySessions: {},
    myUserId: '',
    localStorageSaveTime: 0,
    mySessionId: '',
    sessionLoading: true,
    lostNetworking: false,
    loginstatus: 0,
    enteredBackground: false,

    setEnteredBackground: (enteredBackground) => {
        set({ enteredBackground });
    },

    setLoginstatus: (status) => {
        set({ loginstatus: status });
    },

    setConnectstatus: (connectStatus) => {
        // 连接中不需要更改状态
        if (connectStatus === V2NIMConst.V2NIMConnectStatus.V2NIM_CONNECT_STATUS_CONNECTING) {
            return;
        }
        if (connectStatus === V2NIMConst.V2NIMConnectStatus.V2NIM_CONNECT_STATUS_CONNECTED) {
            set({ lostNetworking: false });
        } else if (
            connectStatus === V2NIMConst.V2NIMConnectStatus.V2NIM_CONNECT_STATUS_DISCONNECTED
        ) {
            // 如果是从后台切回前台，第一次断联不算断网
            if (get().enteredBackground) {
                set({ enteredBackground: false });
                return;
            }
            const oldValue = get().lostNetworking;
            if (!oldValue) {
                showToast({
                    title: '连接中断，请检查网络',
                    icon: 'none',
                });
            }
            set({ lostNetworking: true });
        }
    },

    getStorageSessions: async () => {
        getUserInfo((e) => {
            if (e.uid?.length) {
                set({ myUserId: e.uid });
                const key = `SessionFinalListStorage${e.uid}`;
                const value = getStorageSync(key);
                if (value) {
                    set({ finalSessionList: value });
                }
            }
        });
    },

    setMySessionId: (sessionId) => {
        set({ mySessionId: sessionId });
    },

    setStickySessions: (stickySessions) => {
        set({ stickySessions });
    },

    getUsersInfo: async (conversations) => {
        const { userList } = get();
        // 过滤出需要获取用户信息的会话
        const conversationsNeedingInfo = conversations.filter(
            (item) => !item.toUserDetail && !userList[item.sessionId]
        );

        if (conversationsNeedingInfo.length === 0) {
            // 如果所有会话都已有用户信息，直接返回
            return conversations.map((item) => ({
                ...item,
                toUserDetail: userList[item.sessionId] || item.toUserDetail || null,
            }));
        }

        const batchSize = 30;
        const contactPromises = conversationsNeedingInfo.reduce((acc, conversation, index) => {
            if (index % batchSize === 0) {
                const batch = conversationsNeedingInfo.slice(index, index + batchSize);
                // 映射成 {contactId: xxx, sessionType: 0} 的形式
                const contactIds = batch.map((item) => ({
                    contactId: item.sessionId,
                    sessionType: 0,
                }));
                acc.push(apiContactList(contactIds));
            }
            return acc;
        }, [] as Promise<any>[]);

        const results = await Promise.all(contactPromises);
        const newUserList = { ...userList };

        results.forEach((result) => {
            (result as ChatContactListDto[]).forEach((item) => {
                newUserList[item.contactId] = item;
            });
        });

        // 更新 store 中的 userList
        set({ userList: newUserList });

        // 返回更新后的会话列表
        return conversations.map((item) => ({
            ...item,
            toUserDetail: newUserList[item.sessionId] || item.toUserDetail || null,
        }));
    },

    setSessionList: (sessionList) => {
        set({ sessionList });
        set({ sessionLoading: true });

        const filteredSessions = sessionList
            .filter((session) => session.type === 1)
            .filter((session) => !session.conversationId.includes('@'))
            .filter((session) => {
                // 官方号且在时间范围内则直接显示
                if (officialAccountAccids.some((item) => session.conversationId.includes(item))) {
                    return (
                        session.lastMessage?.messageRefer?.createTime >=
                        officialAccountLastTimestamp
                    );
                }
                // 如果当前有未读数则显示
                if (session.unreadCount > 0) {
                    return true;
                }
                let serverExt = session.serverExtension;
                if (!serverExt || !serverExt.length) {
                    serverExt = '{}';
                }
                const serverExtension = JSON.parse(serverExt);
                return serverExtension.canShow === true;
            })
            .map((session) => {
                const conversation: Conversation = {
                    ...session,
                    sessionId: conversationIdToSessionId(session.conversationId),
                };
                return conversation;
            });

        const { getUsersInfo } = get();
        getUsersInfo(filteredSessions).then((conversations) => {
            const finalSessionList = conversations
                .filter(
                    (conversation) =>
                        conversation.toUserDetail?.bizExtInfo?.bot === true ||
                        officialAccountAccids.some((item) =>
                            conversation.toUserDetail?.userInfo?.imAccId?.toString().includes(item)
                        )
                )
                .filter((conversation) => {
                    const custom = conversation.lastMessage?.serverExtension;
                    if (!custom) {
                        return true;
                    }
                    const aigcInfo = conversation?.toUserDetail?.aigcSessionInfo;
                    const isInMsg =
                        conversation.lastMessage?.messageRefer.senderId === conversation.sessionId;
                    if (isInMsg) {
                        const serverExt = JSON.parse(custom)?.serverExt;
                        if (serverExt && aigcInfo) {
                            return serverExt.sessionPeriodId >= (aigcInfo.sessionPeriodId ?? 0);
                        }
                    } else {
                        const clientExt = JSON.parse(custom)?.clientExt;
                        if (clientExt && aigcInfo) {
                            return clientExt.sessionPeriodId >= (aigcInfo.sessionPeriodId ?? 0);
                        }
                    }

                    return true;
                });

            get().sortFinalSessions(finalSessionList);
            set({ sessionLoading: false });
        });
    },

    sortFinalSessions: (sessions = get().finalSessionList) => {
        const stickySessions = get().stickySessions;
        // 根据isTop，如果isTop相同，则按照updateTime倒序排列，如果该回话存在置顶时间大于updateTime，则使用置顶时间
        const final = sessions.sort((a, b) => {
            if (a.stickTop === true && b.stickTop !== true) return -1;
            if (a.stickTop !== true && b.stickTop === true) return 1;

            const aLastTime = getSessionLastTimestamp(a);
            const bLastTime = getSessionLastTimestamp(b);
            const aStickyTime = stickySessions[a.conversationId];
            const bStickyTime = stickySessions[b.conversationId];
            const aUpdateTime = aStickyTime > aLastTime ? aStickyTime : aLastTime;
            const bUpdateTime = bStickyTime > bLastTime ? bStickyTime : bLastTime;
            return bUpdateTime - aUpdateTime;
        });

        set({ finalSessionList: final });

        get().debouncedSaveToStorage(final);

        const unreadCount = final.reduce((acc, item) => acc + item.unreadCount, 0);
        useHomeStore.getState().setBadgeCount(TabItemKey.message, unreadCount);
        if (IS_MIRTH && compareVersion(Env.getMirthVersion(), '2.11.6') >= 0) {
            rpc.caller('push.setPushBadgeNumber', { badgeNumber: unreadCount });
        }
    },

    deleteUserInfo: (conversationId) => {
        const sessionId = conversationIdToSessionId(conversationId);
        set((state) => {
            const newUserList = { ...state.userList };
            delete newUserList[sessionId];
            return { userList: newUserList };
        });
    },

    debouncedSaveToStorage: debounce((final) => {
        const myUserId = get().myUserId;
        if (myUserId.length) {
            const key = `SessionFinalListStorage${myUserId}`;
            setStorage({ key, data: final });
            // 更新最后保存时间
            set({ localStorageSaveTime: Date.now() });
        }
    }, 2000), // 2秒的防抖时间
}));
