export const injectContentExt = (message) => {
    if (message.contentExt) {
        return;
    }
    const extGetter = function () {
        const parsed = message._contentExt;
        if (parsed) {
            return parsed;
        }
        const contentStr = message.attachment?.raw;
        let contentExt = {};
        if (contentStr) {
            try {
                contentExt = JSON.parse(contentStr);
            } catch (error) {
                contentExt = {};
            }
        }
        message._contentExt = contentExt;
        return contentExt;
    };
    const extSetter = function (input) {
        message._contentExt = input;
        message.contentExt = JSON.stringify(input);
    };
    Object.defineProperty(message, 'contentExt', {
        get: extGetter,
        set: extSetter,
    });
};

export const injectSystemExt = (message) => {
    if (message.contentExt) {
        return;
    }
    const extGetter = function () {
        const parsed = message._contentExt;
        if (parsed) {
            return parsed;
        }
        const contentStr = message.content;
        let contentExt = {};
        if (contentStr) {
            try {
                contentExt = JSON.parse(contentStr);
            } catch (error) {
                contentExt = {};
            }
        }
        message._contentExt = contentExt;
        return contentExt;
    };
    const extSetter = function (input) {
        message._contentExt = input;
        message.contentExt = JSON.stringify(input);
    };
    Object.defineProperty(message, 'contentExt', {
        get: extGetter,
        set: extSetter,
    });
};
