import { SystemMessage, MsgDataType } from '@/types/im';
import { V2NIMConst } from 'nim-web-sdk-ng/dist/esm/nim';
import { injectContentExt, injectSystemExt } from './injectSystemMessage';
import injectExt from './injectMessage';

export type MessageFrom = 'send' | 'receive' | 'load';

export interface NotificationListener {
    onSystemMessage?: (message: SystemMessage) => void;
}

interface MessageListener {
    onMessage?: (receive: MsgDataType, from: MessageFrom) => void;
}

interface MessageListListener {
    onList?: (sessionId: string, messages: MsgDataType[]) => void;
}

interface Listener extends NotificationListener, MessageListener, MessageListListener {
    key?: string;
}

const globalListener: Listener[] = [];
const sessionListener = new Map<string, Listener[]>();
const typedListener = new Map<string, Map<string, MessageListener[]>>();
const notificationListener = new Map<number, NotificationListener[]>();

function dispatchMessage(message: MsgDataType, from: MessageFrom): void {
    injectExt(message);
    globalListener.forEach((listener) => {
        if (listener.onMessage) {
            listener.onMessage?.(message, from);
        }
    });
    const conversationId = message.conversationId;
    const listeners = sessionListener.get(conversationId);
    if (listeners) {
        listeners.forEach((listener) => {
            if (listener.onMessage) {
                listener.onMessage?.(message, from);
            }
        });
    }
    const typed = typedListener.get(conversationId);
    if (typed) {
        if (message.messageType === V2NIMConst.V2NIMMessageType.V2NIM_MESSAGE_TYPE_CUSTOM) {
            const messageContentType = message.contentExt?.content?.type;
            if (messageContentType) {
                const typedListeners = typed.get(messageContentType);
                if (typedListeners) {
                    typedListeners.forEach((listener) => {
                        listener.onMessage?.(message, 'receive');
                    });
                }
            }
        }
    }
}

function dispatchSystemMessage(input?: SystemMessage): void {
    if (!input) {
        return;
    }
    injectSystemExt(input);

    globalListener.forEach((listener) => {
        if (listener.onSystemMessage) {
            listener.onSystemMessage?.(input);
        }
    });

    const code = input.contentExt?.serverExt?.type || 0;
    if (code > 0) {
        const listeners = notificationListener.get(code);

        if (listeners) {
            listeners.forEach((listener) => {
                listener.onSystemMessage?.(input);
            });
        }
    }
}

interface MessageFlowType {
    addGlobalListener(listener: Listener, key?: string): void;
    removeGlobalListener(listener: Listener): void;
    addSessionListener(sessionId: string, listener: Listener): void;
    removeSessionListener(sessionId: string, listener: Listener): void;
    addTypedListener(sessionId: string, type: string, listener: MessageListener): void;
    removeTypedListener(sessionId: string, type: string, listener: MessageListener): void;
    addNotificationListener(code: number, listener: NotificationListener): void;
    removeNotificationListener(code: number, listener: NotificationListener): void;
    onSendMessage(message: MsgDataType): void;
    onReceiveMessage(message: MsgDataType): void;
    onSystemMessage(message: SystemMessage): void;
}

function addGlobalListener(listener: Listener, key?: string): void {
    if (key) {
        listener.key = key;
        const index = globalListener.findIndex((l) => l.key === key);
        if (index !== -1) {
            globalListener.splice(index, 1);
        }
    }
    globalListener.push(listener);
}

function removeGlobalListener(listener: Listener): void {
    const index = globalListener.indexOf(listener);
    if (index !== -1) {
        globalListener.splice(index, 1);
    }
}

function addSessionListener(sessionId: string, listener: Listener): void {
    let listeners = sessionListener.get(sessionId);
    if (!listeners) {
        listeners = [];
        sessionListener.set(sessionId, listeners);
    }
    listeners.push(listener);
}

function removeSessionListener(sessionId: string, listener: Listener): void {
    const listeners = sessionListener.get(sessionId);
    if (!listeners) {
        return;
    }
    const index = listeners.indexOf(listener);
    if (index !== -1) {
        listeners.splice(index, 1);
    }
}

function addTypedListener(sessionId: string, type: string, listener: MessageListener): void {
    let typed = typedListener.get(sessionId);
    if (!typed) {
        typed = new Map();
        typedListener.set(sessionId, typed);
    }
    let listeners = typed.get(type);
    if (!listeners) {
        listeners = [];
        typed.set(type, listeners);
    }
    listeners.push(listener);
}

function removeTypedListener(sessionId: string, type: string, listener: MessageListener): void {
    const typed = typedListener.get(sessionId);
    if (!typed) {
        return;
    }
    const listeners = typed.get(type);
    if (!listeners) {
        return;
    }
    const index = listeners.indexOf(listener);
    if (index !== -1) {
        listeners.splice(index, 1);
    }
}

function addNotificationListener(code: number, listener: NotificationListener): void {
    let listeners = notificationListener.get(code);
    if (!listeners) {
        listeners = [];
        notificationListener.set(code, listeners);
    }
    listeners.push(listener);
}

function removeNotificationListener(code: number, listener: NotificationListener): void {
    const listeners = notificationListener.get(code);
    if (!listeners) {
        return;
    }
    const index = listeners.indexOf(listener);
    if (index !== -1) {
        listeners.splice(index, 1);
    }
}

function onSendMessage(message: MsgDataType): void {
    dispatchMessage(message, 'send');
}

function onReceiveMessage(message: MsgDataType): void {
    dispatchMessage(message, 'receive');
}

function onSystemMessage(message: SystemMessage): void {
    dispatchSystemMessage(message);
}

const MessageFlow: MessageFlowType = {
    addGlobalListener,
    removeGlobalListener,
    addSessionListener,
    removeSessionListener,
    addTypedListener,
    removeTypedListener,
    addNotificationListener,
    removeNotificationListener,
    onSendMessage,
    onReceiveMessage,
    onSystemMessage,
};

export default MessageFlow;
