import { injectContentExt } from './injectSystemMessage';

const injectExt = (message) => {
    try {
        injectContentExt(message);
    } catch (error) {
        console.log('error', error);
    }
    try {
        injectCustomExt(message);
    } catch (error) {
        console.log('error', error);
    }
};

const injectCustomExt = (message) => {
    if (message.customExt) {
        return;
    }
    const extGetter = function () {
        const parsed = message._customExt;
        if (parsed) {
            return parsed;
        }
        const customStr = message.serverExtension;
        let customExt = {};
        if (customStr) {
            try {
                customExt = JSON.parse(customStr);
            } catch (error) {
                customExt = {};
            }
        }
        message._customExt = customExt;
        return customExt;
    };
    const extSetter = function (input) {
        message._customExt = input;
        message.customExt = JSON.stringify(input);
    };
    Object.defineProperty(message, 'customExt', {
        get: extGetter,
        set: extSetter,
    });
};

export default injectExt;
