import { useEffect } from 'react';
import { getCurrentInstance } from '@tarojs/taro';
import EpicPlayerSDK from '@music/epic-player-sdk';

export const usePreloadEpicPlayer = () => {
    useEffect(() => {
        const timerId = setTimeout(() => {
            EpicPlayerSDK.instance.preload({
                unmuteHack: true,
                apiHost: API_HOST || 'http://api-qa.mirth.netease.com',
                basePath: `/${
                    getCurrentInstance().router?.path.split('/')[1] ?? 'st-mirth-aichat'
                }/epic-player-sdk`,
                logLevel: EpicPlayerSDK.LogLevel.SILLY,
            });
        }, 3000);

        return () => clearTimeout(timerId);
    }, []);
};

export default {};
