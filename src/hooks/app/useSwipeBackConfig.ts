import { useEffect } from 'react';
import SwipeBack from '@/utils/adapter/swipeBack';

// 侧滑返回配置Hook
// 注册侧滑事件, 只支持h5端，Taro小程序端有配置可以直接用
export const useSwipeBackConfig = () => {
    /*  #ifdef  h5  */
    useEffect(() => {
        new SwipeBack({
            edgeDistance: 50, // 侧滑手指开始位置距离边缘的距离
            blackList: [
                '/index.html',
                '/message/index.html',
                '/friends',
                '/explore',
                '/pages/chapter-info/index',
                '/pages/ending-info/index',
            ], // 黑名单页面如： /chat/index.html 忽略一级路径 /st-mirth-aichat
        });
    }, []);
    /*  #endif  */
};

export default {};
