import { useEffect } from 'react';
import { apiGetImToken } from '@/service/imApi';
import { setGlobalData } from '@/utils';
import { coronaWarnMessage, rpc } from '@music/mat-base-h5';
import NIMService from '@/hooks/useNewNIM';
import useUserInfoStore from '@/store/useUserInfoStore';
import domAdapter from '@/utils/adapter/domAdapter';
import { getSafeAreaHeight, getStatusBarHeight } from '@/components/safe-area-view';
import setupNavigateBackInterceptor from '@/utils/adapter/navigateBackAdapter';

// IM初始化Hook
export const useIMInit = () => {
    useEffect(() => {
        const fetchImToken = async () => {
            try {
                const imRes = await apiGetImToken();
                setGlobalData('imDetail', imRes);
                coronaWarnMessage(
                    'NIMService',
                    `im token获取成功 accid=${imRes.accId} token=${imRes.token}`
                );
                NIMService.loginNIM(imRes.accId, imRes.token);
            } catch (error) {
                coronaWarnMessage('NIMService', `token 获取失败 失败 ${JSON.stringify(error)}`);
            }
        };

        fetchImToken();
    }, []);
};

// 用户信息初始化Hook
export const useUserInfoInit = () => {
    useEffect(() => {
        const fetchUserInfo = async () => {
            try {
                useUserInfoStore.getState().getUserBaseInfo();
            } catch (error) {
                coronaWarnMessage('UserInfo', `UserInfo 获取失败 失败 ${JSON.stringify(error)}`);
            }
        };

        fetchUserInfo();
    }, []);
};

// 样式和环境初始化Hook
export const useEnvInit = () => {
    // 设置CSS变量
    useEffect(() => {
        const statusBarHeight = getStatusBarHeight();
        const safeAreaHeight = getSafeAreaHeight();

        domAdapter.setCSSProperty('--status-bar-height', `${statusBarHeight}px`);
        domAdapter.setCSSProperty('--safearea-height', `${safeAreaHeight}px`);
    }, []);

    // 初始化导航拦截器和通知容器
    useEffect(() => {
        // 通知前端容器已经加载完毕
        rpc.caller('push.aiChatLoaded', {});
        setupNavigateBackInterceptor();
    }, []);
};

export default {};
