import { useEffect, useState, useRef, useCallback } from 'react';
import Taro, { onAppShow, onAppHide, offAppShow, offAppHide } from '@tarojs/taro';
import fetch from '@/utils/fetch';
import domAdapter from '@/utils/adapter/domAdapter';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { getAppSource } from '@/utils/appSourceAdapter';

// 心跳请求函数
const sendHeartbeat = async () => {
    try {
        await fetch('/api/mirth/home/<USER>/aigc/heart/beat', {});
    } catch (error) {
        const cookie = domAdapter.getCookie('MUSIC_U');
        coronaWarnMessage('cookie', cookie);
    }
};

// 心跳Hook
export const useHeartbeat = (loginstatus: number) => {
    const timer = useRef<NodeJS.Timeout>(null);
    const [gap, setGap] = useState(0);

    // 添加心跳上报逻辑
    useEffect(() => {
        // 设置定时器，每15秒发送一次心跳
        const heartbeatInterval = setInterval(sendHeartbeat, 15000);
        // 组件卸载时清除定时器
        return () => {
            clearInterval(heartbeatInterval);
        };
    }, []);

    // 发送APP级别心跳
    const sendAppHeartbeat = useCallback(
        async (frontStatus: boolean) => {
            try {
                const { gap: resGap } = await fetch('/api/mirth/user/heartbeat/sync', {
                    method: 'POST',
                    data: {
                        frontStatus,
                        fromWeb: true,
                        appSource: getAppSource(),
                    },
                });
                // 如果返回的gap大于0，并且当前gap为0，则设置gap；否则可以不动
                if (resGap > 0 && gap === 0) {
                    setGap(resGap);
                }
            } catch (error) {
                const cookie = domAdapter.getCookie('MUSIC_U');
                coronaWarnMessage('heartbeat error', cookie);
            }
        },
        [gap]
    );

    // 根据app在前台/后台发送心跳
    useEffect(() => {
        if (IS_MIRTH) {
            return undefined;
        }

        const handleAppShow = () => {
            if (timer.current) {
                clearInterval(timer.current);
            }

            // 防止onAppShow的时候gap还没被设置，导致定时器为0
            if (gap > 0) {
                timer.current = setInterval(() => {
                    sendAppHeartbeat(true);
                }, gap * 1000);
            }
        };
        const handleAppHide = () => {
            if (timer.current) {
                clearInterval(timer.current);
            }
            sendAppHeartbeat(false);
        };

        // onAppShow无法监听第一次加载的情况，所以需要手动调用一次
        if (!timer.current) {
            if (gap > 0) {
                handleAppShow();
            } else {
                sendAppHeartbeat(true);
            }
        }

        // 这种Taro API在不同机型，可能回调时机不同，需要兜底处理
        // 手动app切到后台、切会前台会响应
        // 在app内部新开webview也会响应
        // 但是需要区分app内部新开webview心跳要继续发，不能断
        onAppShow(handleAppShow);
        onAppHide(handleAppHide);

        // 跳新的webview，区分于切app到后台，需要继续发心跳
        Taro.eventCenter.on('taro_event_jump2NewWebview', () => {
            setTimeout(() => {
                handleAppShow();
            }, 1000);
        });

        return () => {
            offAppShow(handleAppShow);
            offAppHide(handleAppHide);
            Taro.eventCenter.off('taro_event_jump2NewWebview');
            if (timer.current) {
                clearInterval(timer.current);
            }
        };
    }, [gap, sendAppHeartbeat]);

    // 登录状态变化时发送心跳
    useEffect(() => {
        // V2NIMLoginStatus: 1 登录成功
        if (loginstatus !== 1) {
            return;
        }
        sendHeartbeat();
    }, [loginstatus]);
};

export default {};
