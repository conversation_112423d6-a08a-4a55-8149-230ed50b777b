import Taro from '@tarojs/taro';
import { TabType } from '@/pages/market/store/store';
import { AigcEndingCardInfo } from '@/types/AigcRobotProfile';
import { openKey, openUrl } from '@/utils/rpc';
import { getImage } from '@/utils/image';
import { preloadVideo } from '../const/gacha';

/**
 * 跳转到抽卡页~
 */
export function jump2Gacha({ fromSource, success }: { fromSource?: string; success?: () => void }) {
    const fromSourceParams = fromSource ? `?fromSource=${fromSource}` : '';
    Taro.navigateTo({
        url: `/pages/gacha/index${fromSourceParams}`,
        success: () => {
            preloadVideo();
            success?.();
        },
    });
}

/**
 * 跳转到私聊页
 */
export function jump2Chat({
    robotUserId,
    robotAccid,
    hasAiChapter,
    fromSource,
    botBgUrl,
    botContactUrl,
    botStandByUrl,
    keyboard_action,
}: {
    robotUserId: string;
    robotAccid: string;
    hasAiChapter?: string;
    fromSource?: string;
    botBgUrl?: string;
    botContactUrl?: string;
    botStandByUrl?: string;
    keyboard_action?: number;
}) {
    const keyboardActionParasm = keyboard_action ? `&keyboard_action=${keyboard_action}` : '';
    const botContactUrlParams = botContactUrl
        ? `&botContactUrl=${encodeURIComponent(botContactUrl)}`
        : '';
    const botStandByUrlParams = botStandByUrl
        ? `&botStandByUrl=${encodeURIComponent(botStandByUrl)}`
        : '';
    const botBgUrlParams = botBgUrl ? `&botBgUrl=${encodeURIComponent(botBgUrl)}` : '';
    const fromSourceParams = fromSource ? `&fromSource=${fromSource}` : '';
    Taro.navigateTo({
        url: [
            `/pages/chat/index?robotUserId=${robotUserId}${keyboardActionParasm}${botContactUrlParams}${botStandByUrlParams}${botBgUrlParams}${fromSourceParams}`,
            `hasAiChapter=${hasAiChapter}`,
            `robotAccid=${robotAccid}`,
        ].join('&'),
        success: () => {
            // 预加载背景图
            fetch(getImage(botBgUrl));
        },
    });
}

export function jump2Official({ robotAccid }: { robotAccid: string }) {
    Taro.navigateTo({
        url: `/pages/system-notify/index?robotAccid=${robotAccid}`,
    });
}

export function jump2ChatGuide() {
    Taro.navigateTo({
        url: '/pages/chat/index',
    });
}

export function jump2Market({ type, needShowToast }: { type?: TabType; needShowToast?: boolean }) {
    const typeUrl = type ? `&type=${type}` : '';
    Taro.navigateTo({
        url: `/pages/market/index?needShowToast=${needShowToast ?? false}${typeUrl}`,
    });
}

export function jump2ChapterInfo({
    chapterId,
    robotUserId,
    chapter,
    from,
}: {
    chapterId: string;
    robotUserId?: string;
    chapter?: string;
    from?: string;
}) {
    const fromParams = from ? `&from=${from}` : '';
    const chapterStrParams = chapter ? `&chapter=${chapter}` : '';
    Taro.navigateTo({
        url: `/pages/chapter-info/index?chapterId=${chapterId}&robotUserId=${robotUserId}${chapterStrParams}${fromParams}`,
    });
}

export function jump2EndingInfo({
    uiState,
    userId,
    ending,
    ending2,
    from,
}: {
    uiState: number;
    userId: string;
    ending?: string;
    ending2?: string;
    from?: string;
}) {
    const fromParams = from ? `&from=${from}` : '';
    const ending2StrParamms = ending2 ? `&ending2=${ending2}` : '';
    const uiStateParamms = uiState ? `&uiState=${uiState}` : '';
    Taro.navigateTo({
        url: `/pages/ending-info/index?ending=${ending ?? '{}'}
        &userId=${userId}${uiStateParamms}${ending2StrParamms}${fromParams}`,
    });
}

export function jump2Profile({
    robotUserId,
    hasAiChapter,
    fromSource,
}: {
    robotUserId: string;
    hasAiChapter: boolean;
    fromSource?: string;
}) {
    const fromParams = fromSource ? `&fromSource=${fromSource}` : '';
    const hasAiChapterParams = hasAiChapter ? `&hasAiChapter=${hasAiChapter}` : '';
    Taro.navigateTo({
        url: `/pages/profile/index?robotUserId=${robotUserId}${hasAiChapterParams}${fromParams}`,
    });
}

export function jump2ImgPreview({
    url,
    width,
    height,
}: {
    url: string;
    width?: number;
    height?: number;
}) {
    const widthParams = width ? `&width=${width}` : '';
    const heightParams = height ? `&height=${height}` : '';
    Taro.navigateTo({
        url: `/pages/image-preview/index?url=${encodeURIComponent(
            url
        )}${widthParams}${heightParams}`,
    });
}

export function jump2EndingRetrospect({ ending }: { ending: AigcEndingCardInfo }) {
    Taro.navigateTo({
        url: `/pages/ending-retrospect/index?ending=${encodeURIComponent(JSON.stringify(ending))}`,
    });
}

export function jump2Task({ tab }: { tab?: number }) {
    const tabParams = tab ? `?tab=${tab}` : '';
    Taro.navigateTo({
        url: `/pages/task/index${tabParams}`,
    });
}

export function jump2GachaCard({
    infoJsonStr,
    robotUserId,
}: {
    infoJsonStr: string;
    robotUserId?: string;
}) {
    const robotUserIdParams = robotUserId ? `&robotUserId=${robotUserId}` : '';
    Taro.navigateTo({
        url: `/pages/gacha-card/index?infoJsonStr=${encodeURIComponent(
            infoJsonStr
        )}${robotUserIdParams}`,
    });
}

/**
 *
 * 图鉴~
 * @param endingInfo
 */
export function jump2Appreciate({
    showGachaDialog,
    robotName,
}: {
    showGachaDialog?: boolean;
    robotName?: string;
}) {
    const showGachaDialogParams = showGachaDialog
        ? `?showGachaDialog=${showGachaDialog}`
        : '?showGachaDialog=false';
    const robotNameParams = robotName ? `&robotName=${encodeURIComponent(robotName)}` : '';
    Taro.redirectTo({
        url: `/pages/appreciate/index${showGachaDialogParams}${robotNameParams}`,
    });
}

export function jump2Explain() {
    Taro.navigateTo({
        url: `/pages/gacha-explain/index`,
    });
}

export function jump2JoinGroup({ success }: { success?: () => void }) {
    Taro.navigateTo({
        url: '/pages/join-group/index',
        success: () => {
            success?.();
        },
    });
}

export function jump2ProfileEdit({ keyboard_action }: { keyboard_action: number }) {
    const keyParams = keyboard_action ? `?keyboard_action=${keyboard_action}` : '';
    Taro.navigateTo({
        url: `/pages/profile-edit/index${keyParams}`,
    });
}

// 跳到剧情新webview，需要继续发心跳
export function jump2Epicplayer({
    chapterId,
    roleId,
    chapterPlayRecordId,
}: {
    chapterId: string;
    roleId: string;
    chapterPlayRecordId: string;
}) {
    Taro.eventCenter.trigger('taro_event_jump2NewWebview');
    openKey('st_epicplayer', {
        chapterId,
        roleId,
        chapterPlayRecordId,
    });
}

export const jumpLink = (url: string) => {
    if (!url) return;

    // 首页要用relaunch，不然获取不到url上参数
    if (
        url.startsWith('/pages/message/index') ||
        url.startsWith('/pages/friends/index') ||
        url.startsWith('/pages/explore/index') ||
        url.startsWith('/pages/self/index')
    ) {
        Taro.reLaunch({ url });
    } else if (url.startsWith('/')) {
        Taro.navigateTo({ url });
    } else if (/^https?:\/\//.test(url) || /^mirth:\/\//.test(url) || /^moyi:\/\//.test(url)) {
        openUrl(url);
    } else {
        openKey(url);
    }
};
