import { onPushJump } from '@/utils/rpc';
import { coronaWarnMessage } from '@music/mat-base-h5';
import { useEffect } from 'react';
import { officialAccountAccids } from '@/hooks/sessionStore';
import { jump2Chat, jump2Official } from '.';

interface ParsedResult {
    key: string;
    params: Record<string, string>;
}

const usePushJump = () => {
    const parseCustomUrl = (input: string): ParsedResult => {
        if (!input) return { key: '', params: {} };
        try {
            const url = new URL(input);
            const path = decodeURIComponent(url.searchParams.get('path') || '');

            if (!path) return { key: '', params: {} };
            const [key, query] = path.split('?');
            const result: ParsedResult = {
                key: key || '',
                params: {},
            };

            if (!query) return result;
            query.split('&').forEach((pair) => {
                const [encodedKey, encodedValue = ''] = pair.split('=');
                const params = decodeURIComponent(encodedKey);
                const value = decodeURIComponent(encodedValue);

                result.params[params] = value;
            });

            return result;
        } catch (e) {
            coronaWarnMessage('parseCustomUrl error', e);
        }
        return { key: '', params: {} };
    };

    useEffect(() => {
        const handlePushJump = (data: { url: string }) => {
            const { key, params } = parseCustomUrl(data.url);
            if (key === 'chat') {
                const robotAccid = params.robotAccid;
                if (officialAccountAccids.some((accid) => robotAccid?.includes(accid))) {
                    jump2Official({ robotAccid });
                } else {
                    jump2Chat({
                        robotUserId: params.robotUserId,
                        robotAccid: params.robotAccid,
                        hasAiChapter: params.hasAiChapter,
                        fromSource: params.fromSource,
                        botBgUrl: params.botBgUrl,
                        botStandByUrl: params.botStandByUrl,
                    });
                }
            }
        };
        onPushJump(handlePushJump);
    }, []);
};

export default usePushJump;
