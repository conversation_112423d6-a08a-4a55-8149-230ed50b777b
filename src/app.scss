@import '~taro-ui/dist/style/index.scss';

/*  #ifdef h5  */
@import 'tailwindcss/base';
@import 'tailwindcss/utilities';

/*  #endif */

:root {
    --status-bar-height: env(safe-area-inset-top);
    --sar: env(safe-area-inset-right);
    --safearea-height: env(safe-area-inset-bottom);
    --sal: env(safe-area-inset-left);
}

.taro-tabbar__tabbar {
    display: none !important;
}

.taro_router .taro_page.taro_page_show {
    max-height: 100vh !important;
    height: 100vh !important;
}

.taro_router .taro_page.taro_tabbar_page,
.taro_router .taro_page.taro_page_show.taro_page_stationed {
    transform: none !important;
    transition: none !important;
}

// Taro 默认的页面容器样式 hack，确保页面提前 display
.taro_page {
    overflow-y: hidden !important; // 安卓个别机型底部可以往上拖动
}

/*  #ifdef  h5  */
* {
    box-sizing: border-box;
}

img {
    user-select: none;
    -webkit-user-drag: none;
}

/*  #endif  */

/*  #ifdef  weapp  */
view,
text {
    box-sizing: border-box;
}

/*  #endif  */
