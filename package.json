{"name": "st-mirth-aichat", "version": "1.0.0", "private": true, "description": "心颜AI聊天", "templateInfo": {"name": "default", "typescript": true, "css": "Sass", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "pnpmbuild:h5": "npm install -g @tarojs/cli@3.6.16 && rm -rf node_modules && npx pnpm install && npm run build:h5", "build:h5": "taro build --type h5 && cp webcache.json dist/h5/", "build:h5:dev": "taro build --type h5 --mode prod", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "build:harmony-hybrid": "taro build --type harmony-hybrid", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5:dev -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch", "test": "jest", "publish:pages": "yarn build:h5 && yarn gh-pages -d dist/h5 -t", "lint": "elint lint \"src/**/*.{js,jsx,ts,tsx,scss}\" --fix", "beforecommit": "npm run lint && npm run check-file-size && npm run check-dom-usage && elint lint commit", "check-dom-usage": "node scripts/check-dom-usage.js", "check-file-size": "node scripts/check-file-size.js"}, "author": "", "dependencies": {"@babel/runtime": "^7.21.5", "@babel/runtime-corejs3": "^7.27.1", "@dawn/react": "3.0.14-beta.0", "@music/count-down": "^0.0.4", "@music/ct-animation": "^0.1.16-beta.5", "@music/ct-mobile-ms-tabs": "^2.0.9", "@music/epic-player-sdk": "^0.1.2", "@music/helper": "^0.3.5", "@music/mat-base-h5": "^6.0.0", "@music/mat-keyboard-h5": "^1.0.2", "@tarojs/components": "3.6.16", "@tarojs/plugin-framework-react": "3.6.16", "@tarojs/plugin-platform-h5": "3.6.16", "@tarojs/plugin-platform-weapp": "3.6.16", "@tarojs/react": "3.6.16", "@tarojs/rn-runner": "3.6.16", "@tarojs/runtime": "3.6.16", "@tarojs/shared": "3.6.16", "@tarojs/taro": "3.6.16", "classnames": "^2.5.1", "nim-web-sdk-ng": "10.9.1", "prop-types": "^15.8.1", "react": "^18.0.0", "react-dom": "^18.0.0", "react-native": "^0.70.0", "react-refresh": "^0.14.0", "resize-observer-polyfill": "^1.5.1", "swiper": "^11.2.6", "tailwindcss": "^3.3.3", "taro-ui": "^3.3.0", "zustand": "4.5.4"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-logical-assignment-operators": "^7.20.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-optional-catch-binding": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-instanceof": "^7.27.1", "@babel/plugin-transform-runtime": "^7.20.0", "@babel/preset-env": "^7.26.9", "@music/elint-preset-social": "^1.0.4", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "3.6.16", "@tarojs/plugin-platform-h5": "3.6.16", "@tarojs/taro-loader": "3.6.16", "@tarojs/test-utils-react": "^0.1.1", "@tarojs/webpack5-runner": "3.6.16", "@types/jest": "^29.3.1", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "4.33.0", "@typescript-eslint/parser": "4.33.0", "babel-loader": "8.3.0", "babel-preset-taro": "3.6.16", "chalk": "^5.4.1", "copy-webpack-plugin": "^13.0.0", "elint": "^2.0.1", "eslint": "7.32.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.5.0", "pnpm": "^10.8.1", "postcss": "^8.4.18", "react-native-linear-gradient": "^2.5.6", "react-refresh": "^0.11.0", "regenerator-runtime": "^0.13.7", "run-node": "^2.0.0", "stylelint": "13.8.0", "taro-plugin-environment": "^1.0.1", "tsconfig-paths-webpack-plugin": "^4.2.0", "typescript": "^4.1.0", "vconsole-webpack-plugin": "^1.8.0", "weapp-tailwindcss": "^4.1.7", "webpack": "5.78.0", "webpack-bundle-analyzer": "^4.10.2"}, "browserslist": ["last 3 versions", "Android >= 6", "ios >= 13"], "eslintIgnore": ["node_modules/", "public/"], "sideEffects": ["*.css", "*.less", "*.sass", "*.scss"], "pnpm": {"overrides": {"react": "^18", "react-dom": "^18"}}, "packageManager": "yarn@1.22.19+sha1.4ba7fc5c6e704fce2066ecbfb0b0d8976fe62447"}