declare module '*.png';
declare module '*.gif';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.svg';
declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.styl';
declare module '*.mp4';
declare module '*.webp';
declare module '*.apng';
declare namespace NodeJS {
    interface ProcessEnv {
        TARO_ENV: 'weapp' | 'swan' | 'alipay' | 'h5' | 'rn' | 'tt' | 'quickapp' | 'qq' | 'jd';
    }
    interface Global {
        globalData: object;
    }
}

declare const IS_H5: any;
declare const IS_WEAPP: any;
declare const IS_RN: any;
declare const API_HOST: string;
declare const API_LOG_HOST: string;
declare const IS_MOYI: boolean;
declare const IS_MIRTH: boolean;
declare const IS_ONLINE: boolean;
