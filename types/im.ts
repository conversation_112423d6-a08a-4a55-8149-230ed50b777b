export interface ImDetail {
    accId: string;
    token: string;
    userId: number;
}

export interface UserInfoType {
    userBase: {
        userId: number;
        avatarImgUrl: string;
        initTime: number;
    };
}

export interface RobotInfoType {
    aigcChatResourceInfo?: {
        botActionUrls: string[];
        botPictureUrl?: string;
        botBackgroundUrl?: string;
        botStandByUrls?: string[];
        bgmUrl?: string;
        bgmVolume?: number;
    };
    robotBaseInfo?: {
        accId: string;
        userId: string;
        age: number;
        nickname: string;
        avatarUrl: string;
        intro: string;
        labels?: string[];
        roleDesc?: string;
        gender: number;
    };
    friend: boolean;
    chatStyle: number;
    hasAiChapter: boolean;
}

export type MsgDataType = any;

// export interface MsgDataType extends V2NIMMessage {
//     customExt: {
//         serverExt: {
//             sessionPeriodId: string;
//             receiverInvisible: boolean;
//             messageRecordId: string;
//             monologueId: string;
//             opType: number;
//         } & Record<string, any>;
//         clientExt: {
//             sessionPeriodId: string;
//             receiverInvisible: boolean;
//         } & Record<string, any>;
//         senderInvisible: boolean;
//         receiverInvisible: boolean;
//     };

//     contentExt: {
//         content: {
//             type: string;
//             content: Record<string, any>;
//         };
//     };
//     idServer?: string;
// }

export type SystemMessage = any;
// export interface SystemMessage extends V2NIMCustomNotification {
//     contentExt: {
//         serverExt: {
//             type: number;
//             content: Record<string, any>;
//         };
//     };
// }

export enum MessageErrorCode {
    // 基础-反垃圾检测不通过
    antiSpam = 20001,
    // 基础-反作弊检测不通过
    antiCheat = 20002,
    // 业务状态-余额不足
    balaceInsufficient = 20030,
    // 业务-强制实名处罚
    realNameAuth = 20038,
}
