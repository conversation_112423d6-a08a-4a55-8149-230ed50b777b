interface OxResponse {
    data?: UserDetailVo;
    message?: string;
    code: number;
}

export interface UserDetailVo {
    /** 用户基础信息 */
    userBase?: UserBaseVo;
    /** 用户距离信息 */
    location?: LocationVo;
    /** 用户距离信息扩展 */
    locationExtVo?: LocationExtVo;
    /** 用户在线状态 */
    onlineStatusDto?: OnlineStatusDto;
    /** 用户相册 */
    album?: UserAlbumDto[];
    /** 用户资料信息 */
    userInfos?: UserInfo[];
    /** 用户状态信息 */
    userState?: UserStateVo;
    /** 用户关系信息 */
    userRelation?: UserRelationVo;
    /** 额外显示信息 */
    userExtInfo?: UserExtInfo;
}

interface UserBaseVo {
    userId?: number;
    /** 用户号 */
    userNo?: number;
    /** 昵称 */
    nickname?: string;
    /** 头像 nos key */
    avatarImgNosKey?: string;
    /** 头像 */
    avatarImgUrl?: string;
    /** 人生态度 */
    signature?: string;
    /** 年龄 */
    age?: number;
    /** 生日时间戳 */
    birthday?: number;
    /** 性别 */
    gender?: number;
    /** 星座 */
    constellation?: string;
    /** 省 */
    province?: string;
    /** 市 */
    city?: string;
    /** 身高 cm */
    height?: number;
    /** 体重 kg */
    weight?: number;
    /** 账号状态： */
    status?: number;
    /** 真人认证 */
    realMan?: number;
    /** 账号初始化时间 */
    initTime?: number;
    /** 颜值分 */
    beautyScore?: string;
    /** im账号ID */
    imAccId?: string;
}

interface LocationVo {
    country?: string;
    region?: string;
    city?: string;
    distance?: string;
}

interface LocationExtVo {
    /** 是否开启位置权限 */
    openLocation?: boolean;
}

interface OnlineStatusDto {
    /** 用户ID */
    userId?: number;
    /** 用户是否在线 */
    onlineFlag?: boolean;
    /** 最近在线时间（时间戳）
    在线/20分钟前--在线
    1h之前--xx分钟前活跃
    1d之前--xx小时前活跃
    7d之前--xx天前活跃
    else-->7天前活跃 */
    latestOnlineTime?: number;
    /** 当天在线时间（秒数） */
    todayOnlineTime?: number;
}

interface UserAlbumDto {
    /** 图片id */
    picId?: number;
    /** 所属uid */
    userId?: number;
    /** 照片位置 */
    sortIndex?: number;
    /** nosKey */
    nosKey?: string;
    /** 图片url */
    imgUrl?: string;
}

interface UserInfo {
    type?: number;
    /** 用户内容 */
    content?: string;
    /** 用户已选的标签（新版） */
    labelLibraryInfo?: LabelLibraryDto[];
    /** 图片 */
    picNosKey?: string;
    picUrl?: string;
    /** 生成资料的标签id */
    labelSerialIds?: number[];
    /** 用户手动输入的标签文案 */
    labelContent?: string;
    /** 当前照片的相似度文件 */
    similarNosKey?: string;
    /** 文学作品 */
    literatureInfo?: LiteratureInfo;
}

interface UserStateVo {
    /** 是否拉黑了该用户 */
    inBlackList?: boolean;
    /** 是否关注了该用户 */
    follow?: boolean;
}

interface UserRelationVo {
    /** 好友数量 */
    friendCount?: number;
    /** 关注数量 */
    followCount?: number;
    /** 粉丝数量 */
    fansCount?: number;
}

interface UserExtInfo {
    /** 资料完善度 */
    informationRatio?: number;
    /** 是否真人认证 */
    realMan?: boolean;
    /** 学历 */
    education?: string;
}

interface LabelLibraryDto {
    /** 标签id */
    labelSerialId?: number;
    /** icon */
    icon?: string;
    /** 标签文案 */
    content?: string;
    /** 自定义标签 */
    custom?: boolean;
}

interface LiteratureInfo {
    /** 资源id */
    resourceId?: string;
    /** 标题 */
    title?: string;
    /** 作者 */
    author?: string;
    /** 年份 */
    years?: string;
    /** 地区 */
    region?: string;
}

export interface UpdateUser {
    /** 用户基础信息 */
    base?: {
        nickname?: string;
        gender?: 1 | 2;
        avatarImgNosKey?: string;
    };
    /** 个人信息 */
    info?: string;
    /** 填写来源 */
    source?: string;
}
