export interface FavoriteDetailDataType {
    currentLevelIntimacyAcquire: number;
    currentLevelRelationName: string;
    nextLevelIntimacyAcquire: number;
    nextLevelRelationName: string;
    newIntimacyValue: number;
    targetUserId?: number;
    friend: boolean;
}

export interface FavoriteLevelDataType {
    intimacyAcquire: number; //亲密度等级所需要的亲密度值
    label: string; //关系名称
    level: number; //亲密度等级
    levelDesc: string; //亲密度等级说明
}

export interface FavoriteLevelExtensionDataType extends FavoriteLevelDataType {
    isOpen: boolean;
}
