import { AigcChatResourceInfo } from '@/types/bot';

export interface AigcChapterCardInfo {
    chapterId: string;
    chapterName: string;
    chapterDesc: string;
    chapterLevel: string;
    chapterImg: string;
    cardStatus: number;
    roleId: string;
}

export const ChapterLevel = {
    R: 'R',
    SR: 'SR',
    SSR: 'SSR',
};

export const CardStatus = {
    LOCKED: 0, // 锁定状态
    UNLOCK: 1, // 已解锁状态
};

export const bindChapterRelationName = (type?: string) => {
    if (type === 'familiar') {
        return '朦胧';
    }
    if (type === 'friend') {
        return '共振';
    }
    if (type === 'ambiguous') {
        return '牵念';
    }
    if (type === 'sweetie') {
        return '眷恋';
    }
    if (type === 'marry') {
        return '永恒';
    }
    return '朦 胧';
};

export interface AigcEndingCardInfo {
    endingCardId: string;
    endingName: string;
    endingDesc: string;
    bindChapterId: string;
    bindChapterName: string;
    endingCardIcon: string;
    endingCardImg: string;
    endingCardBigImg: string; // 视频url(策划眼中的动图)
    cardStatus: number;
    relatedRecordId: string;
    roleId: string;
    endingLevelImg: string;
    bindChapterType: number; // 0: 卡面章节；1: 主线章节
    bindChapterRelationType: string;
}

export interface AigcRobotProfile {
    userId: number;
    accId: string;
    userNo: number;
    nickname: string;
    avatarImgUrl: string;
    age: number;
    specialAge?: string;
    gender: number;
    intro: string; // 签名
    backgroundDesc: string; // 背景介绍
    backgroundImg: string;
    labels: string[];
    status: number;
    friend?: boolean;
    chapterCardInfoDtos?: AigcChapterCardInfo[];
    endingCardInfoDtos?: AigcEndingCardInfo[];
    aigcChatResourceInfo?: AigcChatResourceInfo;
}

export const GENDER = {
    MALE: 1,
    FEMALE: 2,
};

export function getAge(ageWrap: { age: number; specialAge?: string }): string {
    const { age, specialAge } = ageWrap;
    if (age >= 0) {
        return `${age}岁`;
    }
    return specialAge || '';
}
