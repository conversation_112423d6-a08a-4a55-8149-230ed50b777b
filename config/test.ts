// 测试/回归环境
const domain = process.env.DOMAIN;

const HOST = (() => {
    if (domain) {
        const match = domain.match(/st-qa-(.*?)\.mirth\.netease\.com/);

        // 测试环境走这里
        if (match) {
            return `http://api-qa-${match[1]}.mirth.netease.com`;
        }
    }

    // 这里不要改！！！回归环境走这里
    return 'http://api-qa.mirth.netease.com';
})();

const testConfig = {
    env: {
        NODE_ENV: '"production"',
    },
    defineConstants: {
        // 这里必须包一层 JSON.stringify，defineConstants 中的值会被 webpack 的 DefinePlugin 处理；
        API_HOST: JSON.stringify(HOST),
        API_LOG_HOST: JSON.stringify(HOST),
        IS_ONLINE: JSON.stringify(false),
    },
    mini: {},
    h5: {
        /**
         * 如果h5端编译后体积过大，可以使用webpack-bundle-analyzer插件对打包体积进行分析。
         * 参考代码如下：
         * webpackChain (chain) {
         *   chain.plugin('analyzer')
         *     .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [])
         * }
         */
    },
};

export default testConfig;
