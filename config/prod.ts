// 线上环境
const prodConfig = {
    env: {
        NODE_ENV: '"production"',
    },
    defineConstants: {
        // 这里必须包一层 JSON.stringify，defineConstants 中的值会被 webpack 的 DefinePlugin 处理；
        API_HOST: JSON.stringify('https://api.wave.163.com'),
        API_LOG_HOST: JSON.stringify('https://clientlogmirth.wave.163.com'),
        IS_ONLINE: JSON.stringify(true),
    },
    mini: {},
    h5: {
        /**
         * 如果h5端编译后体积过大，可以使用webpack-bundle-analyzer插件对打包体积进行分析。
         * 参考代码如下：
         * webpackChain (chain) {
         *   chain.plugin('analyzer')
         *     .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [])
         * }
         */
    },
};

export default prodConfig;
