import { defineConfig, type UserConfigExport } from '@tarojs/cli';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';
import path from 'path';
import TerserPlugin from 'terser-webpack-plugin';
import VConsolePlugin from 'vconsole-webpack-plugin';
import CopyWebpackPlugin from 'copy-webpack-plugin';
import devConfig from './dev';
import testConfig from './test';
import prodConfig from './prod';

// https://taro-docs.jd.com/docs/next/config#defineconfig-辅助函数
export default defineConfig(async (merge, { command, mode }) => {
  const config: UserConfigExport = {
    projectName: 'st-mirth-aichat',
    date: '2025-5-13',
    designWidth: 375,
    deviceRatio: {
        640: 2.34 / 2,
        750: 1,
        375: 2,
        828: 1.81 / 2
    },
    sourceRoot: 'src',
    outputRoot: `dist/${process.env.TARO_ENV}`,
    plugins: [],
    defineConstants: {
        IS_H5: JSON.stringify(process.env.TARO_ENV === 'h5'),
        IS_RN: JSON.stringify(process.env.TARO_ENV === 'rn'),
        IS_WEAPP: JSON.stringify(process.env.TARO_ENV === 'weapp'),
        IS_MOYI: JSON.stringify(process.env.APP_SOURCE === 'moyi'),
        IS_MIRTH: JSON.stringify(process.env.APP_SOURCE === 'mirth'),
        TARO_DOM_COMPAT: JSON.stringify(true),
    },
    alias: {
        '@/src': path.resolve(__dirname, '..', 'src'),
        '@/pages': path.resolve(__dirname, '..', 'src/pages'),
        '@/components': path.resolve(__dirname, '..', 'src/components'),
        '@/utils': path.resolve(__dirname, '..', 'src/utils'),
        '@/router': path.resolve(__dirname, '..', 'src/router'),
        '@/service': path.resolve(__dirname, '..', 'src/service'),
        '@/hooks': path.resolve(__dirname, '..', 'src/hooks'),
        '@/assets': path.resolve(__dirname, '..', 'src/assets'),
        '@/const': path.resolve(__dirname, '..', 'src/const'),
        '@/types': path.resolve(__dirname, '..', 'types'),
        '@/store': path.resolve(__dirname, '..', 'src/store'),
        '@/platformconfig': path.resolve(__dirname, '..', 'platform'),
    },
    copy: {
        patterns: [],
        options: {}
    },
    framework: 'react',
    compiler: {
        type: 'webpack5',
        prebundle: {
            enable: false,
        },
    },
    cache: {
        enable: false // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
    },
    mini: {
        postcss: {
            pxtransform: {
                enable: true,
                config: {}
            },
            url: {
                enable: true,
                config: {
                    limit: 1024 // 设定转换尺寸上限
                }
            },
            cssModules: {
            enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
            config: {
                namingPattern: 'module', // 转换模式，取值为 global/module
                generateScopedName: '[name]__[local]___[hash:base64:5]'
            }
        }
    },
    miniCssExtractPluginOption: {
        ignoreOrder: true
    },
    webpackChain(chain, webpack) {
        chain.resolve.extensions.add('.d.ts');
        chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin)

        // 添加特殊处理，修复 WXSS 编译错误
        if (process.env.TARO_ENV === 'weapp') {
            // 引入weapp-tailwindcss插件
            const { UnifiedWebpackPluginV5 } = require('weapp-tailwindcss/webpack');

            chain.merge({
                plugin: {
                    install: {
                        plugin: UnifiedWebpackPluginV5,
                        args: [{
                            appType: 'taro',
                            // 开启 rem -> rpx 的转化
                            rem2rpx: true
                        }]
                    }
                }
            });

            // chain.merge({
            //     optimization: {
            //       splitChunks: {
            //         chunks: 'all',
            //         cacheGroups: {
            //           vendors: {
            //             test: /[\\/]node_modules[\\/]/,
            //             name: 'vendors',
            //             minSize: 100 * 1024,      // 每份最小 100KB
            //             maxSize: 300 * 1024,      // 超过 300KB 则再拆
            //             priority: -10
            //           },
            //           common: {
            //             name: 'common',
            //             minChunks: 2,
            //             priority: -20,
            //             reuseExistingChunk: true
            //           }
            //         }
            //       }
            //     }
            //   });

            chain.module
                .rule('sass')
                .oneOf('0')
                .use('sass-loader')
                .tap(options => {
                    // 修改 sass-loader 配置，避免生成特殊字符
                    const sassOptions = options?.sassOptions || {};
                    return {
                        ...options,
                        sassOptions: {
                            ...sassOptions,
                            outputStyle: 'expanded', // 使用展开模式而非压缩模式
                            charset: false // 禁用字符集
                        }
                    };
                });
          }
      }
    },
    h5: {
      publicPath: `/${process.env.ROUTE ?? 'st-mirth-aichat'}/`,
      staticDirectory: 'static',
      esnextModules: ['taro-ui'],
      output: {
          filename: 'js/[name].[hash:8].js',
          chunkFilename: 'js/[name].[chunkhash:8].js',
      },
      postcss: {
          pxtransform: {
              enable: true,
              config: {},
          },
          autoprefixer: {
              enable: true,
              config: {},
          },
          cssModules: {
              enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
              config: {
                  namingPattern: 'module', // 转换模式，取值为 global/module
                  generateScopedName: '[name]__[local]___[hash:base64:5]',
              },
          }
      },
      miniCssExtractPluginOption: {
          ignoreOrder: true,
          filename: 'css/[name].[hash].css',
          chunkFilename: 'css/[name].[chunkhash].css'
      },
      devServer: {
          allowedHosts: 'all',
          host: '0.0.0.0',
      },
      router: {
          customRoutes: {
              // "页面路径": "自定义路由"
              '/pages/home/<USER>': ['/home/<USER>', '/home'],
              '/pages/chat/index': ['/chat/index.html', '/chat'],
              '/pages/message/index': ['/message/index.html', '/message', '/index.html'],
              '/pages/friends/index': ['/friends/index.html', '/friends'],
              '/pages/explore/index': ['/explore/index.html', '/explore'],
              '/pages/profile/index': ['/profile/index.html', '/profile'],
          },
          mode: 'browser',
          basename: `/${process.env.ROUTE ?? 'st-mirth-aichat'}`,
      },
    webpackChain: (chain, webpack) => {
        //   chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin);

        chain.optimization.splitChunks({
            chunks: 'all',
            maxInitialRequests: 5, // 入口点最大并行请求数
            maxAsyncRequests: 5, // 异步请求数
            minSize: 50000, // 最小体积到50KB
            maxSize: 300000, // 最大体积到300KB
            cacheGroups: {
                // React核心库打包到一起
                reactVendor: {
                    name: 'react-vendor',
                    test: /[\\/]node_modules[\\/](react|react-dom|zustand|scheduler|use-sync-external-store|immer)[\\/]/,
                    priority: 40,
                    chunks: 'all',
                    enforce: true,
                },
                nimSdk: {
                    name: 'nim-sdk',
                    test: /[\\/]node_modules[\\/](nim-web-sdk-ng)[\\/]/,
                    priority: 30,
                    chunks: 'all',
                    enforce: true,
                },
                taroComponents: {
                    name: 'taro-components',
                    test: /[\\/]node_modules[\\/](@tarojs[\\/]components)[\\/]/,
                    priority: 25,
                    chunks: 'all',
                    enforce: true,
                },
                // 将常用的第三方库单独打包
                commonVendors: {
                    name: 'common-vendors',
                    test: /[\\/]node_modules[\\/](taro-ui|@tarojs\/taro|@tarojs\/runtime|classnames)[\\/]/,
                    priority: 15,
                    chunks: 'all',
                    enforce: true,
                },
                // 其他所有node_modules打包到一起
                vendors: {
                    name: 'vendors',
                    test: /[\\/]node_modules[\\/]/,
                    priority: 10,
                    chunks: 'all',
                    enforce: true,
                    reuseExistingChunk: true,
                },
                commons: {
                    name: 'commons',
                    minChunks: 3,
                    priority: 5,
                    reuseExistingChunk: true,
                },
            },
        });

        if (process.env.NODE_ENV === 'development') {
            // 设置 @pmmmwh/react-refresh-webpack-plugin 插件的 overlay 属性，取消插件错误覆盖层的行为
            chain.plugin('fastRefreshPlugin').tap(() => {
                return [
                    {
                        overlay: false,
                    },
                ];
            });
        }

        if (process.env.ENV === 'test' || process.env.NODE_ENV === 'development') {
            chain.plugin('vconsole').use(
                new VConsolePlugin({
                    enable: true, // 设置为 true 表示启用 vConsole
                })
            );
            // 添加 source map 以便更好地调试
            chain.devtool('source-map');
        }

        if (process.env.NODE_ENV === 'production') {
            chain.optimization
                .minimizer('terserPlugin')
                .tap(args => {
                    // NOTE: epic-player-sdk 中的内容不能经过二次压缩，否则会有问题
                    args[0].exclude = /epic-player-sdk/;
                    args[0].minify = TerserPlugin.swcMinify;
                    args[0].terserOptions = {
                        compress: true,
                        sourceMap: true,
                    };
                    return args;
                });
        }

        chain.merge({
            performance: { maxEntrypointSize: 10000000, maxAssetSize: 30000000 },
        });
        //   chain.resolve.extensions.add('.d.ts');

        // 按部署环境手动注入 puzzle 脚本
        // See: https://docs.popo.netease.com/lingxi/36eec9c17fd34437a4f4d325f641430f
        chain.plugin('htmlWebpackPlugin').tap((args) => {
            const puzzle_id = process.env.PUZZLE_ID;
            const src = puzzle_id ? `https://s6.music.126.net/puzzle/puzzle@${puzzle_id}.js` : '';
            const script = `<script async="" crossorigin="anonymous" src="${src}"></script>`;
            args[0].templateParameters = {
                puzzle_script: script,
            };
            return args;
        });

        // 使 @tarojs/components 和 nim-web-sdk-ng 的 JS 文件也经过 babel-loader 转译
        // 避免其中包含的 Object.entries() 方法在低版本浏览器上报错白屏
        // See: https://github.com/NervJS/taro/blob/v3.5.7/packages/taro-webpack5-runner/src/webpack/H5WebpackModule.ts#L268
        const excludeFunction = chain.module.rule('script').exclude.values()[0];
        // 原先 Taro 内置的 rule 不支持 .mjs 后缀，导致 swiper/react 不被 babel 处理，这里扩展一下
        chain.module.rule('script').test(/\.m?[tj]sx?$/i);
        chain.module.rule('script').exclude.clear();
        chain.module.rule('script').exclude.add((filename) => {
            if (/vconsole/.test(filename)) {
                return false;
            }
            if (/@tarojs\/components/.test(filename)) {
                return false;
            }
            if (/nim-web-sdk-ng/.test(filename)) {
                return false;
            }
            if (/swiper/.test(filename)) {
                return false;
            }
            if (/@music\/epic-player-sdk/.test(filename)) {
                return false;
            }
            return excludeFunction(filename);
        });

        // EPIC 剧情播放器 SDK 接入
        chain.plugin('CopyWebpackPlugin').use(CopyWebpackPlugin, [
            {
                patterns: [
                    {
                        from: 'node_modules/@music/epic-player-sdk/dist',
                        to: 'epic-player-sdk',
                    },
                ],
            },
        ]);
      },
    },
    rn: {
      appName: 'taroDemo',
      postcss: {
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        }
      }
    }
  }
  // 本地开发
  if (process.env.NODE_ENV === 'development') {
    return merge({}, config, devConfig);
  }

  // 测试/回归
  // https://febase-doc.st.netease.com/docs/deploy/web#%E6%98%AF%E5%90%A6%E4%BC%A0%E5%85%A5%E5%91%BD%E4%BB%A4%E8%A1%8C%E5%8F%82%E6%95%B0
  if (process.env.ENV === 'test') {
      return merge({}, config, testConfig);
  }

  // 正式环境
  return merge({}, config, prodConfig);
})
