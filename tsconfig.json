{
  "compilerOptions": {
    "target": "es2017",
    "module": "commonjs",
    "removeComments": false,
    "preserveConstEnums": true,
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "noImplicitAny": false,
    "allowSyntheticDefaultImports": true,
    "outDir": "lib",
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "strictNullChecks": false,
    "sourceMap": true,
    "jsx": "react-jsx",
    "allowJs": true,
    "resolveJsonModule": true,
    "baseUrl": ".",
    "rootDir": ".",
    "esModuleInterop": true,
    "typeRoots": ["node_modules/@types"],
    "paths": {
      "@/components": ["src/components/"],
      "@/components/*": ["src/components/*"],
      "@/utils": ["src/utils"],
      "@/utils/*": ["src/utils/*"],
      "@/service": ["src/service"],
      "@/service/*": ["src/service/*"],
      "@/hooks": ["src/hooks"],
      "@/hooks/*": ["src/hooks/*"],
      "@/assets": ["src/assets"],
      "@/assets/*": ["src/assets/*"],
      "@/types": ["types"],
      "@/types/*": ["types/*"],
      "@/const": ["src/const"],
      "@/const/*": ["src/const/*"],
      "@/pages/*": ["src/pages/*"],
      "@/store": ["src/store"],
      "@/store/*": ["src/store/*"],
      "@/router": ["src/router/"],
      "@/router/*": ["src/router/*"],
    }
  },
  "include": ["./src", "./types", "./config"],
  "compileOnSave": false
}
